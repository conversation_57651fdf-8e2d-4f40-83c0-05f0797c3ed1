dependencies {
    api(project(":cpe-products-auditcenter-api"))
    api(project(":cpe-products-auditcenter-common"))
    api(project(":cpe-products-auditcenter-businflow:cpe-products-auditcenter-businflow-service"))
    api(project(":cpe-products-auditcenter-service:cpe-products-auditcenter-service-core"))

    //智能审核 dubbo调用
    api("com.cairh:cpe-aiaudit-server-api")
    api('org.apache.dubbo:dubbo-spring-boot-starter')
    //注解校验
    api('javax.validation:validation-api:2.0.1.Final')
    api("com.cairh:cpe-context")//bizException
    api('com.cairh:cpe-products-dispatch-server-api')


}

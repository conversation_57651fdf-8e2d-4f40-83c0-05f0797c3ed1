package com.cairh.cpe.service.aidispatch.service;


import com.cairh.cpe.service.aidispatch.response.DispatchTaskDetailResp;

import java.util.Date;

public interface IAiDispatchAchieveService {

    String submitDispatchData(String request_no, String not_allow_auditor, String flowtask_id, Date create_datetime);

    void handleAuditDispatchTask(String dispatch_task_id, String operator_no);

    void finishDispatchTask(String dispatch_task_id, String operator_no);

    void changeOperatorStatus(String dispatchStatus, String operator_no);

    boolean getOperatorStatus(String operator_no);

    void claimTask(String dispatch_task_id, String operator_no, String pop_msg_model);

    void cancelTask(String dispatch_task_id, String operator_no, String pop_msg_model);

    /**
     * 派单 ，需要判断
     *
     * @param serial_id
     */
    void handleDispatchTask(String request_no, String serial_id);

    void cancelReviewTask(String request_no, String serial_id);

    void handleNeedPushFlagDispatchTask(String request_no, String serial_id);

    void taskChangeOperator(String dispatch_task_id, String operator_no, String remove_operator);

    DispatchTaskDetailResp queryTaskDetail(String dispatch_task_id);

    /**
     * 发送redis队列
     *
     * @param subsys_id
     * @param operator_no
     */
    void popUpMsg(Long subsys_id, String operator_no);

    void handUpNotice(String task_id, String pause_flag);

    void invalidTask(String dispatch_task_id, String operator_no, String push_flag);

}
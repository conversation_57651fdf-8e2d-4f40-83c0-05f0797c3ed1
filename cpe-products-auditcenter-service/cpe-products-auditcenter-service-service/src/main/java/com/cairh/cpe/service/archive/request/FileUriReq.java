package com.cairh.cpe.service.archive.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 功能说明: 文件地址上传保存<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 * 开发时间: 2022/5/18<br>
 */
@Data
public class FileUriReq implements Serializable {

    /**
     * 文件地址 文件地址可能是磁盘路径或网络地址
     */
    @NotBlank
    private String file_path;
    /**
     * 文件后缀，当传了该值时下载文件后缀名以该值为准(例子：pdf)
     */
    private String file_name_suffix;
    /**备注*/
    private String remark;
    /**
     * 文件归档类型(1-资源文件；2-普通文件 默认值：2)
     */
    private String file_arch_type;

    /**
     * 当文件不存在时是否创建文件 默认为false
     */
    private boolean created_file_flag;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;


    /**
     * 是否需要下载文件
     */
    private String uploadFileResult;
    /**
     * 是否需要下载文件
     */
    private boolean not_down;


}

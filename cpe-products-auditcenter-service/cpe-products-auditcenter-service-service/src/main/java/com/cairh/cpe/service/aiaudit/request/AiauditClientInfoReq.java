package com.cairh.cpe.service.aiaudit.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 功能说明: TODO<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 */
@Data
public class AiauditClientInfoReq implements Serializable {

    // 姓名
    private String client_name;

    // 证件类型
    private String id_kind;

    // 证件号
    private String id_no;

    // 性别
    private String client_gender;

    // 民族
    private String id_nation;

    // 生日 yyyyMMdd
    private Integer birthday;

    // 住址
    private String id_address;

    // 签发机关
    private String id_grant_organ;

    // 起始时间 yyyyMMdd
    private Integer id_begindate;

    // 截止时间 yyyyMMdd
    private Integer id_enddate;

    // 签发机关匹配的地址行政区划
    private String address_oragn;

    // 签发机关匹配的地址行政区划映射数据
    private Map<String, String> address_oragns;
}

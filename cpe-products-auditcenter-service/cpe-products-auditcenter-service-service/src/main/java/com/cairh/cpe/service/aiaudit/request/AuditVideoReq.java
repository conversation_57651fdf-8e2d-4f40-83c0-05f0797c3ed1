package com.cairh.cpe.service.aiaudit.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 功能说明: TODO<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 */
@Data
public class AuditVideoReq implements Serializable {
    /**
     * 审核编号 Y
     *
     * 对于同一个业务, 编号应一致; 开启一个新业务时, 需重新申请
     */
    private String audit_code;

    /**
     * 服务厂商 N
     *
     * 厂商定制, 不传则使用组件厂商配置 (可能涉及到配置映射转换)
     */
    private String service_vender;

    /**
     * 客户照片 Y
     *
     * 取决于source, 可能是一个base64, 路径, 也可以是fileId
     */
    private String image;

    /**
     * 单双向视频 Y
     *
     * 取决于source, 可能是一个base64, 路径, 也可以是fileId
     */
    private String video;

    /**
     * 是否双向视频 Y
     */
    private Boolean is_twoway_video;

    /**
     * 客户照片 审核源 Y 审核素材源, 泛用base64, 也支持路径, 档案格式
     */
    private String image_source_type;

    /**
     * 单双向视频 审核源 Y 审核素材源, 泛用base64, 也支持路径, 档案格式
     */
    private String video_source_type;
}

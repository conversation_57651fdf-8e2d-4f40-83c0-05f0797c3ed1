package com.cairh.cpe.service.idverify.impl;

import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.common.entity.request.AddressAssociationForm;
import com.cairh.cpe.common.entity.response.AddressAssociationQryResp;
import com.cairh.cpe.common.entity.support.BackendUser;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.base.rpc.IVBaseAutocompleteDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseQueryAddressAssociationReq;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseAddressAssociationQryResp;
import com.cairh.cpe.rpc.CpeRpcException;
import com.cairh.cpe.service.idverify.IAddressAssociationQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class AddressAssociationQuxeryServiceImpl implements IAddressAssociationQuery {

    @DubboReference(check = false)
    IVBaseAutocompleteDubboService baseAutocompleteDubboService;

    @Autowired
    private CacheBackendUser cacheBackendUser;

    @Override
    public List<AddressAssociationQryResp> addressAssociationQuery(AddressAssociationForm addressAssociationForm) {
        try {
            VBaseQueryAddressAssociationReq req = new VBaseQueryAddressAssociationReq();
            BaseBeanUtil.copyProperties(addressAssociationForm,req);
            List<VBaseAddressAssociationQryResp> resps = baseAutocompleteDubboService.queryAddressAssociation(req);
            List<AddressAssociationQryResp> list = new ArrayList<>();
            for (VBaseAddressAssociationQryResp resp : resps) {
                AddressAssociationQryResp qryResp = new AddressAssociationQryResp();
                BaseBeanUtil.copyProperties(resp,qryResp);
                list.add(qryResp);
            }
            return list;
        } catch (
                CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            //throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_查询地址联想异常", e);
        }
        return null;
    }


    @Override
    public String getBackendUserByStaffNo(String operator_no) {
        String newValue ="";
        BackendUser backendUser = cacheBackendUser.getBackendUserByStaffNo(String.valueOf(operator_no));
        if (backendUser != null) {
            return newValue = backendUser.getUser_name();
        }
        return " ";
    }
}

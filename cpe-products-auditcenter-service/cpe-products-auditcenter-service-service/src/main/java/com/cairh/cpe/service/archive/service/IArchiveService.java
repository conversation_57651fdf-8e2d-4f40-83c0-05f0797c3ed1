package com.cairh.cpe.service.archive.service;

import com.cairh.cpe.service.archive.request.*;
import com.cairh.cpe.service.archive.response.ElectRotateImageResp;

/**
 * @program: cpe-products-wskh
 * @description: 对接档案组件
 * @author: syy
 * @create: 2022-02-14 16:11
 **/
public interface IArchiveService {


    /**
     * 图片上传
     *
     * @param imageRequest 图片base64
     * @return String
     */
    String electUploadImage(ImageInfo imageRequest);


    /**
     * 图片下载
     *
     * @param fileRecord_id 文件ID
     * @return String
     */
    String electDownloadImage(String fileRecord_id);


    /**
     * 文件地址上传
     * @param fileUriReq
     * @return String
     */
    String electUploadFileByUri(FileUriReq fileUriReq);


    /**
     * 文件下载
     *
     * @param fileRecord_id 文件ID
     * @return byte[]
     */
    byte[] electDownloadFile(String fileRecord_id);


    /**
     * 文件上传
     *
     * @param fileInfo 文件信息
     * @return String
     */
    String electUploadFile(FileInfo fileInfo);

    /**
     * 图片旋转
     * @param electRotateImageReq
     * @return
     */
    ElectRotateImageResp electRotateImage(ElectRotateImageReq electRotateImageReq);

    /**
     *  3方档案上传
     * @param request
     * @return
     */
    String uploadToThird(ElecUploadToThirdRequest request);
}

package com.cairh.cpe.service.third.impl;

import com.alibaba.fastjson2.JSON;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.component.elect.IEsbComponentElectThirdDubboService;
import com.cairh.cpe.esb.component.elect.dto.req.ElecUploadToThirdReq;
import com.cairh.cpe.esb.component.elect.dto.req.ThirdFileInfo;
import com.cairh.cpe.esb.component.elect.dto.resp.ElecUploadToThirdResp;
import com.cairh.cpe.service.third.IElectThirdFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Objects;

/**
 * 三方文件服务实现类
 *
 * <AUTHOR>
 * @since 2025/5/7 11:25
 */
@Slf4j
@Service
public class ElectThirdFileServiceImpl implements IElectThirdFileService {

    @DubboReference(check = false)
    private IEsbComponentElectThirdDubboService esbComponentElectThirdDubboService;

    @Override
    public String getFileThirdId(String file_id, String id_no) {
        ThirdFileInfo thirdFileInfo = new ThirdFileInfo();
        thirdFileInfo.setFilerecord_ids(file_id);
        thirdFileInfo.setFile_no(WskhConstant.IMAGE_NO_80);

        ElecUploadToThirdReq elecUploadToThirdReq = new ElecUploadToThirdReq();
        elecUploadToThirdReq.setThird_file_info(Collections.singletonList(thirdFileInfo));
        elecUploadToThirdReq.setId_no(id_no);
        elecUploadToThirdReq.setNo_submit_flag("1");

        log.info("获取新意文件上传入参：{}", JSON.toJSONString(elecUploadToThirdReq));

        try {
            ElecUploadToThirdResp elecUploadToThirdResp = esbComponentElectThirdDubboService.uploadToThird(elecUploadToThirdReq);
            log.info("获取新意文件上传出参：{}", JSON.toJSONString(elecUploadToThirdResp));

            if (Objects.nonNull(elecUploadToThirdResp)) {
                String sourceNos = elecUploadToThirdResp.getSource_nos();
                if (StringUtils.isNotBlank(sourceNos)) {
                    return sourceNos.contains(",") ? sourceNos.split(",")[1] : sourceNos;
                }
            }
        } catch (Exception e) {
            log.error("获取新意文件上传返回id异常", e);
            throw new BizException("-1", "获取新意文件上传返回id异常");
        }

        return "";
    }
}

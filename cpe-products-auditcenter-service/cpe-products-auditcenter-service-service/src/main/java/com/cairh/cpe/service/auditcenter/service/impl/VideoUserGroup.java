package com.cairh.cpe.service.auditcenter.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.cairh.cpe.service.auditcenter.service.AbstractGenericVideoGroup;
import com.cairh.cpe.service.auditcenter.service.request.VideoUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 视频用户集, 基于视频集, 对视频用户信息作出维护
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class VideoUserGroup extends AbstractGenericVideoGroup<VideoUser> {
    private static final String PREFIX = "{cpe_esb_video}";
    private static final String VIDEO_USER = "video_user";
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    public VideoUserGroup() {
        super(PREFIX + VIDEO_USER);
    }

    public VideoUserGroup(String videoGroupPrefix) {
        super(videoGroupPrefix);
    }

    @Override
    @Transactional
    public void insert(String id, VideoUser video) {
        Map<String, Object> videoMap = BeanUtil.beanToMap(video);
        try {
            redisTemplate.opsForHash().putAll(id, videoMap);
            redisTemplate.opsForSet().add(videoGroupPrefix, StringUtils.removeStart(id, videoGroupPrefix));
        } catch (Exception e) {
            log.error("video group insert error, id: {}", id, e);
            throw e;
        }
    }
}

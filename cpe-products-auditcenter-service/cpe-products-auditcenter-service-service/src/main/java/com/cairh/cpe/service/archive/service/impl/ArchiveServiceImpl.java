package com.cairh.cpe.service.archive.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.component.elect.IEsbComponentElectDubboService;
import com.cairh.cpe.esb.component.elect.IEsbComponentElectThirdDubboService;
import com.cairh.cpe.esb.component.elect.dto.req.*;
import com.cairh.cpe.esb.component.elect.dto.resp.*;
import com.cairh.cpe.service.archive.request.*;
import com.cairh.cpe.service.archive.response.ElectRotateImageResp;
import com.cairh.cpe.service.archive.service.IArchiveService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ArchiveServiceImpl implements IArchiveService {

    @DubboReference(check = false, connections = 10)
    IEsbComponentElectDubboService componentElectDubboService;
    @DubboReference(check = false, connections = 10)
    IEsbComponentElectThirdDubboService componentElectThirdDubboService;

    @Override
    public String electUploadImage(ImageInfo imageRequest) {
        ElectUploadImageRequest req = new ElectUploadImageRequest();
        BeanUtil.copyProperties(imageRequest, req);

        String file_id = "";
        try {
            ElectUploadImageResponse resp = componentElectDubboService.electUploadImage(req);
            if (resp != null) {
                file_id = resp.getFilerecord_id();
            }
        } catch (Exception e) {
            log.error("dubbo服务_图片上传异常", e);
            throw new BizException("组件图片上传服务异常");
        }

        return file_id;
    }

    @Override
    public String electDownloadImage(String fileRecord_id) {
        ElectDownloadImageRequest req = new ElectDownloadImageRequest();
        req.setFilerecord_id(fileRecord_id);
        String base64 = "";
        try {
            ElectDownloadImageResponse resp = componentElectDubboService.electDownloadImage(req);
            if (resp != null) {
                base64 = resp.getBase64_image();
            }
        } catch (Exception e) {
            log.error("dubbo服务_图片下载异常", e);
        }

        return base64;
    }

    @Override
    public String electUploadFileByUri(FileUriReq fileUriReq) {
        ElectUploadFileByUriRequest req = new ElectUploadFileByUriRequest();
        BeanUtil.copyProperties(fileUriReq, req);

        String file_id = "";
        try {
            ElectUploadFileByUriResponse resp = componentElectDubboService.electUploadFileByUri(req);
            if (resp != null) {
                file_id = resp.getFilerecord_id();
            }
        } catch (Exception e) {
            log.error("dubbo服务_文件地址上传异常",e);
            throw new BizException("组件文件地址上传服务异常");
        }

        return file_id;
    }

    @Override
    public byte[] electDownloadFile(String fileRecord_id) {
        ElectDownloadFileRequest req = new ElectDownloadFileRequest();
        req.setFilerecord_id(fileRecord_id);
        byte[] result = null;
        try {
            ElectDownloadFileResponse resp = componentElectDubboService.electDownloadFile(req);
            if (resp != null) {
                result = resp.getFile();
            }
        } catch (Exception e) {
            log.error("dubbo服务_文件下载异常", e);
            throw new BizException("组件文件下载服务异常");
        }

        return result;
    }

    @Override
    public String electUploadFile(FileInfo fileInfo) {
        ElectUploadFileRequest req = new ElectUploadFileRequest();
        BeanUtil.copyProperties(fileInfo, req);

        String file_id = "";
        try {
            ElectUploadFileResponse resp = componentElectDubboService.electUploadFile(req);
            if (resp != null) {
                file_id = resp.getFilerecord_id();
            }
        } catch (Exception e) {
            log.error("dubbo服务_文件上传异常", e);
            throw new BizException("组件文件上传服务异常");
        }

        return file_id;
    }

    @Override
    public ElectRotateImageResp electRotateImage(ElectRotateImageReq electRotateImageReq) {
        ElectRotateImageRequest req = new ElectRotateImageRequest();
        BeanUtil.copyProperties(electRotateImageReq, req);

        ElectRotateImageResp result = new ElectRotateImageResp();
        try {
            ElectRotateImageResponse resp = componentElectDubboService.electRotateImage(req);
            if (resp != null) {
                BeanUtil.copyProperties(resp, result);
            }
        } catch (Exception e) {
            log.error("dubbo服务_图片旋转异常", e);
            throw new BizException("组件图片旋转服务异常");
        }

        return result;
    }

    @Override
    public String uploadToThird(ElecUploadToThirdRequest request) {
        try {
            ElecUploadToThirdByMobileReq req = new ElecUploadToThirdByMobileReq();
            BeanUtil.copyProperties(request, req);
            componentElectThirdDubboService.uploadToThirdByMobile(req);
        } catch (Exception e) {
            log.error("dubbo服务_3方档案上传异常", e);
        }
        return null;
    }
}

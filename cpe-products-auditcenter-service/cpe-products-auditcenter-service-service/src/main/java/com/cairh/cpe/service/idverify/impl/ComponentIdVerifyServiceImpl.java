package com.cairh.cpe.service.idverify.impl;

import cn.hutool.core.bean.BeanUtil;
import com.cairh.cpe.common.entity.request.VerifyMobileReq;
import com.cairh.cpe.common.entity.request.VerifyPassPortRequest;
import com.cairh.cpe.common.entity.request.VerifyPoliceAllRequest;
import com.cairh.cpe.common.entity.request.VerifyPoliceReq;
import com.cairh.cpe.common.entity.response.*;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.base.rpc.IVBaseMobileLocationService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseMobileLocationQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseMobileLocationQryResponse;
import com.cairh.cpe.esb.component.idverify.IEsbComponentIdVerifyDubboService;
import com.cairh.cpe.esb.component.idverify.dto.req.VerifyMobileRequest;
import com.cairh.cpe.esb.component.idverify.dto.req.VerifyPassPortReq;
import com.cairh.cpe.esb.component.idverify.dto.req.VerifyPoliceAllReq;
import com.cairh.cpe.esb.component.idverify.dto.req.VerifyPoliceRequest;
import com.cairh.cpe.esb.component.idverify.dto.resp.VerifyMobileResp;
import com.cairh.cpe.esb.component.idverify.dto.resp.VerifyPassPortResp;
import com.cairh.cpe.esb.component.idverify.dto.resp.VerifyPoliceAllResp;
import com.cairh.cpe.esb.component.idverify.dto.resp.VerifyPoliceResponse;
import com.cairh.cpe.service.idverify.IComponentIdVerifyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 认证
 */
@Slf4j
@Service
public class ComponentIdVerifyServiceImpl implements IComponentIdVerifyService {

    @DubboReference(check = false)
    IEsbComponentIdVerifyDubboService componentIdVerifyDubboService;
    @DubboReference(check = false)
    IVBaseMobileLocationService baseMobileLocationService;

    @Override
    public VerifyPoliceResp verifyPolice(VerifyPoliceReq verifyPoliceReq) {
        VerifyPoliceResp result = new VerifyPoliceResp();
        try {
            VerifyPoliceRequest req = new VerifyPoliceRequest();
            BeanUtil.copyProperties(verifyPoliceReq, req);
            VerifyPoliceResponse resp = componentIdVerifyDubboService.verifyPolice(req);
            if (resp != null) {
                BeanUtil.copyProperties(resp, result);
                return result;
            }
        } catch (Exception e) {
            log.error("组件dubbo服务_公安认证异常", e);
            throw new BizException("-1", "组件dubbo服务_公安认证异常");
        }

        return null;
    }

    @Override
    public VerifyPoliceAllResult verifyPoliceAll(VerifyPoliceAllRequest verifyPoliceAllRequest) {
        VerifyPoliceAllResult result = new VerifyPoliceAllResult();
        try {
            VerifyPoliceAllReq req = new VerifyPoliceAllReq();
            BeanUtil.copyProperties(verifyPoliceAllRequest, req);
            VerifyPoliceAllResp resp = componentIdVerifyDubboService.verifyPoliceAll(req);
            if (resp != null) {
                BeanUtil.copyProperties(resp, result);
                return result;
            }
        } catch (Exception e) {
            log.error("组件dubbo服务_全要素公安认证异常", e);
            throw new BizException("-1", "组件dubbo服务_全要素公安认证异常");
        }
        return null;
    }

    @Override
    public VerifyMobileResult verifyMobile(VerifyMobileReq verifyMobileReq) {
        try {
            VerifyMobileRequest req = new VerifyMobileRequest();
            BeanUtil.copyProperties(verifyMobileReq, req);
            VerifyMobileResp resp = componentIdVerifyDubboService.verifyMobile(req);
            if (resp != null) {
                VerifyMobileResult result = new VerifyMobileResult();
                BeanUtil.copyProperties(resp, result);
                return result;
            }
        } catch (Exception e) {
            log.error("组件dubbo服务_手机实名制异常", e);
            throw new BizException("-1", "组件dubbo服务_手机实名制异常");
        }

        return null;
    }

    @Override
    public MobileLocationQryResp baseDataQryMobileLocation(String mobile_tel) {
        try {
            VBaseMobileLocationQryRequest req = new VBaseMobileLocationQryRequest();
            List<String> mobList = new ArrayList<>();
            mobList.add(mobile_tel);
            req.setMobile_tel_list(mobList);
            ;
            List<VBaseMobileLocationQryResponse> resp = baseMobileLocationService.baseDataQryMobileLocation(req);
            if (CollectionUtils.isNotEmpty(resp)) {
                MobileLocationQryResp result = new MobileLocationQryResp();
                BeanUtil.copyProperties(resp.get(0), result);
                return result;
            }
        } catch (Exception e) {
            log.error("组件dubbo服务_手机归属地查询异常", e);
            throw new BizException("-1", "组件dubbo服务_手机归属地查询异常");
        }
        return null;
    }

    @Override
    public VerifyPassPortResult verifyPassPort(VerifyPassPortRequest verifyPassPortRequest) {
        try {
            VerifyPassPortReq req = new VerifyPassPortReq();
            BeanUtil.copyProperties(verifyPassPortRequest, req);
            VerifyPassPortResp resp = componentIdVerifyDubboService.verifyPassPort(req);
            if (resp != null) {
                VerifyPassPortResult result = new VerifyPassPortResult();
                BeanUtil.copyProperties(resp, result);
                return result;
            }
        } catch (Exception e) {
            log.error("组件dubbo服务_出入境证件信息核查异常", e);
            throw new BizException("-1", "组件dubbo服务_出入境证件信息核查异常");
        }
        return null;
    }
}

package com.cairh.cpe.service.idverify.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cairh.cpe.common.constant.Fields;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.WskhFields;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.common.util.MessageUtil;
import com.cairh.cpe.esb.component.video.IEsbComponentVideoDubboService;
import com.cairh.cpe.esb.component.video.dto.req.QueryVideowordsReq;
import com.cairh.cpe.esb.component.video.dto.resp.QueryVideowordsRes;
import com.cairh.cpe.esb.component.video.dto.resp.support.VideowordsConfig;
import com.cairh.cpe.esb.component.video.dto.resp.support.VideowordsModel;
import com.cairh.cpe.service.aiaudit.request.SpeechSourceReq;
import com.cairh.cpe.service.idverify.IVideoScripModelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VideoScripModelServiceImpl implements IVideoScripModelService {

    public final static String USER_GENDER_MAN = "先生";
    public final static String USER_GENDER_WOMAN = "女士";
    private static final String CLIENT_NAME = "client_name";
    private static final String USERNAME = "userName";
    private static final String USER_NAME = "user_name";
    private static final String GENDER_NAME = "gender_name";
    private static final String USER_GENDER = "user_gender";
    private static final String CLIENT_GENDER = "client_gender";
    private static final String BIZ_NAME = "biz_name";
    private static final String BIZ_NAME_VALUE = "集中审核";

    @DubboReference
    private IEsbComponentVideoDubboService componentVideoDubboService;
    @Autowired
    private IRequestService requestService;
    @Autowired
    private IBusinFlowRequestService businFlowRequestService;


    /**
     * *获取视频话术模版
     *
     * @param request_no
     * @return
     */
    @Override
    public List<SpeechSourceReq> getSpeechSource(String request_no) {
        ClobContentInfo clob = requestService.getAllDataByRequestNo(request_no);
        Map<String, Object> clobMap = BaseBeanUtil.beanToMap(clob);
        Map<String, String> replaceMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(clobMap)) {
            replaceMap = clobMap.entrySet().stream()
                    .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> String.valueOf(e.getValue())));
        }
        BusinFlowRequest request = businFlowRequestService.getById(request_no);
        List<SpeechSourceReq> list = new ArrayList<>();

        QueryVideowordsReq queryVideowordsReq = new QueryVideowordsReq();
        queryVideowordsReq.setSubsys_no(Integer.valueOf(WskhConstant.SUBSYS_ID));
        queryVideowordsReq.setBusin_type(Integer.valueOf(request.getBusin_type()));
        queryVideowordsReq.setVideo_type(clob.getVideo_type());
        // 网厅业务办理增加规则表达式匹配
        if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, request.getBusin_type())) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(WskhFields.HAND_CLIENT_CATEGORY, clob.getClient_category());
            queryVideowordsReq.setRegular_data(jsonObject.toJSONString());
        }

        // 100058集中见证双向视频&单向视频增加规则表达式匹配
        if (StringUtils.equals(clob.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL) &&
                StringUtils.equalsAny(clob.getVideo_type(), WskhConstant.VIDEO_TYPE_2, WskhConstant.VIDEO_TYPE_1)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Fields.ID_KIND, clob.getId_kind());
            jsonObject.put(Fields.VIDEO_SPEECH_ID, clob.getVideo_speech_id());
            queryVideowordsReq.setRegular_data(jsonObject.toJSONString());
        }

        QueryVideowordsRes queryVideowordsRes = componentVideoDubboService.compvideoQueryVideowords(queryVideowordsReq);
        if (Objects.nonNull(queryVideowordsRes) && CollectionUtil.isNotEmpty(queryVideowordsRes.getVideomodel_resultlist())) {
            // 获取视频话术模版
            List<VideowordsModel> videowordsModels = queryVideowordsRes.getVideomodel_resultlist();
            if (CollectionUtil.isNotEmpty(videowordsModels)) {
                VideowordsModel videowordsModel = videowordsModels.get(0);
                if (CollectionUtil.isNotEmpty(queryVideowordsRes.getVideoconfig_resultlist())) {
                    // 通过视频话术模版id获取对应的视频话术配置表数据
                    List<VideowordsConfig> videowordsConfigs = queryVideowordsRes.getVideoconfig_resultlist()
                            .stream()
                            .filter(videoConfig -> StrUtil.equals(videoConfig.getVideowordsmodel_id(), videowordsModel.getSerial_id()))
                            .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(videowordsConfigs)) {
                        CollectionUtil.sort(videowordsConfigs, (o1, o2) -> (int) (o1.getOrder_no() - o2.getOrder_no()));
                        // 遍历获取对应的问答内容
                        for (VideowordsConfig videowordsConfig : videowordsConfigs) {
                            SpeechSourceReq speechSourceReq = new SpeechSourceReq();
                            if (StringUtils.equals(videowordsConfig.getWords_type(), "1")) {
                                String words_content = videowordsConfig.getWords_content();
                                words_content = renderData(words_content, replaceMap);
                                speechSourceReq.setQuestion(words_content);
                                Map<String, String> finalReplaceMap = replaceMap;
                                //黑名单
                                if (StringUtils.isNotBlank(videowordsConfig.getError_answer())) {
                                    List<String> errorAnswerList = Arrays.asList(videowordsConfig.getError_answer().split(","));
                                    List<String> collect = errorAnswerList.stream().map(answer -> renderData(answer, finalReplaceMap)).collect(Collectors.toList());
                                    speechSourceReq.setAnswer_black(collect);
                                }
                                //白名单
                                if (StringUtils.isNotBlank(videowordsConfig.getCorrect_answer())) {
                                    List<String> correctAnswerList = Arrays.asList(videowordsConfig.getCorrect_answer().split(","));
                                    List<String> collect = correctAnswerList.stream().map(answer -> renderData(answer, finalReplaceMap)).collect(Collectors.toList());
                                    speechSourceReq.setAnswer_white(collect);
                                }
                                speechSourceReq.setAnswer("这是答案");
                            } else { //其他类型
                                String words_content = videowordsConfig.getWords_content();
                                words_content = renderData(words_content, replaceMap);
                                speechSourceReq.setQuestion(words_content);
                            }
                            list.add(speechSourceReq);
                        }
                    }
                }
            }
        }
        log.info("request_no={}，话述生成: {} ", request_no, list);
        return list;
    }

    @Override
    public String renderData(String context, Map<String, String> params) {
        if (StringUtils.isBlank(context) || CollectionUtil.isEmpty(params)) {
            return context;
        }
        if (context.contains(USERNAME) && !params.containsKey(USERNAME)) {
            params.put(USERNAME, params.get(CLIENT_NAME));
        }
        if (context.contains(USER_NAME) && !params.containsKey(USER_NAME)) {
            params.put(USER_NAME, params.get(CLIENT_NAME));
        }
        if (context.contains(GENDER_NAME) && !params.containsKey(GENDER_NAME) ||
                context.contains(USER_GENDER) && !params.containsKey(USER_GENDER)) {
            String sexGender = "";
            if ("1".equals(params.get(CLIENT_GENDER))) {
                sexGender = USER_GENDER_MAN;
            } else if ("2".equals(params.get(CLIENT_GENDER))) {
                sexGender = USER_GENDER_WOMAN;
            }
            params.put(GENDER_NAME, sexGender);
            params.put(USER_GENDER, sexGender);
        }
        params.put(BIZ_NAME, BIZ_NAME_VALUE);
        if (StringUtils.isNotBlank(context) && CollectionUtil.isNotEmpty(params)) {
            context = MessageUtil.getMessage(context, params);
        }
        return context;
    }
}

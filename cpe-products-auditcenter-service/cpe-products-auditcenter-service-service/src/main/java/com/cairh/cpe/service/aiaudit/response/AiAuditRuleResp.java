package com.cairh.cpe.service.aiaudit.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 功能说明: TODO<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 */
@Data
public class AiAuditRuleResp implements Serializable {


    /**
     * 审核 匹配结果
     */
    private String match_result;

    /**
     * 处理信息
     *
     * 审核处理结果信息说明
     */
    private String handle_info;

    // 识别项目组
    private String item_identity;

    /**
     * 规则名
     *
     * 审核规则名, 英文格式
     */
    private String drl_rule_name;

    /**
     * 中文规则名
     *
     * 审核规则名, 中文格式
     */
    private String rule_name;


    // ocr

    /**
     * 原信息
     *
     * 待审核原始信息
     */
    private String source_data;

    /**
     * 识别信息
     *
     * 审核识别信息
     */
    private String identify_data;


    // face

    /**
     * 匹配分数
     *
     * 人像审核得分
     */
    private Double match_score;

    /**
     * 通过分数
     *
     * 人像通过阈值
     */
    private Double pass_score;


    // video

    /**
     * 时间戳
     *
     * 帧时间节点
     */
    private Double time_stamp;

    /**
     * 探测像数
     *
     * 帧探测人像数目
     */
    private Integer detect_amount;

    /**
     * 要求人脸数
     *
     * 单双向视频参数
     */
    private Integer demand_face_amount;

    // audio

    /**
     * 审核详情 语音特定
     */
    private List<SpeechAuditDetailInfo> speechAuditDetails;

    /**
     * 识别数目 语音特定
     */
    private Integer recognize_amount;
}

package com.cairh.cpe.service.idverify.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.service.IBusinFlowRecordService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.common.util.http.RestTemplateUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.service.archive.request.ImageInfo;
import com.cairh.cpe.service.archive.service.IArchiveService;
import com.cairh.cpe.service.idverify.IDealRpaRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class DealRpaRecordServiceImpl implements IDealRpaRecordService {

    @Autowired
    private IArchiveService archiveService;

    @Autowired
    private RedissonUtil redissonUtil;
    @Autowired
    private RestTemplateUtil restTemplateUtil;
    @Autowired
    private IRequestService requestService;
    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private IBusinFlowRecordService businFlowRecordService;

    @Override
    public String dealUserRpaRecord(String request_no) {
        String error_info = "";
        String redisKey = String.format(LockKeyConstant.WSKH_LOCK_TASK_DISHONEST_ID, request_no);
        try {
            RLock lock = redissonClient.getLock(redisKey);
            if (lock.isLocked()) {
                log.info("crh_rpa查询失信图片未获取到锁[{}]", request_no);
                error_info = "crh_rpa查询失信图片未获取到锁";
                return error_info;
            } else {
                redissonUtil.tryLock(redisKey, 60, 30, TimeUnit.SECONDS);
            }
            ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);

            JSONObject result = submitToRpa(request_no, clobContentInfo.getClient_name(), clobContentInfo.getId_no());
            if (result != null) {
                if ("200".equals(result.getString("code"))) {
                    String file_record_id = uploadRpaImage(clobContentInfo, result.getString("data"));
                    HashMap<String, Object> params = new HashMap<>();
                    params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22388);
                    params.put(Fields.BUSINESS_REMARK, "调用crh_rpa接口");
                    params.put(WskhFields.DISHONEST_ID, file_record_id);
                    params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);//审核操作流水
                    businFlowRecordService.saveBusinFlowRecord(request_no, params);
                    log.info("用户[{}]调用crh_rpa接口返回图片id为[{}]", request_no, file_record_id);
                } else {
                    error_info = "crh_rpa接口执行失败";
                }
            } else {
                error_info = "crh_rpa接口返回无结果";
            }
        } catch (Exception e) {
            e.printStackTrace();
            error_info = "crh_rpa接口调用异常";
        } finally {
            redissonUtil.unlock(redisKey);
            return error_info;
        }
    }

    /**
     * * 将返回的诚信档案图片上传。并保存
     *
     * @param clobContentInfo
     * @param data
     * @return
     */
    private String uploadRpaImage(ClobContentInfo clobContentInfo, String data) {
        ImageInfo imageInfo = new ImageInfo();
        imageInfo.setImage_name("common.png");
        imageInfo.setBase64_image(data);
        imageInfo.setRemark(WskhConstant.SUBSYS_ID);
        return archiveService.electUploadImage(imageInfo);
    }


    public JSONObject submitToRpa(String request_no, String client_name, String id_no) {
        JSONObject result = null;
        String crhRpaUrl = PropertySource.get(PropKeyConstant.WSKH_CRHRPA_SERVICE_URL, "");
        if (StringUtils.isNotBlank(crhRpaUrl)) {
            try {
                //HttpHeaders headers = new HttpHeaders();
                //headers.setContentType(MediaType.APPLICATION_JSON);
                //Map<String, Object> body = new HashMap<>();
                //body.put("qry_name", client_name);
                //body.put("qry_id_no", id_no);
                //HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(body, headers);
                String strUrl = crhRpaUrl + "?qry_name=" + client_name + "&qry_id_no=" + id_no;
                String responseResult = restTemplateUtil.restTemplate().getForObject(strUrl, String.class);
                //String responseResult = restTemplateUtil.restTemplate().postForObject(crhRpaUrl, requestEntity, String.class);
                log.info("用户[{}]RPA启动流程出参=={}", request_no, responseResult);
                result = JSON.parseObject(responseResult);
            } catch (Exception e) {
                e.printStackTrace();
                throw new BizException(ErrorEnum.QUERY_TIMEOUT_ERROR.getValue(), ErrorEnum.QUERY_TIMEOUT_ERROR.getDesc());
            }
        } else {
            log.error("crhRpa地址未配置");
        }
        return result;
    }
}

package com.cairh.cpe.service.archive.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 功能说明: 组件图片信息<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 * 开发时间: 2022/4/7<br>
 */
@Data
public class ImageInfo implements Serializable {

    /**图片base64*/
    private String base64_image;
    /**图片名称*/
    private String image_name;
    /**备注*/
    private String remark;

    /**文件记录ID*/
    private String filerecord_id;


}

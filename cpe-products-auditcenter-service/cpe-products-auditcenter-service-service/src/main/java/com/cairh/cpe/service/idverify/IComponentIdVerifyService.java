package com.cairh.cpe.service.idverify;

import com.cairh.cpe.common.entity.request.VerifyMobileReq;
import com.cairh.cpe.common.entity.request.VerifyPassPortRequest;
import com.cairh.cpe.common.entity.request.VerifyPoliceAllRequest;
import com.cairh.cpe.common.entity.request.VerifyPoliceReq;
import com.cairh.cpe.common.entity.response.*;

public interface IComponentIdVerifyService {

    /**
     * 3要素公安认证
     */
    VerifyPoliceResp verifyPolice(VerifyPoliceReq verifyPoliceReq);

    /**
     * 全要素公安认证
     */
    VerifyPoliceAllResult verifyPoliceAll(VerifyPoliceAllRequest verifyPoliceAllRequest);

    /**
     * 手机实名制
     */
    VerifyMobileResult verifyMobile(VerifyMobileReq verifyMobileReq);

    /**
     * 查询手机号归属地
     */
    MobileLocationQryResp baseDataQryMobileLocation(String mobile_tel);

    /**
     * 出入境证件信息核查
     */
    VerifyPassPortResult verifyPassPort(VerifyPassPortRequest verifyPassPortRequest);
}

package com.cairh.cpe.service.aiaudit.service;


import com.cairh.cpe.aiaudit.ocr.req.AuditOcrForeignResidenceInfoRequest;
import com.cairh.cpe.aiaudit.ocr.req.AuditOcrGATPassInfoRequest;
import com.cairh.cpe.aiaudit.ocr.req.AuditOcrGATResidenceInfoRequest;
import com.cairh.cpe.service.aiaudit.request.*;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;
import com.cairh.cpe.service.aiaudit.response.AuditAudioIdentityResp;
import com.cairh.cpe.service.aiaudit.response.AuditBusinRecordQueryResp;

import java.util.List;

public interface IAiAuditService {


    /**
     * 查询智能审核结果
     *
     * @param auditBusinRecordQueryReq
     * @return
     */
    List<AuditBusinRecordQueryResp> queryAuditBusinRecord(AuditBusinRecordQueryReq auditBusinRecordQueryReq);


    /**
     * 查询整体结果
     *
     * @param audit_code
     * @return
     */
    String queryUnitedAuditResult(String audit_code);


    /**
     * 智能审核申请编号
     *
     * @param applyAuditCodeReq
     * @return
     */
    String applyAuditCode(ApplyAuditCodeReq applyAuditCodeReq);

    /**
     * 客户基本信息审核
     *
     * @param auditClientReq
     * @return
     */
    List<AiAuditRuleResp> auditClient(AuditClientReq auditClientReq);

    /**
     * OCR身份证审核
     *
     * @param auditOcrIdCardReq
     * @return
     */
    List<AiAuditRuleResp> auditIdCard(AuditOcrIdCardReq auditOcrIdCardReq);

    /**
     * OCR港澳台通行证审核
     *
     * @param auditOcrGATPassInfoRequest
     * @return
     */
    List<AiAuditRuleResp> auditGATPassInfo(AuditOcrGATPassInfoRequest auditOcrGATPassInfoRequest);

    /**
     * OCR港澳台居住证审核
     *
     * @param auditOcrGATResidenceInfoRequest
     * @return
     */
    List<AiAuditRuleResp> auditGATResidenceInfo(AuditOcrGATResidenceInfoRequest auditOcrGATResidenceInfoRequest);

    /**
     * OCR外国人永居证审核
     *
     * @param auditOcrForeignResidenceInfoRequest
     * @return
     */
    List<AiAuditRuleResp> auditForeignResidenceInfo(AuditOcrForeignResidenceInfoRequest auditOcrForeignResidenceInfoRequest);

    /**
     * OCR图像质检
     *
     * @param auditOcrImageQualityReq
     * @return
     */
    List<AiAuditRuleResp> auditImageQuality(AuditOcrImageQualityReq auditOcrImageQualityReq);

    /**
     * 人像审核
     *
     * @param auditFaceReq
     * @return
     */
    List<AiAuditRuleResp> auditFace(AuditFaceReq auditFaceReq);

    /**
     * 视频审核
     *
     * @param auditVideoReq
     * @return
     */
    List<AiAuditRuleResp> auditVideo(AuditVideoReq auditVideoReq);

    /**
     * 语音发起
     *
     * @param auditAudioIdentifyReq
     * @return
     */
    AuditAudioIdentityResp auditAudioIdentify(AuditAudioIdentifyReq auditAudioIdentifyReq);

    /**
     * 语音审核
     *
     * @param auditAudioReq
     * @return
     */
    List<AiAuditRuleResp> auditAudio(AuditAudioReq auditAudioReq);

}

package com.cairh.cpe.service.aiaudit.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 功能说明: TODO<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 */
@Data
public class AuditBusinRecordUpdateReq implements Serializable {

    /**
     * 审核编号 Y
     */
    private String audit_code;

    /**
     * 规则英文名 Y
     */
    private String drl_rule_name;

    /**
     * 匹配结果 Y
     */
    private String match_result;
}

package com.cairh.cpe.service.auditcenter.service;

import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.service.auditcenter.service.request.VideoUser;

public interface IBidirectionalVideoHandlerService {

    void processVideoInterruption(BusinFlowTask businFlowTask, String reasonMsg);

    void clearBidirectionalVideoTask(BusinFlowTask businFlowTask, String reasonMsg);

    void clearBidirectionalVideoQueue(VideoUser user);

    void clearBidirectionalVideoUser();

    void heartbeatBidirectionalTask(String serial_id);
}

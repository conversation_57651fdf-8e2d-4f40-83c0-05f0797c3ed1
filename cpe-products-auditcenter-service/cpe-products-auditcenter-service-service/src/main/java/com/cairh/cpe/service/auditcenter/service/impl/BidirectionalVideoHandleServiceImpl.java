package com.cairh.cpe.service.auditcenter.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cairh.cpe.businflow.entity.request.AuditResultNotifyReq;
import com.cairh.cpe.businflow.service.IAuditCallbackService;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.UserQueryExtInfo;
import com.cairh.cpe.common.service.*;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.component.video.queue.IQueue;
import com.cairh.cpe.service.aidispatch.service.IAiDispatchAchieveService;
import com.cairh.cpe.service.auditcenter.service.IBidirectionalVideoHandlerService;
import com.cairh.cpe.service.auditcenter.service.IVideoGroup;
import com.cairh.cpe.service.auditcenter.service.request.VideoUser;
import com.cairh.cpe.service.config.AiAuditThreadPoolTaskExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 双向视频处理服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BidirectionalVideoHandleServiceImpl implements IBidirectionalVideoHandlerService {
    private static final String BIDIRECTIONAL_CLEAR = "见证作废";
    private static final String OPEN_BIDIRECTIONAL_CLEAR = "开户见证作废";
    @Autowired
    private IRequestFlowService requestFlowService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private IAiDispatchAchieveService aiDispatchAchieveService;
    @Autowired
    private IBusinFlowRequestService businFlowRequestService;
    @Autowired
    private RedissonUtil redissonUtil;
    @Resource
    private IVideoGroup<VideoUser> videoGroup;
    @Resource
    private QueueGroup queueGroup;
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private IBusinFlowParamsService businFlowParamsService;
    @Resource
    private IUserQueryExtInfoService userQueryExtInfoService;
    @Resource
    private AiAuditThreadPoolTaskExecutor aiAuditThreadPoolTaskExecutor;
    @Autowired
    private IAuditCallbackService auditCallbackService;
    @Autowired
    private IBusinProcessRequestAuditTrailService businProcessRequestAuditTrailService;

    /**
     * 处理视频中断任务
     *
     * @param businFlowTask 中断的任务
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processVideoInterruption(BusinFlowTask businFlowTask, String reasonMsg) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, businFlowTask.getRequest_no());
        boolean isLock = redissonUtil.tryLock(lockKey, 0, 30, TimeUnit.SECONDS);
        if (!isLock) {
            log.info("处理视频中断任务未获取到锁，request_no={}", businFlowTask.getRequest_no());
            return;
        }
        try {
            // 查询BusinFlowTask任务
            businFlowTask = businFlowTaskService.getById(businFlowTask.getSerial_id());
            log.info("视频中断更新任务request_no={}", businFlowTask.getRequest_no());
            // 任务状态只有为1和2
            if (!StringUtils.equalsAny(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_PENDING, FlowStatusConst.AUDIT_AUDITING, FlowStatusConst.AUDIT_TRANSFER)) {
                log.warn("视频中断更新任务状态异常，request_no={},task_status={}", businFlowTask.getRequest_no(), businFlowTask.getTask_status());
                return;
            }
            log.info("任务作废通知派单任务回收 requestNo ={} serial_id ={}", businFlowTask.getRequest_no(), businFlowTask.getSerial_id());
            // 通知派单任务进行任务作废
            if (StringUtils.isNotBlank(businFlowTask.getDispatch_task_id())) {
                aiDispatchAchieveService.invalidTask(businFlowTask.getDispatch_task_id(),
                        StringUtils.isNotBlank(businFlowTask.getOperator_no()) ? businFlowTask.getOperator_no() : WskhConstant.SUPER_USER,
                        businFlowTask.getPush_flag());
            }

            // 更新任务状态为作废-8
            businFlowTask.setTask_status(FlowStatusConst.AUDIT_INVALIDATE);
            businFlowTask.setFinish_datetime(new Date());
            String newReasonMsg = getReasonMsg(reasonMsg);
            businFlowTask.setOp_content(newReasonMsg);
            businFlowTaskService.updateById(businFlowTask);

            // 更新request表状态
            BusinFlowRequest businFlowRequest = new BusinFlowRequest();
            businFlowRequest.setRequest_no(businFlowTask.getRequest_no());
            businFlowRequest.setRequest_status(FlowStatusConst.REQUEST_STATUS_INVALIDATE);
            businFlowRequest.setUpdate_datetime(new Date());
            log.info("任务作废更新businFlowRequest表，request_no={}, serial_id={},更改信息{}", businFlowTask.getRequest_no(), businFlowTask.getSerial_id(), businFlowRequest);
            businFlowRequestService.updateById(businFlowRequest);

            // 更新user扩展表状态
            UserQueryExtInfo userQueryExtInfo = new UserQueryExtInfo();
            userQueryExtInfo.setRequest_no(businFlowTask.getRequest_no());
            userQueryExtInfo.setRequest_status(FlowStatusConst.REQUEST_STATUS_INVALIDATE);
            userQueryExtInfo.setUpdate_datetime(new Date());
            userQueryExtInfo.setAudit_finish_datetime(new Date());
            log.info("任务作废更新userQueryExtInfo表，request_no={}, serial_id={},更改信息{}", businFlowTask.getRequest_no(), businFlowTask.getSerial_id(), userQueryExtInfo);
            userQueryExtInfoService.updateById(userQueryExtInfo);

            // 更新request状态以及记录流水
            Map<String, Object> params = new HashMap<>();
            params.put(Fields.REQUEST_STATUS, FlowStatusConst.REQUEST_STATUS_INVALIDATE);
            params.put(Fields.UPDATE_DATETIME, new Date());
            params.put(Fields.BUSINESS_REMARK, newReasonMsg);
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22400);
            log.info("任务作废更新BusinFlowParams表，request_no={}, serial_id={}", businFlowTask.getRequest_no(), businFlowTask.getSerial_id());
            requestFlowService.saveParamsRecord(businFlowTask.getRequest_no(), params);

            log.info("任务作废保存快照 requestNo ={}", businFlowTask.getRequest_no());
            businFlowParamsService.saveSnapshot(businFlowTask.getRequest_no(), "invalidate");
            userQueryExtInfoService.saveSnapshot(businFlowTask.getRequest_no(), "invalidate");

            // 发送消息到kafka，通知开户端任务作废
            BusinFlowTask finalBusinFlowTask = businFlowTask;
            aiAuditThreadPoolTaskExecutor.execute(() -> {
                log.info("见证作废回调国泰通知开始------ requestNo ={} task_id={} serial_id={}",
                        finalBusinFlowTask.getRequest_no(), finalBusinFlowTask.getTask_id(), finalBusinFlowTask.getSerial_id());
                AuditResultNotifyReq auditResultNotifyReq = new AuditResultNotifyReq();
                auditResultNotifyReq.setRequest_no(finalBusinFlowTask.getRequest_no());
                auditResultNotifyReq.setAnode_id(finalBusinFlowTask.getTask_type());
                auditResultNotifyReq.setHandle_type("auditInvalidate");
                auditResultNotifyReq.setFlowtask_id(finalBusinFlowTask.getSerial_id());
                auditResultNotifyReq.setTask_id(finalBusinFlowTask.getTask_id());
                auditCallbackService.auditResultNotify(auditResultNotifyReq);
                log.info("见证作废回调国泰通知结束------ requestNo ={} task_id={} serial_id={}",
                        finalBusinFlowTask.getRequest_no(), finalBusinFlowTask.getTask_id(), finalBusinFlowTask.getSerial_id());
            });
            // 更新业务流程信息为作废状态
            businProcessRequestAuditTrailService.updateBusinProcessRequestAuditTrail(businFlowTask, null,
                    UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_AUDITINVALIDATE);
        } catch (Exception e) {
            log.error("视频中断更新任务异常，request_no={} serial_id={}", businFlowTask.getRequest_no(), businFlowTask.getSerial_id(), e);
            throw new BizException(ErrorEnum.BIDIRECTIONAL_TASK_VIDEO_INTERRUPT_ERROR.getValue(), e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }

    }

    /**
     * 清理双向视频任务
     *
     * @param businFlowTask 清理的任务信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void clearBidirectionalVideoTask(BusinFlowTask businFlowTask, String reasonMsg) {
        // 任务更新为作废
        processVideoInterruption(businFlowTask, reasonMsg);
        // 队列缓存信息清除
        List<VideoUser> users = videoGroup.queryAll();
        if (CollectionUtil.isNotEmpty(users)) {
            users.stream()
                    .filter(user -> user.getRequest_id().equals(businFlowTask.getSerial_id()))
                    .findFirst()
                    .ifPresent(this::clearBidirectionalVideoQueue);
        }
    }

    /**
     * 清理双向视频队列
     *
     * @param user 视频队列信息
     */
    @Override
    public void clearBidirectionalVideoQueue(VideoUser user) {
        String uniqueId = user.getUnique_id();
        //清理用户 异常退出  判断条件：心跳时间超过十秒无响应 且状态为 排队中（status=0）
        IQueue queue = queueGroup.exist(uniqueId);
        if (Objects.nonNull(queue)) {
            if (abnormalExit(user)) {
                //修改 用户状态
                videoGroup.update(uniqueId, VideoUser.STATUS, "");
                //移除排队
                removeQueue(user, uniqueId);
            }
        }
        if (unLive(user)) {
            //按顺序依次刷新(全局 -> 自身)
            videoGroup.remove(uniqueId);
            redisTemplate.opsForSet().remove("{cpe_esb_video}video_user", uniqueId);
            log.debug("clearBidirectionalVideoQueue 清理非活跃用户(业务流水号)：{}", uniqueId);
            removeQueue(user, uniqueId);
        }
    }

    @Override
    public void clearBidirectionalVideoUser() {
        log.info("clearBidirectionalVideoUser开始清理！");
        // 使用Pipeline优化Redis操作
        redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            // 删除{cpe_esb_video}queue_group
            connection.del("{cpe_esb_video}queue_group".getBytes());
            return null;
        });
        // 查询{cpe_esb_video}video_queue 默认视频级别为10
        Set<String> set = Objects.requireNonNull(stringRedisTemplate
                        .opsForZSet()
                        .range("{cpe_esb_video}video_queue".concat(String.valueOf(VideoConstant.DEFAULT_VIDEO_LEVEL)), 0L, -1L))
                .stream()
                .map(String::valueOf)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(set)) {
            return;
        }
        // 使用Pipeline优化Redis操作
        redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            // 删除{cpe_esb_video}video_user*********
            set.forEach(uniqueId -> {
                String userKey = "{cpe_esb_video}video_user" + uniqueId.replace("\"", "");
                connection.del(userKey.getBytes());
            });
            // 删除{cpe_esb_video}video_queue 默认视频级别为10
            connection.del("{cpe_esb_video}video_queue".concat(String.valueOf(VideoConstant.DEFAULT_VIDEO_LEVEL)).getBytes());
            return null;
        });
        log.info("clearBidirectionalVideoUser结束清理！");
    }

    /**
     * 双向视频心跳记录刷新时间
     *
     * @param serial_id 任务唯一编号
     */
    @Override
    public void heartbeatBidirectionalTask(String serial_id) {
        try {
            // 记录当前时间戳到redis中
            redisTemplate.opsForZSet().add(RedisKeyConstant.WSKH_AC_BIDIRECTIONAL_TASK_HEARTBEAT, serial_id, System.currentTimeMillis());
        } catch (Exception e) {
            log.error("heartbeatBidirectionalTask保存redis异常，serial_id={}", serial_id, e);
            throw new BizException(ErrorEnum.BIDIRECTIONAL_TASK_HEART_ERROR.getValue(), e.getMessage());
        }
    }

    private void removeQueue(VideoUser user, String uniqueId) {
        // 队列信息清掉
        // 按顺序依次刷新(队列)
        IQueue queue = queueGroup.exist(uniqueId);
        if (Objects.nonNull(queue)) {
            // 非活跃用户仍处于排队状态
            if (queue.dequeue(uniqueId)) {
                log.info("clearBidirectionalVideoQueue 清理非活跃用户队列清理成功(队列名称)：{}", queue.getName());
                // 处理用户被清理出视频队列
                user.setStatus(VideoConstant.STATUS_5_VIDEOED);
                log.debug("clearBidirectionalVideoQueue user dequeue success, user_id: {}", uniqueId);
                clearVideoQueuePosition(user);
            } else {
                log.debug("clearBidirectionalVideoQueue user dequeue failed, user_id: {}", uniqueId);
            }
        } else {
            clearVideoQueuePosition(user);
        }
    }

    private void clearVideoQueuePosition(VideoUser user) {
        //清理 缓存排队位置
        String videoNum = VideoConstant.VIDEO_QUEUE_USER_WAIT_POSITION + user.getUnique_id();
        if (stringRedisTemplate.hasKey(videoNum)) {
            stringRedisTemplate.delete(videoNum);
        }
    }

    /**
     * 视频用户是否活跃
     *
     * @param
     * @return 是否活跃, true否, false是
     */
    private boolean abnormalExit(VideoUser user) {
        String uniqueId = user.getUnique_id();
        if (Objects.isNull(user.getLast_status_update_time()) || user.getLast_status_update_time() <= 0 || Objects.isNull(user.getStatus()) || !StringUtils.equals("0", user.getStatus())) {
            return false;
        }
        if (Objects.isNull(user.getLast_status_update_time()) || user.getLast_status_update_time() <= 0L) {
            //用户不存在,但已无凭证或状态最后变更时间尚未赋值/异常取值,统一认为用户新到尚活跃
            return false;
        }

        VideoUser newUser = videoGroup.query(uniqueId);
        if (Objects.isNull(newUser) || Objects.isNull(newUser.getLast_status_update_time()) || newUser.getLast_status_update_time() <= 0 || Objects.isNull(newUser.getStatus()) || !StringUtils.equals("0", newUser.getStatus())) {
            return false;
        }
        long o = System.currentTimeMillis();
        Long lastStatusUpdateTime = newUser.getLast_status_update_time();
        long interval = (o - lastStatusUpdateTime) / 1000;
        //状态最后变更时间超过阈值认为非活跃
        boolean flag = interval > 5;
        if (flag) {
            log.debug("clearBidirectionalVideoQueue 清理异常退出的用户:{},用户最后一次心跳时间:{}, {}", uniqueId, o, newUser.getLast_status_update_time());
        }
        return flag;
    }

    /**
     * 视频用户是否活跃
     *
     * @param user 视频用户
     * @return 是否活跃, true否, false是
     */
    private boolean unLive(VideoUser user) {
        if (Objects.isNull(user) || Objects.isNull(user.getLast_status_update_time()) || user.getLast_status_update_time() <= 0L) {
            //用户不存在,但已无凭证或状态最后变更时间尚未赋值/异常取值,统一认为用户新到尚活跃
            return false;
        }
        return true;
    }

    private String getReasonMsg(String reasonMsg) {
        if (StringUtils.isNotBlank(reasonMsg)) {
            // 长度过长需要截取
            if (reasonMsg.length() > 3900) {
                reasonMsg = StringUtils.substring(reasonMsg, 0, 3900);
            }
            return String.format("%s，原因是：%s", BIDIRECTIONAL_CLEAR, reasonMsg);
        }
        return BIDIRECTIONAL_CLEAR;
    }
}

package com.cairh.cpe.service.aiaudit.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 */
@Data
public class AuditBusinRecordQueryResp implements Serializable {
    /**
     * 流水号
     *
     * 业务记录对应
     */
    private String serial_id;

    /**
     * 识别项目组
     */
    private String item_identity;

    /**
     * 规则英文名
     */
    private String drl_rule_name;

    /**
     * 规则名
     */
    private String rule_name;

    /**
     * 用户数据
     */
    private String source_data;

    /**
     * 识别数据
     *
     *
     */
    private String identify_data;

    /**
     * 匹配结果
     */
    private String match_result;

    /**
     * 处理信息
     */
    private String handle_info;

    /**
     * 比对分数
     */
    private Double match_score;

    /**
     * 时间戳
     */
    private Double time_stamp;

    /**
     * 开始时间
     */
    private Date begin_datetime;

    /**
     * 结果时间
     */
    private Date end_datetime;

    /**
     * 错误码
     */
    private Integer error_no;

    /**
     * 错误信息
     */
    private String error_info;
    /**
     * 字段名
     */
    private String field_name;
    /**
     * 识别字典值
     */
    private String identify_sub_code;

    /**
     * 驳回原因
     */
    private String reject_reason;

    /**
     * 可见性
     */
    private String visibility;
}

package com.cairh.cpe.service.aiaudit.request;

import com.cairh.cpe.service.aiaudit.request.SpeechSourceReq;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 功能说明: TODO<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 */
@Data
public class AuditAudioReq implements Serializable {

    /**
     * 审核编号 Y
     *
     * 对于同一个业务, 编号应一致; 开启一个新业务时, 需重新申请
     */
    private String audit_code;

    /**
     * 服务厂商 N
     *
     * 厂商定制, 不传则使用组件厂商配置 (可能涉及到配置映射转换)
     */
    private String service_vender;

    /**
     * 标识 Y
     *
     * 语音发起任务标识
     */
    private String identity;

    /**
     * 话术 N
     *
     * 定制话术, 如无, 则取决于配置(此时需指定单双向视频类型)
     */
    private List<SpeechSourceReq> speech_sources;


    /**
     * 占位符 N
     *
     * 对话术的弹性适配
     */
    private Map<String, Object> speech_placeholder;

    /**
     * 音频路径 N
     *
     * 按照语音发起结果传值
     */
    private String audio_path;

    /**
     * 是否双向视频 N
     *
     * 未指定定制话术时必需
     */
    private Boolean is_twoway_video;
}

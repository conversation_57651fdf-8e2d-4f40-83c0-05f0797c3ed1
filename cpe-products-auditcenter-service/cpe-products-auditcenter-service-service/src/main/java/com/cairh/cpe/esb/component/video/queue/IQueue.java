package com.cairh.cpe.esb.component.video.queue;


import com.cairh.cpe.service.auditcenter.service.request.VideoUser;

import java.util.List;
import java.util.Set;

/**
 * 视频queue抽象
 *
 * <AUTHOR>
 */
public interface IQueue {

    /**
     * 获取名称
     *
     * @return 名称
     */
    String getName();

    /**
     * 获取级别
     *
     * @return 级别
     */
    Integer getLevel();

    /**
     * 获取所有用户ID
     *
     * @return 用户ID集
     */
    Set<String> getAllUserId();

    /**
     * 获取所有用户
     *
     * @return 用户集
     */
    List<VideoUser> getAllUser();

    /**
     * 用户入队
     *
     * @param user {@link VideoUser}
     * @return 入队结果
     */
    void enqueue(VideoUser user);

    /**
     * 用户出队
     *
     * @param user_id 用户ID
     * @return 出队结果
     */
    boolean dequeue(String user_id);

    /**
     * 队列大小
     *
     * @return 队列大小
     */
    long size();

    /**
     * 所处位置, 从1开始, -1表示不在队列中
     */
    long position(String user_id);
}

package com.cairh.cpe.service.archive.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 功能说明: 组件文件信息<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 * 开发时间: 2022/4/7<br>
 */
@Data
public class FileInfo implements Serializable {

    /**文件流*/
    private byte[] file;
    /**文件名*/
    private String file_name;
    /**备注*/
    private String remark;

    /**文件记录ID*/
    private String filerecord_id;
}

package com.cairh.cpe.service.aidispatch.service;

import com.cairh.cpe.esb.dispatch.dto.req.TaskChangePauseFlagRequest;
import com.cairh.cpe.esb.dispatch.dto.req.TaskPopUpMsgRequest;
import com.cairh.cpe.esb.dispatch.dto.resp.DispatchTaskDetail;
import com.cairh.cpe.service.aidispatch.request.*;

public interface IAiDispatchService {

    String submitDispatchData(DispatchReq dispatchReq);

    void handleDispatchTask(HandleDispatchReq handleDispatchReq);

    void finishDispatchTask(FinishDispatchReq finishDispatchReq);

    void switchToWork(DispatchOperator dispatchOperator);

    void switchToRest(DispatchOperator dispatchOperator);

    boolean isWorking(DispatchOperator dispatchOperator);

    void claimTask(TaskAssignOperatorReq taskAssignOperatorReq);

    void cancelTask(CancelTaskReq cancelTaskReq);

    void taskChangeOperator(TaskChangeOperatorReq taskChangeOperatorReq);

    DispatchTaskDetail queryTaskDetail(String task_id);

    void recyleTask(RecyleTaskReq recyleTaskReq);

    void popUpMsg(TaskPopUpMsgRequest taskPopUpMsgRequest);

    /**
     * 挂起通知
     *
     * @param taskChangePauseFlagRequest
     */
    void taskChangePauseFlag(TaskChangePauseFlagRequest taskChangePauseFlagRequest);

    void invalidTask(InvalidTaskReq invalidTaskReq);
}

package com.cairh.cpe.service.aiaudit.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 功能说明: TODO<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 */
@Data
public class AuditBusinRecordQueryReq implements Serializable {

    /**
     * 审核编号 Y
     */
    private String audit_code;

    /**
     * 所属项目组 N
     *
     * 说明: 不传表明取所有识别项目组
     */
    private List<String> item_identity;
}

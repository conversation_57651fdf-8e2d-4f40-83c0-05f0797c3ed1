package com.cairh.cpe.service.aiaudit.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 功能说明: TODO<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 */
@Data
public class SpeechAuditDetailInfo implements Serializable {

    // 源问
    private String source_question;

    // 识别问
    private String recognize_question;

    // 问匹配分数
    private Float question_score;

    // 问时间戳
    private Float question_time_stamp;

    // 源答(陈述则为空)
    private String source_answer;

    // 识别答(陈述必定为空,问答不一定)
    private String recognize_answer;

    // 答匹配分数(陈述无)
    private Float answer_score;

    // 答时间戳
    private Float answer_time_stamp;

    /**
     * 当前 匹配结果
     */
    private String match_result;
}

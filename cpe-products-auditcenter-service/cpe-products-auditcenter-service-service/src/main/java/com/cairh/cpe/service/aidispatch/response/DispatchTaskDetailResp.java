package com.cairh.cpe.service.aidispatch.response;

import lombok.Data;

import java.util.Date;

@Data
public class DispatchTaskDetailResp {

    private String task_id;

    /**
     * 子系统编号
     */
    private Long subsys_id;

    /**
     * 业务类型
     */
    private Long busin_type;

    /**
     * 业务类型名称
     */
    private String busin_name;

    /**
     * 业务流水号
     */
    private String busi_serial_no;

    /**
     * 客户号
     */
    private String client_id;

    /**
     * 客户名
     */
    private String client_name;

    /**
     * 手机号码
     */
    private String mobile_tel;

    /**
     * 证件类别
     */
    private String id_kind;

    /**
     * 证件号码
     */
    private String id_no;

    /**
     * 客户资产账号
     */
    private String fund_account;

    /**
     * 分支机构
     */
    private String branch_no;

    /**
     * 任务类型
     */
    private String task_type;

    /**
     * 跳转url
     */
    private String action_url;

    /**
     * 数据内容
     */
    private String busi_content;

    /**
     * 活动编号
     */
    private String activity_no;

    /**
     * 活动名称
     */
    private String activity_name;

    /**
     * 营销团队
     */
    private String marketing_team;

    /**
     * 任务标签
     */
    private String dispatch_tags;

    /**
     * 允许的操作员集合
     */
    private String en_operator_nos;

    /**
     * 不允许的操作员集合
     */
    private String anti_operator_nos;

    /**
     * 视频认证类型 1:双向视频；2:单向视频
     */
    private String video_auth_type;

    /**
     * 顶部位置
     */
    private Long top_pos;

    /**
     * 渠道代码
     */
    private String channel_code;

    /**
     * 派单状态
     */
    private String dispatch_status;

    /**
     * 1:自动认领 2:手动认领
     */
    private String dispatch_handle_type;

    /**
     * 队列级别
     */
    private Integer queue_level;

    /**
     * 经过计算后有权限操作的操作员集合
     */
    private String authorized_operators;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建时间
     */
    private Date create_datetime;

    /**
     * 修改人
     */
    private String modify_by;

    /**
     * 修改时间
     */
    private Date modify_datetime;

    /**
     * 任务派单时间
     */
    private Date dispatch_datetime;

    /**
     * 任务处理时间
     */
    private Date deal_datetime;

    /**
     * 任务完成时间
     */
    private Date finish_datetime;

    /**
     * 当前操作员
     */
    private String current_operator;
}

package com.cairh.cpe.service.aidispatch.request;


import lombok.Data;

import java.util.Date;

@Data
public class DispatchReq {


    /**
     * * 子系统编号 必填
     */
    private Long subsys_id;

    /**
     * 业务类型 必填
     */
    private Long busin_type;

    /**
     * 业务名称 必填
     */
    private String busin_name;

    /**
     * 业务流水号 必填
     */
    private String busi_serial_no;

    /**
     * 客户号 必填
     */
    private String client_id;

    /**
     * 客户姓名 必填
     */
    private String client_name;

    /**
     * 手机号 必填
     */
    private String mobile_tel;

    /**
     * 证件类型 必填
     */
    private String id_kind;

    /**
     * 证件号码 必填
     */
    private String id_no;

    /**
     * 资金账号
     */
    private String fund_account;

    /**
     * 开户营业部 必填
     */
    private String branch_no;

    /**
     * 任务类型 必填
     */
    private String task_type;

    /**
     * 跳转到对应的审核菜单或视频菜单对应的url 必填
     */
    private String action_url;

    /**
     * 业务扩展信息
     */
    private String busi_content;

    /**
     * 活动编号
     */
    private String activity_no;

    /**
     * 活动名称
     */
    private String activity_name;

    /**
     * 营销团队
     */
    private String marketing_team;

    /**
     * 任务标签
     */
    private String dispatch_tags;

    /**
     * 允许的操作员集合
     */
    private String en_operator_nos;

    /**
     * 不允许的操作员集合
     */
    private String anti_operator_nos;

    /**
     * 视频认证类型 1:双向视频；2:单向视频
     */
    private String video_auth_type;

    /**
     * 顶部位置
     */
    private Long top_pos;

    /**
     * 渠道代码
     */
    private String channel_code;

    /**
     * 渠道代码
     */
    private String channel_name;
    /**
     * 备注
     */
    private String remark;

    /**
     * 当前操作员
     */
    private String current_operator;


    /**
     * 开户渠道
     */
    private String open_channel;

    /**
     * 手机号归属地
     */
    private String mobile_location;

    /**
     * 接入方式
     */
    private String app_id;

    /**
     * 任务创建时间  作为初次派单时间
     */
    private Date create_datetime;
}

package com.cairh.cpe.service.aidispatch.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.dispatch.IEsbDispatchDubboService;
import com.cairh.cpe.esb.dispatch.dto.req.*;
import com.cairh.cpe.esb.dispatch.dto.resp.CreateTaskResponse;
import com.cairh.cpe.esb.dispatch.dto.resp.DispatchTaskDetail;
import com.cairh.cpe.rpc.CpeRpcException;
import com.cairh.cpe.service.aidispatch.request.*;
import com.cairh.cpe.service.aidispatch.service.IAiDispatchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AiDispatchServiceImpl implements IAiDispatchService {

    @DubboReference(check = false)
    IEsbDispatchDubboService esbDispatchDubboService;


    /**
     * *提交派单数据。通知派单系统派单
     *
     * @param dispatchReq
     * @return
     */
    @Override
    public String submitDispatchData(DispatchReq dispatchReq) {
        try {
            CreateTaskRequest req = new CreateTaskRequest();
            BeanUtil.copyProperties(dispatchReq, req);
            CreateTaskResponse resp = esbDispatchDubboService.creatTask(req);
            if (resp != null) {
                return resp.getTask_id();
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_创建派单任务异常", e);
            throw new BizException("-9997", e.getMessage());
        }

        return null;
    }

    /**
     * * 将派单任务更新为处理中
     *
     * @param handleDispatchReq
     */
    @Override
    public void handleDispatchTask(HandleDispatchReq handleDispatchReq) {
        try {
            DealTaskRequest req = new DealTaskRequest();
            BeanUtil.copyProperties(handleDispatchReq, req);
            log.info("handleDispatchTask入参为[{}]", JSON.toJSONString(req));
            esbDispatchDubboService.handleTask(req);
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_处理派单任务异常", e);
            throw new BizException("-9997", e.getMessage());
        }
    }

    /**
     * *通知派单系统该任务已完成（审核通过或者不通过）
     *
     * @param finishDispatchReq
     */
    @Override
    public void finishDispatchTask(FinishDispatchReq finishDispatchReq) {
        try {
            CompleteTaskRequest req = new CompleteTaskRequest();
            BeanUtil.copyProperties(finishDispatchReq, req);
            log.info("finishDispatchTask入参为[{}]", JSON.toJSONString(req));
            esbDispatchDubboService.finishTask(req);
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_派单任务完成异常", e);
            throw new BizException("-9997", e.getMessage());
        }

    }

    @Override
    public void switchToWork(DispatchOperator dispatchOperator) {
        try {
            boolean result = esbDispatchDubboService.switchToWork(Long.valueOf(WskhConstant.SUBSYS_ID), dispatchOperator.getOperator_no());
            if (!result) {
                log.error("操作员唤醒失败[{}]", dispatchOperator.getOperator_no());
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_操作员唤醒失败", e);
            throw new BizException("-1", e.getMessage());
        }
    }

    @Override
    public void switchToRest(DispatchOperator dispatchOperator) {
        try {
            boolean result = esbDispatchDubboService.switchToRest(Long.valueOf(WskhConstant.SUBSYS_ID), dispatchOperator.getOperator_no());
            if (!result) {
                log.error("操作员休息失败[{}]", dispatchOperator.getOperator_no());
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_操作员休息失败", e);
            throw new BizException("-9997", e.getMessage());
        }
    }

    @Override
    public boolean isWorking(DispatchOperator dispatchOperator) {
        boolean result = false;
        try {
            result = esbDispatchDubboService.isWorking(Long.valueOf(WskhConstant.SUBSYS_ID), dispatchOperator.getOperator_no());
            log.info("操作员[{}]工作状态[{}]", dispatchOperator.getOperator_no(), result);
            return result;
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_操作员休息失败", e);
            throw new BizException("-9997", e.getMessage());
        }
    }

    @Override
    public void claimTask(TaskAssignOperatorReq taskAssignOperatorReq) {
        TaskAssignOperatorRequest req = new TaskAssignOperatorRequest();
        BeanUtil.copyProperties(taskAssignOperatorReq, req);
        log.info("claimTask入参为[{}]", JSON.toJSONString(req));
        esbDispatchDubboService.taskAssignOperator(req);
    }

    @Override
    public void taskChangeOperator(TaskChangeOperatorReq taskChangeOperatorReq) {
        try {
            TaskChangeOperatorRequest req = new TaskChangeOperatorRequest();
            BeanUtil.copyProperties(taskChangeOperatorReq, req);
            log.info("taskChangeOperator入参为[{}]", JSON.toJSONString(req));
            esbDispatchDubboService.taskChangeOperator(req);
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_任务转派失败", e);
            throw new BizException("-9997", e.getMessage());
        }
    }

    @Override
    public DispatchTaskDetail queryTaskDetail(String task_id) {
        try {
            DispatchTaskDetail dispatchTaskDetail = esbDispatchDubboService.queryTaskDetail(task_id);
            log.info("queryTaskDetail出参为[{}]", JSON.toJSONString(dispatchTaskDetail));
            return dispatchTaskDetail;
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_任务查询失败", e);
            throw new BizException("-1", e.getMessage());
        }
    }

    @Override
    public void cancelTask(CancelTaskReq cancelTaskReq) {
        try {
            CancelTaskRequest req = new CancelTaskRequest();
            BeanUtil.copyProperties(cancelTaskReq, req);
            log.info("cancelTask入参为[{}]", JSON.toJSONString(req));
            esbDispatchDubboService.cancelTask(req);
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_任务作废失败", e);
            throw new BizException("-9997", e.getMessage());
        }
    }

    @Override
    public void invalidTask(InvalidTaskReq invalidTaskReq) {
        try {
            CompleteTaskRequest req = new CompleteTaskRequest();
            BeanUtil.copyProperties(invalidTaskReq, req);
            log.info("invalidTask入参为[{}]", JSON.toJSONString(req));
            esbDispatchDubboService.invalidTask(req);
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常[invalidTask] {}", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务[invalidTask]任务作废失败", e);
            throw new BizException("-1", e.getMessage());
        }
    }

    @Override
    public void recyleTask(RecyleTaskReq recyleTaskReq) {
        try {
            TaskRecoveryOperatorRequest req = new TaskRecoveryOperatorRequest();
            BeanUtil.copyProperties(recyleTaskReq, req);
            log.info("recyleTask入参为[{}]", JSON.toJSONString(req));
            esbDispatchDubboService.taskRecoveryOperator(req);
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_任务作废失败", e);
            throw new BizException("-1", e.getMessage());
        }
    }

    @Override
    public void popUpMsg(TaskPopUpMsgRequest taskPopUpMsgRequest) {
        esbDispatchDubboService.popUpMsg(taskPopUpMsgRequest);
    }

    @Override
    public void taskChangePauseFlag(TaskChangePauseFlagRequest taskChangePauseFlagRequest) {
        esbDispatchDubboService.taskChangePauseFlag(taskChangePauseFlagRequest);
    }
}

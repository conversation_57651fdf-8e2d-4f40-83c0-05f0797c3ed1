package com.cairh.cpe.service.aiaudit.response;
import lombok.Data;
import java.io.Serializable;

/**
 * 审核规则 查询
 *
 * 按order_no asc排序
 *
 * <AUTHOR>
 */
@Data
public class AuditRuleQueryResp implements Serializable {

    /**
     * 流水号
     *
     * 业务规则对应
     */
    private String serial_id;

    /**
     * 子系统编号
     *
     * 与传入值一致
     */
    private Integer subsys_id;

    /**
     * 业务类型
     */
    private Integer busin_type;

    /**
     * 识别项目组
     *
     * 1~6 11 21 31 41 51
     */
    private String item_identity;

    /**
     * 识别分组
     */
    private String item_identity_name;

    /**
     * 规则英文名称
     */
    private String drl_rule_name;

    /**
     * 规则名称
     */
    private String rule_name;

    /**
     * 规则内容
     *
     * 与配置规则merge后的结果
     */
//    private String rule_content;

    /**
     * 规则表达式
     *
     * 与配置规则merge后的结果
     */
//    private String expression_prop;

    /**
     * 规则参数
     *
     * 与配置规则merge后的结果
     */
//    private String rule_param;

    /**
     * 规则定义
     */
    private String rule_note;

    /**
     * 级别
     *
     * 菜单属级
     */
    private Integer level;

    /**
     * 驳回原因
     */
    private String reject_reason;

    /**
     * 告警级别
     */
    private Integer alarm_level;

    /**
     * 可见性
     *
     * 0 - 是(默认); 1 - 否
     */
    private String visibility;
}

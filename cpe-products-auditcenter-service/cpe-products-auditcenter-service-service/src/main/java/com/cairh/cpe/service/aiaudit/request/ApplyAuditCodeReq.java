package com.cairh.cpe.service.aiaudit.request;

import com.cairh.cpe.aiaudit.apply.support.AuditBusinParam;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能说明: TODO<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 */
@Data
public class ApplyAuditCodeReq implements Serializable {

    // 子系统标识 Y
    private Integer subsys_id;

    // 业务类型 Y
    private Integer busin_type;

    // 业务编号 Y
    private String busin_no;

    // 审核业务素材 N
    private AuditBusinParam auditBusinParam;
}

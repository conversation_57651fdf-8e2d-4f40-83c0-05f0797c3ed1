package com.cairh.cpe.service.aiaudit.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cairh.cpe.aiaudit.apply.support.AuditBusinParam;
import com.cairh.cpe.aiaudit.common.AiauditGATPassInfo;
import com.cairh.cpe.aiaudit.common.AiauditGATResidenceInfo;
import com.cairh.cpe.aiaudit.common.ImageCategory;
import com.cairh.cpe.aiaudit.ocr.req.AuditOcrForeignResidenceInfoRequest;
import com.cairh.cpe.aiaudit.ocr.req.AuditOcrGATPassInfoRequest;
import com.cairh.cpe.aiaudit.ocr.req.AuditOcrGATResidenceInfoRequest;
import com.cairh.cpe.aiaudit.query.AiAuditQueryDubboService;
import com.cairh.cpe.aiaudit.query.req.AuditRuleQueryRequest;
import com.cairh.cpe.aiaudit.query.resp.AuditRuleQueryResponse;
import com.cairh.cpe.businflow.service.IIssuingAuthorityOrganService;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.UserQueryExtInfo;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.IDCardInfo;
import com.cairh.cpe.common.entity.clob.PoliceVerifyResult;
import com.cairh.cpe.common.entity.clob.UserBaseInfo;
import com.cairh.cpe.common.entity.request.QueryDishonestReq;
import com.cairh.cpe.common.entity.request.VerifyPoliceReq;
import com.cairh.cpe.common.entity.response.QueryDishonestResult;
import com.cairh.cpe.common.entity.response.VerifyPoliceResp;
import com.cairh.cpe.common.service.*;
import com.cairh.cpe.common.util.AgeUtil;
import com.cairh.cpe.common.util.IdentifyUtils;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.rpc.CpeRpcException;
import com.cairh.cpe.service.aiaudit.request.*;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;
import com.cairh.cpe.service.aiaudit.response.AuditAudioIdentityResp;
import com.cairh.cpe.service.aiaudit.response.AuditBusinRecordQueryResp;
import com.cairh.cpe.service.aiaudit.response.AuditRuleQueryResp;
import com.cairh.cpe.service.aiaudit.service.IAiAuditAchieveService;
import com.cairh.cpe.service.aiaudit.service.IAiAuditService;
import com.cairh.cpe.service.archive.request.FileInfo;
import com.cairh.cpe.service.archive.service.IArchiveService;
import com.cairh.cpe.service.config.AiAuditThreadPoolTaskExecutor;
import com.cairh.cpe.service.idverify.IComponentIdVerifyService;
import com.cairh.cpe.service.idverify.IDishonestQueryService;
import com.cairh.cpe.service.idverify.IVideoScripModelService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
@Primary
public class AiAuditAchieveServiceImpl implements IAiAuditAchieveService {

    @Autowired
    IAiAuditService aiAuditService;
    @Autowired
    AiAuditThreadPoolTaskExecutor aiAuditThreadPoolTaskExecutor;
    @Autowired
    IRequestFlowService requestFlowService;
    @Autowired
    IRequestService requestService;
    @DubboReference(check = false)
    AiAuditQueryDubboService aiAuditQueryDubboService;
    @Autowired
    private IVideoScripModelService videoScripModelService;
    @Autowired
    private CacheDict cacheDict;
    @Autowired
    private IBusinFlowRequestService businFlowRequestService;
    @Resource
    private IDishonestQueryService dishonestQueryService;
    @Resource
    private IArchiveService archiveService;
    @Resource
    private IBusinFlowTaskService businFlowTaskService;
    @Resource
    private IUserQueryExtInfoService userQueryExtInfoService;
    @Autowired
    private IComponentIdVerifyService componentIdVerifyService;
    @Autowired
    private IIssuingAuthorityOrganService iIssuingAuthorityOrganService;


    @Override
    public void asyncAiauditAll(String request_no) {
        long startTime = System.currentTimeMillis();
        log.info("业务编号{}响应式全异步审核调用开始", request_no);
        ClobContentInfo clob = requestService.getAllDataByRequestNo(request_no);
        if (StringUtils.isBlank(clob.getAi_audit_code())) {
            getAuditCode(request_no);
        }
        ClobContentInfo newsClob = requestService.getAllDataByRequestNo(request_no);
        // 双向视频-复核任务，仅有免冠照file_80和视频file_8A参与智能审核
        // 外国人永居证双向视频参与智能审核
        if (StringUtils.equals(clob.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL) &&
                StringUtils.equals(clob.getVideo_type(), WskhConstant.VIDEO_TYPE_2) &&
                StringUtils.equalsAny(clob.getId_kind(), IdKindEnum.ID_CARD.getCode(), IdKindEnum.FOREIGN_PREV_PERMIT.getCode())) {
            bidirectionalExecute(newsClob, clob, request_no);
        } else if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, newsClob.getBusin_type())) {
            // 网厅业务办理
            handExecute(newsClob, request_no);
        } else {
            // 外国人永居证
            boolean foreign_prev_permit_flag = StringUtils.equals(clob.getId_kind(), IdKindEnum.FOREIGN_PREV_PERMIT.getCode());
            if (!foreign_prev_permit_flag) {
                CompletableFuture.runAsync(() -> auditIdCard(request_no, newsClob), aiAuditThreadPoolTaskExecutor);
                CompletableFuture.runAsync(() -> auditImageQuality(request_no, WskhConstant.IMAGE_NO_6A, newsClob), aiAuditThreadPoolTaskExecutor);
                CompletableFuture.runAsync(() -> auditImageQuality(request_no, WskhConstant.IMAGE_NO_6B, newsClob), aiAuditThreadPoolTaskExecutor);
            }
            CompletableFuture.runAsync(() -> auditFace(request_no, newsClob), aiAuditThreadPoolTaskExecutor);
            CompletableFuture.runAsync(() -> auditVideo(request_no, newsClob), aiAuditThreadPoolTaskExecutor);
            CompletableFuture.runAsync(() -> auditClient(request_no, newsClob), aiAuditThreadPoolTaskExecutor);

            boolean isDishonestRecordBlank = StringUtils.isBlank(clob.getUser_base_info().getDishonest_record());
            boolean isNormalBusinessWithResidencePermit =
                    StringUtils.equals(clob.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL) &&
                            StringUtils.equalsAny(clob.getId_kind(), IdKindEnum.GAT_RESIDENCE_PERMIT.getCode());
            if (isDishonestRecordBlank && !isNormalBusinessWithResidencePermit) {
                log.info("request_no={},查询失信记录dishonest_record={}", request_no, clob.getUser_base_info().getDishonest_record());
                CompletableFuture.runAsync(() -> getSecurityAlisaRpa(request_no, clob.getClient_name(), clob.getId_no()), aiAuditThreadPoolTaskExecutor);
            }
            CompletableFuture.runAsync(() -> {
                AuditAudioIdentityResp resp = auditAudioIdentify(request_no, null);
                if (resp != null && StringUtils.isNotBlank(resp.getIdentity())) {
                    auditAudio(request_no, null);
                }
            }, aiAuditThreadPoolTaskExecutor);

        }
        log.info("业务编号 = {}响应式全异步审核调用结束,cost = {}", request_no, System.currentTimeMillis() - startTime);
    }

    @Override
    public List<AiAuditRuleResp> againExecuteAiaudit(String request_no, String ai_audit_group) {
        ClobContentInfo clob = requestService.getAllDataByRequestNo(request_no);
        List<AiAuditRuleResp> aiAuditRuleResps = new ArrayList<>();
        switch (ai_audit_group) {
            case "1":
            case "2":
                aiAuditRuleResps = auditIdCard(request_no, clob);
                break;
            case "3":
                aiAuditRuleResps = auditFace(request_no, clob);
                break;
            case "4":
                aiAuditRuleResps = auditVideo(request_no, clob);
                break;
            case "5":
                auditAudioIdentify(request_no, clob);
                auditAudio(request_no, null);
                break;
            case "9":
                aiAuditRuleResps = auditClient(request_no, clob);
                break;
            case "a":
                aiAuditRuleResps = auditImageQuality(request_no, WskhConstant.IMAGE_NO_6A, clob);
                break;
            case "b":
                aiAuditRuleResps = auditImageQuality(request_no, WskhConstant.IMAGE_NO_6B, clob);
                break;
            default:
                break;
        }
        return aiAuditRuleResps;
    }

    @Override
    public List<AiAuditRuleResp> handAgainExecuteAiaudit(String request_no, String ai_audit_group) {
        ClobContentInfo clob = requestService.getAllDataByRequestNo(request_no);
        List<AiAuditRuleResp> aiAuditRuleResps = new ArrayList<>();
        switch (ai_audit_group) {
            case "1":
            case "2":
                aiAuditRuleResps = handAuditIdCard(request_no, clob);
                break;
            case "3":
                aiAuditRuleResps = handAuditFace(request_no, clob);
                break;
            case "4":
                aiAuditRuleResps = handAuditVideo(request_no, clob);
                break;
            case "5":
                auditAudioIdentify(request_no, clob);
                auditAudio(request_no, null);
                break;
            case "a":
                aiAuditRuleResps = handAuditImageQuality(request_no, WskhConstant.IMAGE_NO_6A, clob);
                break;
            case "b":
                aiAuditRuleResps = handAuditImageQuality(request_no, WskhConstant.IMAGE_NO_6B, clob);
                break;
            default:
                break;
        }
        return aiAuditRuleResps;
    }

    @Override
    public List<AuditBusinRecordQueryResp> queryAuditBusinRecord(String request_no, List<String> item_identitys) {
        ClobContentInfo clob = requestService.getAllDataByRequestNo(request_no);
        AuditBusinRecordQueryReq req = new AuditBusinRecordQueryReq();
        req.setAudit_code(clob.getAi_audit_code());
        if (CollectionUtils.isNotEmpty(item_identitys)) {
            req.setItem_identity(item_identitys);
        }
        return aiAuditService.queryAuditBusinRecord(req);
    }

    @Override
    public List<AiAuditRuleResp> auditIdCard(String request_no, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        AuditOcrIdCardReq req = new AuditOcrIdCardReq();
        if (StringUtils.isBlank(clob.getAi_audit_code())) {
            req.setAudit_code(getAuditCode(request_no));
        } else {
            req.setAudit_code(clob.getAi_audit_code());
        }
        req.setFrontImage(clob.getFile_6A());
        req.setBackImage(clob.getFile_6B());
        AiauditClientInfoReq clientInfoReq = new AiauditClientInfoReq();
        IDCardInfo idCardInfo = clob.getId_card_info();
        BeanUtil.copyProperties(idCardInfo, clientInfoReq);
        clientInfoReq.setId_address(idCardInfo.getId_address());
        clientInfoReq.setId_grant_organ(idCardInfo.getIssued_depart());
        clientInfoReq.setId_begindate(StringUtils.isNotBlank(idCardInfo.getId_begindate()) ? Integer.valueOf(idCardInfo.getId_begindate()) : 0);
        clientInfoReq.setId_enddate(StringUtils.isNotBlank(idCardInfo.getId_enddate()) ? Integer.valueOf(idCardInfo.getId_enddate()) : 0);
        clientInfoReq.setBirthday(StringUtils.isNotBlank(idCardInfo.getBirthday()) ? Integer.valueOf(idCardInfo.getBirthday()) : 0);
        clientInfoReq.setClient_gender(cacheDict.getDictDesc(WskhConstant.DIC_1012, idCardInfo.getClient_gender()));
        // 100058 且 身份证
        if (StringUtils.equals(clob.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL)
                && StringUtils.equals(IdKindEnum.ID_CARD.getCode(), clob.getId_kind())) {
            Map<String, String> adressOragnMap = iIssuingAuthorityOrganService.getAdressOragnMap();
            if (!adressOragnMap.isEmpty()) {
                clientInfoReq.setAddress_oragns(adressOragnMap);
            }
        }
        req.setClient_info(clientInfoReq);
        return aiAuditService.auditIdCard(req);
    }

    @Override
    public List<AiAuditRuleResp> auditForeignResidenceInfo(String request_no, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        AuditOcrForeignResidenceInfoRequest req = new AuditOcrForeignResidenceInfoRequest();
        if (StringUtils.isBlank(clob.getAi_audit_code())) {
            req.setAudit_code(getAuditCode(request_no));
        } else {
            req.setAudit_code(clob.getAi_audit_code());
        }
        req.setFrontImage(clob.getFile_6A());
        req.setBackImage(clob.getFile_6B());
        return aiAuditService.auditForeignResidenceInfo(req);
    }

    @Override
    public List<AiAuditRuleResp> auditGATResidenceInfo(String request_no, ImageCategory imageCategory, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        AuditOcrGATResidenceInfoRequest req = new AuditOcrGATResidenceInfoRequest();
        if (StringUtils.isBlank(clob.getAi_audit_code())) {
            req.setAudit_code(getAuditCode(request_no));
        } else {
            req.setAudit_code(clob.getAi_audit_code());
        }
        if (ImageCategory.FRONT == imageCategory) {
            req.setImage(clob.getFile_6A());
        } else if (ImageCategory.BACK == imageCategory) {
            req.setImage(clob.getFile_6B());
        }
        req.setCategory(imageCategory);
        AiauditGATResidenceInfo aiauditGATResidence = new AiauditGATResidenceInfo();
        IDCardInfo idCardInfo = clob.getId_card_info();
        BeanUtil.copyProperties(idCardInfo, aiauditGATResidence);
        aiauditGATResidence.setName(idCardInfo.getClient_name());
        aiauditGATResidence.setGender(cacheDict.getDictDesc(WskhConstant.DIC_1012, idCardInfo.getClient_gender()));
        aiauditGATResidence.setBirthday(StringUtils.isNotBlank(idCardInfo.getBirthday()) ? Integer.parseInt(idCardInfo.getBirthday()) : 0);
        aiauditGATResidence.setAddress(idCardInfo.getId_address());
        aiauditGATResidence.setValidate_date(idCardInfo.getId_begindate() + "-" + idCardInfo.getId_enddate());
        aiauditGATResidence.setId_number(idCardInfo.getId_no());
        aiauditGATResidence.setIssue_authority(idCardInfo.getIssued_depart());
        aiauditGATResidence.setPass_number(idCardInfo.getPermit_no());
        req.setResidence_info(aiauditGATResidence);
        return aiAuditService.auditGATResidenceInfo(req);
    }

    @Override
    public List<AiAuditRuleResp> auditGATPassInfo(String request_no, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        AuditOcrGATPassInfoRequest req = new AuditOcrGATPassInfoRequest();
        if (StringUtils.isBlank(clob.getAi_audit_code())) {
            req.setAudit_code(getAuditCode(request_no));
        } else {
            req.setAudit_code(clob.getAi_audit_code());
        }
        req.setImage(clob.getFile_6A());
        AiauditGATPassInfo aiauditGATPassInfo = new AiauditGATPassInfo();
        IDCardInfo idCardInfo = clob.getId_card_info();
        BeanUtil.copyProperties(idCardInfo, aiauditGATPassInfo);
        aiauditGATPassInfo.setNumber(idCardInfo.getPermit_no());
        aiauditGATPassInfo.setName(idCardInfo.getClient_name());
        aiauditGATPassInfo.setGender(cacheDict.getDictDesc(WskhConstant.DIC_1012, idCardInfo.getClient_gender()));
        aiauditGATPassInfo.setAddress(idCardInfo.getId_address());
        aiauditGATPassInfo.setBirthday(StringUtils.isNotBlank(idCardInfo.getBirthday()) ? Integer.parseInt(idCardInfo.getBirthday()) : 0);
        aiauditGATPassInfo.setValidate_date(idCardInfo.getId_begindate() + "-" + idCardInfo.getId_enddate());
        aiauditGATPassInfo.setId_number(idCardInfo.getId_no());
        aiauditGATPassInfo.setIssue_agency(idCardInfo.getIssued_depart());
        req.setPass_info(aiauditGATPassInfo);
        return aiAuditService.auditGATPassInfo(req);
    }

    /**
     * 网厅身份证智能审核
     *
     * @param request_no
     * @param clob
     * @return
     */
    private List<AiAuditRuleResp> handAuditIdCard(String request_no, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        try {
            AuditOcrIdCardReq req = new AuditOcrIdCardReq();
            if (StringUtils.isBlank(clob.getAi_audit_code())) {
                req.setAudit_code(getAuditCode(request_no));
            } else {
                req.setAudit_code(clob.getAi_audit_code());
            }
            // 个人的取客户信息，机构&产品取经办人信息
            AiauditClientInfoReq clientInfoReq = new AiauditClientInfoReq();
            if (clob.getClient_category().equals(ClientCategoryEnum.CLIENT_PERSON.getCode())) {
                req.setFrontImage(clob.getPhoto_front());
                req.setBackImage(clob.getPhoto_back());
                if (StringUtils.equals(clob.getId_kind(), IdKindEnum.ID_CARD.getCode())) {
                    clientInfoReq.setClient_name(clob.getClient_name());
                    clientInfoReq.setId_kind(clob.getId_kind());
                    clientInfoReq.setId_no(clob.getId_no());
                }
            } else {
                req.setFrontImage(clob.getAgent_photo_front());
                req.setBackImage(clob.getAgent_photo_back());
                if (StringUtils.equals(clob.getAgent_id_kind(), IdKindEnum.ID_CARD.getCode())) {
                    clientInfoReq.setClient_name(clob.getAgent_name());
                    clientInfoReq.setId_kind(clob.getAgent_id_kind());
                    clientInfoReq.setId_no(clob.getAgent_id_no());
                }

            }
            req.setClient_info(clientInfoReq);
            return aiAuditService.auditIdCard(req);
        } catch (Exception e) {
            log.error("[网厅业务办理]智能身份证证件信息识别对比异常", e);
            throw new BizException(ErrorEnum.AUDIT_AI_ID_CARD_ERROR.getValue(), ErrorEnum.AUDIT_AI_ID_CARD_ERROR.getDesc());
        }
    }

    @Override
    public List<AiAuditRuleResp> auditFace(String request_no, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        AuditFaceReq req = new AuditFaceReq();
        if (StringUtils.isBlank(clob.getAi_audit_code())) {
            req.setAudit_code(getAuditCode(request_no));
        } else {
            req.setAudit_code(clob.getAi_audit_code());
        }
        req.setFaceImage(clob.getFile_80());
        req.setPoliceImage(clob.getFile_82());
        req.setCardImage(clob.getFile_6A());
        return aiAuditService.auditFace(req);
    }

    public List<AiAuditRuleResp> handAuditFace(String request_no, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        try {
            // 拼接调用dubbo接口前的参数
            AuditFaceReq req = new AuditFaceReq();
            if (StringUtils.isBlank(clob.getAi_audit_code())) {
                req.setAudit_code(getAuditCode(request_no));
            } else {
                req.setAudit_code(clob.getAi_audit_code());
            }
            req.setFaceImage(clob.getFile_80());
            req.setPoliceImage(clob.getFile_82());
            // 个人的取客户信息，机构&产品取经办人信息
            if (clob.getClient_category().equals(ClientCategoryEnum.CLIENT_PERSON.getCode())) {
                req.setCardImage(clob.getPhoto_front());
            } else {
                req.setCardImage(clob.getAgent_photo_front());
            }
            return aiAuditService.auditFace(req);
        } catch (Exception e) {
            log.error("人像比对信息异常！request_no = {}", request_no, e);
            throw new BizException(ErrorEnum.AUDIT_AI_FACE_ERROR.getValue(), ErrorEnum.AUDIT_AI_FACE_ERROR.getDesc());
        }
    }

    @Override
    public List<AiAuditRuleResp> auditVideo(String request_no, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        long startTime = System.currentTimeMillis();
        AuditVideoReq req = new AuditVideoReq();
        if (StringUtils.isBlank(clob.getAi_audit_code())) {
            req.setAudit_code(getAuditCode(request_no));
        } else {
            req.setAudit_code(clob.getAi_audit_code());
        }
        req.setImage(clob.getFile_6A());
        if (clob.getUser_video_info() == null) {
            log.info("视频智能审核时没有视频文件");
            throw new BizException(ErrorEnum.AUDIT_AI_VIDEO_NOT_EXIST.getValue(), ErrorEnum.AUDIT_AI_VIDEO_NOT_EXIST.getDesc());
        }
        // 接受到的视频为地址，传递为视频路径
        req.setVideo(clob.getUser_video_info().getVideo_address());
        if (WskhConstant.VIDEO_TYPE_2.equals(clob.getVideo_type())) {
            req.setIs_twoway_video(true);
        } else {
            req.setIs_twoway_video(false);
        }
        log.debug("auditVideo startTime end ,cost = {}", System.currentTimeMillis() - startTime);
        return aiAuditService.auditVideo(req);
    }

    public List<AiAuditRuleResp> handAuditVideo(String request_no, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        if (StringUtils.isBlank(clob.getFile_8A())) {
            return null;
        }
        try {
            AuditVideoReq req = new AuditVideoReq();
            // 拼接调用dubbo接口前的参数
            if (StringUtils.isBlank(clob.getAi_audit_code())) {
                req.setAudit_code(getAuditCode(request_no));
            } else {
                req.setAudit_code(clob.getAi_audit_code());
            }
            // 个人的取客户信息，机构&产品取经办人信息
            if (clob.getClient_category().equals(ClientCategoryEnum.CLIENT_PERSON.getCode())) {
                req.setImage(clob.getPhoto_front());
            } else {
                req.setImage(clob.getAgent_photo_front());
            }
            // 接受到的视频为地址，传递为视频路径
            req.setVideo(clob.getFile_8A());
            req.setIs_twoway_video(WskhConstant.VIDEO_TYPE_2.equals(clob.getVideo_type()));
            return aiAuditService.auditVideo(req);
        } catch (Exception e) {
            log.error("dubbo服务智能视频异常", e);
            throw new BizException(ErrorEnum.COMPONENT_DUBBO_AI_VIDEO_ERROR.getValue(), ErrorEnum.COMPONENT_DUBBO_AI_VIDEO_ERROR.getDesc());
        }
    }

    @Override
    public AuditAudioIdentityResp auditAudioIdentify(String request_no, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        long startTime = System.currentTimeMillis();
        AuditAudioIdentifyReq req = new AuditAudioIdentifyReq();
        if (StringUtils.isBlank(clob.getAi_audit_code())) {
            req.setAudit_code(getAuditCode(request_no));
        } else {
            req.setAudit_code(clob.getAi_audit_code());
        }
        if (StringUtils.isBlank(clob.getFile_8A())) {
            log.info("语音智能时没有视频文件");
            throw new BizException(ErrorEnum.AUDIT_AI_AUDIO_NOT_EXIST.getValue(), ErrorEnum.AUDIT_AI_AUDIO_NOT_EXIST.getDesc());
        }
        // 接受到的视频为地址，传递为视频路径
        req.setVideo(clob.getFile_8A());
        AuditAudioIdentityResp list = aiAuditService.auditAudioIdentify(req);
        if (list != null) {
            Map<String, Object> params = new HashMap<>();
            params.put("ai_audio_identity", list.getIdentity());
            if (StringUtils.isNotBlank(list.getAudio_path())) {
                params.put("audio_path", list.getAudio_path());
            }
            params.put(Fields.RECORD_TYPE, FlowStatusConst.SYSTEM_RECORD_TYPE);
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22384);
            params.put(Fields.BUSINESS_REMARK, "智能审核语音发起");
            requestFlowService.saveParamsRecord(request_no, params);
        }
        log.debug("auditAudioIdentify startTime end ,cost = {}", System.currentTimeMillis() - startTime);
        return list;
    }

    @Override
    public List<AiAuditRuleResp> auditAudio(String request_no, ClobContentInfo clob) {
        if (null == clob) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        if (StringUtils.isBlank(clob.getAi_audio_identity())) {
            return Lists.newArrayList();
        }
        AuditAudioReq req = new AuditAudioReq();
        if (StringUtils.isBlank(clob.getAi_audit_code())) {
            req.setAudit_code(getAuditCode(request_no));
        } else {
            req.setAudit_code(clob.getAi_audit_code());
        }
        req.setIdentity(clob.getAi_audio_identity());
        if (WskhConstant.VIDEO_TYPE_2.equals(clob.getVideo_type())) {
            req.setIs_twoway_video(true);
        } else {
            req.setIs_twoway_video(false);
        }
        List<SpeechSourceReq> SpeechSourceReqs = videoScripModelService.getSpeechSource(request_no);
        req.setSpeech_sources(SpeechSourceReqs);
        return aiAuditService.auditAudio(req);
    }


    @Override
    public List<AiAuditRuleResp> auditImageQuality(String request_no, String image_no, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        AuditOcrImageQualityReq req = new AuditOcrImageQualityReq();
        if (StringUtils.isBlank(clob.getAi_audit_code())) {
            req.setAudit_code(getAuditCode(request_no));
        } else {
            req.setAudit_code(clob.getAi_audit_code());
        }
        req.setImage_no(image_no);
        if (WskhConstant.IMAGE_NO_6A.equals(image_no)) {
            req.setImage(clob.getFile_6A());
        } else if (WskhConstant.IMAGE_NO_6B.equals(image_no)) {
            req.setImage(clob.getFile_6B());
        } else {
            throw new BizException(ErrorEnum.ID_NO_IMAGE_ERROR.getValue(), ErrorEnum.ID_NO_IMAGE_ERROR.getDesc());
        }
        return aiAuditService.auditImageQuality(req);
    }

    public List<AiAuditRuleResp> handAuditImageQuality(String request_no, String image_no, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        try {
            AuditOcrImageQualityReq req = new AuditOcrImageQualityReq();
            // 拼接调用dubbo接口前的参数
            if (StringUtils.isBlank(clob.getAi_audit_code())) {
                req.setAudit_code(getAuditCode(request_no));
            } else {
                req.setAudit_code(clob.getAi_audit_code());
            }

            req.setImage_no(image_no);
            // 个人的取客户信息，机构&产品取经办人信息
            String photo_front = clob.getPhoto_front();
            String photo_back = clob.getPhoto_back();
            if (!clob.getClient_category().equals(ClientCategoryEnum.CLIENT_PERSON.getCode())) {
                photo_front = clob.getAgent_photo_front();
                photo_back = clob.getAgent_photo_back();
            }
            if (WskhConstant.IMAGE_NO_6A.equals(image_no)) {
                req.setImage(photo_front);
            } else if (WskhConstant.IMAGE_NO_6B.equals(image_no)) {
                req.setImage(photo_back);
            } else {
                throw new BizException(ErrorEnum.ID_NO_IMAGE_ERROR.getValue(), ErrorEnum.ID_NO_IMAGE_ERROR.getDesc());
            }
            return aiAuditService.auditImageQuality(req);
        } catch (Exception e) {
            log.error("[网厅业务办理]身份证切边检查异常", e);
            throw new BizException(ErrorEnum.HAND_ID_NO_AI_CHECK_ERROR.getValue(), e.getMessage());
        }
    }

    @Override
    public List<AiAuditRuleResp> auditClient(String request_no, ClobContentInfo clob) {
        if (clob == null) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        AuditClientReq req = new AuditClientReq();
        if (StringUtils.isBlank(clob.getAi_audit_code())) {
            req.setAudit_code(getAuditCode(request_no));
        } else {
            req.setAudit_code(clob.getAi_audit_code());
        }
        Map<String, Object> client_info = new HashMap<>();
        if (clob.getId_card_info() != null) {
            IDCardInfo idCardInfo = clob.getId_card_info();
            client_info.put(Fields.USER_NAME, idCardInfo.getClient_name());
            client_info.put(Fields.ADDRESS, idCardInfo.getAddress());
            client_info.put(Fields.ID_ADDRESS, idCardInfo.getId_address());

            String birthday = "";
            if (IdKindEnum.ID_CARD.getCode().equals(clob.getId_kind())) {
                birthday = IdentifyUtils.getIdCardBirthDay(clob.getId_no());
            } else {
                birthday = clob.getBirthday();
            }
            client_info.put(Fields.AGE, AgeUtil.ageUtil(birthday));
            client_info.put(Fields.CLIENT_GENDER, cacheDict.getDictDesc(WskhConstant.DIC_1012, idCardInfo.getClient_gender()));
        }
        if (clob.getUser_base_info() != null) {
            UserBaseInfo userBaseInfo = clob.getUser_base_info();
            if (StringUtils.isNotBlank(userBaseInfo.getDuty())) {
                client_info.put("position", userBaseInfo.getDuty());
            }
            client_info.put(Fields.ADDRESS, clob.getTranslation_address());
            client_info.put("alternative_address", clob.getAlternative_address());
            client_info.put("work_unit", userBaseInfo.getWork_unit());
            client_info.put(Fields.PROFESSION_CODE, userBaseInfo.getProfession_code());
            client_info.put(Fields.DEGREE_CODE, userBaseInfo.getDegree_code());
            client_info.put("comment_reason", cacheDict.getDictDesc("choose_profession_reason", userBaseInfo.getChoose_profession_reason()));
        }
        req.setClient_info(client_info);
        return aiAuditService.auditClient(req);
    }

    @Override
    public List<AuditRuleQueryResp> queryAuditRule(AuditRuleQueryReq auditRuleQueryReq) {
        try {
            AuditRuleQueryRequest req = new AuditRuleQueryRequest();
            BeanUtil.copyProperties(auditRuleQueryReq, req);
            List<AuditRuleQueryResponse> resp = aiAuditQueryDubboService.queryAuditRule(req);
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(resp)) {
                List<AuditRuleQueryResp> list = new ArrayList<>();
                resp.forEach(res -> {
                    AuditRuleQueryResp result = new AuditRuleQueryResp();
                    BeanUtil.copyProperties(res, result);
                    list.add(result);
                });
                return list;
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常：{}", e.getError_info(), e);
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_查询智能审核规则异常", e);
            throw new BizException(ErrorEnum.COMPONENT_DUBBO_QUERY_RULE_ERROR.getValue(), ErrorEnum.COMPONENT_DUBBO_QUERY_RULE_ERROR.getDesc());
        }
        return null;
    }

    /**
     * 获取申请编号
     */
    @Override
    public String getAuditCode(String request_no) {
        BusinFlowRequest request = businFlowRequestService.getById(request_no);
        String busin_type_mapping = PropertySource.get(PropKeyConstant.WSKH_AIAUDIT_BUSIN_TYPE_MAPPING, "");
        Map<String, String> businTypeMap = JSON.parseObject(busin_type_mapping, Map.class);
        String new_busin_type = businTypeMap.getOrDefault(request.getBusin_type() + "-" + request.getId_kind(), request.getBusin_type());
        ApplyAuditCodeReq req = new ApplyAuditCodeReq();
        req.setSubsys_id(Integer.valueOf(WskhConstant.SUBSYS_ID));
        req.setBusin_type(Integer.valueOf(new_busin_type));
        req.setBusin_no(request_no);
        AuditBusinParam param = new AuditBusinParam();
        param.setClient_name(request.getClient_name());
        param.setId_no(request.getId_no());
        param.setId_kind(request.getId_kind());
        req.setAuditBusinParam(param);
        String audit_code = aiAuditService.applyAuditCode(req);

        Map<String, Object> params = new HashMap<>();
        params.put("ai_audit_code", audit_code);
        params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22384);
        params.put(Fields.BUSINESS_REMARK, "智能审核id绑定该用户");
        params.put(Fields.RECORD_TYPE, FlowStatusConst.SYSTEM_RECORD_TYPE);
        requestFlowService.saveParamsRecord(request_no, params);
        return audit_code;
    }

    @Override
    public QueryDishonestResult getSecurityAlisaRpa(String request_no, String clientName, String id_no) {
        QueryDishonestReq dishonestReq = new QueryDishonestReq();
        dishonestReq.setClient_name(clientName);
        dishonestReq.setId_no(id_no);
        QueryDishonestResult queryDishonestResult = dishonestQueryService.queryDishonestBlackList(dishonestReq);
        HashMap<String, Object> params = new HashMap<>();
        if (StringUtils.isBlank(queryDishonestResult.getResult_info_gt())) {
            if (StringUtils.isNotBlank(queryDishonestResult.getBody())) {
                FileInfo fileInfo = new FileInfo();
                fileInfo.setFile(queryDishonestResult.getBody().getBytes());
                fileInfo.setFile_name(queryDishonestResult.getPenDoc() + ".txt");
                fileInfo.setRemark(WskhConstant.SUBSYS_ID);
                log.info("electUploadFile入参为[{}]", JSON.toJSONString(fileInfo));
                String dishonest_content = archiveService.electUploadFile(fileInfo);
                // 由于dishonest_content返回字符过长，导致保存到BusinFlowParams以及UserQueryExtInfo保存异常，取消相关保存，更换为penDoc
                params.put(Fields.DISHONEST_CONTENT, dishonest_content);
                // 1:有失信记录
                params.put(Fields.DISHONEST_RECORD, "1");
                params.put(Fields.ORIGINAL_DISHONEST_RECORD, "1");
                params.put(Fields.BUSINESS_REMARK, "调用失信记录自动查询接口查询成功");
                queryDishonestResult.setDishonest_record("1");
            } else {
                // 0:无失信记录
                params.put(Fields.DISHONEST_RECORD, "0");
                params.put(Fields.ORIGINAL_DISHONEST_RECORD, "1");
                params.put(Fields.BUSINESS_REMARK, "调用失信记录自动查询接口查询成功-无失信记录");
                queryDishonestResult.setDishonest_record("0");
            }
        } else {
            // 5:失信记录系统异常待查
//            params.put(Fields.DISHONEST_RECORD, "5");
            params.put(Fields.BUSINESS_REMARK, "调用失信记录自动查询接口查询失败");
//            queryDishonestResult.setDishonest_record("5");
        }
        params.put(Fields.OPERATOR_NO, " ");
        params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22388);
//        params.put("queryDishonestResult", queryDishonestResult);
        params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
        requestFlowService.saveParamsRecord(request_no, params);

        LambdaUpdateWrapper<UserQueryExtInfo> UserQueryExtInfoWrapper = new LambdaUpdateWrapper<>();
        UserQueryExtInfoWrapper.set(UserQueryExtInfo::getDishonest_record, MapUtils.getString(params, Fields.DISHONEST_RECORD, StrUtil.SPACE));
        UserQueryExtInfoWrapper.set(UserQueryExtInfo::getDishonest_content, MapUtils.getString(params, Fields.DISHONEST_CONTENT, StrUtil.SPACE));
        UserQueryExtInfoWrapper.set(UserQueryExtInfo::getDishonest_record_remark, MapUtils.getString(params, Fields.BUSINESS_REMARK, StrUtil.SPACE));
        UserQueryExtInfoWrapper.eq(UserQueryExtInfo::getRequest_no, request_no);
        userQueryExtInfoService.update(UserQueryExtInfoWrapper);
        return queryDishonestResult;
    }

    /**
     * 获取公安照
     *
     * @param request_no
     * @param clob
     * @return
     */
    public void auditPolicePhoto(String request_no, ClobContentInfo clob) {
        if (null == clob) {
            clob = requestService.getAllDataByRequestNo(request_no);
        }
        VerifyPoliceReq req = new VerifyPoliceReq();
        // 个人
        if (StringUtils.equals(clob.getClient_category(), ClientCategoryEnum.CLIENT_PERSON.getCode())) {
            req.setFull_name(clob.getClient_name());
            req.setId_no(clob.getId_no());
            req.setId_kind(clob.getId_kind());
        } else {
            req.setFull_name(clob.getAgent_name());
            req.setId_no(clob.getAgent_id_no());
            req.setId_kind(clob.getAgent_id_kind());
        }
        req.setOrgan_flag(WskhConstant.ORGAN_FLAG);
        req.setBranch_no(clob.getBranch_no());
        req.setReal_branch_no(clob.getReal_branch_no());
        req.setRealtime_flag("1");
        req.setBranch_no(StringUtils.isBlank(clob.getReal_branch_no()) ? clob.getBranch_no() : clob.getReal_branch_no());
        //指定中登公安厂商
        req.setService_vender(PropertySource.get("comp.id.verify.service.provider"));
        log.info("智能审核获取公安三要素公安认证的入参为:[{}]", JSON.toJSONString(req));
        VerifyPoliceResp resp = componentIdVerifyService.verifyPolice(req);

        if (resp != null) {
            if (StringUtils.equals(resp.getStatus(), "2")) {
                throw new BizException("-1", resp.getResult_info());
            }
            Map<String, Object> params = new HashMap<>();
            params.put(Fields.BUSINESS_REMARK, "智能审核获取公安认证：" + resp.getResult_info());
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22387);
            params.put(Fields.OPERATOR_NO, WskhConstant.SUPER_USER);
            params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
            if (StringUtils.isNotBlank(resp.getImage_data())) {
                params.put("file_82", resp.getFilerecord_id());
            }
            PoliceVerifyResult policeVerifyResult = new PoliceVerifyResult();
            policeVerifyResult.setVerify_status("1".equals(resp.getStatus()) ? "8" : "9");
            if (StringUtils.isNotBlank(resp.getImage_data())) {
                //有照片
                policeVerifyResult.setIs_have_photo("1");
                policeVerifyResult.setFile_82(resp.getFilerecord_id());
            }
            policeVerifyResult.setVerify_result(resp.getResult_info());
            policeVerifyResult.setVerify_score(resp.getScore());
            params.put("police_result", policeVerifyResult);
            requestFlowService.saveParamsRecord(request_no, params);
        }
    }

    /**
     * 双向视频
     *
     * @param newsClob
     * @param clob
     * @param request_no
     */
    private void bidirectionalExecute(ClobContentInfo newsClob, ClobContentInfo clob, String request_no) {
        LambdaQueryWrapper<BusinFlowTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinFlowTask::getRequest_no, request_no)
                .orderByDesc(BusinFlowTask::getCreate_datetime);
        List<BusinFlowTask> businFlowTasks = businFlowTaskService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(businFlowTasks)) {
            BusinFlowTask businFlowTask = businFlowTasks.get(0);
            // 见证
            if (TaskTypeEnum.AUDIT.getCode().equals(businFlowTask.getTask_type())) {
                // 外国人永居证
                boolean isNormalBusinessWithResidencePermit = StringUtils.equalsAny(clob.getId_kind(), IdKindEnum.FOREIGN_PREV_PERMIT.getCode());
                if (!isNormalBusinessWithResidencePermit) {
                    CompletableFuture.runAsync(() -> auditIdCard(request_no, newsClob), aiAuditThreadPoolTaskExecutor);
                    CompletableFuture.runAsync(() -> auditImageQuality(request_no, WskhConstant.IMAGE_NO_6A, newsClob), aiAuditThreadPoolTaskExecutor);
                    CompletableFuture.runAsync(() -> auditImageQuality(request_no, WskhConstant.IMAGE_NO_6B, newsClob), aiAuditThreadPoolTaskExecutor);
                }
                CompletableFuture.runAsync(() -> auditClient(request_no, newsClob), aiAuditThreadPoolTaskExecutor);
                if (StringUtils.isBlank(clob.getDishonest_content())) {
                    CompletableFuture.runAsync(() -> getSecurityAlisaRpa(request_no, clob.getClient_name(), clob.getId_no()), aiAuditThreadPoolTaskExecutor);
                }
            } else {
                // 复核or二次复核
                CompletableFuture.runAsync(() -> auditVideo(request_no, newsClob), aiAuditThreadPoolTaskExecutor);
                CompletableFuture.runAsync(() -> {
                    AuditAudioIdentityResp resp = auditAudioIdentify(request_no, null);
                    if (resp != null && StringUtils.isNotBlank(resp.getIdentity())) {
                        auditAudio(request_no, null);
                    }
                }, aiAuditThreadPoolTaskExecutor);
            }
        }
    }

    /**
     * 网厅业务办理
     *
     * @param newsClob
     * @param request_no
     */
    private void handExecute(ClobContentInfo newsClob, String request_no) {
        LambdaQueryWrapper<BusinFlowTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinFlowTask::getRequest_no, request_no)
                .orderByDesc(BusinFlowTask::getCreate_datetime);
        List<BusinFlowTask> businFlowTasks = businFlowTaskService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(businFlowTasks)) {
            BusinFlowTask businFlowTask = businFlowTasks.get(0);
            // 见证
            if (TaskTypeEnum.AUDIT.getCode().equals(businFlowTask.getTask_type())) {
                // 个人-身份证、机构&产品-经办人
                if (StringUtils.equalsAny(IdKindEnum.ID_CARD.getCode(), newsClob.getId_kind(), newsClob.getAgent_id_kind())) {
                    CompletableFuture.runAsync(() -> handAuditIdCard(request_no, newsClob), aiAuditThreadPoolTaskExecutor);
                    CompletableFuture.runAsync(() -> handAuditImageQuality(request_no, WskhConstant.IMAGE_NO_6A, newsClob), aiAuditThreadPoolTaskExecutor);
                    CompletableFuture.runAsync(() -> handAuditImageQuality(request_no, WskhConstant.IMAGE_NO_6B, newsClob), aiAuditThreadPoolTaskExecutor);
                    // 是否新数据标签
                    if (!StringUtils.equals(newsClob.getData_sign(), WskhConstant.DATA_SIGN_STATUS)) {
                        CompletableFuture.runAsync(() -> auditPolicePhoto(request_no, newsClob), aiAuditThreadPoolTaskExecutor);
                    }
                }
            } else {
                // 复核or二次复核
                CompletableFuture.runAsync(() -> handAuditVideo(request_no, newsClob), aiAuditThreadPoolTaskExecutor);
                CompletableFuture.runAsync(() -> {
                    AuditAudioIdentityResp resp = auditAudioIdentify(request_no, null);
                    if (resp != null && StringUtils.isNotBlank(resp.getIdentity())) {
                        auditAudio(request_no, null);
                    }
                }, aiAuditThreadPoolTaskExecutor);
            }
        }
    }

}

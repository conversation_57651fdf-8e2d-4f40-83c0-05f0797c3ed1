package com.cairh.cpe.service.archive.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 功能说明: 图片旋转入参<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 * 开发时间: 2022/5/23<br>
 */
@Data
public class ElectRotateImageReq implements Serializable {

    /**image_data、filerecord_id二者必传其一、
     * 返回值与传值对应，传文件ID时，自行根据文件ID取数据做旋转，
     * 旋转完成后根据文件ID获取数据应为旋转后的结果数据*/

    /**图片base64字符串*/
    private String image_data;
    /**图片类型*/
    private String filerecord_id;
    /**旋转角度 正数顺时针旋转，负数逆时针旋转*/
    @NotNull
    private String rotate_angle;
}

package com.cairh.cpe.service.idverify;

import com.cairh.cpe.common.entity.request.AddressAssociationForm;
import com.cairh.cpe.common.entity.response.AddressAssociationQryResp;

import java.util.List;

public interface IAddressAssociationQuery {

    List<AddressAssociationQryResp> addressAssociationQuery(AddressAssociationForm addressAssociationForm);

    /**
     * 查询用户的姓名
     */
    String getBackendUserByStaffNo(String operator_no);
}

package com.cairh.cpe.task.schedule;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.task.service.IDataArchivingService;
import com.google.common.collect.Lists;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 数据归档标记执行器
 */
@Slf4j
@Component
public class DataArchivingMarkerScheduler {


    @Resource
    private IBusinFlowRequestService businFlowRequestService;

    @Resource
    private IDataArchivingService dataArchivingService;


    /**
     * 1 根据 request_status  =3 ,anode_id = theEnd  tohis_flag =0 查看代处理的数据
     * 2 根据 request_no 标记  BUSINFLOWREQUEST，BUSINFLOWTASK，BUSINFLOWPARAMS ，BUSINFLOWRECORD，
     * CUSTMODIFYRECORD，RPARECORD，USERQUERYEXTINFO  中相 TOHIS_FLAG =1 ，TOHIS_DATETIME = sysdate() + 7
     * 注意 :
     * BUSINFLOWREQUEST -- REQUEST_NO
     * BUSINFLOWTASK --> REQUEST_NO
     * BUSINFLOWPARAMS --> REQUEST_NO + BUSINFLOWTASK(SERIAL_ID)
     * BUSINFLOWRECORD --> REQUEST_NO
     * CUSTMODIFYRECORD --> REQUEST_NO
     * USERQUERYEXTINFO --> REQUEST_NO + BUSINFLOWTASK(SERIAL_ID)
     */
    @XxlJob(value = "dataArchivingMarkerScheduler")
    public void execute() {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("数据归档标记开始....");
        log.info("数据归档已审核通过标记开始 executed........");
        AtomicInteger total = new AtomicInteger();
        Page<BusinFlowRequest> page = new Page<>(1, 500);
        page.setOptimizeCountSql(false);
        page.setSearchCount(false);

        // 审核通过的
        while (true) {
            LambdaQueryWrapper<BusinFlowRequest> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BusinFlowRequest::getAnode_id, FlowNodeConst.END)
                    .eq(BusinFlowRequest::getRequest_status, FlowStatusConst.REQUEST_STATUS_AUDIT_PASS)
                    .eq(BusinFlowRequest::getTohis_flag, Constant.TOHIS_FLAG_N)
                    .select(BusinFlowRequest::getRequest_no);
            List<BusinFlowRequest> requestList = businFlowRequestService.page(page, wrapper).getRecords();
            if (CollectionUtils.isEmpty(requestList)) {
                break;
            }
            total.addAndGet(requestList.size());
            dataArchivingService.toArchivingMarkerList(requestList.stream().map(BusinFlowRequest::getRequest_no).collect(Collectors.toList()));
        }
        stopWatch.stop();
        log.info("数据归档标记结束不通过标记数量结束：{},{}", total.get(), stopWatch.prettyPrint());
    }
}

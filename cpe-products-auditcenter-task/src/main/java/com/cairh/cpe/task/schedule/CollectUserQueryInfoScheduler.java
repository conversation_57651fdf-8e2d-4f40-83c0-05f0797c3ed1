package com.cairh.cpe.task.schedule;

import cn.hutool.core.util.StrUtil;
import com.cairh.cpe.common.constant.QueueConstant;
import com.cairh.cpe.common.service.IUserQueryExtInfoService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 用户扩展信息收集
 */
@Slf4j
@Component
public class CollectUserQueryInfoScheduler {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private IUserQueryExtInfoService userQueryExtInfoService;


    @XxlJob(value = "userQueryInfoCollectHandler")
    public void execute() {
        try {
            String request_no;
            int count = 0;

            do {
                request_no = StrUtil.removeAll(redisTemplate.opsForList().rightPop(QueueConstant.QUERY_CLOB_QUEUE), '\"');
                log.info("任务执行器[userQueryInfoCollectHandler]开始处理业务编号[{}]", request_no);
                if (StrUtil.isNotBlank(request_no)) {
                    userQueryExtInfoService.saveClobParamsToUserQueryExtInfo(request_no);
                    log.info("任务执行器[userQueryInfoCollectHandler]完成处理业务编号[{}]", request_no);

                    if (++count == 10) {
                        TimeUnit.MILLISECONDS.sleep(100L);
                        count = 0;
                    }
                }
            } while (StrUtil.isNotBlank(request_no));
        } catch (Exception ex) {
            log.error("收集用户开户查询信息异常", ex);
            Thread.currentThread().interrupt();
        }
    }
}

package com.cairh.cpe.task.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.*;
import com.cairh.cpe.common.entity.request.AuditForm;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.entity.support.BackendUser;
import com.cairh.cpe.common.service.*;
import com.cairh.cpe.common.util.KHDateUtil;
import com.cairh.cpe.db.config.IdGenerator;
import com.cairh.cpe.task.service.IReportRawDataStatisticsService;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description：报表原始数据统计服务实现
 * Author： slx
 * Date： 2024/5/7 下午7:12
 */
@Slf4j
@Service
public class ReportRawDataStatisticsServiceImpl implements IReportRawDataStatisticsService {

    // 挂起
    private static final String SUSPEND_FLAG = "22395";
    // 回收
    private static final String RECOVERY_FLAG = "22392";
    // 转交
    private static final String TRANSFER_FLAG = "22390";
    // 开始见证
    private static final String AUDIT_FLAG = "1001";
    // 开始复核
    private static final String REVIEW_FLAG = "1101";
    // 双向视频复核申请
    private static final String BIDIRECTIONAL_REVIEW_FLAG = "22401";
    // 开始二次复核
    private static final String SECONDARY_REVIEW_FLAG = "1201";
    // 见证通过
    private static final String AUDIT_PASS_FLAG = "1002";
    // 见证驳回
    private static final String AUDIT_NO_PASS_FLAG = "1003";
    // 见证作废
    private static final String BIDIRECTIONAL_AUDIT_INVALIDATE_FLAG = "22400";
    // 复核通过
    private static final String REVIEW_PASS_FLAG = "1102";
    // 复核驳回
    private static final String REVIEW_NO_PASS_FLAG = "1103";
    // 二次复核通过
    private static final String SECONDARY_REVIEW_PASS_FLAG = "1202";
    // 二次复核驳回
    private static final String SECONDARY_REVIEW_NO_PASS_FLAG = "1203";
    // 日期分隔符
    private static final String DATE_SPLIT = "~";
    private final static Map<String, AuditForm> userMap = new HashMap<>(128);
    private final static Map<String, BranchInfo> branchInfoMap = new HashMap<>(128);
    private final static Map<String, Label> labelMap = new HashMap<>(128);
    @Autowired
    private IBusinFlowRequestService businFlowRequestService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private IUserQueryExtInfoService userQueryExtInfoService;
    @Autowired
    private IBusinProcessRequestAuditTrailService businProcessRequestAuditTrailService;
    @Autowired
    private IFlowTaskRecordDetailsService flowTaskRecordDetailsService;
    @Autowired
    private IBusinFlowRecordService businFlowRecordService;
    @Autowired
    private IHandupDetailsService handupDetailsService;
    @Autowired
    private CacheBranch cacheBranch;
    @Autowired
    private CacheBackendUser cacheBackendUser;
    @Autowired
    private ILabelService labelService;
    @Resource
    private IdGenerator idGenerator;

    /**
     * 统计task_id维度数据-BusinProcessRequestAuditTrail
     * 入参格式： date=2024-08-01~2024-09-01,pageSize=1000
     */
    @Override
    public void statisticsTaskIdData(String jobParam) {
        Map<String, String> param = new HashMap<>();
        Arrays.stream(jobParam.split(",")).forEach((s) -> {
            String[] arr = s.split("=");
            param.put(arr[0].trim(), arr[1].trim());
        });

        if (param.containsKey("branchMap")) {
            userMap.clear();
            branchInfoMap.clear();
        }
        List<String> dateList = getDateList(param);
        int pageSize = Integer.parseInt(param.getOrDefault("pageSize", "1000"));

        for (String date : dateList) {
            log.info("task_id job date：{}", date);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("task_id job start");
            List<BusinFlowRequest> businFlowRequestList = selectBusinFlowRequestList(date);
            if (CollectionUtils.isEmpty(businFlowRequestList)) {
                stopWatch.stop();
                log.info("task_id job size：{},{}", businFlowRequestList.size(), stopWatch.prettyPrint());
                continue;
            }
            Map<String, Label> labelDataMap = getLabelMap();

            Lists.partition(businFlowRequestList, pageSize).forEach(child -> {
                StopWatch childWatch = new StopWatch();
                childWatch.start(child.get(0).getRequest_no());
                batchHandleTaskIdAuditTrail(child, labelDataMap);
                childWatch.stop();
                log.info("task_id child：{},{}", child.size(), childWatch.prettyPrint());
            });
            stopWatch.stop();
            log.info("task_id job size：{},{}", businFlowRequestList.size(), stopWatch.prettyPrint());
        }
    }


    /**
     * 统计flow_task_id维度数据-FlowTaskRecordDetails
     * 入参格式： date=2024-08-01~2024-09-01,pageSize=1000
     */
    @Override
    public void statisticsFlowTaskIdData(String jobParam) {
        Map<String, String> param = new HashMap<>();
        Arrays.stream(jobParam.split(",")).forEach((s) -> {
            String[] arr = s.split("=");
            param.put(arr[0].trim(), arr[1].trim());
        });
        List<String> dateList = getDateList(param);
        int pageSize = Integer.parseInt(param.getOrDefault("pageSize", "1000"));

        for (String date : dateList) {
            log.info("flowtaskid job date：{}", date);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("flowtaskid job start");

            List<BusinFlowRequest> requestList = selectBusinFlowRequestList(date);
            if (CollectionUtils.isEmpty(requestList)) {
                stopWatch.stop();
                log.info("flowtaskid job size：{},{}", requestList.size(), stopWatch.prettyPrint());
                continue;
            }

            //分批处理
            Lists.partition(requestList, pageSize).forEach(child -> {
                StopWatch childWatch = new StopWatch();
                childWatch.start(child.get(0).getRequest_no());
                batchHandleFlowTaskIdRecord(child);
                childWatch.stop();
                log.info("flowtaskid child：{},{}", child.size(), childWatch.prettyPrint());
            });

            stopWatch.stop();
            log.info("flowtaskid job size：{},{}", requestList.size(), stopWatch.prettyPrint());
        }

    }

    @Override
    public void statisticsHandupData(String jobParam) {
        Map<String, String> param = new HashMap<>();
        Arrays.stream(jobParam.split(",")).forEach((s) -> {
            String[] arr = s.split("=");
            param.put(arr[0].trim(), arr[1].trim());
        });
        List<String> dateList = getDateList(param);
        int pageSize = Integer.parseInt(param.getOrDefault("pageSize", "1000"));

        for (String date : dateList) {
            log.info("statisticsHandup job date：{}", date);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("statisticsHandup job start");

            List<BusinFlowRequest> requestList = selectBusinFlowRequestList(date);
            if (CollectionUtils.isEmpty(requestList)) {
                stopWatch.stop();
                log.info("statisticsHandup job size：{},{}", requestList.size(), stopWatch.prettyPrint());
                continue;
            }

            //分批处理
            Lists.partition(requestList, pageSize).forEach(child -> {
                StopWatch childWatch = new StopWatch();
                childWatch.start(child.get(0).getRequest_no());
                batchHandleStatisticsHandup(child);
                childWatch.stop();
                log.info("statisticsHandup child：{},{}", child.size(), childWatch.prettyPrint());
            });

            stopWatch.stop();
            log.info("statisticsHandup job size：{},{}", requestList.size(), stopWatch.prettyPrint());
        }
    }

    /**
     * 批量处理statisticsHandup
     */
    private void batchHandleStatisticsHandup(List<BusinFlowRequest> businFlowRequestList) {
        if (CollectionUtils.isEmpty(businFlowRequestList)) {
            log.info("未查询到BusinFlowRequest数据，结束统计报表[statisticsHandupData]原始数据");
            return;
        }
        List<String> requestNoList = businFlowRequestList
                .stream()
                .map(BusinFlowRequest::getRequest_no)
                .collect(Collectors.toList());
        // 获取date日期流水表数据
        List<BusinFlowRecord> businFlowRecordList = selectBusinFlowRecordList(requestNoList);
        if (CollectionUtils.isEmpty(businFlowRecordList)) {
            log.info("未查询到数据，结束统计报表[statisticsHandupData]原始数据");
            return;
        }
        List<HandupDetails> handupDetailsList = Lists.newArrayList();
        businFlowRequestList.forEach(businFlowRequest -> {
            try {
                List<BusinFlowRecord> filterBusinFlowRecordList = businFlowRecordList.stream()
                        .filter(info -> info.getRequest_no().equals(businFlowRequest.getRequest_no()))
                        .sorted(Comparator.comparing(BusinFlowRecord::getCreate_datetime))
                        .collect(Collectors.toList());
                filterBusinFlowRecordList.forEach(businFlowRecord -> {
                    HandupDetails handupDetails = new HandupDetails();
                    handupDetails.setSerial_id(idGenerator.nextUUID(null))
                            .setRequest_no(businFlowRecord.getRequest_no())
                            .setTask_id(businFlowRecord.getRequest_no())
                            .setOperator_no(businFlowRecord.getOperator_no())
                            .setOperator_name(businFlowRecord.getOperator_name())
                            .setHandup_type(getHandupType(businFlowRecord.getBusiness_flag()))
                            .setCreate_datetime(businFlowRecord.getCreate_datetime())
                            .setTohis_flag("0");
                    handupDetailsList.add(handupDetails);
                });
            } catch (Exception e) {
                log.error("batchHandleStatisticsHandup出错，request_no：{} ", businFlowRequest.getRequest_no(), e);
            }
        });

        // 先删后增
        LambdaQueryWrapper<HandupDetails> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(HandupDetails::getRequest_no, requestNoList);
        handupDetailsService.remove(deleteWrapper);
        handupDetailsService.saveBatch(handupDetailsList);
    }

    /**
     * task_id 处理
     *
     * @param businFlowRequestList
     * @param labelMap
     */
    private void batchHandleTaskIdAuditTrail(List<BusinFlowRequest> businFlowRequestList, Map<String, Label> labelMap) {
        List<String> requestNoList = businFlowRequestList.stream().map(BusinFlowRequest::getRequest_no).collect(Collectors.toList());
        List<BusinFlowTask> businFlowTaskList = selectBusinFlowTaskListByRequestNoList(requestNoList);
        List<UserQueryExtInfo> userQueryExtInfoList = selectUserQueryExtInfoList(requestNoList);
        List<BusinFlowRecord> businFlowRecordList = selectBusinFlowRecordToFlowTaskIdList(requestNoList);

        List<BusinProcessRequestAuditTrail> auditTrails = Lists.newArrayList();

        businFlowRequestList.forEach(businFlowRequest -> {
            try {
                List<BusinFlowTask> businFlowTaskFilter = businFlowTaskList.stream()
                        .filter(businFlowTask -> businFlowTask.getRequest_no().equals(businFlowRequest.getRequest_no()))
                        .collect(Collectors.toList());

                UserQueryExtInfo userQueryExtInfo = userQueryExtInfoList.stream()
                        .filter(info -> info.getRequest_no().equals(businFlowRequest.getRequest_no()))
                        .findFirst()
                        .orElse(new UserQueryExtInfo());

                List<BusinFlowRecord> filterBusinFlowRecordList = businFlowRecordList.stream()
                        .filter(info -> info.getRequest_no().equals(businFlowRequest.getRequest_no()))
                        .sorted(Comparator.comparing(BusinFlowRecord::getCreate_datetime))
                        .collect(Collectors.toList());

                Map<String, List<BusinFlowTask>> taskIdMap = businFlowTaskFilter.stream().collect(Collectors.groupingBy(BusinFlowTask::getTask_id));
                if (taskIdMap.size() == 1) {
                    List<BusinFlowTask> sortBusinFlowTasks = taskIdMap.get(businFlowTaskFilter.get(0).getTask_id())
                            .stream().sorted(Comparator.comparing(BusinFlowTask::getCreate_datetime)).collect(Collectors.toList());
                    auditTrails.add(businProcessRequestAuditTrailHandle(sortBusinFlowTasks, filterBusinFlowRecordList, businFlowRequest, userQueryExtInfo));
                } else {
                    taskIdMap.forEach((taskId, businFlowTasks) -> {
                        List<BusinFlowTask> sortBusinFlowTasks = businFlowTasks.stream().sorted(Comparator.comparing(BusinFlowTask::getCreate_datetime)).collect(Collectors.toList());
                        Date startDateTime = sortBusinFlowTasks.get(0).getCreate_datetime();
                        Date endDateTime = sortBusinFlowTasks.get(sortBusinFlowTasks.size() - 1).getFinish_datetime();
                        // 获取流水表数据
                        List<BusinFlowRecord> datetimeFilterBusinFlowRecordList = filterBusinFlowRecordList.stream()
                                .filter(info -> info.getCreate_datetime().after(startDateTime) && info.getCreate_datetime().before(endDateTime))
                                .collect(Collectors.toList());
                        auditTrails.add(businProcessRequestAuditTrailHandle(sortBusinFlowTasks, datetimeFilterBusinFlowRecordList, businFlowRequest, userQueryExtInfo));
                    });
                }
//                BusinProcessRequestAuditTrail businProcessRequestAuditTrail = new BusinProcessRequestAuditTrail();
//                AtomicInteger requestNoTime = new AtomicInteger(0);
//                for (BusinFlowTask businFlowTask : businFlowTaskFilter) {
//                    requestNoTime.addAndGet(1);
//                    AuditForm auditForm = queryAndUpdateAuditForm(businFlowTask.getOperator_no());
//                    // 见证
//                    if (StringUtils.equals(TaskTypeEnum.AUDIT.getCode(), businFlowTask.getTask_type())) {
//                        if (StringUtils.isBlank(businProcessRequestAuditTrail.getRequest_no())) {
//                            audit(filterBusinFlowRecordList, businProcessRequestAuditTrail, businFlowTask, businFlowRequest, userQueryExtInfo, auditForm, labelMap);
//                        } else {
//                            auditTwo(filterBusinFlowRecordList, businProcessRequestAuditTrail, businFlowTask, auditForm, requestNoTime.get());
//                        }
//                        if (StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_NO_PASS)) {
//                            auditTrails.add(businProcessRequestAuditTrail);
//                            businProcessRequestAuditTrail = new BusinProcessRequestAuditTrail();
//                        }
//                    }
//                    // 复核
//                    if (StringUtils.equals(TaskTypeEnum.REVIEW.getCode(), businFlowTask.getTask_type()) &&
//                            StringUtils.isBlank(businProcessRequestAuditTrail.getReview_operator_no())) {
//                        review(filterBusinFlowRecordList, businProcessRequestAuditTrail, businFlowTask, auditForm, requestNoTime.get());
//                        auditTrails.add(businProcessRequestAuditTrail);
//                        businProcessRequestAuditTrail = new BusinProcessRequestAuditTrail();
//                    }
//                    // 二次复核
//                    if (StringUtils.equals(TaskTypeEnum.SECONDARY_REVIEW.getCode(), businFlowTask.getTask_type())) {
//                        secondaryReview(filterBusinFlowRecordList, businProcessRequestAuditTrail, businFlowTask, auditForm, requestNoTime.get());
//                        auditTrails.add(businProcessRequestAuditTrail);
//                        businProcessRequestAuditTrail = new BusinProcessRequestAuditTrail();
//                    }
//                }
            } catch (Exception e) {
                log.error("查询审批任务审批数据失败：request_no ={}", businFlowRequest.getRequest_no(), e);
            }
        });

        // 先删除后新增，防止多次执行插入异常
        LambdaQueryWrapper<BusinProcessRequestAuditTrail> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(BusinProcessRequestAuditTrail::getRequest_no, requestNoList);
        businProcessRequestAuditTrailService.remove(deleteWrapper);
        businProcessRequestAuditTrailService.saveBatch(auditTrails);
    }

    /**
     * flowtask_id 审批数据
     *
     * @param businFlowRequestList
     */
    private void batchHandleFlowTaskIdRecord(List<BusinFlowRequest> businFlowRequestList) {
        List<String> requestNoList = businFlowRequestList.stream().map(BusinFlowRequest::getRequest_no).collect(Collectors.toList());
        List<UserQueryExtInfo> userQueryExtInfoList = selectUserQueryExtInfoList(requestNoList);
        List<BusinFlowRecord> businFlowRecordList = selectBusinFlowRecordToFlowTaskIdList(requestNoList);
        List<BusinFlowTask> businFlowTaskList = selectBusinFlowTaskListByRequestNoList(requestNoList);
        List<FlowTaskRecordDetails> flowTaskRecordDetailsList = new ArrayList<>();
        String firstRequestNo = businFlowTaskList.get(0).getRequest_no();
        Date auditFinishTime = null;

        int requestNoTime = 0;
        int auditRequestNoTime = 1;
        for (BusinFlowTask businFlowTask : businFlowTaskList) {
            try {
                if (StringUtils.equals(firstRequestNo, businFlowTask.getRequest_no())) {
                    requestNoTime += 1;
                } else {
                    firstRequestNo = businFlowTask.getRequest_no();
                    requestNoTime = 1;
                    auditFinishTime = null;
                }
                String requestNo = businFlowTask.getRequest_no();
                UserQueryExtInfo userQueryExtInfo = userQueryExtInfoList.stream()
                        .filter(info -> info.getRequest_no().equals(requestNo))
                        .findFirst()
                        .orElse(null);
                if (Objects.isNull(userQueryExtInfo)) {
                    continue;
                }
                BusinFlowRequest businFlowRequest = businFlowRequestList
                        .stream()
                        .filter(info -> info.getRequest_no().equals(requestNo))
                        .findFirst()
                        .orElse(null);
                if (Objects.isNull(businFlowRequest)) {
                    continue;
                }
                List<BusinFlowRecord> filterBusinFlowRecordList = businFlowRecordList.stream()
                        .filter(info -> info.getRequest_no().equals(requestNo))
                        .sorted(Comparator.comparing(BusinFlowRecord::getCreate_datetime))
                        .collect(Collectors.toList());
                BusinFlowRecord pauseBusinFlowRecord = selectBusinFlowRecord(filterBusinFlowRecordList, requestNoTime);
                if (StringUtils.equals(TaskTypeEnum.AUDIT.getCode(), businFlowTask.getTask_type())) {
                    if (requestNoTime > 1) {
                        // 上一条见证任务作废
                        flowTaskRecordDetailsList.stream()
                                .filter(info -> info.getRequest_no().equals(requestNo) && StringUtils.equals(TaskTypeEnum.AUDIT.getCode(), info.getTask_type()))
                                .forEach(flowTaskRecordDetails -> flowTaskRecordDetails.setInvalid_flag("1"));
                    }
                    auditFinishTime = businFlowTask.getFinish_datetime();
                    auditRequestNoTime = requestNoTime;
                } else if (StringUtils.equals(TaskTypeEnum.REVIEW.getCode(), businFlowTask.getTask_type())) {
                    if ((requestNoTime - auditRequestNoTime) > 1) {
                        // 上一条复核任务作废
                        flowTaskRecordDetailsList.stream()
                                .filter(info -> info.getRequest_no().equals(requestNo) && StringUtils.equals(TaskTypeEnum.REVIEW.getCode(), info.getTask_type()))
                                .forEach(flowTaskRecordDetails -> flowTaskRecordDetails.setInvalid_flag("1"));
                    }
                }
                String branchNo = userQueryExtInfo.getBranch_no();
                BranchData branchData = new BranchData().getBranchInfo(branchNo);
                AuditForm auditForm = queryAndUpdateAuditForm(businFlowTask.getOperator_no());
                FlowTaskRecordDetails flowTaskRecordDetails = new FlowTaskRecordDetails();
                flowTaskRecordDetails.setRequest_no(businFlowTask.getRequest_no())
                        .setTask_id(businFlowTask.getTask_id())
                        .setSerial_id(businFlowTask.getSerial_id())
                        .setTime_property(getTimeProperty(businFlowTask.getCreate_datetime()))
                        .setOperator_no(businFlowTask.getOperator_no())
                        .setOperator_name(auditForm.getOperator_name())
                        .setOperator_branch_no(auditForm.getBranch_no())
                        .setOperator_up_branch_no(auditForm.getUp_branch_no())
                        .setBranch_no(userQueryExtInfo.getBranch_no())
                        .setBranch_name(branchData.getBranch_name())
                        .setUp_branch_no(branchData.getUp_branch_no())
                        .setUp_branch_name(branchData.getUp_branch_name())
                        .setRequest_datetime(businFlowRequest.getRequest_datetime())
                        .setTask_datetime(businFlowTask.getCreate_datetime())
                        .setTask_type(businFlowTask.getTask_type())
                        .setTask_status(businFlowTask.getTask_status())
                        .setDeal_datetime(businFlowTask.getDeal_datetime())
                        .setFinish_datetime(businFlowTask.getFinish_datetime())
                        .setEnd_deal_operator_no(businFlowTask.getOperator_no())
                        .setEnd_deal_datetime(businFlowTask.getDeal_datetime())
                        .setIs_branch_managed(isBranchManaged(branchNo, branchData.getUp_branch_no()))
                        .setWhite_flag(businFlowTask.getWhite_flag())
                        .setCreate_datetime(businFlowTask.getCreate_datetime())
                        .setFirst_pause_operator_no(Objects.nonNull(pauseBusinFlowRecord) ? pauseBusinFlowRecord.getOperator_no() : null)
                        .setBusin_type(businFlowRequest.getBusin_type());
                if (StringUtils.equals(TaskTypeEnum.REVIEW.getCode(), businFlowTask.getTask_type())) {
                    flowTaskRecordDetails.setAudit_finish_datetime(auditFinishTime);
                }
                pause(businFlowTask, flowTaskRecordDetails);
                if (StringUtils.equals(flowTaskRecordDetails.getPause_flag(), "0")) {
                    flowTaskRecordDetails.setPause_flag(Objects.nonNull(pauseBusinFlowRecord) ? "1" : "0")
                            .setFirst_pause_datetime(Objects.nonNull(pauseBusinFlowRecord) ? pauseBusinFlowRecord.getCreate_datetime() : null);
                }
                if (flowTaskRecordDetails.getFirst_deal_datetime() == null) {
                    BusinFlowRecord handleBusinFlowRecord = getStartBusinFlowRecord(filterBusinFlowRecordList, requestNoTime);
                    flowTaskRecordDetails
                            .setFirst_deal_operator_no(Objects.nonNull(handleBusinFlowRecord) ? handleBusinFlowRecord.getOperator_no() : businFlowTask.getOperator_no())
                            .setFirst_deal_datetime(Objects.nonNull(handleBusinFlowRecord) ? handleBusinFlowRecord.getCreate_datetime() : null);
                }
                flowTaskRecordDetailsList.add(flowTaskRecordDetails);
            } catch (Exception e) {
                log.error("查询任务数据出错，request_no：{} ", businFlowTask.getRequest_no(), e);
            }
        }
        LambdaQueryWrapper<FlowTaskRecordDetails> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(FlowTaskRecordDetails::getRequest_no, requestNoList);
        flowTaskRecordDetailsService.remove(deleteWrapper);
        flowTaskRecordDetailsService.saveBatch(flowTaskRecordDetailsList);
    }

    private String getHandupType(String handupType) {
        switch (handupType) {
            case SUSPEND_FLAG:
                return HandupTypeEnum.SUSPEND.getCode();
            case TRANSFER_FLAG:
                return HandupTypeEnum.TRANSFER.getCode();
            case RECOVERY_FLAG:
                return HandupTypeEnum.RECOVERY.getCode();
            default:
                return handupType;
        }
    }

    private BusinFlowRecord selectBusinFlowRecord(List<BusinFlowRecord> filterBusinFlowRecordList, int requestNoTime) {
        // 查询开始和结束记录，根据任务类型适配
        BusinFlowRecord startRecord = getStartBusinFlowRecord(filterBusinFlowRecordList, requestNoTime);
        BusinFlowRecord endRecord = getEndBusinFlowRecordToTime(filterBusinFlowRecordList, requestNoTime);

        // 使用流一次性完成过滤和查找
        return filterBusinFlowRecordList.stream()
                .filter(businFlowRecord -> isSuspendRecoveryOrTransfer(businFlowRecord) &&
                        (endRecord == null || isBetween(businFlowRecord, startRecord, endRecord)))
                .findFirst()
                .orElse(null);
    }

    private BusinFlowRecord getPauseBusinFlowRecord(List<BusinFlowRecord> filterBusinFlowRecordList) {
        List<BusinFlowRecord> pauseBusinFlowRecordList = filterBusinFlowRecordList
                .stream()
                .filter(businFlowRecord -> StringUtils.equalsAny(businFlowRecord.getBusiness_flag(), SUSPEND_FLAG, RECOVERY_FLAG, TRANSFER_FLAG))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(pauseBusinFlowRecordList) ? null : pauseBusinFlowRecordList.get(0);
    }

    private BusinFlowRecord getHandleBusinFlowRecord(List<BusinFlowRecord> filterBusinFlowRecordList) {
        List<BusinFlowRecord> handleBusinFlowRecordList = filterBusinFlowRecordList
                .stream()
                .filter(businFlowRecord -> StringUtils.equalsAny(businFlowRecord.getBusiness_flag(), AUDIT_FLAG))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(handleBusinFlowRecordList) ? null : handleBusinFlowRecordList.get(0);
    }

    private boolean isSuspendRecoveryOrTransfer(BusinFlowRecord record) {
        return SUSPEND_FLAG.equals(record.getBusiness_flag()) ||
                RECOVERY_FLAG.equals(record.getBusiness_flag()) ||
                TRANSFER_FLAG.equals(record.getBusiness_flag());
    }

    private boolean isBetween(BusinFlowRecord record, BusinFlowRecord startRecord, BusinFlowRecord endRecord) {
        if (startRecord == null || endRecord == null || record.getCreate_datetime() == null) {
            return false;
        }
        return record.getCreate_datetime().after(startRecord.getCreate_datetime()) &&
                record.getCreate_datetime().before(endRecord.getCreate_datetime());
    }

    private BusinFlowRecord getEndBusinFlowRecordToTime(List<BusinFlowRecord> filterBusinFlowRecordList, int requestNoTime) {
        // 参数合法性检查
        if (filterBusinFlowRecordList == null || requestNoTime < 1) {
            return null;
        }

        BusinFlowRecord endBusinFlowRecord = null;
        int roundTime = 0;

        for (BusinFlowRecord businFlowRecord : filterBusinFlowRecordList) {
            // 合并通过和驳回的处理逻辑
            if (StringUtils.equalsAny(businFlowRecord.getBusiness_flag(), AUDIT_PASS_FLAG, REVIEW_PASS_FLAG, SECONDARY_REVIEW_PASS_FLAG,
                    AUDIT_NO_PASS_FLAG, REVIEW_NO_PASS_FLAG, SECONDARY_REVIEW_NO_PASS_FLAG, BIDIRECTIONAL_AUDIT_INVALIDATE_FLAG)) {
                endBusinFlowRecord = businFlowRecord;
                roundTime += 1;

                if (roundTime == requestNoTime) {
                    break;
                }
            }
        }
        return endBusinFlowRecord;
    }

    /**
     * 获取第n次开始处理任务，开始->通过 or 开始->驳回 为一次轮循 不包含自动分配，只从任务开始标识
     *
     * @param filterBusinFlowRecordList
     * @param requestNoTime
     * @return
     */
    private BusinFlowRecord getStartBusinFlowRecord(List<BusinFlowRecord> filterBusinFlowRecordList, int requestNoTime) throws IllegalArgumentException {
        if (requestNoTime < 1) {
            throw new IllegalArgumentException("RequestNoTime cannot be less than 1.");
        }

        if (CollectionUtils.isEmpty(filterBusinFlowRecordList)) {
            return null;
        }

        BusinFlowRecord startBusinFlowRecord = null;
        int roundTime = 0;

        for (BusinFlowRecord businFlowRecord : filterBusinFlowRecordList) {
            if (StringUtils.equalsAny(businFlowRecord.getBusiness_flag(), AUDIT_FLAG, REVIEW_FLAG, SECONDARY_REVIEW_FLAG, BIDIRECTIONAL_REVIEW_FLAG) && startBusinFlowRecord == null) {
                startBusinFlowRecord = businFlowRecord;
                if (requestNoTime == 1) {
                    return startBusinFlowRecord;
                }
            }

            if (StringUtils.equalsAny(businFlowRecord.getBusiness_flag(), AUDIT_PASS_FLAG, REVIEW_PASS_FLAG, SECONDARY_REVIEW_PASS_FLAG,
                    AUDIT_NO_PASS_FLAG, REVIEW_NO_PASS_FLAG, SECONDARY_REVIEW_NO_PASS_FLAG, BIDIRECTIONAL_AUDIT_INVALIDATE_FLAG)) {
                roundTime += 1;
                if (roundTime == requestNoTime) {
                    break;
                }
                startBusinFlowRecord = null;
            }
        }
        return startBusinFlowRecord;
    }

    private void pause(BusinFlowTask businFlowTask, FlowTaskRecordDetails flowTaskRecordDetails) {
        if (StringUtils.equalsAny(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_SUSPEND, FlowStatusConst.AUDIT_TRANSFER)) {
            flowTaskRecordDetails.setPause_flag("1")
                    .setFirst_pause_datetime(businFlowTask.getDeal_datetime());
        }
    }

    private BusinProcessRequestAuditTrail businProcessRequestAuditTrailHandle(List<BusinFlowTask> sortBusinFlowTasks, List<BusinFlowRecord> filterBusinFlowRecordList, BusinFlowRequest businFlowRequest,
                                                                              UserQueryExtInfo userQueryExtInfo) {
        BusinProcessRequestAuditTrail businProcessRequestAuditTrail = new BusinProcessRequestAuditTrail();
        for (BusinFlowTask businFlowTask : sortBusinFlowTasks) {
            AuditForm auditForm = queryAndUpdateAuditForm(businFlowTask.getOperator_no());
            // 见证
            if (StringUtils.equals(TaskTypeEnum.AUDIT.getCode(), businFlowTask.getTask_type())) {
                if (StringUtils.isBlank(businProcessRequestAuditTrail.getRequest_no())) {
                    newAudit(filterBusinFlowRecordList, businProcessRequestAuditTrail, businFlowTask, businFlowRequest, userQueryExtInfo, auditForm, labelMap);
                } else {
                    auditTwo(businProcessRequestAuditTrail, businFlowTask, auditForm);
                }
            }
            // 复核
            if (StringUtils.equals(TaskTypeEnum.REVIEW.getCode(), businFlowTask.getTask_type())) {
                review(businProcessRequestAuditTrail, businFlowTask, auditForm);
            }
            // 二次复核
            if (StringUtils.equals(TaskTypeEnum.SECONDARY_REVIEW.getCode(), businFlowTask.getTask_type())) {
                secondaryReview(businProcessRequestAuditTrail, businFlowTask, auditForm);
            }
        }
        return businProcessRequestAuditTrail;
    }


    private void newAudit(List<BusinFlowRecord> filterBusinFlowRecordList, BusinProcessRequestAuditTrail businProcessRequestAuditTrail,
                          BusinFlowTask businFlowTask, BusinFlowRequest businFlowRequest, UserQueryExtInfo userQueryExtInfo, AuditForm auditForm,
                          Map<String, Label> labelMap) {
        BusinFlowRecord pauseBusinFlowRecord = getPauseBusinFlowRecord(filterBusinFlowRecordList);
        BusinFlowRecord handleBusinFlowRecord = getHandleBusinFlowRecord(filterBusinFlowRecordList);
        String branchNo = userQueryExtInfo.getBranch_no();
        BranchData branchData = new BranchData().getBranchInfo(branchNo);
        businProcessRequestAuditTrail.setRequest_no(businFlowTask.getRequest_no())
                .setTask_id(businFlowTask.getTask_id())
                .setUser_name(userQueryExtInfo.getClient_name())
                .setId_no(userQueryExtInfo.getId_no())
                .setId_kind(userQueryExtInfo.getId_kind())
                .setTime_property(getTimeProperty(businFlowRequest.getRequest_datetime()))
                .setRequest_status(getRequestStatus(businFlowTask.getTask_type(), businFlowTask.getTask_status()))
                .setRequest_datetime(businFlowTask.getCreate_datetime())
                .setAudit_operator_no(businFlowTask.getOperator_no())
                .setAudit_operator_name(auditForm.getOperator_name())
                .setAudit_operator_branch_no(auditForm.getBranch_no())
                .setAudit_operator_branch_name(auditForm.getBranch_name())
                .setAudit_operator_up_branch_no(auditForm.getUp_branch_no())
                .setAudit_operator_up_branch_name(auditForm.getUp_branch_name())
                .setAudit_create_datetime(businFlowTask.getCreate_datetime())
                .setAudit_deal_datetime(businFlowTask.getDeal_datetime())
                .setAudit_finish_datetime(businFlowTask.getFinish_datetime())
                .setAudit_op_content(businFlowTask.getOp_content())
                .setChannel_code(userQueryExtInfo.getChannel_code())
                .setActivity_no(userQueryExtInfo.getActivity_no())
                .setBusin_type(userQueryExtInfo.getBusin_type())
                .setUser_branch_no(userQueryExtInfo.getBranch_no())
                .setUser_branch_name(branchData.getBranch_name())
                .setUser_up_branch_no(branchData.getUp_branch_no())
                .setUser_up_branch_name(branchData.getUp_branch_name())
                .setMatch_labels(getMatchLabelTypes(labelMap, businFlowTask.getMatch_labels()))
                .setTask_source(businFlowTask.getTask_source())
                .setApp_id(userQueryExtInfo.getApp_id())
                .setIs_branch_managed(isBranchManaged(branchNo, branchData.getUp_branch_no()))
                .setPause_flag(Objects.nonNull(pauseBusinFlowRecord) ? "1" : "0")
                .setFirst_pause_datetime(Objects.nonNull(pauseBusinFlowRecord) ? pauseBusinFlowRecord.getCreate_datetime() : null)
                .setEnd_deal_datetime(businFlowTask.getDeal_datetime())
                .setCreate_datetime(businFlowTask.getCreate_datetime());

        if (businProcessRequestAuditTrail.getFirst_deal_datetime() == null) {
            businProcessRequestAuditTrail
                    .setFirst_deal_operator_no(Objects.nonNull(handleBusinFlowRecord) ? handleBusinFlowRecord.getOperator_no() : businFlowTask.getOperator_no())
                    .setFirst_deal_datetime(Objects.nonNull(handleBusinFlowRecord) ? handleBusinFlowRecord.getCreate_datetime() : null);
        }
    }


    private void audit(List<BusinFlowRecord> filterBusinFlowRecordList, BusinProcessRequestAuditTrail businProcessRequestAuditTrail,
                       BusinFlowTask businFlowTask, BusinFlowRequest businFlowRequest, UserQueryExtInfo userQueryExtInfo, AuditForm auditForm,
                       Map<String, Label> labelMap) {
        BusinFlowRecord pauseBusinFlowRecord = selectBusinFlowRecord(filterBusinFlowRecordList, 1);
        BusinFlowRecord handleBusinFlowRecord = getStartBusinFlowRecord(filterBusinFlowRecordList, 1);
        String branchNo = userQueryExtInfo.getBranch_no();
        BranchData branchData = new BranchData().getBranchInfo(branchNo);
        businProcessRequestAuditTrail.setRequest_no(businFlowTask.getRequest_no())
                .setTask_id(businFlowTask.getTask_id())
                .setUser_name(userQueryExtInfo.getClient_name())
                .setId_no(userQueryExtInfo.getId_no())
                .setId_kind(userQueryExtInfo.getId_kind())
                .setTime_property(getTimeProperty(businFlowRequest.getRequest_datetime()))
                .setRequest_status(getRequestStatus(businFlowTask.getTask_type(), businFlowTask.getTask_status()))
                .setRequest_datetime(businFlowTask.getCreate_datetime())
                .setAudit_operator_no(businFlowTask.getOperator_no())
                .setAudit_operator_name(auditForm.getOperator_name())
                .setAudit_operator_branch_no(auditForm.getBranch_no())
                .setAudit_operator_branch_name(auditForm.getBranch_name())
                .setAudit_operator_up_branch_no(auditForm.getUp_branch_no())
                .setAudit_operator_up_branch_name(auditForm.getUp_branch_name())
                .setAudit_create_datetime(businFlowTask.getCreate_datetime())
                .setAudit_deal_datetime(businFlowTask.getDeal_datetime())
                .setAudit_finish_datetime(businFlowTask.getFinish_datetime())
                .setAudit_op_content(businFlowTask.getOp_content())
                .setChannel_code(userQueryExtInfo.getChannel_code())
                .setActivity_no(userQueryExtInfo.getActivity_no())
                .setBusin_type(userQueryExtInfo.getBusin_type())
                .setUser_branch_no(userQueryExtInfo.getBranch_no())
                .setUser_branch_name(branchData.getBranch_name())
                .setUser_up_branch_no(branchData.getUp_branch_no())
                .setUser_up_branch_name(branchData.getUp_branch_name())
                .setMatch_labels(getMatchLabelTypes(labelMap, businFlowTask.getMatch_labels()))
                .setTask_source(businFlowTask.getTask_source())
                .setApp_id(userQueryExtInfo.getApp_id())
                .setIs_branch_managed(isBranchManaged(branchNo, branchData.getUp_branch_no()))
                .setPause_flag(Objects.nonNull(pauseBusinFlowRecord) ? "1" : "0")
                .setFirst_pause_datetime(Objects.nonNull(pauseBusinFlowRecord) ? pauseBusinFlowRecord.getCreate_datetime() : null)
                .setEnd_deal_datetime(businFlowTask.getDeal_datetime())
                .setCreate_datetime(businFlowTask.getCreate_datetime());

        if (businProcessRequestAuditTrail.getFirst_deal_datetime() == null) {
            businProcessRequestAuditTrail
                    .setFirst_deal_operator_no(Objects.nonNull(handleBusinFlowRecord) ? handleBusinFlowRecord.getOperator_no() : businFlowTask.getOperator_no())
                    .setFirst_deal_datetime(Objects.nonNull(handleBusinFlowRecord) ? handleBusinFlowRecord.getCreate_datetime() : null);
        }
    }

    private void auditTwo(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask, AuditForm auditForm) {
        businProcessRequestAuditTrail
                .setAudit_operator_no(businFlowTask.getOperator_no())
                .setAudit_operator_name(auditForm.getOperator_name())
                .setAudit_operator_branch_no(auditForm.getBranch_no())
                .setAudit_operator_branch_name(auditForm.getBranch_name())
                .setAudit_operator_up_branch_no(auditForm.getUp_branch_no())
                .setAudit_operator_up_branch_name(auditForm.getUp_branch_name())
                .setAudit_create_datetime(businFlowTask.getCreate_datetime())
                .setAudit_deal_datetime(businFlowTask.getDeal_datetime())
                .setAudit_finish_datetime(businFlowTask.getFinish_datetime())
                .setAudit_op_content(businFlowTask.getOp_content())
                .setEnd_deal_datetime(businFlowTask.getDeal_datetime());
    }

    private void review(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask, AuditForm auditForm) {
        businProcessRequestAuditTrail.setRequest_status(getRequestStatus(businFlowTask.getTask_type(), businFlowTask.getTask_status()))
                .setReview_operator_no(businFlowTask.getOperator_no())
                .setReview_operator_name(auditForm.getOperator_name())
                .setReview_operator_branch_no(auditForm.getBranch_no())
                .setReview_operator_branch_name(auditForm.getBranch_name())
                .setReview_operator_up_branch_no(auditForm.getUp_branch_no())
                .setReview_operator_up_branch_name(auditForm.getUp_branch_name())
                .setReview_create_datetime(businFlowTask.getCreate_datetime())
                .setReview_deal_datetime(businFlowTask.getDeal_datetime())
                .setReview_finish_datetime(businFlowTask.getFinish_datetime())
                .setEnd_deal_datetime(businFlowTask.getDeal_datetime())
                .setReview_op_content(businFlowTask.getOp_content());
    }

    private void secondaryReview(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask, AuditForm auditForm) {
        businProcessRequestAuditTrail.setRequest_status(getRequestStatus(businFlowTask.getTask_type(), businFlowTask.getTask_status()))
                .setSec_rv_operator_no(businFlowTask.getOperator_no())
                .setSec_rv_operator_name(auditForm.getOperator_name())
                .setSec_rv_operator_branch_no(auditForm.getBranch_no())
                .setSec_rv_operator_branch_name(auditForm.getBranch_name())
                .setSec_rv_operator_up_branch_no(auditForm.getUp_branch_no())
                .setSec_rv_operator_up_branch_name(auditForm.getUp_branch_name())
                .setSec_rv_create_datetime(businFlowTask.getCreate_datetime())
                .setSec_rv_deal_datetime(businFlowTask.getDeal_datetime())
                .setSec_rv_finish_datetime(businFlowTask.getFinish_datetime())
                .setEnd_deal_datetime(businFlowTask.getDeal_datetime())
                .setSec_rv_op_content(businFlowTask.getOp_content());

    }

    private List<BusinFlowRequest> selectBusinFlowRequestList(String date) {
        // 每页大小
        LocalDate targetDate = LocalDate.parse(date);
        LocalDateTime startOfDay = LocalDateTime.of(targetDate, LocalTime.MIN);
        LocalDateTime endOfDay = LocalDateTime.of(targetDate, LocalTime.MAX);
        LambdaQueryWrapper<BusinFlowRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BusinFlowRequest::getRequest_no, BusinFlowRequest::getRequest_datetime, BusinFlowRequest::getBusin_type);
        wrapper.in(BusinFlowRequest::getRequest_status, FlowStatusConst.REQUEST_STATUS_AUDIT_PASS, FlowStatusConst.REQUEST_STATUS_AUDIT_FAIL, FlowStatusConst.AUDIT_INVALIDATE);
        wrapper.between(BusinFlowRequest::getRequest_datetime, startOfDay, endOfDay);
        return businFlowRequestService.list(wrapper);
    }


    /**
     * 获取taskId
     *
     * @param requestNoList
     * @return
     */
    private List<BusinFlowTask> selectBusinFlowTaskListByRequestNoList(List<String> requestNoList) {
        LambdaQueryWrapper<BusinFlowTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BusinFlowTask::getSerial_id,
                BusinFlowTask::getTask_id,
                BusinFlowTask::getTask_type,
                BusinFlowTask::getTask_status,
                BusinFlowTask::getRequest_no,
                BusinFlowTask::getOperator_no,
                BusinFlowTask::getOp_content,
                BusinFlowTask::getWhite_flag,
                BusinFlowTask::getMatch_labels,
                BusinFlowTask::getTask_source,
                BusinFlowTask::getCreate_datetime,
                BusinFlowTask::getDeal_datetime,
                BusinFlowTask::getFinish_datetime);
        wrapper.in(BusinFlowTask::getRequest_no, requestNoList);
        wrapper.notIn(BusinFlowTask::getTask_id, StrUtil.SPACE);
        return businFlowTaskService.list(wrapper);
    }

    /**
     * 获取user 拓展表
     *
     * @param requestNoList
     * @return
     */
    private List<UserQueryExtInfo> selectUserQueryExtInfoList(List<String> requestNoList) {
        LambdaQueryWrapper<UserQueryExtInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(UserQueryExtInfo::getRequest_no,
                UserQueryExtInfo::getClient_name,
                UserQueryExtInfo::getId_no,
                UserQueryExtInfo::getId_kind,
                UserQueryExtInfo::getBranch_no,
                UserQueryExtInfo::getChannel_code,
                UserQueryExtInfo::getActivity_no,
                UserQueryExtInfo::getBusin_type,
                UserQueryExtInfo::getApp_id);
        wrapper.in(UserQueryExtInfo::getRequest_no, requestNoList);
        return userQueryExtInfoService.list(wrapper);
    }

    /**
     * 获取流水表
     *
     * @param requestNoList
     * @return
     */
    private List<BusinFlowRecord> selectBusinFlowRecordToFlowTaskIdList(List<String> requestNoList) {
        LambdaQueryWrapper<BusinFlowRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BusinFlowRecord::getBusiness_flag,
                BusinFlowRecord::getCreate_datetime,
                BusinFlowRecord::getRequest_no,
                BusinFlowRecord::getBusiness_remark);
        wrapper.in(BusinFlowRecord::getBusiness_flag, SUSPEND_FLAG, RECOVERY_FLAG,
                        TRANSFER_FLAG, AUDIT_FLAG, REVIEW_FLAG, SECONDARY_REVIEW_FLAG,
                        AUDIT_PASS_FLAG, AUDIT_NO_PASS_FLAG, REVIEW_PASS_FLAG, REVIEW_NO_PASS_FLAG,
                        SECONDARY_REVIEW_PASS_FLAG, SECONDARY_REVIEW_NO_PASS_FLAG, BIDIRECTIONAL_AUDIT_INVALIDATE_FLAG, BIDIRECTIONAL_REVIEW_FLAG)
                .in(BusinFlowRecord::getRequest_no, requestNoList);
        return businFlowRecordService.list(wrapper);
    }

    private List<BusinFlowRecord> selectBusinFlowRecordList(List<String> requestNoList) {
        LambdaQueryWrapper<BusinFlowRecord> wrapper = new LambdaQueryWrapper<>();
        // 查询挂起：22395、回收：22392、转交接受：22390
        wrapper.in(BusinFlowRecord::getBusiness_flag, SUSPEND_FLAG, RECOVERY_FLAG, TRANSFER_FLAG)
                .in(BusinFlowRecord::getRequest_no, requestNoList);
        return businFlowRecordService.list(wrapper);
    }


    private AuditForm queryAndUpdateAuditForm(String operator_no) {
        if (userMap.containsKey(operator_no)) {
            return userMap.get(operator_no);
        }
        Optional<BackendUser> optionalBackendUser = Optional.ofNullable(cacheBackendUser.getBackendUserByStaffNo(operator_no));

        AuditForm auditForm = new AuditForm();
        optionalBackendUser.ifPresent(backendUser -> {
            auditForm.setOperator_name(backendUser.getUser_name());
            auditForm.setBranch_no(backendUser.getBranch_no());
            updateAuditFormBranchInfo(auditForm, backendUser.getBranch_no());
        });
        userMap.put(operator_no, auditForm);
        return auditForm;
    }

    private void updateAuditFormBranchInfo(AuditForm auditForm, String branchNo) {
        BranchInfo branchInfo = branchInfoMap.computeIfAbsent(branchNo, this::loadBranchInfo);
        if (Objects.nonNull(branchInfo)) {
            String branchName = StringUtils.isNotBlank(branchInfo.getBranch_name()) ? branchInfo.getBranch_name() : StrUtil.SPACE;
            auditForm.setBranch_name(branchName);
            if (StringUtils.equalsAny(branchInfo.getBranch_type(), BranchConstant.LEVEL_HEADQUARTERS, BranchConstant.LEVEL_SUBSIDIARY_COMPANY, BranchConstant.LEVEL_BRANCH_OFFICE)) {
                auditForm.setUp_branch_no(branchNo);
                auditForm.setUp_branch_name(branchName);
            } else {
                // 获取上级分支信息
                String upBranchNo = StringUtils.isNotBlank(branchInfo.getUp_branch_no()) ? branchInfo.getUp_branch_no() : branchNo;
                BranchInfo upBranchInfo = branchInfoMap.computeIfAbsent(upBranchNo, this::loadBranchInfo);
                auditForm.setUp_branch_no(upBranchNo);
                auditForm.setUp_branch_name(StringUtils.isNotBlank(upBranchInfo.getBranch_name()) ? upBranchInfo.getBranch_name() : StrUtil.SPACE);
            }
        }
    }

    private BranchInfo loadBranchInfo(String branchNo) {
        return cacheBranch.getBranchByNo(branchNo);
    }

    private Map<String, Label> getLabelMap() {
        if (CollectionUtil.isNotEmpty(labelMap)) {
            return labelMap;
        }
        try {
            Map<String, Label> tempMap = labelService.getMapLabels();
            if (tempMap != null) {
                labelMap.putAll(tempMap);
            }
        } catch (Exception e) {
            log.error("Error occurred while getting labels", e);
        }
        return labelMap;
    }

    /**
     * 时间属性
     *
     * @return 1:工作时间 2:非工作时间
     */
    private String getTimeProperty(Date date) {
        // 工作时间 时间范围在9点-5点
        if (isWorkHour(date)) {
            return TimePropertyEnum.WORKTIME.getCode();
        }
        return TimePropertyEnum.NOWORKTIME.getCode();
    }

    private boolean isWorkHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 获取小时（0-23）
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        // 获取分钟
        int minute = calendar.get(Calendar.MINUTE);
        // 工作时间是从9:00到17:00
        return hour >= 9 && hour < 17 && minute >= 0 && minute < 60;
    }

    private String getRequestStatus(String anodeId, String status) {
        return anodeId + "-" + status;
    }

    private String getMatchLabelTypes(Map<String, Label> labelMap, String matchLabels) {
        if (StringUtils.isBlank(matchLabels)) {
            return StrUtil.SPACE;
        }
        return Arrays.stream(matchLabels.split(StrUtil.COMMA))
                .map(e -> labelMap.get(e).getLabel_type())
                .collect(Collectors.joining(StrUtil.COMMA));
    }

    private String isBranchManaged(String branchNo, String upBranchNo) {
//        String baseNotManageBranch = PropertySource.get(PropKeyConstant.BASE_NOTMANAGE_BRANCH, "0");
//        Set<String> notManageBranchSet = new HashSet<>(Arrays.asList(baseNotManageBranch.split(",")));
//        if (CollectionUtil.isNotEmpty(notManageBranchSet)) {
//            if (notManageBranchSet.contains(branchNo) || notManageBranchSet.contains(upBranchNo)) {
//                return Constant.BRANCH_MANAGED_N;
//            }
//            List<BranchInfo> allBranchList = cacheBranch.getAllBranchList();
//            for (BranchInfo branch : allBranchList) {
//                if (StringUtils.equals(upBranchNo, branch.getBranch_no())) {
//                    if (StringUtils.isNotBlank(branch.getUp_branch_no()) && notManageBranchSet.contains(branch.getUp_branch_no())) {
//                        return Constant.BRANCH_MANAGED_N;
//                    }
//                    upBranchNo = branch.getUp_branch_no();
//                }
//            }
//        }
        return Constant.BRANCH_MANAGED_Y;
    }

    private List<String> getDateList(Map<String, String> param) {
        String dateParam = param.getOrDefault("date", KHDateUtil.getDate());
        List<String> dateList = new ArrayList<>(Collections.singletonList(dateParam));
        if (!dateParam.contains(DATE_SPLIT)) {
            return dateList;
        }
        dateList.clear();
        String[] dateSplit = dateParam.split(DATE_SPLIT);
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        LocalDate startDate = LocalDate.parse(dateSplit[0], formatter);
        LocalDate endDate = LocalDate.parse(dateSplit[1], formatter);

        // 计算两个日期之间的所有日期
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dateList.add(currentDate.format(formatter));
            currentDate = currentDate.plusDays(1);
        }
        return dateList;
    }


    @Data
    @EqualsAndHashCode(callSuper = false)
    class BranchData {
        private String branch_no;
        private String branch_name;
        private String up_branch_no;
        private String up_branch_name;

        public BranchData getBranchInfo(String branchNo) {
            BranchInfo branchInfo;
            if (branchInfoMap.containsKey(branchNo)) {
                branchInfo = branchInfoMap.get(branchNo);
            } else {
                branchInfo = cacheBranch.getBranchByNo(branchNo);
                branchInfoMap.put(branchNo, branchInfo);
            }
            BranchData branchData = new BranchData();
            if (Objects.nonNull(branchInfo)) {
                branchData.setBranch_no(branchNo);
                branchData.setBranch_name(branchInfo.getBranch_name());

                String upBranchNo = StringUtils.isNotBlank(branchInfo.getUp_branch_no())
                        ? branchInfo.getUp_branch_no() : branchNo;
                branchData.setUp_branch_no(upBranchNo);
                BranchInfo upBranchInfo;
                if (branchInfoMap.containsKey(upBranchNo)) {
                    upBranchInfo = branchInfoMap.get(upBranchNo);
                } else {
                    upBranchInfo = cacheBranch.getBranchByNo(upBranchNo);
                    branchInfoMap.put(upBranchNo, upBranchInfo);
                }

                branchData.setUp_branch_name(upBranchInfo.getBranch_name());
            }
            return branchData;
        }
    }
}

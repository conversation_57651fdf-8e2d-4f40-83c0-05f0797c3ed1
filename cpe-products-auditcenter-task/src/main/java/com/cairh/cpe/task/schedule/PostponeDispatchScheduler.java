package com.cairh.cpe.task.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.cache.component.ComponentWorkTimeService;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.service.aidispatch.service.IAiDispatchAchieveService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 暂缓派单任务中登时间重新派单
 */
@Slf4j
@Component
public class PostponeDispatchScheduler {

    @Autowired
    private IAiDispatchAchieveService aiDispatchAchieveService;


    @Autowired
    private IBusinFlowTaskService businFlowTaskService;

    @Autowired
    private ComponentWorkTimeService componentWorkTimeService;

    @Autowired
    private RedissonUtil redissonUtil;


    /**
     * 中登异常的任务,在正常工作时重新推送
     */
    @XxlJob(value = "postponeDispatchHandle")
    public void execute() {
        if (!PropertySource.getBoolean(PropKeyConstant.WSKH_ISOPEN_DISPATCH)) {
           return;
        }
        if (!componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_ZD_GA)) {
            return;
        }
        int cur_page = 1;
        int page_size = 200;
        while (true){
            LambdaQueryWrapper<BusinFlowTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BusinFlowTask::getPush_flag, WskhConstant.NEED_PUSH_FLAG)
                    .eq(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING)
                    .orderByAsc(BusinFlowTask::getCreate_datetime);
            Page<BusinFlowTask> pageTask = new Page<>(cur_page, page_size);
            pageTask.setSearchCount(Boolean.FALSE);
            Page<BusinFlowTask> page = businFlowTaskService.page(pageTask, wrapper);
            if (CollectionUtils.isEmpty(page.getRecords())) {
                break;
            }
            for (BusinFlowTask record : page.getRecords()) {
                String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, record.getRequest_no());
                try {
                    boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
                    if (!isLock) {
                        log.info("中登异常的任务推送派单未获取到锁，request_no =【{}】", record.getRequest_no());
                        return;
                    }
                    aiDispatchAchieveService.handleNeedPushFlagDispatchTask(record.getRequest_no(), record.getSerial_id());
                }catch (Exception e) {
                    log.error("中登异常的任务推送派单失败request_no= {},serial_id +{} ",record.getRequest_no(),record.getSerial_id(),e);
                }finally {
                    redissonUtil.unlock(lockKey);
                }
            }
        }
    }


}

package com.cairh.cpe.businflow.service.impl;

import com.cairh.cpe.activiti.client.extension.ActivitiClientExtensionConfigService;
import com.cairh.cpe.activiti.client.extension.dto.req.ServiceTaskConfigRequest;
import com.cairh.cpe.activiti.client.extension.dto.resp.ServiceTaskConfigResponse;
import com.cairh.cpe.activiti.common.exception.ActivitiException;
import com.cairh.cpe.businflow.service.IActivitiTaskExecuteListener;
import com.cairh.cpe.businflow.service.IActivitiTaskService;
import com.cairh.cpe.context.expression.ExpressionParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ActivitiTaskServiceImpl implements IActivitiTaskService {

    @Autowired
    private ActivitiClientExtensionConfigService clientExtensionConfigService;

    @Autowired
    private ExpressionParser expressionParser;

    @Autowired
    private ObjectProvider<List<IActivitiTaskExecuteListener>> listenersProvider;


    @Override
    public boolean execute(String processDefinitionKey, String activityId, Map<String, Object> acceptanceData) {
        String expression = getExpression(processDefinitionKey, activityId);

        try {
            onExecuteBefore(processDefinitionKey, activityId, acceptanceData);
            Object returnObj = expressionParser.parse(acceptanceData, expression, Object.class);
            onExecuteAfter(processDefinitionKey, activityId, acceptanceData, returnObj);
        } catch (Throwable e) {
            log.error("processDefinitionKey: {}, activityId: {}, execute serviceTask, error: ", processDefinitionKey, activityId, e);
            onExecuteException(processDefinitionKey, activityId, acceptanceData, e);
            return false;
        }
        return true;
    }

    private void onExecuteBefore(String processDefinitionKey, String activityId, Map<String, Object> acceptanceData) {
        listenersProvider.ifAvailable(listeners -> listeners.forEach(l -> l.onExecuteBefore(processDefinitionKey, activityId, acceptanceData)));
    }

    private void onExecuteAfter(String processDefinitionKey, String activityId, Map<String, Object> acceptanceData, Object returnObj) {
        listenersProvider.ifAvailable(listeners -> listeners.forEach(l -> l.onExecuteAfter(processDefinitionKey, activityId, acceptanceData, returnObj)));
    }

    private void onExecuteException(String processDefinitionKey, String activityId, Map<String, Object> acceptanceData, Throwable e) {
        listenersProvider.ifAvailable(listeners -> listeners.forEach(l -> l.onExecuteException(processDefinitionKey, activityId, acceptanceData, e)));
    }

    private String getExpression(String processDefinitionKey, String activityId) {
        ServiceTaskConfigRequest request = new ServiceTaskConfigRequest(processDefinitionKey, activityId, null);
        ServiceTaskConfigResponse response = clientExtensionConfigService.queryServiceTaskConfig(request);

        if (response.getMetadata() == null) {
            throw new ActivitiException("processDefinitionKey['" + processDefinitionKey + "'], activityId['" + activityId + "'], ServiceTaskConfigEntity is null");
        }

        String expression = (String) response.getMetadata().get(ExpressionParser.EXPRESSION);
        if (StringUtils.isBlank(expression)) {
            throw new ActivitiException("processDefinitionKey['" + processDefinitionKey + "'], activityId['" + activityId + "'], the property['metadata.expression'] of ServiceTaskConfigEntity is null");
        }

        return expression;
    }
}

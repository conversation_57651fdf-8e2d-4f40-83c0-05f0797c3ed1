package com.cairh.cpe.businflow.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.activiti.spring.boot.ProcessEngineConfigurationConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

@Configuration
public class FlowConfiguration {

    @Bean
    public ProcessEngineConfigurationConfigurer h2DataSourceProcessEngineConfigurationConfigurer() {
        return configuration -> {
            HikariConfig hikariConfig = new HikariConfig();
            hikariConfig.setJdbcUrl("jdbc:h2:mem:CRH_ACTIVITI;DB_CLOSE_DELAY=1000");
            hikariConfig.setDriverClassName(org.h2.Driver.class.getName());

            DataSource dataSource = new HikariDataSource(hikariConfig);
            configuration.setDataSource(dataSource);

            PlatformTransactionManager platformTransactionManager = new DataSourceTransactionManager(dataSource);
            configuration.setTransactionManager(platformTransactionManager);
        };
    }
}

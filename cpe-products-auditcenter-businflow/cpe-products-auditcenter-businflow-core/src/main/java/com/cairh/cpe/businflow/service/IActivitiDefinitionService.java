package com.cairh.cpe.businflow.service;

import com.cairh.cpe.activiti.api.dto.resp.ProcessDefinitionResponse;
import com.cairh.cpe.activiti.common.entity.ActivityConditionConfigEntity;
import com.cairh.cpe.activiti.common.entity.ProcessActivityDefinition;
import com.cairh.cpe.activiti.common.entity.ProcessDefinitionConfigEntity;
import com.cairh.cpe.activiti.common.entity.ReceiveTaskConfigEntity;
import com.cairh.cpe.activiti.common.entity.ServiceTaskConfigEntity;
import com.cairh.cpe.activiti.common.entity.UserTaskConfigEntity;
import com.cairh.cpe.businflow.model.ActivityInfo;
import com.cairh.cpe.businflow.model.StartProcessForm;

import java.util.List;
import java.util.Map;

public interface IActivitiDefinitionService {

    Map<String, Object> prepareProcessConfig(StartProcessForm startProcessForm);

    ProcessDefinitionConfigEntity prepareProcessDefinitionConfig(String securityAlias, String appId, String businType);

    List<ReceiveTaskConfigEntity> prepareReceiveTaskConfig(String processDefinitionKey, ProcessDefinitionResponse processDefinitionResponse);

    List<ServiceTaskConfigEntity> prepareServiceTaskConfig(String processDefinitionKey, ProcessDefinitionResponse processDefinitionResponse);

    List<UserTaskConfigEntity> prepareUserTaskConfig(String processDefinitionKey, ProcessDefinitionResponse processDefinitionResponse);

    List<ActivityConditionConfigEntity> prepareActivityConditionConfig(String processDefinitionKey, ProcessDefinitionResponse processDefinitionResponse);

    String prepareBoundaryEventConfig(String processDefinitionKey, ProcessDefinitionResponse processDefinitionResponse);

    String prepareUrlIfReceiveTask(String processDefinitionKey, ProcessActivityDefinition activityDefinition);

    ProcessDefinitionResponse getProcessDefinitionByKey(String processDefinitionKey);

    ActivityInfo nextStep(StartProcessForm processForm);

    ActivityInfo lastStep(StartProcessForm processForm);

    ActivityInfo currentStep(StartProcessForm processForm);
}

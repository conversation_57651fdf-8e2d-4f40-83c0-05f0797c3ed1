package com.cairh.cpe.businflow.service.impl;

import com.cairh.cpe.activiti.client.extension.ActivitiClientExtensionConfigService;
import com.cairh.cpe.activiti.client.extension.dto.req.ActivityConditionConfigRequest;
import com.cairh.cpe.activiti.client.extension.dto.resp.ActivityConditionConfigResponse;
import com.cairh.cpe.activiti.common.exception.ActivitiException;
import com.cairh.cpe.context.expression.ExpressionParser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cairh.cpe.businflow.service.IActivitiConditionService;

import java.util.Map;

@Service
public class ActivitiConditionServiceImpl implements IActivitiConditionService {

    @Autowired
    private ActivitiClientExtensionConfigService clientExtensionConfigService;

    @Autowired
    private ExpressionParser expressionParser;


    @Override
    public boolean evaluate(String processDefinitionKey, String fromActivityId, String toActivityId, Map<String, Object> params) {
        String expression = getExpression(processDefinitionKey, fromActivityId, toActivityId);
        return expressionParser.parse(params, expression, Boolean.class);
    }

    private String getExpression(String processDefinitionKey, String fromActivityId, String toActivityId) {
        ActivityConditionConfigRequest request = new ActivityConditionConfigRequest(processDefinitionKey, fromActivityId, toActivityId, null);
        ActivityConditionConfigResponse response = clientExtensionConfigService.queryActivityConditionConfig(request);
        if (response == null) {
            throw new ActivitiException("fromActivityId: '" + fromActivityId + "', toActivityId: '" + toActivityId + "', the ActivityConditionConfigEntity doesn't exist");
        }

        String expression = (String) response.getMetadata().get(ExpressionParser.EXPRESSION);
        if (StringUtils.isBlank(expression)) {
            throw new ActivitiException("fromActivityId: '" + fromActivityId + "', toActivityId: '" + toActivityId + "'," + " the property 'metadata.expression' is required in ActivityConditionConfigEntity");
        }

        return expression;
    }
}

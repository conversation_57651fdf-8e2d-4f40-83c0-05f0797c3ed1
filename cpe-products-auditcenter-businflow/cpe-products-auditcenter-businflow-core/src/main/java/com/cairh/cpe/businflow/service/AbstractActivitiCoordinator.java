package com.cairh.cpe.businflow.service;

import com.alibaba.fastjson.JSON;
import com.cairh.cpe.activiti.api.dto.resp.ProcessDefinitionResponse;
import com.cairh.cpe.activiti.client.extension.ActivitiClientExtensionConfigService;
import com.cairh.cpe.activiti.client.extension.dto.req.ActivityConditionConfigRequest;
import com.cairh.cpe.activiti.client.extension.dto.req.ProcessDefinitionConfigRequest;
import com.cairh.cpe.activiti.client.extension.dto.req.ReceiveTaskConfigRequest;
import com.cairh.cpe.activiti.client.extension.dto.req.ServiceTaskConfigRequest;
import com.cairh.cpe.activiti.client.extension.dto.req.UserTaskConfigRequest;
import com.cairh.cpe.activiti.client.extension.dto.resp.ActivityConditionConfigResponse;
import com.cairh.cpe.activiti.client.extension.dto.resp.ProcessDefinitionConfigResponse;
import com.cairh.cpe.activiti.client.extension.dto.resp.ReceiveTaskConfigResponse;
import com.cairh.cpe.activiti.client.extension.dto.resp.ServiceTaskConfigResponse;
import com.cairh.cpe.activiti.client.extension.dto.resp.UserTaskConfigResponse;
import com.cairh.cpe.activiti.common.entity.ActivityConditionConfigEntity;
import com.cairh.cpe.activiti.common.entity.ProcessActivityDefinition;
import com.cairh.cpe.activiti.common.entity.ProcessDefinitionConfigEntity;
import com.cairh.cpe.activiti.common.entity.ProcessSequenceFlowDefinition;
import com.cairh.cpe.activiti.common.entity.ReceiveTaskConfigEntity;
import com.cairh.cpe.activiti.common.entity.ServiceTaskConfigEntity;
import com.cairh.cpe.activiti.common.entity.UserTaskConfigEntity;
import com.cairh.cpe.activiti.common.exception.ActivitiException;
import com.cairh.cpe.businflow.model.StartProcessForm;
import com.cairh.cpe.businflow.service.IActivitiDefinitionService;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.cairh.cpe.activiti.common.constants.ActivitiConfigConstants.ACTIVITY_CONDITION_CONFIG_ENTITY_KEY;
import static com.cairh.cpe.activiti.common.constants.ActivitiConfigConstants.BOUNDARY_EVENT_ENDDATE_KEY;
import static com.cairh.cpe.activiti.common.constants.ActivitiConfigConstants.PROCESS_DEFINITION_CONFIG_ENTITY_KEY;
import static com.cairh.cpe.activiti.common.constants.ActivitiConfigConstants.PROCESS_DEFINITION_KEY;
import static com.cairh.cpe.activiti.common.constants.ActivitiConfigConstants.RECEIVE_TASK_CONFIG_ENTITY_KEY;
import static com.cairh.cpe.activiti.common.constants.ActivitiConfigConstants.SERVICE_TASK_CONFIG_ENTITY_KEY;
import static com.cairh.cpe.activiti.common.constants.ActivitiConfigConstants.USER_TASK_CONFIG_ENTITY_KEY;
import static com.cairh.cpe.activiti.common.enums.ActivityType.RECEIVE_TASK;
import static com.cairh.cpe.activiti.common.enums.ActivityTypeHelper.isEmptyTask;
import static com.cairh.cpe.activiti.common.enums.ActivityTypeHelper.isReceiveTask;
import static com.cairh.cpe.activiti.common.enums.ActivityTypeHelper.isServiceTask;
import static com.cairh.cpe.activiti.common.enums.ActivityTypeHelper.isUserTask;

public abstract class AbstractActivitiCoordinator implements IActivitiDefinitionService {

    @Autowired
    private ActivitiClientExtensionConfigService clientExtensionConfigService;


    public Map<String, Object> prepareProcessConfig(StartProcessForm startProcessForm) {
        Map<String, Object> variables = startProcessForm.getVariables();

        if (!variables.containsKey(PROCESS_DEFINITION_KEY)) {
            ProcessDefinitionConfigEntity processDefinitionConfigEntity = prepareProcessDefinitionConfig(startProcessForm.getSecurityAlias(), startProcessForm.getAppId(), startProcessForm.getBusinType());
            variables.put(PROCESS_DEFINITION_KEY, processDefinitionConfigEntity.getProcessDefinitionKey());
            variables.put(PROCESS_DEFINITION_CONFIG_ENTITY_KEY, JSON.toJSONString(Collections.singletonList(processDefinitionConfigEntity)));
        }

        String processDefinitionKey = (String) variables.get(PROCESS_DEFINITION_KEY);
        ProcessDefinitionResponse processDefinitionResponse = getProcessDefinitionByKey(processDefinitionKey);

        if (!variables.containsKey(RECEIVE_TASK_CONFIG_ENTITY_KEY)) {
            List<ReceiveTaskConfigEntity> receiveTaskConfigEntityList = prepareReceiveTaskConfig(processDefinitionKey, processDefinitionResponse);
            variables.put(RECEIVE_TASK_CONFIG_ENTITY_KEY, JSON.toJSONString(receiveTaskConfigEntityList));
        }

        if (!variables.containsKey(SERVICE_TASK_CONFIG_ENTITY_KEY)) {
            List<ServiceTaskConfigEntity> serviceTaskConfigEntityList = prepareServiceTaskConfig(processDefinitionKey, processDefinitionResponse);
            variables.put(SERVICE_TASK_CONFIG_ENTITY_KEY, JSON.toJSONString(serviceTaskConfigEntityList));
        }

        if (!variables.containsKey(USER_TASK_CONFIG_ENTITY_KEY)) {
            List<UserTaskConfigEntity> userTaskConfigEntityList = prepareUserTaskConfig(processDefinitionKey, processDefinitionResponse);
            variables.put(USER_TASK_CONFIG_ENTITY_KEY, JSON.toJSONString(userTaskConfigEntityList));
        }

        if (!variables.containsKey(ACTIVITY_CONDITION_CONFIG_ENTITY_KEY)) {
            List<ActivityConditionConfigEntity> activityConditionConfigEntityList = prepareActivityConditionConfig(processDefinitionKey, processDefinitionResponse);
            variables.put(ACTIVITY_CONDITION_CONFIG_ENTITY_KEY, JSON.toJSONString(activityConditionConfigEntityList));
        }

        if (!variables.containsKey(BOUNDARY_EVENT_ENDDATE_KEY)) {
            String endDate = prepareBoundaryEventConfig(processDefinitionKey, processDefinitionResponse);
            variables.put(BOUNDARY_EVENT_ENDDATE_KEY, endDate);
        }

        return variables;
    }

    public ProcessDefinitionConfigEntity prepareProcessDefinitionConfig(String securityAlias, String appId, String businType) {
        ProcessDefinitionConfigRequest processDefinitionConfigRequest = new ProcessDefinitionConfigRequest(businType, appId, securityAlias);
        ProcessDefinitionConfigResponse processDefinitionConfigResponse = clientExtensionConfigService.queryProcessDefinitionConfig(processDefinitionConfigRequest);

        ProcessDefinitionConfigEntity processDefinitionConfigEntity = new ProcessDefinitionConfigEntity();
        processDefinitionConfigEntity.setProcessDefinitionKey(processDefinitionConfigResponse.getProcessDefinitionKey());
        processDefinitionConfigEntity.setAppId(appId);
        processDefinitionConfigEntity.setBusinType(businType);
        processDefinitionConfigEntity.setSecurityAlias(securityAlias);
        processDefinitionConfigEntity.setMetadata(processDefinitionConfigResponse.getMetadata());

        return processDefinitionConfigEntity;
    }


    public List<ServiceTaskConfigEntity> prepareServiceTaskConfig(String processDefinitionKey, ProcessDefinitionResponse processDefinitionResponse) {
        List<ServiceTaskConfigEntity> serviceTaskConfigEntityList = new ArrayList<>();
        processDefinitionResponse.getActivityDefinitionMap().forEach((__, activityDefinition) -> {
            if (isServiceTask(activityDefinition)) {
                ServiceTaskConfigRequest request = new ServiceTaskConfigRequest(processDefinitionKey, activityDefinition.getActivityId(), null);
                ServiceTaskConfigResponse response = clientExtensionConfigService.queryServiceTaskConfig(request);
                ServiceTaskConfigEntity serviceTaskConfigEntity = new ServiceTaskConfigEntity(activityDefinition.getActivityId(), response.getUrl(), response.getMetadata());

                serviceTaskConfigEntityList.add(serviceTaskConfigEntity);
            }
        });

        return serviceTaskConfigEntityList;
    }

    public List<UserTaskConfigEntity> prepareUserTaskConfig(String processDefinitionKey, ProcessDefinitionResponse processDefinitionResponse) {
        List<UserTaskConfigEntity> userTaskConfigEntityList = new ArrayList<>();
        processDefinitionResponse.getActivityDefinitionMap().forEach((__, activityDefinition) -> {
            if (isUserTask(activityDefinition)) {
                UserTaskConfigRequest request = new UserTaskConfigRequest(processDefinitionKey, activityDefinition.getActivityId());
                UserTaskConfigResponse response = clientExtensionConfigService.queryUserTaskConfig(request);
                if (response != null) {
                    UserTaskConfigEntity userTaskConfigEntity = new UserTaskConfigEntity(activityDefinition.getActivityId(), response.getRoles(), response.getUsers(), response.getMetadata());

                    userTaskConfigEntityList.add(userTaskConfigEntity);
                }
            }
        });

        return userTaskConfigEntityList;
    }

    public List<ReceiveTaskConfigEntity> prepareReceiveTaskConfig(String processDefinitionKey, ProcessDefinitionResponse processDefinitionResponse) {
        List<ReceiveTaskConfigEntity> receiveTaskConfigEntityList = new ArrayList<>();
        processDefinitionResponse.getActivityDefinitionMap().forEach((__, activityDefinition) -> {
            if (isReceiveTask(activityDefinition) && !isEmptyTask(activityDefinition)) {
                ReceiveTaskConfigRequest request = new ReceiveTaskConfigRequest(processDefinitionKey, activityDefinition.getActivityId(), null);
                ReceiveTaskConfigResponse response = clientExtensionConfigService.queryReceiveTaskConfig(request);
                ReceiveTaskConfigEntity receiveTaskConfigEntity = new ReceiveTaskConfigEntity(activityDefinition.getActivityId(), response.getUrl(), response.getMetadata());

                receiveTaskConfigEntityList.add(receiveTaskConfigEntity);
            }
        });

        return receiveTaskConfigEntityList;
    }

    public List<ActivityConditionConfigEntity> prepareActivityConditionConfig(String processDefinitionKey, ProcessDefinitionResponse processDefinitionResponse) {
        List<ActivityConditionConfigEntity> activityConditionConfigEntityList = new ArrayList<>();

        processDefinitionResponse.getActivityDefinitionMap().forEach((__, activityDefinition) -> {
            if (activityDefinition.getOutgoingFlowIds().size() > 1) {
                String defaultOutgoingFlowId = activityDefinition.getDefaultOutgoingFlowId();

                for (String outgoingFlowId : activityDefinition.getOutgoingFlowIds()) {
                    if (Objects.equals(defaultOutgoingFlowId, outgoingFlowId)) {
                        continue;
                    }

                    ProcessSequenceFlowDefinition sequenceFlowDefinition = processDefinitionResponse.getSequenceFlowDefinitionMap().get(outgoingFlowId);
                    String fromActivityId = sequenceFlowDefinition.getSourceActivityId();
                    String toActivityId = sequenceFlowDefinition.getTargetActivityId();

                    ActivityConditionConfigRequest request = new ActivityConditionConfigRequest(processDefinitionKey, fromActivityId, toActivityId, null);
                    ActivityConditionConfigResponse response = clientExtensionConfigService.queryActivityConditionConfig(request);
                    if (response == null) {
                        throw new ActivitiException(
                                "processDefinitionKey['" + processDefinitionKey + "'], fromActivityId['" + fromActivityId + "'], toActivityId['" + toActivityId + "'] not configure activiityCondition");
                    }
                    ActivityConditionConfigEntity config = new ActivityConditionConfigEntity(fromActivityId, toActivityId, response.getUrl(), response.getMetadata());
                    activityConditionConfigEntityList.add(config);
                }
            }
        });

        return activityConditionConfigEntityList;
    }

    public String prepareBoundaryEventConfig(String processDefinitionKey, ProcessDefinitionResponse processDefinitionResponse) {
        DateTimeFormatter fmt = ISODateTimeFormat.dateTime();
        return fmt.print(System.currentTimeMillis() + 1000 * 60 * 60 * 24 * 3);
    }

    public String prepareUrlIfReceiveTask(String processDefinitionKey, ProcessActivityDefinition activityDefinition) {
        if (isEmptyTask(activityDefinition)) {
            return null;
        }

        String url = null;
        if (RECEIVE_TASK.equals(activityDefinition.getActivityType())) {
            ReceiveTaskConfigRequest receiveTaskConfigRequest = new ReceiveTaskConfigRequest(processDefinitionKey, activityDefinition.getActivityId(), null);
            ReceiveTaskConfigResponse receiveTaskConfigResponse = clientExtensionConfigService.queryReceiveTaskConfig(receiveTaskConfigRequest);
            url = receiveTaskConfigResponse.getUrl();
        }
        return url;
    }
}

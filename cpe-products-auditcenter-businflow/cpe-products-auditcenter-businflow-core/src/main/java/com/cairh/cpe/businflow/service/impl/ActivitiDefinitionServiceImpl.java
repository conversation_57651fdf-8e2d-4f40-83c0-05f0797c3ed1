package com.cairh.cpe.businflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cairh.cpe.activiti.api.ActivitiAdminService;
import com.cairh.cpe.activiti.api.dto.req.ProcessDefinitionForm;
import com.cairh.cpe.activiti.api.dto.resp.ProcessDefinitionResponse;
import com.cairh.cpe.activiti.api.dto.resp.ProcessDefinitionResult;
import com.cairh.cpe.activiti.common.entity.ProcessActivityDefinition;
import com.cairh.cpe.activiti.common.entity.ProcessDefinitionConfigEntity;
import com.cairh.cpe.activiti.common.entity.ProcessSequenceFlowDefinition;
import com.cairh.cpe.activiti.common.exception.ActivitiException;
import com.cairh.cpe.businflow.model.ActivityInfo;
import com.cairh.cpe.businflow.model.StartProcessForm;
import com.cairh.cpe.businflow.service.AbstractActivitiCoordinator;
import com.cairh.cpe.businflow.service.IActivitiConditionService;
import com.cairh.cpe.businflow.service.IActivitiTaskService;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.context.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.cairh.cpe.activiti.common.enums.ActivityTypeHelper.isReceiveTask;
import static com.cairh.cpe.activiti.common.enums.ActivityTypeHelper.isServiceTask;
import static com.cairh.cpe.activiti.common.enums.ActivityTypeHelper.isStartEvent;
import static com.cairh.cpe.activiti.common.enums.ActivityTypeHelper.isUserTask;

@Service
public class ActivitiDefinitionServiceImpl extends AbstractActivitiCoordinator {

    @Autowired
    private ActivitiAdminService activitiAdminService;

    @Autowired
    private IActivitiTaskService activitiServiceTaskService;

    @Autowired
    private IActivitiConditionService activitiConditionService;


    @Override
    public ActivityInfo nextStep(StartProcessForm processForm) {
        ProcessDefinitionConfigEntity processDefinitionConfigEntity = prepareProcessDefinitionConfig(processForm.getSecurityAlias(), processForm.getAppId(), processForm.getBusinType());
        ProcessDefinitionResponse processDefinition = getProcessDefinitionByKey(processDefinitionConfigEntity.getProcessDefinitionKey());
        String activityId = StringUtils.isBlank(processForm.getActivityId()) ? FlowNodeConst.START : processForm.getActivityId();
        ProcessActivityDefinition activityDefinition = doNextByBehavior(activityId, processDefinition, processForm.getVariables());

        ActivityInfo result = new ActivityInfo();
        if (activityDefinition != null) {
            result.setActivityId(activityDefinition.getActivityId());
            result.setActivityName(activityDefinition.getActivityName());
            result.setActivityType(activityDefinition.getActivityType());
            result.setUrl(prepareUrlIfReceiveTask(processDefinition.getProcessDefinitionKey(), activityDefinition));
        }

        return result;
    }

    @Override
    public ActivityInfo lastStep(StartProcessForm processForm) {
        ProcessDefinitionConfigEntity processDefinitionConfigEntity = prepareProcessDefinitionConfig(processForm.getSecurityAlias(), processForm.getAppId(), processForm.getBusinType());
        ProcessDefinitionResponse processDefinition = getProcessDefinitionByKey(processDefinitionConfigEntity.getProcessDefinitionKey());
        String activityId = StringUtils.isBlank(processForm.getActivityId()) ? FlowNodeConst.START : processForm.getActivityId();

        ProcessActivityDefinition activityDefinition = getLastActivityDefinition(activityId, processDefinition, processForm.getVariables());

        ActivityInfo result = new ActivityInfo();
        result.setActivityId(activityDefinition.getActivityId());
        result.setActivityName(activityDefinition.getActivityName());
        result.setActivityType(activityDefinition.getActivityType());
        result.setUrl(prepareUrlIfReceiveTask(processDefinition.getProcessDefinitionKey(), activityDefinition));
        return result;
    }

    @Override
    public ActivityInfo currentStep(StartProcessForm processForm) {
        ProcessDefinitionConfigEntity processDefinitionConfigEntity = prepareProcessDefinitionConfig(processForm.getSecurityAlias(), processForm.getAppId(), processForm.getBusinType());
        ProcessDefinitionResponse processDefinition = getProcessDefinitionByKey(processDefinitionConfigEntity.getProcessDefinitionKey());
        ProcessActivityDefinition activityDefinition = processDefinition.getActivityDefinitionMap().get(processForm.getActivityId());
        ActivityInfo result = new ActivityInfo();
        result.setActivityId(activityDefinition.getActivityId());
        result.setActivityName(activityDefinition.getActivityName());
        result.setActivityType(activityDefinition.getActivityType());
        result.setUrl(prepareUrlIfReceiveTask(processDefinition.getProcessDefinitionKey(), activityDefinition));
        return result;
    }

    @Override
    public ProcessDefinitionResponse getProcessDefinitionByKey(String processDefinitionKey) {
        ProcessDefinitionResult processDefinitionResult = activitiAdminService.getProcessDefinition(new ProcessDefinitionForm(processDefinitionKey, null));
        ProcessDefinitionResponse response = new ProcessDefinitionResponse();
        response.setActivityDefinitionMap(processDefinitionResult.getActivityDefinitionMap());
        response.setSequenceFlowDefinitionMap(processDefinitionResult.getSequenceFlowDefinitionMap());
        response.setProcessDefinitionKey(processDefinitionResult.getProcessDefinitionKey());
        return response;
    }


    private ProcessActivityDefinition doNextByBehavior(String activityId, ProcessDefinitionResponse processDefinition, Map<String, Object> params) {
        ProcessActivityDefinition activityDefinition = processDefinition.getActivityDefinitionMap().get(activityId);

        if (activityDefinition == null) {
            throw new BizException("流程定义查找不到");
        }

        if (isStartEvent(activityDefinition) || isReceiveTask(activityDefinition) || isUserTask(activityDefinition)) {
            activityDefinition = getNextActivityDefinition(activityDefinition.getActivityId(), processDefinition, params);
            return doNextIfServiceTask(activityDefinition.getActivityId(), processDefinition, params);
        }

        if (isServiceTask(activityDefinition)) {
            return doNextIfServiceTask(activityDefinition.getActivityId(), processDefinition, params);
        }

        throw new ActivitiException("the behavior is not expected");
    }

    private ProcessActivityDefinition doNextIfServiceTask(String activityId, ProcessDefinitionResponse processDefinition, Map<String, Object> params) {
        ProcessActivityDefinition activityDefinition = processDefinition.getActivityDefinitionMap().get(activityId);
        while (isServiceTask(activityDefinition)) {
            if (!activitiServiceTaskService.execute(processDefinition.getProcessDefinitionKey(), activityDefinition.getActivityId(), params)) {
                break;
            }
            activityDefinition = getNextActivityDefinition(activityDefinition.getActivityId(), processDefinition, params);
        }
        return activityDefinition;
    }

    private ProcessActivityDefinition getNextActivityDefinition(String activityId, ProcessDefinitionResponse processDefinition, Map<String, Object> params) {
        Map<String, ProcessActivityDefinition> activityDefinitionMap = processDefinition.getActivityDefinitionMap();
        Map<String, ProcessSequenceFlowDefinition> sequenceFlowDefinitionMap = processDefinition.getSequenceFlowDefinitionMap();

        ProcessActivityDefinition activityDefinition = activityDefinitionMap.get(activityId);
        String targetActivityId = null;

        if (activityDefinition.getOutgoingFlowIds().size() > 1) {
            String defaultOutgoingFlowId = activityDefinition.getDefaultOutgoingFlowId();

            for (String outgoingFlowId : activityDefinition.getOutgoingFlowIds()) {
                if (!StringUtils.equals(outgoingFlowId, defaultOutgoingFlowId)) {
                    String fromActivityId = activityId;
                    String toActivityId = sequenceFlowDefinitionMap.get(outgoingFlowId).getTargetActivityId();
                    if (activitiConditionService.evaluate(processDefinition.getProcessDefinitionKey(), fromActivityId, toActivityId, params)) {
                        targetActivityId = toActivityId;
                        break;
                    }
                }
            }

            if (targetActivityId == null && defaultOutgoingFlowId != null) {
                targetActivityId = sequenceFlowDefinitionMap.get(defaultOutgoingFlowId).getTargetActivityId();
            }
        } else {
            targetActivityId = sequenceFlowDefinitionMap.get(activityDefinition.getOutgoingFlowIds().get(0)).getTargetActivityId();
        }

        if (targetActivityId == null) {
            throw new ActivitiException("all condition expression is evaluated to 'false' or the default flow doesn't exist ");
        }

        return activityDefinitionMap.get(targetActivityId);
    }

    private ProcessActivityDefinition getLastActivityDefinition(String activityId, ProcessDefinitionResponse processDefinition, Map<String, Object> params) {
        Map<String, ProcessActivityDefinition> activityDefinitionMap = processDefinition.getActivityDefinitionMap();
        Map<String, ProcessSequenceFlowDefinition> sequenceFlowDefinitionMap = processDefinition.getSequenceFlowDefinitionMap();
        ProcessActivityDefinition activityDefinition = activityDefinitionMap.get(activityId);

        if (CollectionUtil.isNotEmpty(activityDefinition.getIncomingFlowIds())) {
            String targetActivityId = null;
            if (activityDefinition.getIncomingFlowIds().size() > 1) {
                for (String incomingFlowId : activityDefinition.getIncomingFlowIds()) {
                    String fromActivityId = sequenceFlowDefinitionMap.get(incomingFlowId).getSourceActivityId();
                    String toActivityId = activityId;
                    if (activitiConditionService.evaluate(processDefinition.getProcessDefinitionKey(), fromActivityId, toActivityId, params)) {
                        targetActivityId = fromActivityId;
                        break;
                    }
                }
            } else {
                targetActivityId = sequenceFlowDefinitionMap.get(activityDefinition.getIncomingFlowIds().get(0)).getSourceActivityId();
            }
            return activityDefinitionMap.get(targetActivityId);
        } else {
            throw new ActivitiException("flow doesn't exist");
        }
    }
}

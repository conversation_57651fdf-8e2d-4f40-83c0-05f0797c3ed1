package com.cairh.cpe.businflow.service;

import java.util.Map;

public interface IActivitiTaskExecuteListener {

    void onExecuteBefore(String processDefinitionKey, String activityId, Map<String, Object> acceptanceData);

    void onExecuteAfter(String processDefinitionKey, String activityId, Map<String, Object> acceptanceData, Object returnObj);

    void onExecuteException(String processDefinitionKey, String activityId, Map<String, Object> acceptanceData, Throwable e);
}

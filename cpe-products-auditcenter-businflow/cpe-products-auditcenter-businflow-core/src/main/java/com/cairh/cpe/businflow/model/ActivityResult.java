package com.cairh.cpe.businflow.model;

import com.cairh.cpe.activiti.common.entity.ProcessActivityDefinition;
import com.cairh.cpe.activiti.common.enums.ActivityType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class ActivityResult {

    protected String activityId;

    protected String activityName;

    protected ActivityType activityType;


    @JsonIgnore
    public ProcessActivityDefinition buildActivityDefinition() {
        return new ProcessActivityDefinition(activityId, activityName, activityType);
    }
}

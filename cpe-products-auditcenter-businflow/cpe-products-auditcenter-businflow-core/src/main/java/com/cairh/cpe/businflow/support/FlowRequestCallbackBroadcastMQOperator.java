package com.cairh.cpe.businflow.support;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.net.NetUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.cairh.cpe.businflow.support.FlowRequestCallbackBroadcastMQOperator.CallbackMsg;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.response.AuditResultResp;
import com.cairh.cpe.mq.operator.AbstractBroadcastMQOperator;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
public class FlowRequestCallbackBroadcastMQOperator extends AbstractBroadcastMQOperator<CallbackMsg> {

    @Getter
    private final String destinationName = WskhConstant.AC_KAFKA_TOPIC;

    @Getter
    @Setter
    public static class CallbackMsg {

        @JSONField(name = "SRC")
        private String SRC = "27";
        @JSONField(name = "SRC_IP")
        private String SRC_IP = NetUtil.getLocalhostStr();
        @JSONField(name = "MSG_TYPE")
        private String MSG_TYPE = "AUDIT_CALLBACK";
        @JSONField(name = "TARGET")
        private List<String> TARGET = Collections.singletonList("27");
        @JSONField(name = "SEND_DATE")
        private Integer SEND_DATE;
        @JSONField(name = "SEND_TIME")
        private Integer SEND_TIME;
        @JSONField(name = "MSG_ID")
        private String MSG_ID = UUID.randomUUID(true).toString();
        @JSONField(name = "CONTENT")
        private MsgContent CONTENT;

        @Getter
        @Setter
        public static class MsgContent {

            // 业务编号
            @JSONField(name = "REQUEST_NO")
            private String REQUEST_NO;
            // 审核结果
            @JSONField(name = "AUDIT_RESULT")
            private AuditResultResp AUDIT_RESULT;
            // 任务节点
            @JSONField(name = "ANODE_ID")
            private String ANODE_ID;
            //任务id
            @JSONField(name = "FLOWTASK_ID")
            private String FLOWTASK_ID;
            //单笔提交任务id
            @JSONField(name = "TASK_ID")
            private String TASK_ID;

        }

    }
}

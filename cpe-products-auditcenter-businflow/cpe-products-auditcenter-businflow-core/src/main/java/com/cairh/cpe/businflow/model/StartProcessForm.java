package com.cairh.cpe.businflow.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.MapUtils;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StartProcessForm {

    private String securityAlias;

    private String appId;

    private String businType;

    private String activityId;

    private Map<String, Object> variables;


    public Map<String, Object> getVariables() {
        if (MapUtils.isEmpty(variables)) {
            variables = new LinkedHashMap<>();
        }
        return variables;
    }
}

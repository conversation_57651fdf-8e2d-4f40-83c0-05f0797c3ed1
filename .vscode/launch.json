{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "AuditCenterBackendApplication",
            "request": "launch",
            "mainClass": "com.cairh.cpe.backend.AuditCenterBackendApplication",
            "projectName": "cpe-products-auditcenter-backend"
        },
        {
            "type": "java",
            "name": "DateUtils",
            "request": "launch",
            "mainClass": "com.cairh.cpe.common.util.DateUtils",
            "projectName": "cpe-products-auditcenter-common"
        },
        {
            "type": "java",
            "name": "KHDateUtil",
            "request": "launch",
            "mainClass": "com.cairh.cpe.common.util.KHDateUtil",
            "projectName": "cpe-products-auditcenter-common"
        },
        {
            "type": "java",
            "name": "AuditCenterTaskApplication",
            "request": "launch",
            "mainClass": "com.cairh.cpe.task.AuditCenterTaskApplication",
            "projectName": "cpe-products-auditcenter-task"
        }
    ]
}
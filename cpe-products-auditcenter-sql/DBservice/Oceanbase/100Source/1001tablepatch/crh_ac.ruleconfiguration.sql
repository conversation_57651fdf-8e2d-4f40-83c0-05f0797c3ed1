create table crh_ac.ruleconfiguration
(
    serial_id    varchar2(32)  default ' ' not null,
    field_code   varchar2(64)  default ' ' not null,
    field_name   varchar2(255) default ' ' not null,
    field_type   varchar2(32)  default ' ' not null,
    expression   varchar2(255) default ' ' not null,
    value_type   varchar2(32)  default ' ' not null,
    value_source varchar2(255) default ' ' not null,
    source_value varchar2(255) default ' ' not null,
    order_no     number(18)    default 0   not null
);
create unique index crh_ac.idx_ruleconfig_serialid on crh_ac.ruleconfiguration (serial_id);
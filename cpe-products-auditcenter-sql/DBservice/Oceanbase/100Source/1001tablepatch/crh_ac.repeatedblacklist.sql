create table CRH_AC.REPEATEDBLACKLIST
(
    SERIAL_ID       VARCHAR2(50)              not null,
    CONTENT         VARCHAR2(255) default ' ' not null,
    BLA<PERSON>K_STATUS    CHAR          default '1' not null,
    CREATE_BY       VARCHAR2(50)  default ' ' not null,
    MODIFY_BY       VARCHAR2(50)  default ' ' not null,
    CREATE_DATETIME DATE                      not null,
    MODIFY_DATETIME DATE                      not null
);

CREATE INDEX CRH_AC.IDX_BLACK_SERIAL ON CRH_AC.REPEATEDBLACKLIST (SERIAL_ID);
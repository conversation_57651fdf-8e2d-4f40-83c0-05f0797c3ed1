create table CRH_AC.BUSINFLOWREQUEST
(
    REQUEST_NO        VARCHAR2(32)  default ' '     not null,
    BUSIN_TYPE        NUMBER(10)    default 0       not null,
    REQUEST_DATETIME  DATE          default sysdate not null,
    SUBMIT_DATETIME   DATE,
    UPDATE_DATETIME   DATE          default sysdate not null,
    TOHIS_FLAG        CHAR          default ' '     not null,
    REQUEST_STATUS    CHAR          default ' '     not null,
    ANODE_ID          VARCHAR2(64)  default ' '     not null,
    OPERATOR_NO       VARCHAR2(18)  default ' '     not null,
    USER_ID           VARCHAR2(32)  default ' '     not null,
    MOBILE_TEL        VARCHAR2(24)  default ' '     not null,
    CLIENT_NAME       VARCHAR2(255) default ' '     not null,
    ID_KIND           CHAR          default ' '     not null,
    ID_NO             VARCHAR2(40)  default ' '     not null,
    BRANCH_NO         VARCHAR2(20)  default ' '     not null,
    CHANNEL_CODE      VARCHAR2(32)  default ' '     not null,
    TOHIS_DATETIME    DATE,
    INFORM            CHAR          default ' ',
    BUSI_SERIAL_NO    VARCHAR2(32)  default ' '     not null,
    BRANCH_REPEATED   CHAR          default '0'     not null,
    ADDRESS_REPEATED  CHAR          default '0'     not null,
    MATCH_LABELS      VARCHAR2(320) default ' '     not null,
    BUSINESS_APPLY_NO VARCHAR2(64)  default ' '     not null
);

CREATE INDEX CRH_AC.IDX_BFR_REQUESTNO ON CRH_AC.BUSINFLOWREQUEST (REQUEST_NO);
CREATE INDEX CRH_AC.IDX_BFR_REQUESTDATE ON CRH_AC.BUSINFLOWREQUEST (REQUEST_DATETIME);
CREATE INDEX CRH_AC.IDX_BFR_SUBMITDATE ON CRH_AC.BUSINFLOWREQUEST (SUBMIT_DATETIME);
CREATE INDEX CRH_AC.IDX_BFR_STATUS ON CRH_AC.BUSINFLOWREQUEST (REQUEST_STATUS);
CREATE INDEX CRH_AC.IDX_BFR_MOBILETEL ON CRH_AC.BUSINFLOWREQUEST (MOBILE_TEL);
CREATE INDEX CRH_AC.IDX_BFR_CLIENTNAME ON CRH_AC.BUSINFLOWREQUEST (CLIENT_NAME);
CREATE INDEX CRH_AC.IDX_BFR_IDNO ON CRH_AC.BUSINFLOWREQUEST (ID_NO);
CREATE INDEX CRH_AC.IDX_BFR_BRANCHNO ON CRH_AC.BUSINFLOWREQUEST (BRANCH_NO);
CREATE INDEX CRH_AC.IDX_BFR_CHANNELCODE ON CRH_AC.BUSINFLOWREQUEST (CHANNEL_CODE);
CREATE INDEX CRH_AC.IDX_BFR_BSN ON CRH_AC.BUSINFLOWREQUEST (BUSI_SERIAL_NO);
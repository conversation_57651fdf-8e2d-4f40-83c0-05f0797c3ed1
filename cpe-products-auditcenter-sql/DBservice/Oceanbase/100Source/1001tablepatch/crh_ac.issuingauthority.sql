create table CRH_AC.ISSUINGAUTHORITY
(
    SERIAL_ID       VARCHAR2(32)   default ' '     not null,
    PROVINCE_NO     VARCHAR2(20)   default ' '     not null,
    AUTHORITY_NAME  VARCHAR2(255)  default ' '     not null,
    ADDRESS_ORAGN   VARCHAR2(255)  default ' '     not null,
    REMARK          VARCHAR2(2000) default ' '     not null,
    STATUS          CHAR           default '8'     not null,
    CREATE_BY       VARCHAR2(18)   default ' '     not null,
    CREATE_DATETIME DATE           default sysdate not null,
    UPDATE_BY       VARCHAR2(18)   default ' '     not null,
    UPDATE_DATETIME DATE           default sysdate not null
);

CREATE INDEX CRH_AC.IDX_ISSUINGAUTHORITY ON CRH_AC.ISSUINGAUTHORITY (SERIAL_ID);
CREATE INDEX CRH_AC.IDX_ISSAUTH_PROVINCE_NO ON CRH_AC.ISSUINGAUTHORITY (PROVINCE_NO);
CREATE INDEX CRH_AC.IDX_ISSAUTH_AUTHORITY_NAME ON CRH_AC.ISSUINGAUTHORITY (AUTHORITY_NAME);
CREATE INDEX CRH_AC.IDX_ISSAUTH_ADDRESS_ORAGN ON CRH_AC.ISSUINGAUTHORITY (ADDRESS_ORAGN);
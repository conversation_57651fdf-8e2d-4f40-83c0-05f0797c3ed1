create table CRH_AC.CALLDETAILS
(
    SERIAL_ID               VARCHAR2(32)  default ' ' not null,
    REQUEST_NO              VARCHAR2(32)  default ' ' not null,
    BIZ_ID                  VARCHAR2(32)  default ' ' not null,
    CALL_ID                 VARCHAR2(100) default ' ' not null,
    BIZ_TYPE                NUMBER(38),
    CALLEE_CREATED_EPOCH    DATE,
    CALLEE_RINGING_EPOCH    DATE,
    CALLEE_ANSWER_EPOCH     DATE,
    CALLEE_HANGUP_EPOCH     DATE,
    RECORDING_FILE_NAME     VARCHAR2(50),
    OUTBOUND_CALLEE_NUMBER  VARCHAR2(50),
    CALLEE_NUMBER           VARCHAR2(150),
    STAFFID                 VARCHAR2(32),
    CUSTOMERID              VARCHAR2(32),
    CALLBIZID               VARCHAR2(50),
    CALLBIZKEY              VARCHAR2(50),
    EXTSTR                  VARCHAR2(200),
    CALL_NUMBER             VARCHAR2(20),
    HANG_UP_SIDE            NUMBER(38),
    IS_ANSWER               NUMBER(38),
    CALL_DURATION           NUMBER(38),
    FINISH_DATETIME         DATE,
    CREATE_DATETIME         DATE,
    STAFF_NAME              VARCHAR2(32)  default ' ' not null,
    DEPT_ID                 VARCHAR2(32)  default ' ' not null,
    MODIFY_DATETIME         DATE                      not null,
    CALL_STATUS             NUMBER(38)    default '0' not null,
    KEYNOTE_FLAG            CHAR(2)       default '0',
    OPERATOR_BRANCH_NO      VARCHAR2(20),
    OPERATOR_BRANCH_NAME    VARCHAR2(200),
    OPERATOR_UP_BRANCH_NO   VARCHAR2(20),
    OPERATOR_UP_BRANCH_NAME VARCHAR2(200),
    CLIENT_BRANCH_NO        VARCHAR2(32)  default ' ' not null,
    BUSIN_TYPE              VARCHAR2(32)  default ' ' not null
);

ALTER TABLE CRH_AC.CALLDETAILS
    ADD CONSTRAINT IDX_CALL_SERIAL_ID PRIMARY KEY (SERIAL_ID);
CREATE INDEX CRH_AC.IDX_CALL_REQUEST_NO ON CRH_AC.CALLDETAILS (REQUEST_NO);
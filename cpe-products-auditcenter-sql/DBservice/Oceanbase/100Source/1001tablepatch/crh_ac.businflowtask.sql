create table CRH_AC.BUSINFLOWTASK
(
    REQUEST_NO         VARCHAR2(32)   default ' '     not null,
    SERIAL_ID          VARCHAR2(32)   default ' '     not null,
    TASK_STATUS        VARCHAR2(2)    default ' '     not null,
    CREATE_DATETIME    DATE           default sysdate not null,
    DEAL_DATETIME      DATE,
    FINISH_DATETIME    DATE,
    TASK_TYPE          VARCHAR2(32)   default ' '     not null,
    OPERATOR_NO        VARCHAR2(18)   default ' '     not null,
    OPERATOR_NAME      VARCHAR2(60)   default ' '     not null,
    OP_CONTENT         VARCHAR2(4000) default ' '     not null,
    VIDEO_TYPE         VARCHAR2(30)   default ' '     not null,
    ALLOW_AUDITOR      VARCHAR2(64)   default ' '     not null,
    NOT_ALLOW_AUDITOR  VARCHAR2(64)   default ' '     not null,
    TOHIS_FLAG         CHAR           default ' '     not null,
    TOHIS_DATETIME     DATE,
    DEAL_NUM           NUMBER(10)     default 0       not null,
    REMARK             VARCHAR2(2000) default ' '     not null,
    PUSH_FLAG          CHAR           default ' '     not null,
    SUSPEND_REMIND_NUM NUMBER(10)     default 0       not null,
    TASK_SOURCE        VARCHAR2(2)    default ' '     not null,
    TASK_ID            VARCHAR2(32)   default ' '     not null,
    WHITE_FLAG         CHAR           default '0'     not null,
    MATCH_LABELS       VARCHAR2(320)  default ' '     not null,
    EFFECTIVE_WORK     CHAR           default '1'     not null,
    BRANCH_REPEATED    CHAR           default '0'     not null,
    ADDRESS_REPEATED   CHAR           default '0'     not null,
    DISPATCH_TASK_ID   VARCHAR2(64)   default ' '     not null,
    CALL_FLAG          CHAR           default '0'     not null
);

CREATE INDEX CRH_AC.IDX_BFTASK_TASKSTATUS ON CRH_AC.BUSINFLOWTASK (TASK_STATUS);
CREATE INDEX CRH_AC.IDX_BFTASK_CREATEDATE ON CRH_AC.BUSINFLOWTASK (CREATE_DATETIME);
CREATE INDEX CRH_AC.IDX_BFTASK_FINISHEDATETIME ON CRH_AC.BUSINFLOWTASK (FINISH_DATETIME);
CREATE INDEX CRH_AC.IDX_BFTASK_TASKTYPE ON CRH_AC.BUSINFLOWTASK (TASK_TYPE);
CREATE INDEX CRH_AC.IDX_BFTASK_OPERATORNO ON CRH_AC.BUSINFLOWTASK (OPERATOR_NO);
CREATE INDEX CRH_AC.IDX_BFTASK_TASKID ON CRH_AC.BUSINFLOWTASK (TASK_ID);
CREATE INDEX CRH_AC.IDX_BUSINFLOWTASK ON CRH_AC.BUSINFLOWTASK (REQUEST_NO, SERIAL_ID);
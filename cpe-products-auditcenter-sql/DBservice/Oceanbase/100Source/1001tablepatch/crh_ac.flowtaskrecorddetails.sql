create table CRH_AC.FLOWTASKRECORDDETAILS
(
    REQUEST_NO              VARCHAR2(32)  default ' '     not null,
    TASK_ID                 VARCHAR2(32)  default ' '     not null,
    SERIAL_ID               VARCHAR2(32)  default ' '     not null,
    TIME_PROPERTY           CHAR          default ' '     not null,
    OPERATOR_NO             VARCHAR2(18)  default ' '     not null,
    OPERATOR_NAME           VARCHAR2(60)  default ' '     not null,
    BRANCH_NO               VARCHAR2(20)  default ' '     not null,
    BRANCH_NAME             VARCHAR2(255) default ' '     not null,
    UP_BRANCH_NO            VARCHAR2(20)  default ' '     not null,
    UP_BRANCH_NAME          VARCHAR2(255) default ' '     not null,
    REQUEST_DATETIME        DATE                          not null,
    TASK_TYPE               VARCHAR2(32)  default ' '     not null,
    TASK_STATUS             VARCHAR2(2)   default ' '     not null,
    CREATE_DATETIME         DATE          default sysdate not null,
    DEAL_DATETIME           DATE,
    FINISH_DATETIME         DATE,
    PAUSE_FLAG              CHAR          default '0'     not null,
    FIRST_PAUSE_DATETIME    DATE,
    FIRST_DEAL_OPERATOR_NO  VARCHAR2(18)  default ' '     not null,
    FIRST_DEAL_DATETIME     DATE,
    END_DEAL_OPERATOR_NO    VARCHAR2(18)  default ' '     not null,
    END_DEAL_DATETIME       DATE,
    IS_BRANCH_MANAGED       CHAR          default '0'     not null,
    WHITE_FLAG              CHAR          default '0'     not null,
    OPERATOR_BRANCH_NO      VARCHAR2(20)  default ' '     not null,
    OPERATOR_UP_BRANCH_NO   VARCHAR2(20)  default ' '     not null,
    TASK_DATETIME           DATE                          not null,
    INVALID_FLAG            VARCHAR2(1)   default '0'     not null,
    AUDIT_FINISH_DATETIME   DATE,
    BUSIN_TYPE              VARCHAR2(10)  default ' '     not null,
    FIRST_PAUSE_OPERATOR_NO VARCHAR2(18)  default ' '     not null
);

CREATE INDEX CRH_AC.IDX_FTRDETAILS_REQUESTNO ON CRH_AC.FLOWTASKRECORDDETAILS (REQUEST_NO);
CREATE INDEX CRH_AC.IDX_FTRDTAILS_SERIAL_ID ON CRH_AC.FLOWTASKRECORDDETAILS (SERIAL_ID);
CREATE INDEX CRH_AC.IDX_FTRDETAILS_OPERATORNO ON CRH_AC.FLOWTASKRECORDDETAILS (OPERATOR_NO);
CREATE INDEX CRH_AC.IDX_FTRDETAILS_TASKTYPE ON CRH_AC.FLOWTASKRECORDDETAILS (TASK_TYPE);
CREATE INDEX CRH_AC.IDX_FTRDETAILS_TASKSTATUS ON CRH_AC.FLOWTASKRECORDDETAILS (TASK_STATUS);
CREATE INDEX CRH_AC.IDX_FTRDETAILS_TASK_DATETIME ON CRH_AC.FLOWTASKRECORDDETAILS (TASK_DATETIME);
CREATE INDEX CRH_AC.IDX_FLOWTASKRECORDDETAILS ON CRH_AC.FLOWTASKRECORDDETAILS (REQUEST_NO, TASK_ID, SERIAL_ID);
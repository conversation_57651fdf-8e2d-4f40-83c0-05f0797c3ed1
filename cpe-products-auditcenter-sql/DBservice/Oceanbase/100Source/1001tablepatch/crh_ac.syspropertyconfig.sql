create table CRH_AC.SY<PERSON>ROPER<PERSON>CONFIG
(
    CONFIG_ID         VARCHAR2(64)   DEFAULT ' ' NOT NULL,
    LABEL_SYS         VARCHAR2(2000) DEFAULT ' ' NOT NULL,
    DESCRIPTION       VARCHAR2(2000) DEFAULT ' ' NOT NULL,
    ORDER_NO          NUMBER(18, 0)  DEFAULT 0   NOT NULL,
    SHOW_FLAG         CHAR(1)        DEFAULT ' ' NOT NULL,
    PROPERTY_KEY      VARCHAR2(100)  DEFAULT ' ' NOT NULL,
    PROPERTY_VALUE    VARCHAR2(2000) DEFAULT ' ' NOT NULL,
    VALIDATOR_TYPE    VARCHAR2(32)   DEFAULT ' ' NOT NULL,
    VALIDATOR_CONTENT VARCHAR2(4000) DEFAULT ' ' NOT NULL
);
CREATE UNIQUE INDEX CRH_AC.IDX_SYSPROPERTYCONFIG ON CRH_AC.SYSPROPERTYCONFIG (PROPERTY_KEY);
create table CRH_AC.BUSINFLOWRECORD
(
    REQUEST_NO      VARCHAR2(32)   default ' '     not null,
    SERIAL_ID       VARCHAR2(32)   default ' '     not null,
    CREATE_DATETIME DATE           default sysdate not null,
    REQUEST_STATUS  CHAR           default ' '     not null,
    OPERATOR_NO     VARCHAR2(18)   default ' '     not null,
    BRANCH_NO       VARCHAR2(20)   default ' '     not null,
    APP_ID          VARCHAR2(20)   default ' '     not null,
    ANODE_ID        VARCHAR2(64)   default ' '     not null,
    RECORD_TYPE     VARCHAR2(3)    default ' '     not null,
    BUSINESS_FLAG   NUMBER(10)     default 0       not null,
    BUSINESS_REMARK VARCHAR2(4000) default ' '     not null,
    BUSI_CONTENT    VARCHAR2(4000) default ' '     not null,
    TOHIS_FLAG      CHAR           default ' '     not null,
    TOHIS_DATETIME  DATE,
    OPERATOR_NAME   VARCHAR2(60)   default ' '     not null
);

CREATE INDEX CRH_AC.IDX_BFRD_RECORDTYPE ON CRH_AC.BUSINFLOWRECORD (RECORD_TYPE);
CREATE INDEX CRH_AC.IDX_BFRD_BUSINESSFLAG ON CRH_AC.BUSINFLOWRECORD (BUSINESS_FLAG);
CREATE INDEX CRH_AC.IDX_BUSINFLOWRECORD ON CRH_AC.BUSINFLOWRECORD (REQUEST_NO, SERIAL_ID);
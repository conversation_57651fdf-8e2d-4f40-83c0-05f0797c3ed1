create table CRH_AC.SYSMARKLOG
(
    SERIAL_ID    VARCHAR2(32)   DEFAULT ' '     NOT NULL,
    STAFF_NO     VARCHAR2(18)   DEFAULT ' '     NOT NULL,
    MENU_ID      NUMBER(10, 0)  DEFAULT 0       NOT NULL,
    ACCESS_URL   VARCHAR2(255)  DEFAULT ' '     NOT NULL,
    FUNC_CONTENT VARCHAR2(4000) DEFAULT ' '     NOT NULL,
    FUNC_REQTIME DATE           DEFAULT sysdate NOT NULL,
    IP_ADDRESS   VARCHAR2(500)  DEFAULT ' '     NOT NULL,
    MAC_ADDRESS  VARCHAR2(255)  DEFAULT ' '     NOT NULL
);
CREATE INDEX CRH_AC.IDX_SY<PERSON>ARKLOGSTAFFNO ON CRH_AC.SYSMARKLOG (STAFF_NO);
CREATE INDEX CRH_AC.IDX_SYSMARKLOGMENUID ON CRH_AC.SYSMARKLOG (MENU_ID);

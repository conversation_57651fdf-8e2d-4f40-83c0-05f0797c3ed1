create table CRH_AC.REPULSETREASONCONFIG
(
    SERIAL_ID       VARCHAR2(32)   default ' '     not null,
    AUDIT_TYPE      VARCHAR2(32)   default ' '     not null,
    ANODE_ID        VARCHAR2(64)   default ' '     not null,
    CAUSE_NAME      VARCHAR2(500)  default ' '     not null,
    CAUSE_CONTENT   VARCHAR2(4000) default ' '     not null,
    CAUSE_GROUP     VARCHAR2(255)  default ' '     not null,
    ORDER_NO        NUMBER(18)     default 0       not null,
    CREATE_BY       VARCHAR2(18)   default ' '     not null,
    CREATE_DATETIME DATE           default sysdate not null,
    UPDATE_BY       VARCHAR2(18)   default ' '     not null,
    UPDATE_DATETIME DATE           default sysdate not null,
    UN_SERIAL_ID    VARCHAR2(32)   default ' '     not null,
    CAUSE_ID        VARCHAR2(32)   default ' '     not null,
    BUSIN_TYPE      VARCHAR2(50)   default ' '
);

CREATE INDEX CRH_AC.IDX_REASONCONFIG ON CRH_AC.REPULSETREASONCONFIG (SERIAL_ID);
CREATE INDEX CRH_AC.IDX_REASONCONFIG_TYPE ON CRH_AC.REPULSETREASONCONFIG (AUDIT_TYPE);
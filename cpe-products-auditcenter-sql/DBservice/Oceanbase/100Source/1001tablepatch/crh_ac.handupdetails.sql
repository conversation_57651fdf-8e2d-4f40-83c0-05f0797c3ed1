create table CRH_AC.HANDUPDETAILS
(
    REQUEST_NO      VARCHAR2(32) default ' '     not null,
    SERIAL_ID       VARCHAR2(32) default ' '     not null,
    TASK_ID         VARCHAR2(32) default ' '     not null,
    OPERATOR_NO     VARCHAR2(18) default ' '     not null,
    OPERATOR_NAME   VARCHAR2(60) default ' '     not null,
    HANDUP_TYPE     CHAR         default ' '     not null,
    CREATE_DATETIME DATE         default sysdate not null,
    TOHIS_FLAG      CHAR         default ' '     not null,
    TOHIS_DATETIME  DATE,
    INVALID_FLAG    VARCHAR2(1)  default '0'     not null
);

CREATE INDEX CRH_AC.IDX_HUDETAILS_TASKID ON CRH_AC.HANDUPDETAILS (TASK_ID);
CREATE INDEX CRH_AC.IDX_HUDETAILS_OPERATORNO ON CRH_AC.HANDUPDETAILS (OPERATOR_NO);
CREATE INDEX CRH_AC.IDX_HANDUPDETAILS ON CRH_AC.HANDUPDETAILS (REQUEST_NO, SERI<PERSON>_ID);

create table CRH_AC.CUSTMODIFYRECORD
(
    SERIAL_ID            VARCHAR2(32)   default ' '     not null,
    REQUEST_NO           VARCHAR2(32)   default ' '     not null,
    OPERATOR_NO          VARCHAR2(18)   default ' '     not null,
    OPERATOR_NAME        VARCHAR2(60)   default ' '     not null,
    MODIFY_LINK          VARCHAR2(32)   default ' '     not null,
    MODIFY_ITEM          VARCHAR2(64)   default ' '     not null,
    MODIFY_BEFORECONTENT VARCHAR2(2000) default ' '     not null,
    MODIFY_AFTERCONTENT  VARCHAR2(2000) default ' '     not null,
    MODIFY_DATETIME      DATE           default sysdate not null,
    TOHIS_FLAG           CHAR           default ' '     not null,
    TOHIS_DATETIME       DATE,
    BUSIN_TYPE           VARCHAR2(32)   default ' ',
    FLOW_TASK_ID         VARCHAR2(32)   default ' '
);

CREATE INDEX CRH_AC.IDX_CUSTRECORD_MODIFYTIME ON CRH_AC.CUSTMODIFYRECORD (MODIFY_DATETIME);
CREATE INDEX CRH_AC.IDX_CUSTMODIFYRECORD ON CRH_AC.CUSTMODIFYRECORD (REQUEST_NO, SERIAL_ID);
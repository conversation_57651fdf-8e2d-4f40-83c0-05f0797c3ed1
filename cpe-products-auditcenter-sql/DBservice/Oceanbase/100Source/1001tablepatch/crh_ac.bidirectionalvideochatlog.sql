create table CRH_AC.BIDIRECTIONALVIDEOCHATLOG
(
    SERIAL_ID       VARCHAR2(32)   default ' '     not null,
    REQUEST_NO      VARCHAR2(32)   default ' '     not null,
    WORDS_CONTENT   VARCHAR2(4000) default ' '     not null,
    OPERATOR_NO     VARCHAR2(18)   default ' '     not null,
    OPERATOR_NAME   VARCHAR2(60)   default ' '     not null,
    CREATE_DATETIME DATE           default sysdate not null,
    TOHIS_FLAG      CHAR           default ' '     not null,
    TOHIS_DATETIME  DATE
);

CREATE UNIQUE INDEX CRH_AC.IDX_BIDIRECTIONALVIDEOCHATLOG ON CRH_AC.BIDIRECTIONALVIDEOCHATLOG (SERIAL_ID);
CREATE INDEX CRH_AC.IDX_BDVCHATLOG_REQUESTNO ON CRH_AC.BIDIRECTIONALVIDEOCHATLOG (REQUEST_NO);

COMMENT ON COLUMN crh_ac.bidirectionalvideochatlog.serial_id IS '流水号';
COMMENT ON COLUMN crh_ac.bidirectionalvideochatlog.request_no IS 'request_no';
COMMENT ON COLUMN crh_ac.bidirectionalvideochatlog.words_content IS '话术内容';
COMMENT ON COLUMN crh_ac.bidirectionalvideochatlog.operator_no IS '操作员编号';
COMMENT ON COLUMN crh_ac.bidirectionalvideochatlog.operator_name IS '操作员名称';
COMMENT ON COLUMN crh_ac.bidirectionalvideochatlog.create_datetime IS '创建时间';
COMMENT ON COLUMN crh_ac.bidirectionalvideochatlog.tohis_flag IS '归档标识，取值范围，1-已归档、0-未归档';
COMMENT ON COLUMN crh_ac.bidirectionalvideochatlog.tohis_datetime IS '归档时间';

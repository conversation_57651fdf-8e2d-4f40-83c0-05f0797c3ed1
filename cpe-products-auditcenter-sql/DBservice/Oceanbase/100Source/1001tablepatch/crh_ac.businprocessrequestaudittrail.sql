create table CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL
(
    REQUEST_NO                     VARCHAR2(32)   default ' '     not null,
    TASK_ID                        VARCHAR2(32)   default ' '     not null,
    USER_NAME                      VARCHAR2(60)   default ' '     not null,
    ID_NO                          VARCHAR2(40)   default ' '     not null,
    TIME_PROPERTY                  CHAR           default ' '     not null,
    REQUEST_STATUS                 VARCHAR2(30)   default ' '     not null,
    REQUEST_DATETIME               DATE                           not null,
    AUDIT_OPERATOR_NO              VARCHAR2(18)   default ' '     not null,
    AUDIT_OPERATOR_NAME            VARCHAR2(60)   default ' '     not null,
    AUDIT_OPERATOR_BRANCH_NO       VARCHAR2(20)   default ' '     not null,
    AUDIT_OPERATOR_BRANCH_NAME     VARCHAR2(255)  default ' '     not null,
    AUDIT_OPERATOR_UP_BRANCH_NO    VARCHAR2(20)   default ' '     not null,
    AUDIT_OPERATOR_UP_BRANCH_NAME  VARCHAR2(255)  default ' '     not null,
    AUDIT_CREATE_DATETIME          DATE,
    AUDIT_DEAL_DATETIME            DATE,
    AUDIT_FINISH_DATETIME          DATE,
    AUDIT_OP_CONTENT               VARCHAR2(4000) default ' '     not null,
    REVIEW_OPERATOR_NO             VARCHAR2(18)   default ' '     not null,
    REVIEW_OPERATOR_NAME           VARCHAR2(60)   default ' '     not null,
    REVIEW_OPERATOR_BRANCH_NO      VARCHAR2(20)   default ' '     not null,
    REVIEW_OPERATOR_BRANCH_NAME    VARCHAR2(255)  default ' '     not null,
    REVIEW_OPERATOR_UP_BRANCH_NO   VARCHAR2(20)   default ' '     not null,
    REVIEW_OPERATOR_UP_BRANCH_NAME VARCHAR2(255)  default ' '     not null,
    REVIEW_CREATE_DATETIME         DATE,
    REVIEW_DEAL_DATETIME           DATE,
    REVIEW_FINISH_DATETIME         DATE,
    REVIEW_OP_CONTENT              VARCHAR2(4000) default ' '     not null,
    SEC_RV_OPERATOR_NO             VARCHAR2(18)   default ' '     not null,
    SEC_RV_OPERATOR_NAME           VARCHAR2(60)   default ' '     not null,
    SEC_RV_OPERATOR_BRANCH_NO      VARCHAR2(20)   default ' '     not null,
    SEC_RV_OPERATOR_BRANCH_NAME    VARCHAR2(255)  default ' '     not null,
    SEC_RV_OPERATOR_UP_BRANCH_NO   VARCHAR2(20)   default ' '     not null,
    SEC_RV_OPERATOR_UP_BRANCH_NAME VARCHAR2(255)  default ' '     not null,
    SEC_RV_CREATE_DATETIME         DATE,
    SEC_RV_DEAL_DATETIME           DATE,
    SEC_RV_FINISH_DATETIME         DATE,
    SEC_RV_OP_CONTENT              VARCHAR2(4000) default ' '     not null,
    CHANNEL_CODE                   VARCHAR2(32)   default ' '     not null,
    ACTIVITY_NO                    VARCHAR2(64)   default ' '     not null,
    BUSIN_TYPE                     VARCHAR2(10)   default ' '     not null,
    CREATE_DATETIME                DATE           default sysdate not null,
    USER_BRANCH_NO                 VARCHAR2(20)   default ' '     not null,
    USER_BRANCH_NAME               VARCHAR2(255)  default ' '     not null,
    USER_UP_BRANCH_NO              VARCHAR2(20)   default ' '     not null,
    USER_UP_BRANCH_NAME            VARCHAR2(255)  default ' '     not null,
    TASK_SOURCE                    VARCHAR2(2)    default ' '     not null,
    APP_ID                         VARCHAR2(20)   default ' '     not null,
    ID_KIND                        CHAR           default ' '     not null,
    IS_BRANCH_MANAGED              CHAR           default '0'     not null,
    FIRST_DEAL_OPERATOR_NO         VARCHAR2(18)   default ' '     not null,
    FIRST_DEAL_DATETIME            DATE,
    MATCH_LABELS                   VARCHAR2(320)  default ' '     not null,
    CALL_FLAG                      CHAR           default '0'     not null,
    PAUSE_FLAG                     CHAR           default '0',
    FIRST_PAUSE_DATETIME           DATE,
    END_DEAL_DATETIME              DATE
);

CREATE INDEX CRH_AC.IDX_BPRATRAIL_REQUESTNO ON CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL (REQUEST_NO);
CREATE INDEX CRH_AC.IDX_BPRATRAIL_TASK_ID ON CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL (TASK_ID);
CREATE INDEX CRH_AC.IDX_BPRATRAIL_IDNO ON CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL (ID_NO);
CREATE INDEX CRH_AC.IDX_BPRATRAIL_REQUESTSTATUS ON CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL (REQUEST_STATUS);
CREATE INDEX CRH_AC.IDX_BPRATRAIL_REQUESTDATETIME ON CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL (REQUEST_DATETIME);
CREATE INDEX CRH_AC.IDX_BPRATRAIL_BUSINTYPE ON CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL (BUSIN_TYPE);
CREATE INDEX CRH_AC.IDX_BPRATRAIL_REQUESTSTATUS_REQUESTNO ON CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL (REQUEST_STATUS, REQUEST_NO);
CREATE INDEX CRH_AC.IDX_BUSINPROCESSREQUESTAUDITTRAIL ON CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL (REQUEST_NO, TASK_ID);
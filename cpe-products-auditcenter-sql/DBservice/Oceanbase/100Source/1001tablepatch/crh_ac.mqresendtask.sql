create table CRH_AC.MQRESENDTASK
(
    SERIAL_ID         VARCHAR2(32) default ' ' not null,
    TASK_ID           VARCHAR2(32) default ' ' not null,
    REQUEST_NO        VARCHAR2(32) default ' ' not null,
    FLOW_TASK_ID      VARCHAR2(32) default ' ' not null,
    ANODE_ID          VARCHAR2(32) default ' ' not null,
    RESEND_STATUS     VARCHAR2(2)  default ' ' not null,
    RESEND_NUM        NUMBER(10)   default 0   not null,
    CREATE_DATETIME   DATE,
    FINISHED_DATETIME DATE,
    UPDATE_DATETIME   DATE
);

CREATE INDEX CRH_AC.IDX_MQRESENDTASK_ID ON CRH_AC.MQRESENDTASK (SERIAL_ID);
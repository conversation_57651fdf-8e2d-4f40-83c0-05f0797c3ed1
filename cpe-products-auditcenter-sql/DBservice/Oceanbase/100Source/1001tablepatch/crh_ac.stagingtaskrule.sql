create table crh_ac.stagingtaskrule
(
    serial_id           varchar2(32)    default ' ' 		not null,
    rule_name           varchar2(255)   default ' ' 		not null,
    rule_type           char(1) default '1'     not null,
    rule_datetime_start date    default sysdate not null,
    rule_datetime_end   date    default sysdate not null,
    expression          varchar2(4000)  default ' ' 		not null,
    status              char(1) default '1'     not null,
    description         varchar2(2000)  default ' ' 		not null,
    order_no            number  default 1       not null,
    create_by           varchar2(255)   default ' ' 		not null,
    create_datetime     date    default sysdate not null,
    update_by           varchar2(255) 	default ' ' 		not null,
    update_datetime     date    default sysdate not null
);
create unique index crh_ac.idx_stataskrule_serialid on crh_ac.stagingtaskrule (serial_id);
create index crh_ac.idx_stataskrule_status on crh_ac.stagingtaskrule (status);
create index crh_ac.idx_stataskrule_name on crh_ac.stagingtaskrule (rule_name);
create index crh_ac.idx_stataskrule_statustime on crh_ac.stagingtaskrule (status, rule_datetime_start, rule_datetime_end);
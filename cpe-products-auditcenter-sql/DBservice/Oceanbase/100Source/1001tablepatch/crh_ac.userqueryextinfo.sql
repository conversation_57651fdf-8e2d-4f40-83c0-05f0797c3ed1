create table CRH_AC.USERQUERYEXTINFO
(
    REQUEST_NO               VARCHAR2(32)   default ' '     not null,
    REQUEST_STATUS           CHAR           default ' '     not null,
    REQUEST_DATETIME         DATE           default sysdate not null,
    SUBMIT_DATETIME          DATE,
    MOBILE_TEL               VARCHAR2(24)   default ' '     not null,
    BRANCH_NO                VARCHAR2(20)   default ' '     not null,
    CLIENT_ID                VARCHAR2(18)   default ' '     not null,
    FUND_ACCOUNT             VARCHAR2(18)   default ' '     not null,
    APP_ID                   VARCHAR2(20)   default ' '     not null,
    CHANNEL_CODE             VARCHAR2(32)   default ' '     not null,
    CLIENT_NAME              VARCHAR2(255)  default ' '     not null,
    ID_KIND                  CHAR           default ' '     not null,
    ID_NO                    VARCHAR2(40)   default ' '     not null,
    CLIENT_GENDER            CHAR           default ' '     not null,
    NATION_ID                VARCHAR2(4)    default ' '     not null,
    ISSUED_DEPART            VARCHAR2(64)   default ' '     not null,
    ID_BEGINDATE             NUMBER(10),
    ID_ENDDATE               NUMBER(10),
    ID_ADDRESS               VARCHAR2(255)  default ' '     not null,
    ADDRESS                  VARCHAR2(255)  default ' '     not null,
    ZIPCODE                  VARCHAR2(6)    default ' '     not null,
    INDUSTRY_TYPE            VARCHAR2(2)    default ' '     not null,
    DEGREE_CODE              VARCHAR2(2)    default ' '     not null,
    PROFESSION_CODE          VARCHAR2(2)    default ' '     not null,
    IDENTITY_CATEGORY        VARCHAR2(2)    default ' '     not null,
    AUDIT_OPERATOR_NO        VARCHAR2(18)   default ' '     not null,
    AUDIT_OPERATOR_NAME      VARCHAR2(60)   default ' '     not null,
    AUDIT_FINISH_DATETIME    DATE,
    REVIEW_OPERATOR_NO       VARCHAR2(18)   default ' '     not null,
    REVIEW_OPERATOR_NAME     VARCHAR2(60)   default ' '     not null,
    REVIEW_FINISH_DATETIME   DATE,
    DOUBLE_OPERATOR_NO       VARCHAR2(18)   default ' '     not null,
    DOUBLE_OPERATOR_NAME     VARCHAR2(60)   default ' '     not null,
    DOUBLE_FINISH_DATETIME   DATE,
    CHOOSE_BRANCH_REASON     VARCHAR2(2000) default ' '     not null,
    CHOOSE_PROFESSION_REASON VARCHAR2(2000) default ' '     not null,
    OP_STATION               VARCHAR2(255)  default ' '     not null,
    UPDATE_DATETIME          DATE           default sysdate not null,
    ANODE_ID                 VARCHAR2(64)   default ' '     not null,
    CLIENT_TAGS              VARCHAR2(2000) default ' '     not null,
    MOBILE_LOCATION          VARCHAR2(32)   default ' '     not null,
    ACTIVITY_NO              VARCHAR2(64)   default ' '     not null,
    ACTIVITY_NAME            VARCHAR2(64)   default ' '     not null,
    MARKETING_TEAM           VARCHAR2(60)   default ' '     not null,
    DISHONEST_RECORD         CHAR(10)       default ' '     not null,
    DISHONEST_RECORD_REMARK  VARCHAR2(2000) default ' '     not null,
    DISHONEST_CONTENT        VARCHAR2(255)  default ' '     not null,
    WORK_UNIT                VARCHAR2(2000) default ' '     not null,
    BROKER_NAME              VARCHAR2(2000) default ' '     not null,
    MOBILE_DIFFPLACE         CHAR           default ' '     not null,
    VIDEO_TYPE               VARCHAR2(30)   default ' '     not null,
    TOHIS_FLAG               CHAR           default ' '     not null,
    TOHIS_DATETIME           DATE,
    AI_AUDIT_CODE            VARCHAR2(32)   default ' ',
    CHANNEL_NAME             VARCHAR2(120)  default ' ',
    BUSIN_TYPE               NUMBER(10)     default 0       not null,
    BROKER_CODE              VARCHAR2(32)   default ' '     not null,
    VIDEO_OPERATOR_NO        VARCHAR2(18)   default ' '     not null,
    VIDEO_OPERATOR_NAME      VARCHAR2(60)   default ' '     not null,
    OPEN_CHANNEL             VARCHAR2(32)   default ' '     not null,
    END_NODE                 VARCHAR2(64)   default NULL,
    AUXILIARY_ID_KIND        CHAR           default ' '     not null,
    AUXILIARY_ID_NO          VARCHAR2(40)   default ' '     not null,
    OVERSEA_CLIENT_NAME      VARCHAR2(255)  default ' '     not null,
    OVERSEA_ID_KIND          CHAR           default ' '     not null,
    OVERSEA_ID_NO            VARCHAR2(40)   default ' '     not null,
    IS_SNAPSHOT              CHAR           default '0'     not null,
    AUXILIARY_ID_BEGINDATE   NUMBER(10),
    AUXILIARY_ID_ENDDATE     NUMBER(10),
    AUXILIARY_ID_ADDRESS     VARCHAR2(255),
    CLIENT_CATEGORY          CHAR           default ' '     not null
);

CREATE INDEX CRH_AC.IDX_USER_REQUESTNO ON CRH_AC.USERQUERYEXTINFO (REQUEST_NO);
CREATE INDEX CRH_AC.IDX_USER_STATUS ON CRH_AC.USERQUERYEXTINFO (REQUEST_STATUS);
CREATE INDEX CRH_AC.IDX_USER_REQUESTDATE ON CRH_AC.USERQUERYEXTINFO (REQUEST_DATETIME);
CREATE INDEX CRH_AC.IDX_USER_SUBMITDATE ON CRH_AC.USERQUERYEXTINFO (SUBMIT_DATETIME);
CREATE INDEX CRH_AC.IDX_USER_MOBILETEL ON CRH_AC.USERQUERYEXTINFO (MOBILE_TEL);
CREATE INDEX CRH_AC.IDX_USER_BRANCHNO ON CRH_AC.USERQUERYEXTINFO (BRANCH_NO);
CREATE INDEX CRH_AC.IDX_USER_FUNDACCOUNT ON CRH_AC.USERQUERYEXTINFO (FUND_ACCOUNT);
CREATE INDEX CRH_AC.IDX_USER_APPID ON CRH_AC.USERQUERYEXTINFO (APP_ID);
CREATE INDEX CRH_AC.IDX_USER_CHANNELCODE ON CRH_AC.USERQUERYEXTINFO (CHANNEL_CODE);
CREATE INDEX CRH_AC.IDX_USER_CLIENTNAME ON CRH_AC.USERQUERYEXTINFO (CLIENT_NAME);
CREATE INDEX CRH_AC.IDX_USER_IDNO ON CRH_AC.USERQUERYEXTINFO (ID_NO);
CREATE INDEX CRH_AC.IDX_USERQUERYEXTINFO_SNAPSHOT ON CRH_AC.USERQUERYEXTINFO (IS_SNAPSHOT);
create table crh_ac.stagingtaskrecord
(
    serial_id       varchar2(32)    default ' ' 		 not null,
    rule_id         varchar2(32)    default ' ' 		 not null,
    rule_name       varchar2(255)   default ' ' 		 not null,
    request_no      varchar2(32)    default ' ' 		 not null,
    task_id         varchar2(32)    default ' ' 		 not null,
    flow_task_id    varchar2(32) 	default ' ' 		 not null,
    record_type     char(1)         default '1'          not null,
    operator_no     varchar2(18) 	default ' ' 		 not null,
    operator_name   varchar2(60)    default ' ' 		 not null,
    create_datetime date            default sysdate      not null,
    match_info      varchar2(4000)  default ' ' 		 not null
);
create unique index crh_ac.idx_stataskrecord_serialid on crh_ac.stagingtaskrecord (serial_id);
create index crh_ac.idx_stataskrecord_requestno on crh_ac.stagingtaskrecord (request_no);
create index crh_ac.idx_stataskrecord_flowtaskid on crh_ac.stagingtaskrecord (flow_task_id);
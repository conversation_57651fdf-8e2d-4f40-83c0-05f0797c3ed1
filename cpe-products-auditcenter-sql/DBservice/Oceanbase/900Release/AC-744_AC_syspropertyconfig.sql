INSERT INTO CRH_AC.SYSPROPERTYCONFIG (CONFIG_ID, LABEL_SYS, DESC<PERSON>P<PERSON><PERSON>, ORDER_NO, SHOW_FLAG, PROPERTY_KEY,
                                        PROPERTY_VALUE, VALIDATOR_TYPE, VALIDATOR_CONTENT)
select 'wskh_operator',
       '用户无审核超时下线',
       '单位(分钟)',
       '3',
       '1',
       'wskh.logout.minute',
       '60',
       ' ',
       ' '
from dual
where not exists (select 1
                  from crh_user.syspropertyconfig
                  where property_key = 'wskh.logout.minute');
commit;


declare
v_rowcount integer;
begin
select 1 into v_rowcount  from dual;
end;

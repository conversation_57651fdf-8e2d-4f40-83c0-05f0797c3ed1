INSERT INTO CRH_AC.SYSPROPERTYCONFIG (CONFIG_ID, LABEL_SYS, DESC<PERSON>P<PERSON><PERSON>, ORDER_NO, SHOW_FLAG, PROPERTY_KEY,
                                        PROPERTY_VALUE, VALIDATOR_TYPE, VALIDATOR_CONTENT)
select 'wskh_operator',
       '超时无审核超时白名单',
       '员工编号逗号隔开',
       '4',
       '1',
       'wskh.audit.staff.whiteList',
       ' ',
       ' ',
       ' '
from dual
where not exists (select 1
                  from crh_user.syspropertyconfig
                  where property_key = 'wskh.audit.staff.whiteList');
commit;


declare
v_rowcount integer;
begin
select 1 into v_rowcount  from dual;
end;

insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (24, 'modify_item', '修改项目', 'photo_front', '证件照正面', 0, ' ', '8', ' ', ' ');
insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (24, 'modify_item', '修改项目', 'photo_back', '证件照反面', 0, ' ', '8', ' ', ' ');
insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (24, 'modify_item', '修改项目', 'agent_photo_front', '证件照正面', 0, ' ', '8', ' ', ' ');
insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (24, 'modify_item', '修改项目', 'agent_photo_back', '证件照反面', 0, ' ', '8', ' ', ' ');
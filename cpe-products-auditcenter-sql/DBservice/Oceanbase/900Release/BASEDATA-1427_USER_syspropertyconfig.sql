insert into CRH_USER.SYSPROPERTYCONFIG (<PERSON><PERSON><PERSON>_ID, <PERSON><PERSON><PERSON>_SYS, DESC<PERSON><PERSON><PERSON><PERSON>, ORDER_NO, SHOW_FLAG, PROPERTY_KEY, PROPERTY_VALUE, VALIDATOR_TYPE, VALIDATOR_CONTENT)
values  ('basedata_business', '坐席展示营业部机构树配置', ' ', 32, '1', 'base.zx.branch.code', '03,18', ' ', ' ');

insert into CRH_USER.SYSPROPERTYCONFIG (CONFIG_ID, LABEL_SYS, DESCRIPTION, ORDER_NO, SHOW_FLAG, PROPERTY_KEY, PROPERTY_VALUE, VALIDATOR_TYPE, VALIDATOR_CONTENT)
values  ('basedata_business', '客户展示营业部机构树配置', ' ', 33, '1', 'base.kh.branch.code', '18', ' ', ' ');
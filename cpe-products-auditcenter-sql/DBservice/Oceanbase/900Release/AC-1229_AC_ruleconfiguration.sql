--初始化数据  branch_no, up_branch_no, open_channel, channel_code, activity_no, marketing_team, mobile_location, task_status, busin_type, task_type, request_datetime
insert into crh_ac.ruleconfiguration (serial_id, field_code, field_name, field_type, expression, value_type,
                                      value_source, source_value, order_no)
values ('202503131400000000001', 'branch_no', '分支机构代码', 'string', 'in,not_in', 'cascader', 'interface',
        'basedata/getbranchinfolist', 0);
insert into crh_ac.ruleconfiguration (serial_id, field_code, field_name, field_type, expression, value_type,
                                      value_source, source_value, order_no)
values ('202503131400000000002', 'up_branch_no', '上级分支机构代码', 'string', 'in,not_in', 'cascader', 'interface',
        'basedata/getbranchinfolist', 1);
insert into crh_ac.ruleconfiguration (serial_id, field_code, field_name, field_type, expression, value_type,
                                      value_source, source_value, order_no)
values ('202503131400000000003', 'channel_code', '渠道码', 'string', 'in,not_in', 'input', ' ', ' ', 2);
insert into crh_ac.ruleconfiguration (serial_id, field_code, field_name, field_type, expression, value_type,
                                      value_source, source_value, order_no)
values ('202503131400000000004', 'open_channel', '开户渠道', 'string', 'in,not_in', 'input', ' ', ' ', 3);
insert into crh_ac.ruleconfiguration (serial_id, field_code, field_name, field_type, expression, value_type,
                                      value_source, source_value, order_no)
values ('202503131400000000005', 'activity_no', '活动码', 'string', 'in,not_in', 'input', ' ', ' ', 4);
insert into crh_ac.ruleconfiguration (serial_id, field_code, field_name, field_type, expression, value_type,
                                      value_source, source_value, order_no)
values ('202503131400000000006', 'marketing_team', '营销团队', 'string', 'in,not_in', 'input', ' ', ' ', 5);
insert into crh_ac.ruleconfiguration (serial_id, field_code, field_name, field_type, expression, value_type,
                                      value_source, source_value, order_no)
values ('202503131400000000007', 'mobile_location', '手机号归属地', 'string', 'in,not_in', 'input', ' ', ' ', 6);
insert into crh_ac.ruleconfiguration (serial_id, field_code, field_name, field_type, expression, value_type,
                                      value_source, source_value, order_no)
values ('202503131400000000009', 'busin_type', '业务类型', 'string', 'in,not_in', 'mutli_select', 'dict', 'busin_type',
        8);
insert into crh_ac.ruleconfiguration (serial_id, field_code, field_name, field_type, expression, value_type,
                                      value_source, source_value, order_no)
values ('202503131400000000010', 'task_type', '任务类型', 'string', 'in,not_in', 'mutli_select', 'dict', 'task_type',
        9);
insert into crh_ac.ruleconfiguration (serial_id, field_code, field_name, field_type, expression, value_type,
                                      value_source, source_value, order_no)
values ('202503131400000000011', 'request_datetime', '开户申请时间', 'date', 'between', 'time_range', ' ', ' ', 10);
commit;
/
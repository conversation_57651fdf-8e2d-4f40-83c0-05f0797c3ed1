--更新表CRH_AC.USERQUERYEXTINFO 字段CLIENT_CATEGORY
alter table CRH_AC.USERQUERYEXTINFO
    drop column CLIENT_CATEGORY;
alter table CRH_AC.USERQUERYEXTINFO
    add CLIENT_CATEGORY CHAR(1) default ' ' not null;

--删除 字典字段：busin_type  历史业务类型
delete
from CRH_USER.BASEDICTIONARY
where DICT_CODE = 'busin_type'
  and SUB_CODE in
      ('100400', '100401', '100500', '100501', '100600', '100601', '100700', '100701', '100800', '100801', '100900',
       '100901');

--新增 字典字段：busin_type 100910、100920、100930
INSERT INTO CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
VALUES (24, 'busin_type', '业务类型', '100910', '见证开户-普通证券账户', 20, ' ', '8', ' ', ' ');
INSERT INTO CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
VALUES (24, 'busin_type', '业务类型', '100920', '见证开户-普通资金账户', 21, ' ', '8', ' ', ' ');
INSERT INTO CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
VALUES (24, 'busin_type', '业务类型', '100930', '见证开户-信用账户', 22, ' ', '8', ' ', ' ');


--删除 智能审核项：历史业务类型
delete
from CRH_AIAUDIT.AIBUSINDEFINITION
where BUSIN_TYPE in
      ('100400', '100401', '100500', '100501', '100600', '100601', '100700', '100701', '100800', '100801', '100900',
       '100901');
delete
from CRH_AIAUDIT.AIBUSINRULE
where BUSIN_TYPE in
      ('100400', '100401', '100500', '100501', '100600', '100601', '100700', '100701', '100800', '100801', '100900',
       '100901');

--新增 智能审核项：100910、100920、100930
INSERT INTO CRH_AIAUDIT.AIBUSINDEFINITION (SERIAL_ID, SUBSYS_ID, BUSIN_TYPE, BUSIN_TYPE_NAME)
VALUES ('14', 24, 100910, '见证开户-普通证券账户');
INSERT INTO CRH_AIAUDIT.AIBUSINDEFINITION (SERIAL_ID, SUBSYS_ID, BUSIN_TYPE, BUSIN_TYPE_NAME)
VALUES ('15', 24, 100920, '见证开户-普通资金账户');
INSERT INTO CRH_AIAUDIT.AIBUSINDEFINITION (SERIAL_ID, SUBSYS_ID, BUSIN_TYPE, BUSIN_TYPE_NAME)
VALUES ('16', 24, 100930, '见证开户-信用账户');

--删除 视频话术模板 历史业务类型 按顺序执行
delete
from CRH_COMP.VIDEOWORDSBUSINESS
where BUSIN_TYPE in
      ('100400', '100401', '100500', '100501', '100600', '100601', '100700', '100701', '100800', '100801', '100900',
       '100901');

delete
from CRH_COMP.VIDEOWORDSCONFIG
where VIDEOWORDSMODEL_ID in (select SERIAL_ID
                             from CRH_COMP.VIDEOWORDSMODEL
                             where BUSIN_TYPE in
                                   ('100400', '100401', '100500', '100501', '100600', '100601', '100700', '100701',
                                    '100800', '100801', '100900', '100901'));

delete
from CRH_COMP.VIDEOWORDSMODEL
where BUSIN_TYPE in
      ('100400', '100401', '100500', '100501', '100600', '100601', '100700', '100701', '100800', '100801', '100900',
       '100901');


--新增 视频话术模板：100910、100920、100930
--表VIDEOWORDSMODEL
INSERT INTO CRH_COMP.VIDEOWORDSMODEL (SERIAL_ID, MODEL_NAME, MODEL_TYPE, VIDEO_TYPE, SEND_MSG_WORDS, CLIENT_TIPS_WORDS,
                                      SEX_APPLY, STATUS, CREATE_BY, MODIFY_BY, BUSIN_TYPE, PLAY_RATE)
VALUES ('202411191657440120000', '见证开户-普通证券账户（个人）双向视频话术', '1', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        '尊敬的投资者，您好！我是国泰君安证券股份有限公司${operator_branch_name}的开户见证人员，姓名：${operator_name}，工号：${operator_no}，证券从业登记编号：${profession_cert}，您可通过国泰君安网站www.gtja.com开户见证人系统核实我的见证人资格，或通过中国证券业协会从业人员信息公司系统核实我的执业资格。如您对我的身份无异议，请配合回答以下问题：',
        ' ', '8', ' ', ' ', 100910, null);
INSERT INTO CRH_COMP.VIDEOWORDSMODEL (SERIAL_ID, MODEL_NAME, MODEL_TYPE, VIDEO_TYPE, SEND_MSG_WORDS, CLIENT_TIPS_WORDS,
                                      SEX_APPLY, STATUS, CREATE_BY, MODIFY_BY, BUSIN_TYPE, PLAY_RATE)
VALUES ('202411191700310120000', '见证开户-普通证券账户（机构）双向视频话术', '1', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        '尊敬的投资者，您好！我是国泰君安证券股份有限公司${operator_branch_name}的开户见证人员，姓名：${operator_name}，工号：${operator_no}，证券从业登记编号：${profession_cert}，贵公司可通过国泰君安网站www.gtja.com开户见证人系统核实我的见证人资格，或通过中国证券业协会从业人员信息公司系统核实我的执业资格。如您对我的身份无异议，请配合回答以下问题：',
        ' ', '8', ' ', ' ', 100910, null);
INSERT INTO CRH_COMP.VIDEOWORDSMODEL (SERIAL_ID, MODEL_NAME, MODEL_TYPE, VIDEO_TYPE, SEND_MSG_WORDS, CLIENT_TIPS_WORDS,
                                      SEX_APPLY, STATUS, CREATE_BY, MODIFY_BY, BUSIN_TYPE, PLAY_RATE)
VALUES ('202411201313000120000', '见证开户-普通证券账户（产品）双向视频话术', '1', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        '尊敬的投资者，您好！我是国泰君安证券股份有限公司${operator_branch_name}的开户见证人员，姓名：${operator_name}，工号：${operator_no}，证券从业登记编号：${profession_cert}，贵公司产品可通过国泰君安网站www.gtja.com开户见证人系统核实我的见证人资格，或通过中国证券业协会从业人员信息公司系统核实我的执业资格。如您对我的身份无异议，请配合回答以下问题：',
        ' ', '8', ' ', ' ', 100910, null);


INSERT INTO CRH_COMP.VIDEOWORDSMODEL (SERIAL_ID, MODEL_NAME, MODEL_TYPE, VIDEO_TYPE, SEND_MSG_WORDS, CLIENT_TIPS_WORDS,
                                      SEX_APPLY, STATUS, CREATE_BY, MODIFY_BY, BUSIN_TYPE, PLAY_RATE)
VALUES ('202411191657440120012', '见证开户-普通资金账户（个人）双向视频话术', '1', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        '尊敬的投资者，您好！我是国泰君安证券股份有限公司${operator_branch_name}的开户见证人员，姓名：${operator_name}，工号：${operator_no}，证券从业登记编号：${profession_cert}，您可通过国泰君安网站www.gtja.com开户见证人系统核实我的见证人资格，或通过中国证券业协会从业人员信息公司系统核实我的执业资格。如您对我的身份无异议，请配合回答以下问题：',
        ' ', '8', ' ', ' ', 100920, null);
INSERT INTO CRH_COMP.VIDEOWORDSMODEL (SERIAL_ID, MODEL_NAME, MODEL_TYPE, VIDEO_TYPE, SEND_MSG_WORDS, CLIENT_TIPS_WORDS,
                                      SEX_APPLY, STATUS, CREATE_BY, MODIFY_BY, BUSIN_TYPE, PLAY_RATE)
VALUES ('202411191700310120012', '见证开户-普通资金账户（机构）双向视频话术', '1', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        '尊敬的投资者，您好！我是国泰君安证券股份有限公司${operator_branch_name}的开户见证人员，姓名：${operator_name}，工号：${operator_no}，证券从业登记编号：${profession_cert}，贵公司可通过国泰君安网站www.gtja.com开户见证人系统核实我的见证人资格，或通过中国证券业协会从业人员信息公司系统核实我的执业资格。如您对我的身份无异议，请配合回答以下问题：',
        ' ', '8', ' ', ' ', 100920, null);
INSERT INTO CRH_COMP.VIDEOWORDSMODEL (SERIAL_ID, MODEL_NAME, MODEL_TYPE, VIDEO_TYPE, SEND_MSG_WORDS, CLIENT_TIPS_WORDS,
                                      SEX_APPLY, STATUS, CREATE_BY, MODIFY_BY, BUSIN_TYPE, PLAY_RATE)
VALUES ('202411201317000120000', '见证开户-普通资金账户（产品）双向视频话术', '1', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        '尊敬的投资者，您好！我是国泰君安证券股份有限公司${operator_branch_name}的开户见证人员，姓名：${operator_name}，工号：${operator_no}，证券从业登记编号：${profession_cert}，贵公司产品可通过国泰君安网站www.gtja.com开户见证人系统核实我的见证人资格，或通过中国证券业协会从业人员信息公司系统核实我的执业资格。如您对我的身份无异议，请配合回答以下问题：',
        ' ', '8', ' ', ' ', 100920, null);


INSERT INTO CRH_COMP.VIDEOWORDSMODEL (SERIAL_ID, MODEL_NAME, MODEL_TYPE, VIDEO_TYPE, SEND_MSG_WORDS, CLIENT_TIPS_WORDS,
                                      SEX_APPLY, STATUS, CREATE_BY, MODIFY_BY, BUSIN_TYPE, PLAY_RATE)
VALUES ('202411191657440120013', '见证开户-信用账户（个人）双向视频话术', '1', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        '尊敬的投资者，您好！我是国泰君安证券股份有限公司${operator_branch_name}的开户见证人员，姓名：${operator_name}，工号：${operator_no}，证券从业登记编号：${profession_cert}，您可通过国泰君安网站www.gtja.com开户见证人系统核实我的见证人资格，或通过中国证券业协会从业人员信息公司系统核实我的执业资格。如您对我的身份无异议，请配合回答以下问题：',
        ' ', '8', ' ', ' ', 100930, null);
INSERT INTO CRH_COMP.VIDEOWORDSMODEL (SERIAL_ID, MODEL_NAME, MODEL_TYPE, VIDEO_TYPE, SEND_MSG_WORDS, CLIENT_TIPS_WORDS,
                                      SEX_APPLY, STATUS, CREATE_BY, MODIFY_BY, BUSIN_TYPE, PLAY_RATE)
VALUES ('202411191700310120013', '见证开户-信用账户（机构）双向视频话术', '1', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        '尊敬的投资者，您好！我是国泰君安证券股份有限公司${operator_branch_name}的开户见证人员，姓名：${operator_name}，工号：${operator_no}，证券从业登记编号：${profession_cert}，贵公司可通过国泰君安网站www.gtja.com开户见证人系统核实我的见证人资格，或通过中国证券业协会从业人员信息公司系统核实我的执业资格。如您对我的身份无异议，请配合回答以下问题：',
        ' ', '8', ' ', ' ', 100930, null);
INSERT INTO CRH_COMP.VIDEOWORDSMODEL (SERIAL_ID, MODEL_NAME, MODEL_TYPE, VIDEO_TYPE, SEND_MSG_WORDS, CLIENT_TIPS_WORDS,
                                      SEX_APPLY, STATUS, CREATE_BY, MODIFY_BY, BUSIN_TYPE, PLAY_RATE)
VALUES ('202411201321250120000', '见证开户-信用账户（产品）双向视频话术', '1', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        '尊敬的投资者，您好！我是国泰君安证券股份有限公司${operator_branch_name}的开户见证人员，姓名：${operator_name}，工号：${operator_no}，证券从业登记编号：${profession_cert}，贵公司产品可通过国泰君安网站www.gtja.com开户见证人系统核实我的见证人资格，或通过中国证券业协会从业人员信息公司系统核实我的执业资格。如您对我的身份无异议，请配合回答以下问题：',
        ' ', '8', ' ', ' ', 100930, null);


--VIDEOWORDSCONFIG
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191657440120001', '202411191657440120000', '1', '请问您的姓名和证件号码是？',
        '本人：${agent_name}，证件号码：${agent_id_no}。', ' ', '8', ' ', 1, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191657440120002', '202411191657440120000', '1',
        '请问您是否已知晓证券市场风险，已阅读且充分理解开户协议条款，并自愿在国泰君安开立证券账户？ 请用是或否回答。', '是',
        ' ', '8', ' ', 2, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191657440120003', '202411191657440120000', '2', '谢谢您的回答，我将审核您的申请资料，请稍等。', ' ', ' ',
        '8', ' ', 3, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411211657440120003', '202411191657440120000', '2', '感谢您的耐心等待，请根据系统提示进行后续操作，再见！', ' ',
        ' ', '8', ' ', 4, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120001', '202411191700310120000', '1', '请问您是${user_name}的代理人${agent_name}吗', '是', ' ',
        '8', ' ', 1, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120002', '202411191700310120000', '1', '现需核对贵公司的基本信息。请问贵公司全称和证件号码是？',
        '公司全称为：${user_name}，证件号码为：${id_no}。', ' ', '8', ' ', 2, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120003', '202411191700310120000', '1', '请问您的姓名和证件号码是？',
        '本人：${agent_name}，证件号码：${agent_id_no}。', ' ', '8', ' ', 3, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120004', '202411191700310120000', '1',
        '请问贵公司是否已知晓证券市场风险，已阅读且充分理解开户协议条款，并自愿在国泰君安开立证券账户？ 请用是或否回答。',
        '是', ' ', '8', ' ', 4, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120005', '202411191700310120000', '2', '谢谢您的回答，我将审核您的申请资料，请稍等。', ' ', ' ',
        '8', ' ', 5, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411211700310120005', '202411191700310120000', '2', '感谢您的耐心等待，请根据系统提示进行后续操作，再见！', ' ',
        ' ', '8', ' ', 6, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201313000120001', '202411201313000120000', '1', '请问您是${user_name}的代理人${agent_name}吗', '是', ' ',
        '8', ' ', 1, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201313000120002', '202411201313000120000', '1',
        '现需核对贵公司产品的基本信息。请问贵公司产品全称和证件号码是？', '产品全称为：${user_name}，证件号码为：${id_no}。',
        ' ', '8', ' ', 2, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201313000120003', '202411201313000120000', '1', '请问您的姓名和证件号码是？',
        '本人：${agent_name}，证件号码：${agent_id_no}。', ' ', '8', ' ', 3, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201313000120004', '202411201313000120000', '1',
        '请问贵公司产品是否已知晓证券市场风险，已阅读且充分理解开户协议条款，并自愿在国泰君安开立证券账户？ 请用是或否回答。',
        '是', ' ', '8', ' ', 4, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201313000120005', '202411201313000120000', '2', '谢谢您的回答，我将审核您的申请资料，请稍等。', ' ', ' ',
        '8', ' ', 5, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411211313000120005', '202411201313000120000', '2', '感谢您的耐心等待，请根据系统提示进行后续操作，再见！', ' ',
        ' ', '8', ' ', 6, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201724240120000', '202411191657440120000', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        ' ', ' ', '8', ' ', 0, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201725190120000', '202411191700310120000', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        ' ', ' ', '8', ' ', 0, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201725540120000', '202411201313000120000', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        ' ', ' ', '8', ' ', 0, ' ', ' ', null);

INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191657440120022', '202411191657440120012', '1', '请问您的姓名和证件号码是？',
        '本人：${agent_name}，证件号码：${agent_id_no}。', ' ', '8', ' ', 1, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191657440120023', '202411191657440120012', '1',
        '请问您是否已知晓证券市场风险，已阅读且充分理解开户协议条款，并自愿在国泰君安开立证券账户？ 请用是或否回答。', '是',
        ' ', '8', ' ', 2, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191657440120014', '202411191657440120012', '2', '谢谢您的回答，我将审核您的申请资料，请稍等。', ' ', ' ',
        '8', ' ', 3, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411211657440120014', '202411191657440120012', '2', '感谢您的耐心等待，请根据系统提示进行后续操作，再见！', ' ',
        ' ', '8', ' ', 4, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120022', '202411191700310120012', '1', '请问您是${user_name}的代理人${agent_name}吗', '是', ' ',
        '8', ' ', 1, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120014', '202411191700310120012', '1', '现需核对贵公司的基本信息。请问贵公司全称和证件号码是？',
        '公司全称为：${user_name}，证件号码为：${id_no}。', ' ', '8', ' ', 2, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120024', '202411191700310120012', '1', '请问您的姓名和证件号码是？',
        '本人：${agent_name}，证件号码：${agent_id_no}。', ' ', '8', ' ', 3, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120015', '202411191700310120012', '1',
        '请问贵公司是否已知晓证券市场风险，已阅读且充分理解开户协议条款，并自愿在国泰君安开立账户？ 请用是或否回答。', '是',
        ' ', '8', ' ', 4, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120016', '202411191700310120012', '2', '谢谢您的回答，我将审核您的申请资料，请稍等。', ' ', ' ',
        '8', ' ', 5, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411211700310120016', '202411191700310120012', '2', '感谢您的耐心等待，请根据系统提示进行后续操作，再见！', ' ',
        ' ', '8', ' ', 6, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201317000120001', '202411201317000120000', '1', '请问您是${user_name}的代理人${agent_name}吗', '是', ' ',
        '8', ' ', 1, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201317000120002', '202411201317000120000', '1',
        '现需核对贵公司产品的基本信息。请问贵公司产品全称和证件号码是？', '产品全称为：${user_name}，证件号码为：${id_no}。',
        ' ', '8', ' ', 2, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201317000120003', '202411201317000120000', '1', '请问您的姓名和证件号码是？',
        '本人：${agent_name}，证件号码：${agent_id_no}。', ' ', '8', ' ', 3, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201317000120004', '202411201317000120000', '1',
        '请问贵公司产品是否已知晓证券市场风险，已阅读且充分理解开户协议条款，并自愿在国泰君安开立证券账户？ 请用是或否回答。',
        '是', ' ', '8', ' ', 4, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201317000120005', '202411201317000120000', '2', '谢谢您的回答，我将审核您的申请资料，请稍等。', ' ', ' ',
        '8', ' ', 5, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411211317000120005', '202411201317000120000', '2', '感谢您的耐心等待，请根据系统提示进行后续操作，再见！', ' ',
        ' ', '8', ' ', 6, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201724540120000', '202411191657440120012', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        ' ', ' ', '8', ' ', 0, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201725310120000', '202411191700310120012', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        ' ', ' ', '8', ' ', 0, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201726040120000', '202411201317000120000', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        ' ', ' ', '8', ' ', 0, ' ', ' ', null);


INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191657440120122', '202411191657440120013', '1', '请问您的姓名和证件号码是？',
        '本人：${agent_name}，证件号码：${agent_id_no}。', ' ', '8', ' ', 1, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191657440120123', '202411191657440120013', '1',
        '请问您是否已知晓融资融券业务的风险，已阅读且充分理解《融资融券合同》的协议条款，并自愿在国泰君安开立信用账户？ 请用是或否回答。',
        '是', ' ', '8', ' ', 2, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191657440120114', '202411191657440120013', '2', '谢谢您的回答，我将审核您的申请资料，请稍等。', ' ', ' ',
        '8', ' ', 3, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411211657440120114', '202411191657440120013', '2', '感谢您的耐心等待，请根据系统提示进行后续操作，再见！', ' ',
        ' ', '8', ' ', 4, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120122', '202411191700310120013', '1', '请问您是${user_name}的代理人${agent_name}吗', '是', ' ',
        '8', ' ', 1, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120114', '202411191700310120013', '1', '现需核对贵公司的基本信息。请问贵公司全称和证件号码是？',
        '公司全称为：${user_name}，证件号码为：${id_no}。', ' ', '8', ' ', 2, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120124', '202411191700310120013', '1', '请问您的姓名和证件号码是？',
        '本人：${agent_name}，证件号码：${agent_id_no}。', ' ', '8', ' ', 3, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120115', '202411191700310120013', '1',
        '请问贵公司是否已知晓融资融券业务的风险，已阅读且充分理解《融资融券合同》等协议条款，并自愿在国泰君安开立信用账户？请用是或否回答。',
        '是', ' ', '8', ' ', 4, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411191700310120116', '202411191700310120013', '2', '谢谢您的回答，我将审核您的申请资料，请稍等。', ' ', ' ',
        '8', ' ', 5, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411211700310120116', '202411191700310120013', '2', '感谢您的耐心等待，请根据系统提示进行后续操作，再见！', ' ',
        ' ', '8', ' ', 6, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201321250120001', '202411201321250120000', '1', '请问您是${user_name}的代理人${agent_name}吗', '是', ' ',
        '8', ' ', 1, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201321250120002', '202411201321250120000', '1',
        '现需核对贵公司产品的基本信息。请问贵公司产品全称和证件号码是？', '产品全称为：${user_name}，证件号码为：${id_no}。',
        ' ', '8', ' ', 2, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201321250120003', '202411201321250120000', '1', '请问您的姓名和证件号码是？',
        '本人：${agent_name}，证件号码：${agent_id_no}。', ' ', '8', ' ', 3, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201321250120004', '202411201321250120000', '1',
        '请问贵公司产品是否已知晓融资融券业务的风险，已阅读且充分理解《融资融券合同》等协议条款，并自愿在国泰君安开立信用账户？请用是或否回答。',
        '是', ' ', '8', ' ', 4, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201321250120005', '202411201321250120000', '2', '谢谢您的回答，我将审核您的申请资料，请稍等。', ' ', ' ',
        '8', ' ', 5, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411211321250120005', '202411201321250120000', '2', '感谢您的耐心等待，请根据系统提示进行后续操作，再见！', ' ',
        ' ', '8', ' ', 6, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201725060120000', '202411191657440120013', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        ' ', ' ', '8', ' ', 0, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201725420120000', '202411191700310120013', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        ' ', ' ', '8', ' ', 0, ' ', ' ', null);
INSERT INTO CRH_COMP.VIDEOWORDSCONFIG (SERIAL_ID, VIDEOWORDSMODEL_ID, WORDS_TYPE, WORDS_CONTENT, CORRECT_ANSWER,
                                       ERROR_ANSWER, STATUS, VOICE_FILEID, ORDER_NO, CREATE_BY, MODIFY_BY, AUTO_PLAY)
VALUES ('202411201726190120000', '202411201321250120000', '2',
        '您好，根据中国结算等监管机构对见证开户业务的要求，下面我们将通过实时双向视频方式与您核实身份，确认您的开户意愿，相关过程将录音录像，您是否同意，并已做好准备？请用是或否回答。',
        ' ', ' ', '8', ' ', 0, ' ', ' ', null);


--VIDEOWORDSBUSINESS
INSERT INTO CRH_COMP.VIDEOWORDSBUSINESS (SERIAL_ID, VIDEOWORDSMODEL_ID, VIDEO_TYPE, SUBSYS_NO, PROD_CODE, PRODTA_NO,
                                         BUSIN_TYPE, ORGAN_FLAG, CREATE_BY, MODIFY_BY, STATUS, REGULAR_EXPRE)
VALUES ('202411191703380120000', '202411191657440120000', ' ', 24, '100910', ' ', 100910, ' ', ' ', ' ', '8',
        '#{ #client_category.equals(''1'') }');
INSERT INTO CRH_COMP.VIDEOWORDSBUSINESS (SERIAL_ID, VIDEOWORDSMODEL_ID, VIDEO_TYPE, SUBSYS_NO, PROD_CODE, PRODTA_NO,
                                         BUSIN_TYPE, ORGAN_FLAG, CREATE_BY, MODIFY_BY, STATUS, REGULAR_EXPRE)
VALUES ('202411191704310120000', '202411191700310120000', ' ', 24, '100910', ' ', 100910, ' ', ' ', ' ', '8',
        '#{ #client_category.equals(''2'') }');
INSERT INTO CRH_COMP.VIDEOWORDSBUSINESS (SERIAL_ID, VIDEOWORDSMODEL_ID, VIDEO_TYPE, SUBSYS_NO, PROD_CODE, PRODTA_NO,
                                         BUSIN_TYPE, ORGAN_FLAG, CREATE_BY, MODIFY_BY, STATUS, REGULAR_EXPRE)
VALUES ('202411201313470120000', '202411201313000120000', ' ', 24, '100910', ' ', 100910, ' ', ' ', ' ', '8',
        '#{ #client_category.equals(''3'') }');


INSERT INTO CRH_COMP.VIDEOWORDSBUSINESS (SERIAL_ID, VIDEOWORDSMODEL_ID, VIDEO_TYPE, SUBSYS_NO, PROD_CODE, PRODTA_NO,
                                         BUSIN_TYPE, ORGAN_FLAG, CREATE_BY, MODIFY_BY, STATUS, REGULAR_EXPRE)
VALUES ('202411191703380120011', '202411191657440120012', ' ', 24, '100920', ' ', 100920, ' ', ' ', ' ', '8',
        '#{ #client_category.equals(''1'') }');
INSERT INTO CRH_COMP.VIDEOWORDSBUSINESS (SERIAL_ID, VIDEOWORDSMODEL_ID, VIDEO_TYPE, SUBSYS_NO, PROD_CODE, PRODTA_NO,
                                         BUSIN_TYPE, ORGAN_FLAG, CREATE_BY, MODIFY_BY, STATUS, REGULAR_EXPRE)
VALUES ('202411191704310120012', '202411191700310120012', ' ', 24, '100920', ' ', 100920, ' ', ' ', ' ', '8',
        '#{ #client_category.equals(''2'') }');
INSERT INTO CRH_COMP.VIDEOWORDSBUSINESS (SERIAL_ID, VIDEOWORDSMODEL_ID, VIDEO_TYPE, SUBSYS_NO, PROD_CODE, PRODTA_NO,
                                         BUSIN_TYPE, ORGAN_FLAG, CREATE_BY, MODIFY_BY, STATUS, REGULAR_EXPRE)
VALUES ('202411201317250120000', '202411201317000120000', ' ', 24, '100920', ' ', 100920, ' ', ' ', ' ', '8',
        '#{ #client_category.equals(''3'') }');


INSERT INTO CRH_COMP.VIDEOWORDSBUSINESS (SERIAL_ID, VIDEOWORDSMODEL_ID, VIDEO_TYPE, SUBSYS_NO, PROD_CODE, PRODTA_NO,
                                         BUSIN_TYPE, ORGAN_FLAG, CREATE_BY, MODIFY_BY, STATUS, REGULAR_EXPRE)
VALUES ('202411191703380120021', '202411191657440120013', ' ', 24, '100930', ' ', 100930, ' ', ' ', ' ', '8',
        '#{ #client_category.equals(''1'') }');
INSERT INTO CRH_COMP.VIDEOWORDSBUSINESS (SERIAL_ID, VIDEOWORDSMODEL_ID, VIDEO_TYPE, SUBSYS_NO, PROD_CODE, PRODTA_NO,
                                         BUSIN_TYPE, ORGAN_FLAG, CREATE_BY, MODIFY_BY, STATUS, REGULAR_EXPRE)
VALUES ('202411191704310120022', '202411191700310120013', ' ', 24, '100930', ' ', 100930, ' ', ' ', ' ', '8',
        '#{ #client_category.equals(''2'') }');
INSERT INTO CRH_COMP.VIDEOWORDSBUSINESS (SERIAL_ID, VIDEOWORDSMODEL_ID, VIDEO_TYPE, SUBSYS_NO, PROD_CODE, PRODTA_NO,
                                         BUSIN_TYPE, ORGAN_FLAG, CREATE_BY, MODIFY_BY, STATUS, REGULAR_EXPRE)
VALUES ('202411201321450120000', '202411201321250120000', ' ', 24, '100930', ' ', 100930, ' ', ' ', ' ', '8',
        '#{ #client_category.equals(''3'') }');

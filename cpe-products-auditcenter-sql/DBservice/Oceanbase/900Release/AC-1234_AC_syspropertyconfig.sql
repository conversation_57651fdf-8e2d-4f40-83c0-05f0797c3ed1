--任务暂存开关
insert into crh_ac.syspropertyconfig (config_id, label_sys, description, order_no, show_flag, property_key,
                                      property_value, validator_type, validator_content)
values ('wskh_back', '任务暂存开关', ' ', 20, '1', 'wskh.task.staging.switch', '0', '1',
        '[{\"label\":\"是\",\"value\":\"1\"},{\"label\":\"否\",\"value\":\"0\"}]');
--任务暂存/释放上限
insert into crh_ac.syspropertyconfig (config_id, label_sys, description, order_no, show_flag, property_key,
                                      property_value, validator_type, validator_content)
values ('wskh_back', '任务暂存/释放上限', '条', 21, '1', 'wskh.task.staging.max.num', '10', ' ', ' ');
commit;
/
declare
    v_count integer;
begin
    select count(1) into v_count from crh_user.basedictionary where dict_code = 'process_status' and sub_code = '8';
    if v_count = 0 then
        insert into crh_user.basedictionary(subsys_no,
                                            dict_code,
                                            dict_name,
                                            sub_code,
                                            sub_name,
                                            order_no,
                                            remark,
                                            status,
                                            create_by,
                                            modify_by)
        values ('24',
                'process_status',
                '主流程状态',
                '8',
                '审核作废',
                1,
                ' ',
                '8',
                ' ',
                ' ');
    end if;
    commit;
end;
/

declare
    v_rowcount integer;
begin
    select count(1)
    into v_rowcount
    from dual
    where exists(select *
                 from all_tab_columns
                 where table_name = upper('businflowtask')
                   and column_name = upper('dispatch_task_id')
                   and owner = upper('crh_ac'));
    if v_rowcount = 0 then
        execute immediate 'ALTER TABLE crh_ac.businflowtask ADD dispatch_task_id varchar2(64) default '' '' NOT NULL';
    end if;
end;
/
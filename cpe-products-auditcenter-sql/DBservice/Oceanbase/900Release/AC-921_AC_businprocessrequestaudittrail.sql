ALTER TABLE CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL RENAME COLUMN secondary_review_operator_no TO sec_rv_operator_no;
ALTER TABLE CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL RENAME COLUMN secondary_review_operator_name TO sec_rv_operator_name;
ALTER TABLE CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL RENAME COLUMN secondary_review_operator_branch_no TO sec_rv_operator_branch_no;
ALTER TABLE CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL RENAME COLUMN secondary_review_operator_branch_name TO sec_rv_operator_branch_name;
ALTER TABLE CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL RENAME COLUMN secondary_review_operator_up_branch_no TO sec_rv_operator_up_branch_no;
ALTER TABLE CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL RENAME COLUMN secondary_review_operator_up_branch_name TO sec_rv_operator_up_branch_name;
ALTER TABLE CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL RENAME COLUMN secondary_review_create_datetime TO sec_rv_create_datetime;
ALTER TABLE CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL RENAME COLUMN secondary_review_deal_datetime TO sec_rv_deal_datetime;
ALTER TABLE CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL RENAME COLUMN secondary_review_finish_datetime TO sec_rv_finish_datetime;
ALTER TABLE CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL RENAME COLUMN secondary_review_op_content TO sec_rv_op_content;
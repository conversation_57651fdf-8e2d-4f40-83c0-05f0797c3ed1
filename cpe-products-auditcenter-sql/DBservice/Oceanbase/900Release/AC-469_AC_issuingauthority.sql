--签发机关
CREATE TABLE "CRH_AC"."ISSUINGAUTHORITY"
(
    "SERIAL_ID"       VARCHAR2(32)   DEFAULT ' '     NOT NULL,
    "PROVINCE_NO"     VARCHAR2(20)   DEFAULT ' '     NOT NULL,
    "AUTHORITY_NAME"  VARCHAR2(255)  DEFAULT ' '     NOT NULL,
    "ADDRESS_ORAGN"   VARCHAR2(255)  DEFAULT ' '     NOT NULL,
    "REMARK"          VARCHAR2(2000) DEFAULT ' '     NOT NULL,
    "STATUS"          CHAR(1)        DEFAULT '8'     NOT NULL,
    "CREATE_BY"       VARCHAR2(18)   DEFAULT ' '     NOT NULL,
    "CREATE_DATETIME" DATE           DEFAULT SYSDATE NOT NULL,
    "UPDATE_BY"       VARCHAR2(18)   DEFAULT ' '     NOT NULL,
    "UPDATE_DATETIME" DATE           DEFAULT SYSDATE NOT NULL,
    CONSTRAINT "IDX_ISSUINGAUTHORITY" UNIQUE ("SERIAL_ID")
) COMPRESS FOR ARCHIVE REPLICA_NUM = 1 BLOCK_SIZE = 16384 USE_BLOOM_FILTER = FALSE TABLET_SIZE = 134217728 PCTFREE = 0;
commit;

--后执行

COMMENT ON COLUMN crh_ac.issuingauthority.serial_id IS 'id';
COMMENT ON COLUMN crh_ac.issuingauthority.province_no IS '省份编号';
COMMENT ON COLUMN crh_ac.issuingauthority.authority_name IS '签发机关名称';
COMMENT ON COLUMN crh_ac.issuingauthority.address_oragn IS '证件地址行政区划';
COMMENT ON COLUMN crh_ac.issuingauthority.remark IS '备注';
COMMENT ON COLUMN crh_ac.issuingauthority.status IS '状态 8-可用 9-不可用';
COMMENT ON COLUMN crh_ac.issuingauthority.create_by IS '创建人';
COMMENT ON COLUMN crh_ac.issuingauthority.create_datetime IS '创建时间';
COMMENT ON COLUMN crh_ac.issuingauthority.update_by IS '更新人';
COMMENT ON COLUMN crh_ac.issuingauthority.update_datetime IS '更新时间';
commit;


CREATE UNIQUE INDEX "CRH_AC"."IDX_ISSAUTH_AUTHORITY_NAME" on "CRH_AC"."ISSUINGAUTHORITY" (
                                                                                          "AUTHORITY_NAME"
    ) GLOBAL;

CREATE INDEX "CRH_AC"."IDX_ISSAUTH_PROVINCE_NO" on "CRH_AC"."ISSUINGAUTHORITY" (
                                                                                "PROVINCE_NO"
    ) GLOBAL;

CREATE INDEX "CRH_AC"."IDX_ISSAUTH_ADDRESS_ORAGN" on "CRH_AC"."ISSUINGAUTHORITY" (
                                                                                  "ADDRESS_ORAGN"
    ) GLOBAL;
commit;
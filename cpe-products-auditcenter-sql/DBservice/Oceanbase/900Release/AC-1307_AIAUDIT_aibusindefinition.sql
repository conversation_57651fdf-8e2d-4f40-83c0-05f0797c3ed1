declare
    v_count integer;
begin
    select count(1) into v_count from crh_aiaudit.aibusindefinition where busin_type=100090;
    if v_count = 0 then
        insert into crh_aiaudit.aibusindefinition(
            serial_id,
            subsys_id,
            busin_type,
            busin_type_name
        )
        values (
                   '20',
                   24,
                   100090,
                   '普通开户-港澳台通行证'
               );
    end if;
    commit;
end;
/
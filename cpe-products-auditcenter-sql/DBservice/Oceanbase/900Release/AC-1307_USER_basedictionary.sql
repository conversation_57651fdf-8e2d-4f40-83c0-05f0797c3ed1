
declare
    v_count integer;
begin
    select count(1) into v_count from crh_user.basedictionary where dict_code='modify_item' and sub_code='file_Bm';
    if v_count = 0 then
        insert into crh_user.basedictionary(
            subsys_no,
            dict_code,
            dict_name,
            sub_code,
            sub_name,
            order_no,
            remark,
            status,
            create_by,
            modify_by
        )
        values (
                   '24',
                   'modify_item',
                   '修改项目',
                   'file_Bm',
                   '机构的营业执照',
                   26,
                   ' ',
                   '8',
                   ' ',
                   ' '
               );
    end if;
    commit;
end;
/

declare
    v_count integer;
begin
    select count(1) into v_count from crh_user.basedictionary where dict_code='modify_item' and sub_code='file_7W';
    if v_count = 0 then
        insert into crh_user.basedictionary(
            subsys_no,
            dict_code,
            dict_name,
            sub_code,
            sub_name,
            order_no,
            remark,
            status,
            create_by,
            modify_by
        )
        values (
                   '24',
                   'modify_item',
                   '修改项目',
                   'file_7W',
                   '境内机构出具的就业证明',
                   27,
                   ' ',
                   '8',
                   ' ',
                   ' '
               );
    end if;
    commit;
end;
/

declare
    v_count integer;
begin
    select count(1) into v_count from crh_user.basedictionary where dict_code='modify_item' and sub_code='file_7X';
    if v_count = 0 then
        insert into crh_user.basedictionary(
            subsys_no,
            dict_code,
            dict_name,
            sub_code,
            sub_name,
            order_no,
            remark,
            status,
            create_by,
            modify_by
        )
        values (
                   '24',
                   'modify_item',
                   '修改项目',
                   'file_7X',
                   '住宿登记证明表原件',
                   28,
                   ' ',
                   '8',
                   ' ',
                   ' '
               );
    end if;
    commit;
end;
/

declare
    v_count integer;
begin
    select count(1) into v_count from crh_user.basedictionary where dict_code='modify_item' and sub_code='auxiliary_id_begindate';
    if v_count = 0 then
        insert into crh_user.basedictionary(
            subsys_no,
            dict_code,
            dict_name,
            sub_code,
            sub_name,
            order_no,
            remark,
            status,
            create_by,
            modify_by
        )
        values (
                   '24',
                   'modify_item',
                   '修改项目',
                   'auxiliary_id_begindate',
                   '辅助证件开始日期',
                   30,
                   ' ',
                   '8',
                   ' ',
                   ' '
               );
    end if;
    commit;
end;
/

declare
    v_count integer;
begin
    select count(1) into v_count from crh_user.basedictionary where dict_code='modify_item' and sub_code='auxiliary_id_enddate';
    if v_count = 0 then
        insert into crh_user.basedictionary(
            subsys_no,
            dict_code,
            dict_name,
            sub_code,
            sub_name,
            order_no,
            remark,
            status,
            create_by,
            modify_by
        )
        values (
                   '24',
                   'modify_item',
                   '修改项目',
                   'auxiliary_id_enddate',
                   '辅助证件结束日期',
                   31,
                   ' ',
                   '8',
                   ' ',
                   ' '
               );
    end if;
    commit;
end;
/

declare
    v_count integer;
begin
    select count(1) into v_count from crh_user.basedictionary where dict_code='modify_item' and sub_code='auxiliary_id_address';
    if v_count = 0 then
        insert into crh_user.basedictionary(
            subsys_no,
            dict_code,
            dict_name,
            sub_code,
            sub_name,
            order_no,
            remark,
            status,
            create_by,
            modify_by
        )
        values (
                   '24',
                   'modify_item',
                   '修改项目',
                   'auxiliary_id_address',
                   '辅助证件地址',
                   32,
                   ' ',
                   '8',
                   ' ',
                   ' '
               );
    end if;
    commit;
end;
/

declare
    v_count integer;
begin
    select count(1) into v_count from crh_user.basedictionary where dict_code='modify_item' and sub_code='birthday';
    if v_count = 0 then
        insert into crh_user.basedictionary(
            subsys_no,
            dict_code,
            dict_name,
            sub_code,
            sub_name,
            order_no,
            remark,
            status,
            create_by,
            modify_by
        )
        values (
                   '24',
                   'modify_item',
                   '修改项目',
                   'birthday',
                   '出生日期',
                   33,
                   ' ',
                   '8',
                   ' ',
                   ' '
               );
    end if;
    commit;
end;
/

declare
    v_count integer;
begin
    select count(1) into v_count from crh_user.basedictionary where dict_code='reject_reason_group' and sub_code='7';
    if v_count = 0 then
        insert into crh_user.basedictionary(
            subsys_no,
            dict_code,
            dict_name,
            sub_code,
            sub_name,
            order_no,
            remark,
            status,
            create_by,
            modify_by
        )
        values (
                   '24',
                   'reject_reason_group',
                   '原因分组',
                   '7',
                   '辅助证明材料',
                   7,
                   ' ',
                   '8',
                   ' ',
                   ' '
               );
    end if;
    commit;
end;
/
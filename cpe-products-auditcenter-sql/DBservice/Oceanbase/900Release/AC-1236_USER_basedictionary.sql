--新增DICT_CODE为business_flag的 手动暂存、手动释放、自动暂存
insert into crh_user.basedictionary (subsys_no, dict_code, dict_name, sub_code, sub_name, order_no, remark, status,
                                     create_by, modify_by)
values (24, 'business_flag', '业务标识', '22405', '手动暂存', 203, ' ', '8', ' ', ' ');
insert into crh_user.basedictionary (subsys_no, dict_code, dict_name, sub_code, sub_name, order_no, remark, status,
                                     create_by, modify_by)
values (24, 'business_flag', '业务标识', '22406', '手动释放', 204, ' ', '8', ' ', ' ');
insert into crh_user.basedictionary (subsys_no, dict_code, dict_name, sub_code, sub_name, order_no, remark, status,
                                     create_by, modify_by)
values (24, 'business_flag', '业务标识', '22407', '自动暂存', 205, ' ', '8', ' ', ' ');

commit;
/
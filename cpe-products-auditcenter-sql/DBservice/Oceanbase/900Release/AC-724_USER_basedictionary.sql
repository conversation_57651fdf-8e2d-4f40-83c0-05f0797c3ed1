insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (10, 'archfile_no', '档案文件编号', '6A', '身份证正面', 1, ' ', '8', ' ', ' ');
insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (10, 'archfile_no', '档案文件编号', '6B', '身份证反面', 2, ' ', '8', ' ', ' ');
insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (10, 'archfile_no', '档案文件编号', '80', '客户图像', 3, ' ', '8', ' ', ' ');
insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (10, 'archfile_no', '档案文件编号', 'Aj', '金融资产证明', 4, ' ', '8', ' ', ' ');
insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (10, 'archfile_no', '档案文件编号', 'B1', '交易经验', 5, ' ', '8', ' ', ' ');


insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (10, 'dict_trans_kind', '字典转换类型', '9', '本地转国泰君安', 10, ' ', '8', ' ', ' ');
insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (10, 'dict_trans_kind', '字典转换类型', '8', '本地转银银平台', 9, ' ', '8', ' ', ' ');
insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (10, 'dict_trans_kind', '字典转换类型', '7', '本地转BOP', 8, ' ', '8', ' ', ' ');
insert into CRH_USER.BASEDICTIONARY (SUBSYS_NO, DICT_CODE, DICT_NAME, SUB_CODE, SUB_NAME, ORDER_NO, REMARK, STATUS,
                                     CREATE_BY, MODIFY_BY)
values (24, 'dict_trans_kind', '字典转换类型', '2', '本地转新意', 2, ' ', '8', ' ', ' ');


insert into CRH_USER.DICTIONARYTRANS (SERIAL_ID, DICT_CODE, DICT_TRANS_KIND, SUB_CODE, OUTER_SUB_CODE, REMARK,
                                      MODIFY_BY, CREATE_BY)
values ('20240829481100050000', 'archfile_no', '2', '80', '70', ' ', ' ', ' ');

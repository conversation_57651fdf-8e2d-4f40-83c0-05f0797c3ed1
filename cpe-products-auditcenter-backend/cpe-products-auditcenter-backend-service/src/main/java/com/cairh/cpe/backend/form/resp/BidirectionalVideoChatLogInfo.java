package com.cairh.cpe.backend.form.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 查询双向视频对话记录信息
 *
 * <AUTHOR>
 * @since 2024/12/3 18:34
 */
@Data
public class BidirectionalVideoChatLogInfo {

    /**
     * 流水号
     */
    private String serial_id;

    /**
     * request_no
     */
    private String request_no;

    /**
     * 话术内容
     */
    private String words_content;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员名称
     */
    private String operator_name;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

}

package com.cairh.cpe.backend.service;

import com.cairh.cpe.backend.form.req.IdCardImageRetryForm;
import com.cairh.cpe.backend.form.resp.IdPhotoResp;
import com.cairh.cpe.backend.form.resp.IdPhotosResp;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;

import java.util.List;

/**
 * 审核详情 - 证件照
 */
public interface IAuditDetailIdPhotoService {

    /**
     * 取证件照正面
     */
    String getIdPhotoFront(String request_no);

    /**
     * 取证件照反面
     */
    String getIdPhotoBack(String request_no);

    /**
     * 查询用户的历史照片（身份证正面，身份证反面）
     */
    List<IdPhotosResp> queryHisImage(String request_no);

    /**
     * 照片重试并保存至修改流水
     */
    List<AiAuditRuleResp> imageRetryAndImageSave(IdCardImageRetryForm idCardImageRetryForm);

    /**
     * 网厅业务办理-照片重试
     */
    List<AiAuditRuleResp> handImageRetryAndImageSave(IdCardImageRetryForm idCardImageRetryForm);

    IdPhotoResp getAllIdPhoto(String request_no);
}

package com.cairh.cpe.backend.service;

import com.cairh.cpe.backend.form.req.ExitVideoQueueRequest;
import com.cairh.cpe.backend.form.req.JoinVideoRequest;
import com.cairh.cpe.backend.form.req.QueryOperatorInfoRequest;
import com.cairh.cpe.backend.form.req.QueryVideoQueueRequest;
import com.cairh.cpe.backend.form.resp.QueryOperatorInfoResponse;
import com.cairh.cpe.backend.form.resp.QueryZegoTokenResp;
import com.cairh.cpe.esb.component.video.dto.resp.VideoQryQueueInfoResponse;

/**
 * Description：双向视频服务
 * Author： slx
 * Date： 2024/5/14 下午3:35
 */
public interface IAuditDetailBidirectionalVideoService {


    void videoJoinQueue(JoinVideoRequest request);

    VideoQryQueueInfoResponse queryVideoQueue(QueryVideoQueueRequest request);

    void exitVideoQueue(ExitVideoQueueRequest request);

    QueryOperatorInfoResponse queryOperatorInfo(QueryOperatorInfoRequest request);

    QueryZegoTokenResp refreshUserZegoToken(QueryVideoQueueRequest request);

}

package com.cairh.cpe.backend.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.backend.form.req.BusinessCentralizedQueryForm;
import com.cairh.cpe.backend.form.req.BusinessHandCreateForm;
import com.cairh.cpe.backend.form.resp.CreateResult;
import com.cairh.cpe.backend.form.resp.HallBackInfo;
import com.cairh.cpe.backend.form.resp.UserAcBaseInfo;
import com.cairh.cpe.backend.form.support.VideoRequestDTO;
import com.cairh.cpe.backend.service.IHandApiService;
import com.cairh.cpe.businflow.model.ActivityInfo;
import com.cairh.cpe.businflow.service.IActivitiFlowStepService;
import com.cairh.cpe.cache.component.ComponentWorkTimeService;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.*;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.service.*;
import com.cairh.cpe.common.util.BeanMapUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.db.config.IdGenerator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HandApiServiceImpl implements IHandApiService {


    @Resource
    private IBusinFlowRequestService businFlowRequestService;

    @Resource
    private IBusinFlowTaskService businFlowTaskService;

    @Resource
    private IBusinFlowRecordService businFlowRecordService;

    @Resource
    private IBusinFlowParamsService businFlowParamsService;

    @Resource
    private IUserQueryExtInfoService userQueryExtInfoService;

    @Resource
    private IActivitiFlowStepService activitiFlowStepService;

    @Resource
    private ILabelService labelService;

    @Resource
    private IRequestFlowService requestFlowService;

    @Resource
    private IdGenerator idGenerator;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ConverterApplyFactory converterApplyFactory;
    @Autowired
    private CacheBranch cacheBranch;
    @Autowired
    private ComponentWorkTimeService componentWorkTimeService;
    @Autowired
    private IBusinProcessRequestAuditTrailService businProcessRequestAuditTrailService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public CreateResult create(BusinessHandCreateForm businessHandForm) {

        Map<String, Object> params = JSONObject.parseObject(JSON.toJSONString(businessHandForm), Map.class);
        UserAcBaseInfo convert = converterApplyFactory.convert(businessHandForm, new UserAcBaseInfo());

        // 校验业务类型
        if (!ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, businessHandForm.getBusin_type())) {
            log.error("请传入指定的业务类型与开户类别, busin_type={}", businessHandForm.getBusin_type());
            throw new BizException(ErrorEnum.HAND_APPLY_DATA_ERROR.getValue(), ErrorEnum.HAND_APPLY_DATA_ERROR.getDesc());
        }
        params.putAll(BeanMapUtil.beanToMap(convert));
        // 1 创建request
        Date date = new Date();
        BusinFlowRequest businFlowRequest = new BusinFlowRequest();
        businFlowRequest.setBusin_type(businessHandForm.getBusin_type());
        businFlowRequest.setId_kind(businessHandForm.getId_kind());
        businFlowRequest.setId_no(businessHandForm.getId_no());
        businFlowRequest.setClient_name(businessHandForm.getClient_name());
        businFlowRequest.setBranch_no(businessHandForm.getBranch_no());
        businFlowRequest.setRequest_datetime(date);
        businFlowRequest.setRequest_no(idGenerator.nextUUID(null));
        businFlowRequest.setRequest_status(FlowStatusConst.REQUEST_STATUS_APPLYING);
        businFlowRequest.setUpdate_datetime(date);
        businFlowRequest.setSubmit_datetime(date);
        businFlowRequest.setAnode_id(FlowNodeConst.AUDIT);
        businFlowRequest.setBusiness_apply_no(StringUtils.isNotBlank(businessHandForm.getBusiness_apply_no()) ? businessHandForm.getBusiness_apply_no() : StrUtil.SPACE);
        List<Label> matchLabel = labelService.getMatchLabel(businFlowRequest.getRequest_no(), params);
        if (CollectionUtils.isEmpty(matchLabel)) {
            businFlowRequest.setMatch_labels(" ");
        } else {
            businFlowRequest.setMatch_labels(matchLabel.stream().map(Label::getSerial_id).collect(Collectors.joining(",")));
        }
        businFlowRequest.setMobile_tel(businessHandForm.getMobile_tel());
        businFlowRequest.setBranch_repeated(requestFlowService.getBranch_repeated(businessHandForm.getId_no()));
        businFlowRequestService.save(businFlowRequest);

        String request_no = businFlowRequest.getRequest_no();
        //任务表
        ActivityInfo activityInfo = activitiFlowStepService.getNextActivitiNodeInfo(params, "");
        BusinFlowTask businFlowTask = new BusinFlowTask();
        businFlowTask.setRequest_no(request_no);
        businFlowTask.setCreate_datetime(date);
        businFlowTask.setTask_status(FlowStatusConst.AUDIT_PENDING);
        businFlowTask.setTask_type(activityInfo.getActivityId());
        businFlowTask.setVideo_type(WskhConstant.VIDEO_TYPE_2);
        businFlowTask.setTohis_flag(Constant.TOHIS_FLAG_N);
        businFlowTask.setPush_flag(WskhConstant.NO_PUSH_FLAG);
        businFlowTask.setBranch_repeated(businFlowRequest.getBranch_repeated());
        businFlowTask.setMatch_labels(businFlowRequest.getMatch_labels());
        businFlowTask.setTask_id(idGenerator.nextUUID(null));
        businFlowTask.setSerial_id(idGenerator.nextUUID(null));
        businFlowTaskService.save(businFlowTask);

        // user 拓展表
        UserQueryExtInfo userQueryExtInfo = new UserQueryExtInfo();
        userQueryExtInfo.setBranch_no(businessHandForm.getBranch_no());
        userQueryExtInfo.setIs_snapshot("0");
        userQueryExtInfo.setClient_name(businessHandForm.getClient_name());
        userQueryExtInfo.setRequest_no(request_no);
        userQueryExtInfo.setRequest_status(businFlowRequest.getRequest_status());
        userQueryExtInfo.setRequest_datetime(businFlowRequest.getRequest_datetime());
        userQueryExtInfo.setId_no(businessHandForm.getId_no());
        userQueryExtInfo.setId_kind(businessHandForm.getId_kind());
        userQueryExtInfo.setVideo_type(WskhConstant.VIDEO_TYPE_2);
        userQueryExtInfo.setBusin_type(businessHandForm.getBusin_type());
        userQueryExtInfo.setClient_category(StringUtils.isNotBlank(businessHandForm.getClient_category()) ? businessHandForm.getClient_category() : StrUtil.SPACE);
        userQueryExtInfo.setAnode_id(FlowNodeConst.AUDIT);
        userQueryExtInfo.setMobile_tel(businessHandForm.getMobile_tel());
        userQueryExtInfoService.save(userQueryExtInfo);

        // 大字段更新
        Map<String, Object> contentMap = BeanMapUtil.beanToMap(businFlowRequest);
        contentMap.put(WskhFields.HAND_CLIENT_CATEGORY, businessHandForm.getClient_category());
        contentMap.put(WskhFields.HAND_AGENT_NAME, businessHandForm.getAgent_name());
        contentMap.put(WskhFields.HAND_AGENT_ID_KIND, businessHandForm.getAgent_id_kind());
        contentMap.put(WskhFields.HAND_AGENT_ID_NO, businessHandForm.getAgent_id_no());
        contentMap.put(WskhFields.HAND_AGENT_PHOTO_FRONT, convert.getAgent_photo_front());
        contentMap.put(WskhFields.HAND_AGENT_PHOTO_BACK, convert.getAgent_photo_back());
        contentMap.put(WskhFields.HAND_CERT_FILE_PATH, convert.getCert_file_path());
        contentMap.put(WskhFields.HAND_PHOTO_FRONT, convert.getPhoto_front());
        contentMap.put(WskhFields.HAND_PHOTO_BACK, convert.getPhoto_back());
        contentMap.put(WskhFields.HAND_MOBILE_TEL, businessHandForm.getMobile_tel());
        contentMap.put(Fields.VIDEO_TYPE, WskhConstant.VIDEO_TYPE_2);
        // 中登无公安照返回业务开关
        boolean open_result = PropertySource.getBoolean(PropKeyConstant.WSKH_CSDC_VERIFICATION_SWITCH);
        if (open_result) {
            contentMap.put(Fields.DATA_SIGN, "1");
        }
        businFlowParamsService.saveParams(businFlowRequest.getRequest_no(), contentMap);

        // 操作流水
        Map<String, Object> recordMap = Maps.newHashMap();
        recordMap.put(Fields.REQUEST_STATUS, businFlowRequest.getRequest_status());
        recordMap.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22114);
        recordMap.put(Fields.ANODE_ID, FlowNodeConst.START);
        recordMap.put(Fields.OPERATOR_NO, WskhConstant.SUPER_USER);
        recordMap.put(Fields.OPERATOR_NAME, WskhConstant.SUPER_USER);
        recordMap.put(Fields.BUSINESS_REMARK, "用户提交见证申请");
        businFlowRecordService.saveBusinFlowRecord(businFlowRequest, recordMap);

        // 加入智能审核缓存
        String submit_request_no = request_no;
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                redisTemplate.opsForList().leftPush(QueueConstant.SUBMIT_AIAUDIT_DISPATCH_QUEUE, submit_request_no);
            }
        });

        // 保存报表数据
        saveReportData(businFlowRequest, businFlowTask);

        return CreateResult.builder().task_id(businFlowTask.getTask_id()).busin_serial_no(businFlowRequest.getRequest_no()).build();
    }


    @Override
    public List<HallBackInfo> centralizedQuery(BusinessCentralizedQueryForm queryForm) {
        LambdaQueryWrapper<BusinFlowRequest> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(queryForm.getBusiness_apply_no())) {
            queryWrapper.eq(BusinFlowRequest::getBusiness_apply_no, queryForm.getBusiness_apply_no());
        } else {
            queryWrapper.eq(StringUtils.isNotBlank(queryForm.getId_kind()), BusinFlowRequest::getId_kind, queryForm.getId_kind());
            queryWrapper.eq(StringUtils.isNotBlank(queryForm.getId_no()), BusinFlowRequest::getId_no, queryForm.getId_no());
            queryWrapper.eq(StringUtils.isNotBlank(queryForm.getBranch_no()), BusinFlowRequest::getBranch_no, queryForm.getBranch_no());
            if (StringUtils.isNotBlank(queryForm.getBusin_type())) {
                String mapping_relationship = PropertySource.get(PropKeyConstant.WSKH_HAND_MAPPING_RELATIONSHIP, "");
                Map<String, String> relationshipMap = JSON.parseObject(mapping_relationship, Map.class);
                if (StringUtils.isBlank(relationshipMap.get(queryForm.getBusin_type()))){
                    queryWrapper.eq(BusinFlowRequest::getBusin_type,queryForm.getBusin_type());
                } else {
                    queryWrapper.in(BusinFlowRequest::getBusin_type, Arrays.stream(relationshipMap.get(queryForm.getBusin_type()).split(",")).collect(Collectors.toList()));
                }
            }
        }
        List<BusinFlowRequest> requestListList = businFlowRequestService.list(queryWrapper);
        if (StringUtils.isBlank(queryForm.getBusiness_apply_no()) && StringUtils.isNotBlank(queryForm.getClient_name())) {
             requestListList = requestListList.stream().filter(item->{
                if (StringUtils.contains(item.getClient_name(),Constant.SEMICOLON_SEPARATOR)) {
                    return Arrays.stream(item.getClient_name().split(Constant.SEMICOLON_SEPARATOR))
                            .anyMatch(ie -> StringUtils.equals(toHalfWidth(ie),toHalfWidth(queryForm.getClient_name())));
                }
                return StringUtils.equals(toHalfWidth(item.getClient_name()),toHalfWidth(queryForm.getClient_name()));
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(requestListList)) {
            return Lists.newArrayList();
        }
        List<String> requestNos = requestListList.stream().map(BusinFlowRequest::getRequest_no).collect(Collectors.toList());
        Map<String, String> filterMap = businFlowParamsService.list(new LambdaQueryWrapper<>(BusinFlowParams.class)
                        .in(BusinFlowParams::getRequest_no, requestNos))
                .stream()
                .collect(Collectors.groupingBy(BusinFlowParams::getRequest_no))
                .entrySet()
                .stream()
                .map(item -> {
                    StringBuilder param_content = new StringBuilder();
                    for (BusinFlowParams param : item.getValue()) {
                        param_content.append(param.getBusi_content());
                    }
                    String submit_content = param_content.toString();
                    VideoRequestDTO info = new VideoRequestDTO();
                    info.setFile_8A(JSON.parseObject(submit_content, Map.class).getOrDefault(WskhFields.FILE_8A, "").toString());
                    info.setRequest_no(item.getKey());
                    return info;
                }).collect(Collectors.toMap(key -> key.getRequest_no(), val -> val.getFile_8A(), (k, v) -> v));
        return requestListList.stream()
                .filter(item -> filterMap.containsKey(item.getRequest_no()))
                .map(e -> {
                    HallBackInfo hallBackInfo = new HallBackInfo();
                    hallBackInfo.setVideo_url(filterMap.get(e.getRequest_no()));
                    hallBackInfo.setClient_name(e.getClient_name());
                    hallBackInfo.setId_kind(e.getId_kind());
                    hallBackInfo.setId_no(e.getId_no());
                    hallBackInfo.setBusin_type(e.getBusin_type());
                    hallBackInfo.setBranch_no(e.getBranch_no());
                    hallBackInfo.setBusiness_apply_no(e.getBusiness_apply_no());
                    hallBackInfo.setVideo_status(getVideoStatus(e));
                    return hallBackInfo;
                }).collect(Collectors.toList());
    }

    /**
     * 获取任务状态
     * 3-通过
     * 4-驳回
     * 8-作废
     * 9-审核中
     */
    private String getVideoStatus(BusinFlowRequest request) {
        String requestStatus = request.getRequest_status();
        String anodeId = request.getAnode_id();
        if (TaskTypeEnum.AUDIT.getCode().equals(anodeId)) {
            return getAuditStatus(requestStatus);
        } else {
            return getNonAuditStatus(requestStatus);
        }
    }

    private String getAuditStatus(String requestStatus) {
        switch (requestStatus) {
            case FlowStatusConst.REQUEST_STATUS_AUDIT_FAIL:
            case FlowStatusConst.REQUEST_STATUS_INVALIDATE:
                return requestStatus;
            default:
                return FlowStatusConst.HAND_STATUS_AUDITING;
        }
    }

    private String getNonAuditStatus(String requestStatus) {
        switch (requestStatus) {
            case FlowStatusConst.REQUEST_STATUS_AUDIT_PASS:
            case FlowStatusConst.REQUEST_STATUS_AUDIT_FAIL:
            case FlowStatusConst.REQUEST_STATUS_INVALIDATE:
                return requestStatus;
            default:
                return FlowStatusConst.HAND_STATUS_AUDITING;
        }
    }

    private void saveReportData(BusinFlowRequest businFlowRequest, BusinFlowTask businFlowTask) {
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put(Fields.CLIENT_NAME, businFlowRequest.getClient_name());
        contentMap.put(Fields.ID_NO, businFlowRequest.getId_no());
        contentMap.put(Fields.ID_KIND, businFlowRequest.getId_kind());
        contentMap.put(Fields.BUSIN_TYPE, businFlowRequest.getBusin_type());
        // contentMap增加当前时间是否是工作时间、以及转换branch_no的名称
        contentMap.put(Fields.IS_WORK_TIME, componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_AUDIT_WORKING));
        // 见证申请时间
        contentMap.put(Fields.REQUEST_DATETIME, businFlowRequest.getRequest_datetime());
        String branchNo = MapUtils.getString(contentMap, Fields.BRANCH_NO);
        if (StringUtils.isBlank(branchNo)) {
            log.warn("创建任务request_no：{}时，查询大字段表branchNo为空！！！", businFlowRequest.getRequest_no());
            branchNo = businFlowRequest.getBranch_no();
            contentMap.put(Fields.BRANCH_NO, branchNo);
        }
        BranchInfo branchInfo = cacheBranch.getBranchByNo(branchNo);
        if (Objects.nonNull(branchInfo)) {
            String upBranchNo = StringUtils.isNotBlank(branchInfo.getUp_branch_no()) ? branchInfo.getUp_branch_no() : branchNo;
            contentMap.put(Fields.BRANCH_NAME, branchInfo.getBranch_name());
            contentMap.put(Fields.UP_BRANCH_NO, upBranchNo);
            BranchInfo upBranchInfo = cacheBranch.getBranchByNo(upBranchNo);
            contentMap.put(Fields.UP_BRANCH_NAME, Objects.nonNull(upBranchInfo) ? upBranchInfo.getBranch_name() : upBranchNo);
            //是否为托管分支机构
            contentMap.put(Fields.IS_BRANCH_MANAGED, Constant.BRANCH_MANAGED_Y);
        }
        // 保存参数到业务流程请求审核跟踪
        contentMap.put(Fields.MATCH_LABELS, getMatchLabelTypes(businFlowRequest.getMatch_labels()));
        businProcessRequestAuditTrailService.saveBusinProcessRequestAuditTrail(businFlowTask, contentMap);
    }


    private String getMatchLabelTypes(String matchLabels) {
        if (StringUtils.isBlank(matchLabels)) {
            return StrUtil.SPACE;
        }
        Map<String, Label> labelMap = labelService.getMapLabels();
        return Arrays.stream(matchLabels.split(StrUtil.COMMA))
                .map(e -> labelMap.get(e).getLabel_type())
                .collect(Collectors.joining(StrUtil.COMMA));
    }

    /**
     * 全角转半角
     * @param input
     * @return
     */
    private String toHalfWidth(String input) {
        if (input == null) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (c >= 65281 && c <= 65374) {
                // 全角字符转为半角字符
                c = (char) (c - 0xFEE0);
            } else if (c == 12288) {
                // 全角空格转为半角空格
                c = ' ';
            } else if (c >= 65296 && c <= 65305) {
                // 处理全角数字 ０-９ 的转换
                c = (char) (c - 0xFEE0);
            } else if ((c >= 65313 && c <= 65338) || (c >= 65345 && c <= 65370)) {
                // 处理全角大写字母 Ａ-Ｚ 和小写字母 ａ-ｚ 的转换
                c = (char) (c - 0xFEE0);
            }
            sb.append(c);
        }
        return sb.toString();
    }
}

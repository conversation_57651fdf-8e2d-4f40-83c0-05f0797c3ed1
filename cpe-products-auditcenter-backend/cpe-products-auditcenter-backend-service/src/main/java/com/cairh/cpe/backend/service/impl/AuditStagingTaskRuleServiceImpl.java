package com.cairh.cpe.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.StagingTaskRuleDetailResponse;
import com.cairh.cpe.backend.form.resp.StagingTaskRuleListResponse;
import com.cairh.cpe.backend.form.resp.StagingTaskRulePageResponse;
import com.cairh.cpe.backend.service.IAuditStagingTaskRuleService;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.StagingTaskRule;
import com.cairh.cpe.common.service.IStagingTaskRuleService;
import com.cairh.cpe.common.util.DateUtils;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.db.config.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/12 09:51
 */
@Slf4j
@Service
public class AuditStagingTaskRuleServiceImpl implements IAuditStagingTaskRuleService {

    @Resource
    private IStagingTaskRuleService stagingTaskRuleService;
    @Resource
    private IdGenerator idGenerator;

    @Override
    public Page<StagingTaskRulePageResponse> page(StagingTaskRulePageReq request) {
        Page<StagingTaskRule> page = new Page<>(request.getCur_page(), request.getPage_size());
        LambdaQueryWrapper<StagingTaskRule> wrapper = setQueryWrapper(request);
        Page<StagingTaskRule> rulePage = stagingTaskRuleService.page(page, wrapper);
        List<StagingTaskRulePageResponse> ruleList = rulePage
                .getRecords()
                .stream()
                .map(this::convertData)
                .collect(Collectors.toList());
        Page<StagingTaskRulePageResponse> responsePage = new Page<>(request.getCur_page(), request.getPage_size());
        responsePage.setRecords(ruleList);
        responsePage.setTotal(rulePage.getTotal());
        return responsePage;
    }

    @Override
    public List<StagingTaskRuleListResponse> list(StagingTaskRuleListReq request) {
        LambdaQueryWrapper<StagingTaskRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StagingTaskRule::getRule_name, StagingTaskRule::getRule_type, StagingTaskRule::getSerial_id);
        wrapper.eq(StagingTaskRule::getStatus, "1");
        wrapper.eq(StringUtils.isNotBlank(request.getRule_type()), StagingTaskRule::getRule_type, request.getRule_type());
        wrapper.orderByAsc(StagingTaskRule::getOrder_no);
        return stagingTaskRuleService.list(wrapper)
                .stream()
                .map(item -> new StagingTaskRuleListResponse().setSerial_id(item.getSerial_id()).setRule_name(item.getRule_name()))
                .collect(Collectors.toList());
    }

    @Override
    public StagingTaskRuleDetailResponse detail(String serial_id) {
        StagingTaskRule rule = stagingTaskRuleService.getById(serial_id);
        if (Objects.isNull(rule)) {
            log.error("[detail]暂存任务规则不存在，serial_id={}", serial_id);
            throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), ErrorEnum.DATA_NOT_EXIST.getDesc());
        }
        StagingTaskRuleDetailResponse response = new StagingTaskRuleDetailResponse();
        BeanUtils.copyProperties(rule, response);
        return response;
    }

    @Override
    public void addRule(BaseUser baseUser, StagingTaskRuleAddReq request) {
        stagingTaskRuleService.clearCache();
        Date date = new Date();
        StagingTaskRule stagingTaskRule = new StagingTaskRule();
        BeanUtils.copyProperties(request, stagingTaskRule);
        stagingTaskRule.setRule_datetime_start(DateUtils.parseDateStrictly(request.getRule_datetime_start(), DateUtils.DATE_TIME_FORMAT));
        stagingTaskRule.setRule_datetime_end(DateUtils.parseDateStrictly(request.getRule_datetime_end(), DateUtils.DATE_TIME_FORMAT));
        stagingTaskRule.setSerial_id(idGenerator.nextUUID(null));
        stagingTaskRule.setCreate_by(baseUser.getStaff_no());
        stagingTaskRule.setUpdate_by(baseUser.getStaff_no());
        stagingTaskRule.setCreate_datetime(date);
        stagingTaskRule.setUpdate_datetime(date);
        stagingTaskRule.setStatus(request.getStatus());
        stagingTaskRuleService.save(stagingTaskRule);
    }

    @Override
    public void editRule(BaseUser baseUser, StagingTaskRuleEditReq request) {
        StagingTaskRule stagingTaskRule = stagingTaskRuleService.getById(request.getSerial_id());
        if (Objects.isNull(stagingTaskRule)) {
            log.error("[editRule]暂存任务规则不存在，serial_id={}", request.getSerial_id());
            throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), ErrorEnum.DATA_NOT_EXIST.getDesc());
        }
        stagingTaskRuleService.clearCache();
        StagingTaskRule rule = new StagingTaskRule();
        BeanUtils.copyProperties(request, rule);
        rule.setRule_datetime_start(DateUtils.parseDateStrictly(request.getRule_datetime_start(), DateUtils.DATE_TIME_FORMAT));
        rule.setRule_datetime_end(DateUtils.parseDateStrictly(request.getRule_datetime_end(), DateUtils.DATE_TIME_FORMAT));
        LambdaUpdateWrapper<StagingTaskRule> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StagingTaskRule::getSerial_id, request.getSerial_id());
        rule.setUpdate_by(baseUser.getStaff_no());
        rule.setUpdate_datetime(new Date());
        stagingTaskRuleService.update(rule, updateWrapper);
    }

    @Override
    public void updateStatus(BaseUser baseUser, StagingTaskRuleStatusReq request) {
        StagingTaskRule stagingTaskRule = stagingTaskRuleService.getById(request.getSerial_id());
        if (Objects.isNull(stagingTaskRule)) {
            log.error("[updateStatus]暂存任务规则不存在，serial_id={}", request.getSerial_id());
            throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), ErrorEnum.DATA_NOT_EXIST.getDesc());
        }
        stagingTaskRuleService.clearCache();
        LambdaUpdateWrapper<StagingTaskRule> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StagingTaskRule::getSerial_id, request.getSerial_id());
        updateWrapper.set(StagingTaskRule::getStatus, request.getStatus());
        updateWrapper.set(StagingTaskRule::getUpdate_by, baseUser.getStaff_no());
        updateWrapper.set(StagingTaskRule::getUpdate_datetime, new Date());
        stagingTaskRuleService.update(updateWrapper);
    }

    @Override
    public void deleteRule(String serial_id) {
        StagingTaskRule stagingTaskRule = stagingTaskRuleService.getById(serial_id);
        if (Objects.isNull(stagingTaskRule)) {
            log.error("[deleteRule]暂存任务规则不存在，serial_id={}", serial_id);
            throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), ErrorEnum.DATA_NOT_EXIST.getDesc());
        }
        if (StringUtils.equals(stagingTaskRule.getStatus(), WskhConstant.AVAILABLE_STATUS)) {
            throw new BizException(ErrorEnum.DATA_STATUS_ERROR.getValue(), ErrorEnum.DATA_STATUS_ERROR.getDesc());
        }
        LambdaQueryWrapper<StagingTaskRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StagingTaskRule::getSerial_id, serial_id);
        stagingTaskRuleService.clearCache();
        stagingTaskRuleService.remove(wrapper);
    }


    private StagingTaskRulePageResponse convertData(StagingTaskRule rule) {
        StagingTaskRulePageResponse response = new StagingTaskRulePageResponse();
        BeanUtils.copyProperties(rule, response);
        return response;
    }

    private LambdaQueryWrapper<StagingTaskRule> setQueryWrapper(StagingTaskRulePageReq request) {
        LambdaQueryWrapper<StagingTaskRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(request.getRule_name()), StagingTaskRule::getRule_name, request.getRule_name());
        wrapper.eq(StringUtils.isNotBlank(request.getStatus()), StagingTaskRule::getStatus, request.getStatus());
        wrapper.ge(StringUtils.isNotBlank(request.getUpdate_datetime_start()), StagingTaskRule::getUpdate_datetime, DateUtils.parseDateStrictly(request.getUpdate_datetime_start(), DateUtils.DATE_TIME_FORMAT2));
        wrapper.le(StringUtils.isNotBlank(request.getUpdate_datetime_end()), StagingTaskRule::getUpdate_datetime, DateUtils.parseDateStrictly(request.getUpdate_datetime_end(), DateUtils.DATE_TIME_FORMAT2));
        return wrapper;
    }
}

package com.cairh.cpe.backend.form.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class IdCardImageRetryForm {

    @NotBlank(message = "业务流水号不能为空")
    private String request_no;

    private String operator_no;
    private String operator_name;


    private String file_6A;

    private String file_6B;

    private String file_7C;

    private String file_7D;

    /**
     * 机构的营业执照
     */
    private String file_Bm;

    /**
     * 境内机构出具的就业证明
     */
    private String file_7W;

    /**
     * 住宿登记证明表原件
     */
    private String file_7X;


    /**
     * 任务id
     */
    private String task_id;

}

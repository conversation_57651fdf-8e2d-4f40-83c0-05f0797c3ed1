package com.cairh.cpe.backend.service;

import com.cairh.cpe.backend.form.req.BusinessCentralizedQueryForm;
import com.cairh.cpe.backend.form.req.BusinessHandCreateForm;
import com.cairh.cpe.backend.form.resp.CreateResult;
import com.cairh.cpe.backend.form.resp.HallBackInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IHandApiService {

    /**
     * 网厅数据提交
     * @param businessHandForm
     * @return
     */
    CreateResult create(BusinessHandCreateForm businessHandForm);





    /**
     * 集中营运查询
     * @param queryForm
     * @return
     */
    List<HallBackInfo> centralizedQuery(BusinessCentralizedQueryForm queryForm);

}

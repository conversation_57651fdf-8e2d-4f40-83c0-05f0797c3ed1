package com.cairh.cpe.backend.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.backend.form.req.HandProfileImageRetryForm;
import com.cairh.cpe.backend.form.req.HandVerifyPoliceAllForm;
import com.cairh.cpe.backend.form.req.ProfileImageRetryForm;
import com.cairh.cpe.backend.form.resp.ProfileImageResp;
import com.cairh.cpe.backend.form.resp.ProfilePhotosResp;
import com.cairh.cpe.backend.form.resp.VerificationResult;
import com.cairh.cpe.backend.form.support.VerifyPoliceInfo;
import com.cairh.cpe.backend.service.IAuditDetailProfilePhotoService;
import com.cairh.cpe.backend.service.VerificationStrategy;
import com.cairh.cpe.businflow.entity.CustModifyRecord;
import com.cairh.cpe.businflow.service.ICustModifyRecordService;
import com.cairh.cpe.cache.component.ComponentWorkTimeService;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.backend.util.BaseBeanUtil;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.PoliceVerifyResult;
import com.cairh.cpe.common.entity.clob.UserIndependenceInfo;
import com.cairh.cpe.common.entity.request.AuditChangeForm;
import com.cairh.cpe.common.entity.request.VerifyPoliceAllRequest;
import com.cairh.cpe.common.entity.response.VerifyPoliceAllResult;
import com.cairh.cpe.common.entity.response.VerifyPoliceResp;
import com.cairh.cpe.common.service.IRequestFlowService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.AgeUtil;
import com.cairh.cpe.common.util.IdentifyUtils;
import com.cairh.cpe.common.util.KHDateUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;
import com.cairh.cpe.service.aiaudit.service.IAiAuditAchieveService;
import com.cairh.cpe.service.idverify.IComponentIdVerifyService;
import com.cairh.cpe.service.third.IElectThirdFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class AuditDetailProfilePhotoServiceImpl implements IAuditDetailProfilePhotoService {

    @Autowired
    private IRequestService requestService;
    @Autowired
    private ICustModifyRecordService custModifyRecordService;
    @Autowired
    private IAiAuditAchieveService aiAuditAchieveService;
    @Autowired
    private IComponentIdVerifyService componentIdVerifyService;
    @Autowired
    private ComponentWorkTimeService componentWorkTimeService;
    @Autowired
    private IRequestFlowService requestFlowService;
    @Resource
    private IElectThirdFileService electThirdFileService;
    @Resource
    private ApplicationContext applicationContext;

    /**
     * * 客户免冠照的重新识别
     *
     * @param profileImageRetryForm
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<AiAuditRuleResp> profileImageRetry(ProfileImageRetryForm profileImageRetryForm) {
        saveCustModifyRecord(profileImageRetryForm.getRequest_no(), profileImageRetryForm.getFile_80(), profileImageRetryForm.getFile_80_type(),
                profileImageRetryForm.getOperator_no(), profileImageRetryForm.getOperator_name(), profileImageRetryForm.getFile_6A(),
                profileImageRetryForm.getFile_6B(), profileImageRetryForm.getTask_id());
        return aiAuditAchieveService.againExecuteAiaudit(profileImageRetryForm.getRequest_no(), "3");
    }

    /**
     * 网厅业务办理-客户免冠照的重新识别
     *
     * @param profileImageRetryForm
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<AiAuditRuleResp> handProfileImageRetry(HandProfileImageRetryForm profileImageRetryForm) {
        saveCustModifyRecord(profileImageRetryForm.getRequest_no(), profileImageRetryForm.getFile_80(), profileImageRetryForm.getFile_80_type(),
                profileImageRetryForm.getOperator_no(), profileImageRetryForm.getOperator_name(), profileImageRetryForm.getFile_6A(),
                profileImageRetryForm.getFile_6B(), profileImageRetryForm.getTask_id());
        List<AiAuditRuleResp> aiAuditRuleResps = aiAuditAchieveService.handAgainExecuteAiaudit(profileImageRetryForm.getRequest_no(), "3");
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(profileImageRetryForm.getRequest_no());
        // 是否新数据标签
        if (!StringUtils.equals(clobContentInfo.getData_sign(), WskhConstant.DATA_SIGN_STATUS)) {
            // 中登时间
            if (componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_ZD_GA)) {
                // 获取大头照与证件信息匹配分数
                String face_score_82_80 = "0";
                if (CollectionUtils.isNotEmpty(aiAuditRuleResps)) {
                    AiAuditRuleResp result = aiAuditRuleResps
                            .stream()
                            .filter(aiAuditRuleResp -> "face_contrast_face_police".equals(aiAuditRuleResp.getDrl_rule_name()))
                            .findFirst().orElse(null);
                    if (result != null) {
                        face_score_82_80 = String.valueOf(result.getMatch_score());
                    }
                }
                Map<String, Object> params = new HashMap<>();
                params.put(Fields.BUSINESS_REMARK, "刷新人像比对结果！");
                params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22387);
                params.put(Fields.OPERATOR_NO, profileImageRetryForm.getOperator_no());
                params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
                params.put("face_score_82_80", face_score_82_80);
                requestFlowService.saveParamsRecord(profileImageRetryForm.getRequest_no(), params);
            }
        }
        return aiAuditRuleResps;
    }

    /**
     * 保存客户资料修改信息
     */
    private void saveCustModifyRecord(String request_no, String file_80, String file_80_type, String operator_no,
                                      String operator_name, String file_6A, String file_6B, String flow_task_id) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
        UserIndependenceInfo userIndependenceInfo_one = new UserIndependenceInfo();
        //判断客户免冠照是否改变
        if (!StringUtils.equals(clobContentInfo.getFile_80(), file_80)) {
            userIndependenceInfo_one.setFile_80(file_80);
            userIndependenceInfo_one.setUpload_80_type(file_80_type);
        }
        AuditChangeForm auditChangeForm = new AuditChangeForm();
        //填充参数。用来记录修改流水
        auditChangeForm.setRequest_no(request_no);
        auditChangeForm.setFlow_task_id(flow_task_id);
        auditChangeForm.setModify_link(clobContentInfo.getAnode_id());
        auditChangeForm.setOperator_no(operator_no);
        auditChangeForm.setOperator_name(operator_name);
        UserIndependenceInfo userIndependenceInfo = new UserIndependenceInfo();
        userIndependenceInfo.setFile_80(file_80);
        userIndependenceInfo.setFile_6A(file_6A);
        userIndependenceInfo.setFile_6B(file_6B);
        auditChangeForm.setIndependenceInfo_one(userIndependenceInfo_one);
        auditChangeForm.setIndependenceInfo(userIndependenceInfo);
        custModifyRecordService.saveParamsAndRecord(auditChangeForm);
    }

    /**
     * * 获取客户的大头照和公安照 证通分数
     *
     * @param request_no
     * @return
     */
    @Override
    public ProfileImageResp getProfileImageAndScore(String request_no) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
        ProfileImageResp profileImageResp = buildBaseProfileImageResp(clobContentInfo);

        // 计算年龄
        String businType = StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL);

        if (StringUtils.equals(businType, WskhConstant.BUSIN_TYPE_NORMAL)
                && !StringUtils.equals(IdKindEnum.FOREIGN_PREV_PERMIT.getCode(), clobContentInfo.getId_kind())) {
            // 港澳居民来往内地通行证or台湾居民来往大陆通行证
            if (StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                profileImageResp.setAge(AgeUtil.ageUtil(StringUtils.isNotBlank(clobContentInfo.getBirthday()) ? clobContentInfo.getBirthday() :
                        IdentifyUtils.getIdCardBirthDay(clobContentInfo.getId_card_info().getId_no())));
            } else {
                profileImageResp.setAge(AgeUtil.ageUtil(IdentifyUtils.getIdCardBirthDay(clobContentInfo.getId_card_info().getId_no())));
            }
        } else if (StringUtils.equals(businType, WskhConstant.CROSS_BUSIN_TYPE) &&
                StringUtils.isNotBlank(clobContentInfo.getBirthday())) {
            profileImageResp.setAge(AgeUtil.ageUtil(clobContentInfo.getBirthday()));
        }

        // 新数据标签
        boolean isHandleNewData = StringUtils.equals(clobContentInfo.getData_sign(), WskhConstant.DATA_SIGN_STATUS);
        // 普通开户100058&港澳台居住证&外国人永居证&港澳台通行证
        boolean isPermit = StringUtils.equals(WskhConstant.BUSIN_TYPE_NORMAL, clobContentInfo.getBusin_type()) &&
                StringUtils.equalsAny(clobContentInfo.getId_kind(),
                        IdKindEnum.GAT_RESIDENCE_PERMIT.getCode(),
                        IdKindEnum.GA_PASS_CARD.getCode(),
                        IdKindEnum.TAIWAN_PASS_CARD.getCode(),
                        IdKindEnum.FOREIGN_PREV_PERMIT.getCode());
        // 跨境理财通业务100100
        boolean isCross = StringUtils.equals(WskhConstant.CROSS_BUSIN_TYPE, clobContentInfo.getBusin_type());

        // 是否需要特殊处理人脸分数
        if (!isHandleNewData && !isPermit && !isCross) {
            return profileImageResp;
        }

        // 设置分数相关信息
        String faceScore = clobContentInfo.getFace_score();
        // 处理分数类型为1的情况
        if (StringUtils.equals(clobContentInfo.getScore_type(), "1")) {
            faceScore = clobContentInfo.getFace_score_82_80();
        }
        // 设置基本信息
        profileImageResp.setScore_type(clobContentInfo.getScore_type());
        profileImageResp.setWork_time(componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_ZD_GA) ? "1" : "2");
        profileImageResp.setFace_score(faceScore);
        // 设置结果类型
        profileImageResp.setResult_type(ResultTypeEnum.NOTCERTIFIED.getCode());
        // 检查是否有有效的分数
        if (!(StringUtils.isNotBlank(clobContentInfo.getFace_score()) ||
                (StringUtils.isNotBlank(clobContentInfo.getFace_score_82_80()) &&
                        !StringUtils.equals(String.valueOf(clobContentInfo.getFace_score_82_80()), "0.0")))) {
            return profileImageResp;
        }
        if (isPermit || StringUtils.isNotBlank(clobContentInfo.getFace_score())) {
            setZhengTongScore(clobContentInfo.getFace_score(), profileImageResp);
        } else if (StringUtils.isNotBlank(clobContentInfo.getFace_score_82_80())) {
            setFaceScore(clobContentInfo.getFace_score_82_80(), profileImageResp);
        }
        return profileImageResp;
    }

    /**
     * * 查询客户的历史头像
     *
     * @param request_no
     * @return
     */
    @Override
    public List<ProfilePhotosResp> queryHisProfileImage(String request_no) {
        List<ProfilePhotosResp> photosList = new ArrayList<>();
        LambdaQueryWrapper<CustModifyRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustModifyRecord::getRequest_no, request_no)
                .eq(CustModifyRecord::getModify_item, "file_80")
                .orderByAsc(CustModifyRecord::getModify_datetime);
        List<CustModifyRecord> list = custModifyRecordService.list(wrapper);
        for (CustModifyRecord modifyRecord : list) {
            ProfilePhotosResp resp = new ProfilePhotosResp();
            resp.setModify_datetime(KHDateUtil.formatDate(modifyRecord.getModify_datetime(), KHDateUtil.DATE_TIME_FORMAT));
            resp.setFile_80(modifyRecord.getModify_aftercontent());
            photosList.add(resp);
        }
        return photosList;
    }

    /**
     * 公安照刷新
     */
    @Override
    public VerifyPoliceResp refreshPolicePhoto(VerifyPoliceInfo verifyPoliceInfo) {
        String requestNo = verifyPoliceInfo.getRequest_no();
        ClobContentInfo clob = requestService.getAllDataByRequestNo(requestNo);

        // 是否中登时间
        boolean workTime = componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_ZD_GA);
        // 是否新数据标签
        boolean isNewDataSign = StringUtils.equals(clob.getData_sign(), WskhConstant.DATA_SIGN_STATUS);

        // 获取对应的验证策略
        VerificationStrategy strategy = getVerificationStrategy(clob, isNewDataSign, workTime);

        // 执行验证并获取结果
        VerificationResult result = strategy.verify(clob, verifyPoliceInfo, isNewDataSign, workTime);

        // 保存结果
        if (result.getResponse() != null) {
            saveVerificationResult(clob, verifyPoliceInfo.getStaff_no(), requestNo,
                    result.getResponse(), result.isWorkTime(), result.isNewDataSign());
            result.getResponse().setWork_time(result.isWorkTime() ? "1" : "2");
        }

        return result.getResponse();
    }

    /**
     * 全要素验证并获取证通分
     * 双向视频&网厅业务办理
     */
    @Override
    public VerifyPoliceAllResult handVerifyPoliceAll(HandVerifyPoliceAllForm handVerifyPoliceAllForm) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(handVerifyPoliceAllForm.getRequest_no());
        return StringUtils.equals(clobContentInfo.getData_sign(), WskhConstant.DATA_SIGN_STATUS) ?
                newHandleVerifyPoliceAll(handVerifyPoliceAllForm, clobContentInfo) :
                oldHandleVerifyPoliceAll(handVerifyPoliceAllForm, clobContentInfo);
    }

    /**
     * 新数据三要素查询获取证通分
     */
    private VerifyPoliceAllResult newHandleVerifyPoliceAll(HandVerifyPoliceAllForm handVerifyPoliceAllForm, ClobContentInfo clobContentInfo) {
        boolean workTime = componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_ZD_GA);
        VerifyPoliceAllRequest req = new VerifyPoliceAllRequest();
        req.setOrgan_flag(WskhConstant.ORGAN_FLAG);
        req.setRealtime_flag("1");
        req.setBranch_no(clobContentInfo.getBranch_no());
        req.setId_kind(clobContentInfo.getId_kind());
        req.setId_no(clobContentInfo.getId_no());
        req.setFull_name(clobContentInfo.getClient_name());
        if (StringUtils.equalsAny(clobContentInfo.getClient_category(), ClientCategoryEnum.CLIENT_INSTITUTION.getCode(), ClientCategoryEnum.CLIENT_PRODUCT.getCode())) {
            req.setId_kind(clobContentInfo.getAgent_id_kind());
            req.setId_no(clobContentInfo.getAgent_id_no());
            req.setFull_name(clobContentInfo.getAgent_name());
        }
        req.setBase64_image(electThirdFileService.getFileThirdId(clobContentInfo.getFile_80(), req.getId_no()));
        req.setCsdc_busi_kind(workTime ? "12" : "1");
        req.setOper_flag("3");

        VerifyPoliceAllResult verifyPoliceAllResult = componentIdVerifyService.verifyPoliceAll(req);
        log.info("handVerifyPoliceAll无公安照返回结果: {}", JSON.toJSONString(verifyPoliceAllResult));

        if (Objects.nonNull(verifyPoliceAllResult)) {
            if (StringUtils.equals(verifyPoliceAllResult.getStatus(), "2")) {
                verifyPoliceAllResult = exceptionHandle();
            }

            String score = WskhConstant.FACE_SCORE_FAIL;
            if (workTime && "1".equals(verifyPoliceAllResult.getStatus())) {
                score = WskhConstant.FACE_SCORE_SUCCESS;
            }
            verifyPoliceAllResult.setScore(score);

            VerifyPoliceResp resp = new VerifyPoliceResp();
            BaseBeanUtil.copyProperties(verifyPoliceAllResult, resp);
            saveVerificationResult(clobContentInfo, WskhConstant.SUPER_USER, handVerifyPoliceAllForm.getRequest_no(), resp, workTime, true);
        }
        return verifyPoliceAllResult;
    }

    /**
     * 老数据三要素查询获取证通分
     */
    private VerifyPoliceAllResult oldHandleVerifyPoliceAll(HandVerifyPoliceAllForm handVerifyPoliceAllForm, ClobContentInfo clobContentInfo) {
        // 获取证通分
        boolean isCheckWorkTime = PropertySource.getBoolean(PropKeyConstant.WSKH_SCORE_DEPEND_ZDGA_TIME_CONFIG)
                || !componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_ZD_GA);
        // 非身份证或者不在工作时间内,直接返回null
        if (!StringUtils.equalsAny(IdKindEnum.ID_CARD.getCode(), clobContentInfo.getId_kind(), clobContentInfo.getAgent_id_kind())
                || !isCheckWorkTime) {
            return null;
        }

        VerifyPoliceAllRequest req = new VerifyPoliceAllRequest();
        req.setOrgan_flag(WskhConstant.ORGAN_FLAG);
        req.setRealtime_flag("1");
        req.setBranch_no(clobContentInfo.getBranch_no());
        req.setId_kind(clobContentInfo.getId_kind());
        req.setId_no(clobContentInfo.getId_no());
        req.setFull_name(clobContentInfo.getClient_name());
        // 机构客户或产品客户使用代理人信息
        if (StringUtils.equalsAny(clobContentInfo.getClient_category(), ClientCategoryEnum.CLIENT_INSTITUTION.getCode(), ClientCategoryEnum.CLIENT_PRODUCT.getCode())) {
            req.setId_kind(clobContentInfo.getAgent_id_kind());
            req.setId_no(clobContentInfo.getAgent_id_no());
            req.setFull_name(clobContentInfo.getAgent_name());
        }
        req.setBase64_image(electThirdFileService.getFileThirdId(handVerifyPoliceAllForm.getFile_80(), req.getId_no()));

        // 通过三要素查询获取证通分
        VerifyPoliceAllResult verifyPoliceAllResult = componentIdVerifyService.verifyPoliceAll(req);
        log.info("handVerifyPoliceAll三要素查询获取证通分结果: {}", verifyPoliceAllResult);
        if (Objects.nonNull(verifyPoliceAllResult)) {
            if (StringUtils.equals(verifyPoliceAllResult.getStatus(), "2")) {
                verifyPoliceAllResult = exceptionHandle();
            }
            Map<String, Object> params = new HashMap<>();
            params.put(Fields.BUSINESS_REMARK, "证通分获取公安认证：" + verifyPoliceAllResult.getResult_info());
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22387);
            params.put(Fields.OPERATOR_NO, WskhConstant.SUPER_USER);
            params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
            params.put("face_score", verifyPoliceAllResult.getScore());
            params.put("face_score_desc", verifyPoliceAllResult.getResult_info());
            requestFlowService.saveParamsRecord(handVerifyPoliceAllForm.getRequest_no(), params);

            // 智能审核-auditFace
            aiAuditAchieveService.handAgainExecuteAiaudit(handVerifyPoliceAllForm.getRequest_no(), "3");
        }
        return verifyPoliceAllResult;
    }

    private VerifyPoliceAllResult exceptionHandle() {
        return new VerifyPoliceAllResult()
                .setStatus("0")
                .setResult_info("不一致")
                .setScore("1");
    }

    private void saveVerificationResult(ClobContentInfo clob, String staff_no, String request_no, VerifyPoliceResp resp, boolean workTime,
                                        boolean data_sign) {
        // 状态为2时抛出异常
        if (StringUtils.equals(resp.getStatus(), "2")) {
            throw new BizException("-1", resp.getResult_info());
        }

        // 构建认证结果
        PoliceVerifyResult result = new PoliceVerifyResult();
        result.setVerify_status("1".equals(resp.getStatus()) ? "8" : "9");
        result.setVerify_result(resp.getResult_info());
        result.setVerify_score(resp.getScore());

        // 构建参数Map
        Map<String, Object> params = new HashMap<>();

        // 处理照片信息
        if (StringUtils.isNotBlank(resp.getImage_data())) {
            String filerecordId = resp.getFilerecord_id();
            result.setIs_have_photo("1");
            result.setFile_82(filerecordId);
            params.put(Fields.FILE_82, filerecordId);
        }

        // 添加基础验证参数
        params.putAll(buildVerificationParams(staff_no, resp, result, workTime, data_sign));

        // 处理普通业务类型的人脸分数
        boolean isNormalBusiness = StringUtils.equals(WskhConstant.BUSIN_TYPE_NORMAL, clob.getBusin_type());
        boolean isVideoType2 = StringUtils.equals(WskhConstant.VIDEO_TYPE_2, clob.getVideo_type());
        boolean isForeignPermit = StringUtils.equals(clob.getId_kind(), IdKindEnum.FOREIGN_PREV_PERMIT.getCode());
        // 非中登时间&身份证类型数据，需要更新分数
        boolean isIdCardNoWorkTime = !workTime && StringUtils.equals(clob.getId_kind(), IdKindEnum.ID_CARD.getCode());

        if (isNormalBusiness && (isVideoType2 || isForeignPermit || isIdCardNoWorkTime)) {
            params.put(Fields.FACE_SCORE, resp.getScore());
            params.put(Fields.FACE_SCORE_DESC, resp.getResult_info());
        }

        // 处理外国人永居证特殊信息
        if (isForeignPermit) {
            params.put(Fields.PERMANENT_ID_INFO_CHECK_RESULT, clob.getPermanent_id_info_check_result());
        }

        // 保存参数记录
        requestFlowService.saveParamsRecord(request_no, params);
    }

    private Map<String, Object> buildVerificationParams(String staff_no, VerifyPoliceResp resp, PoliceVerifyResult policeVerifyResult,
                                                        boolean workTime, boolean data_sign) {
        Map<String, Object> params = new HashMap<>(10);
        // 基础参数
        params.put(Fields.BUSINESS_REMARK, "公安认证：" + resp.getResult_info());
        params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22387);
        params.put(Fields.OPERATOR_NO, staff_no);
        params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
        params.put(Fields.POLICE_RESULT, policeVerifyResult);

        // 新数据标签处理
        if (data_sign) {
            String score = resp.getScore();
            String scoreType = workTime ? "1" : "2";
            String scoreCode = workTime ? Fields.FACE_SCORE_82_80 : Fields.FACE_SCORE;

            params.put(Fields.FACE_SCORE, "");
            params.put(Fields.FACE_SCORE_82_80, "");
            params.put(Fields.SCORE_TYPE, scoreType);
            params.put(Fields.FACE_SCORE_DESC, resp.getResult_info());

            if (StringUtils.isNotEmpty(score)) {
                params.put(scoreCode, score);
            }
        }

        return params;
    }

    /**
     * 构建基础的ProfileImageResp对象
     */
    private ProfileImageResp buildBaseProfileImageResp(ClobContentInfo clobContentInfo) {
        return new ProfileImageResp()
                .setFile_82(clobContentInfo.getFile_82())
                .setFile_80(clobContentInfo.getFile_80())
                .setFace_score_desc(clobContentInfo.getFace_score_desc())
                .setFace_score(clobContentInfo.getFace_score())
                .setData_sign(clobContentInfo.getData_sign());
    }

    /**
     * 设置人脸比对分数结果
     */
    private void setFaceScore(String faceScore, ProfileImageResp profileImageResp) {
        if (StringUtils.isBlank(faceScore)) {
            return;
        }

        try {
            int score = new BigDecimal(faceScore).intValue();
            profileImageResp.setResult_type(score <= 1 ? ResultTypeEnum.INCONSISTENCY.getCode() :
                    score >= 100 ? ResultTypeEnum.CONSISTENCY.getCode() :
                            profileImageResp.getResult_type());
        } catch (NumberFormatException e) {
            log.warn("人脸比对分数转换异常, faceScore:{}", faceScore);
            throw new BizException("-1", "人脸比对分数转换异常");
        }
    }

    /**
     * 设置证通分数结果
     */
    private void setZhengTongScore(String faceScore, ProfileImageResp profileImageResp) {
        try {
            String[] scoreRange = PropertySource.get(PropKeyConstant.WSKH_ZT_SCORE_CONFIDENCE_RANGE, "40,60").split(",");
            int score = new BigDecimal(faceScore).intValue();
            int minScore = Integer.parseInt(scoreRange[0]);
            int maxScore = Integer.parseInt(scoreRange[1]);

            profileImageResp.setResult_type(score < minScore ? ResultTypeEnum.INCONSISTENCY.getCode() :
                            score >= maxScore ? ResultTypeEnum.CONSISTENCY.getCode() :
                                    ResultTypeEnum.TOBECONFIRMED.getCode())
                    .setScore_type("2");
        } catch (Exception e) {
            log.warn("证通分数转换异常, faceScore:{}", faceScore);
            throw new BizException("-1", "证通分数转换异常");
        }
    }

    /**
     * 策略工厂方法
     */
    private VerificationStrategy getVerificationStrategy(ClobContentInfo clob, boolean isNewDataSign, boolean workTime) {
        // 业务类型
        // 普通业务类型100058
        boolean isBusinTypeNormal = StringUtils.equals(WskhConstant.BUSIN_TYPE_NORMAL, clob.getBusin_type());
        // 跨境业务类型100100
        boolean isCross = StringUtils.equals(WskhConstant.CROSS_BUSIN_TYPE, clob.getBusin_type());

        // 证件类型
        // 外国人永居证
        boolean isForeignPrevPermit = StringUtils.equals(IdKindEnum.FOREIGN_PREV_PERMIT.getCode(), clob.getId_kind());
        // 台湾居民来往大陆通行证
        boolean isTaiwanPassCard = StringUtils.equals(IdKindEnum.TAIWAN_PASS_CARD.getCode(), clob.getId_kind());
        // 港澳居民来往内地通行证
        boolean isGaPassCard = StringUtils.equals(IdKindEnum.GA_PASS_CARD.getCode(), clob.getId_kind());
        // 港澳台居民居住证
        boolean isGatResidencePermit = StringUtils.equals(IdKindEnum.GAT_RESIDENCE_PERMIT.getCode(), clob.getId_kind());
        // 身份证
        boolean isIdCard = StringUtils.equals(IdKindEnum.ID_CARD.getCode(), clob.getId_kind());

        // 视频类型
        // 双向视频
        boolean isVideoType2 = StringUtils.equals(WskhConstant.VIDEO_TYPE_2, clob.getVideo_type());

        // 外国人永居证 or 港澳台往来通行证 or 跨境业务
        if (isBusinTypeNormal && (isForeignPrevPermit || isTaiwanPassCard || isGaPassCard)
                || isCross) {
            return applicationContext.getBean(VerifyPassPortVerificationStrategy.class);
        }

        // 港澳台居民居住证 or 新数据标签 or 非工作时间且标准业务&身份证or双向视频
        if (isBusinTypeNormal && isGatResidencePermit
                || isNewDataSign
                || !workTime && isBusinTypeNormal && (isIdCard || isVideoType2)) {
            return applicationContext.getBean(VerifyPoliceAllVerificationStrategy.class);
        }

        // 其他标准业务
        return applicationContext.getBean(VerifyPoliceVerificationStrategy.class);
    }
}

package com.cairh.cpe.backend.task.call;

import com.alibaba.fastjson.annotation.JSONField;
import com.cairh.cpe.backend.task.call.CallBackMqOperator.CallBackMsg;
import com.cairh.cpe.mq.operator.AbstractP2PMQOperator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */

@Component
public class CallBackMqOperator extends AbstractP2PMQOperator<CallBackMsg> {


    @Getter
    @Value("${cpe.topic.callBackMqOperator:G_P_USER_DX2EM}")
    private String destinationName;


    @Data
    @AllArgsConstructor
    public static class CallBackMsg implements Serializable {

        @JSONField(name = "SRC")
        private String SRC;
        @JSONField(name = "MSG_TYPE")
        private String MSG_TYPE ;
        @JSONField(name = "MSG_SUBTYPE")
        private String MSG_SUBTYPE ;
        @J<PERSON><PERSON><PERSON>(name = "SEND_DATE")
        private Integer SEND_DATE;
        @JSONField(name = "SEND_TIME")
        private Integer SEND_TIME;
        @JSONField(name = "MSG_ID")
        private String MSG_ID ;
        @JSONField(name = "TARGET")
        private List<String> TARGET ;
        @JSONField(name = "CONTENT")
        private Object CONTENT;


        @Getter
        @Setter
        public static class MsgContent {

            /**
             * 业务标识
             */
            @JSONField(name = "bizId")
            private String bizId;
            /**
             * 呼叫标识
             */
            @JSONField(name = "callId")
            private String callId;
            /**
             * 业务类型： 1、移动端呼出类型2、PC呼出3 、客户回呼类型
             */
            @JSONField(name = "bizType")
            private Integer bizType;

            /**
             *  挂断方 0 经理挂断 1 客户挂断
             */
            @JSONField(name = "callDuration")
            private Integer callDuration;
            /**
             * 被叫创建时间
             */
            @JSONField(name = "calleeCreatedEpoch")
            private String calleeCreatedEpoch;

            /**
             * 被叫振铃时间
             */
            @JSONField(name = "calleeRingingEpoch")
            private String calleeRingingEpoch;


            /**
             * 被叫接听时间
             */
            @JSONField(name = "calleeAnswerEpoch")
            private String calleeAnswerEpoch;


            /**
             * 被叫挂断时间
             */
            @JSONField(name = "calleeHangupEpoch")
            private String calleeHangupEpoch;

            /**
             * 录音文件路径（ 保存的相对路径）
             */
            @JSONField(name = "recordingFileName")
            private String recordingFileName;
            /**
             * 拨打被叫号码时外显号码/外显号 95 或虚拟短号
             */
            @JSONField(name = "outboundCalleeNumber")
            private String outboundCalleeNumber;

            /**
             * 被叫手机号码 客户手机号码
             */
            @JSONField(name = "calleeNumber")
            private String calleeNumber;

            /**
             * 客户代码
             */
            @JSONField(name = "customerid")
            private String customerid;


            /**
             * 呼叫业务 flowTaskID
             */
            @JSONField(name = "callbizid")
            private String callbizid;

            /**
             * 呼叫业务 SPJZ
             */
            @JSONField(name = "callbizkey")
            private String callbizkey;

            /**
             * 扩展留着备用
             */
            @JSONField(name = "extstr")
            private String extstr;


            /**
             * 主叫号码   null
             */
            @JSONField(name = "callNumber")
            private String callNumber;

            /**
             *  挂断方 0 经理挂断 1 客户挂断 -0
             */
            @JSONField(name = "hangUpSide")
            private Integer hangUpSide;


            /**
             *  是否接通 0 未接通 1 接通 - 1
             */
            @JSONField(name = "isAnswer")
            private Integer isAnswer;

            /**
             * 部门号 2
             */
            @JSONField(name = "deptId")
            private Integer deptId;

        }


    }

}

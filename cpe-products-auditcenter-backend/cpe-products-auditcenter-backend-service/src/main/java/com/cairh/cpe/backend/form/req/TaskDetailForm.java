package com.cairh.cpe.backend.form.req;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper=false)
public class TaskDetailForm extends PageInfo  {


    private String operator_no;

    private String operator_name;

    private String user_role;

    private String task_types_role;

    /**
     * 客户姓名
     */
    private String client_name;


    /**
     * 手机号码
     */
    private String mobile_tel;

    /**
     * 证件类型
     */
    private String id_kind;


    /**
     * 证件号码
     */
    private String id_no;


    /**
     * 渠道名称
     */
    private String channel_name;


    /**
     * 活动码
     */
    private String activity_no;


    /**
     * 营销团队
     */
    private String marketing_team;


    /**
     * 见证人姓名
     */
    private String audit_operator_name;


    /**
     * 见证人工号
     */
    private String audit_operator_no;


    /**
     * 复核人姓名
     */
    private String review_operator_name;

    /**
     * 复核人工号
     */
    private String review_operator_no;

    /**
     * 开户申请提交开始时间
     */
    private String request_datetime_start;


    /**
     * 开户申请提交结束时间
     */
    private String request_datetime_end;

    /**
     * 视频见证方式
     */
    private String video_type;

    /**
     * 业务类型
     */
    private String busin_type;


    /**
     * 所属分公司
     */
    private String company_name;

    /**
     * 所属分公司
     */
    private List<String> company_names;

    /**
     * 所属营业部
     */
    private String branch_no;

    /**
     * 所属营业部
     */
    private List<String> branch_nos;

    /**
     * 任务类型
     */
    private String task_type;

    /**
     * 任务类型
     */
    private List<String> task_types;

    /**
     * 任务状态
     */
    private String task_status;

    /**
     * 任务状态
     */
    private List<String> task_status_list;


    /**
     * 可操作营业部
     */
    private List<String> operator_branch;



    /**
     * 是否开启智能派单（0 关闭 1 开启）
     */
    private String isOpenDispatch;


    /**
     * 1 姓名 2 手机号码 3身份证号
     */
    private Integer queryType;

    /**
     * 手动认领查询关键词
     */
    private String queryKeyWord;


}

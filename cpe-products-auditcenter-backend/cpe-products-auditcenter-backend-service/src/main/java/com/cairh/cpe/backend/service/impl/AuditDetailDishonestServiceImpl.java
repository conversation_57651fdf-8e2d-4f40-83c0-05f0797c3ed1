package com.cairh.cpe.backend.service.impl;

import com.cairh.cpe.backend.form.req.DishonestForm;
import com.cairh.cpe.backend.form.req.UploadDishonestImageForm;
import com.cairh.cpe.backend.form.req.UploadRcpMisRequest;
import com.cairh.cpe.backend.form.resp.DishonestDetail;
import com.cairh.cpe.backend.form.resp.DishonestResp;
import com.cairh.cpe.backend.service.IAuditDetailDishonestService;
import com.cairh.cpe.businflow.service.ICustModifyRecordService;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.UserBaseInfo;
import com.cairh.cpe.common.entity.clob.UserRpcInfo;
import com.cairh.cpe.common.entity.request.AuditChangeForm;
import com.cairh.cpe.common.entity.response.DictInfo;
import com.cairh.cpe.common.entity.response.QueryDishonestResult;
import com.cairh.cpe.common.service.IRequestFlowService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.service.aiaudit.service.IAiAuditAchieveService;
import com.cairh.cpe.service.archive.service.IArchiveService;
import com.cairh.cpe.service.idverify.IDealRpaRecordService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AuditDetailDishonestServiceImpl implements IAuditDetailDishonestService {

    @Autowired
    private IRequestService requestService;
    @Autowired
    private IRequestFlowService requestFlowService;
    @Autowired
    private RedissonUtil redissonUtil;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IDealRpaRecordService iDealRpaRecordService;
    @Autowired
    private IArchiveService archiveService;
    @Autowired
    private CacheDict cacheDict;
    @Resource
    private IAiAuditAchieveService aiAuditAchieveService;
    @Autowired
    private ICustModifyRecordService custModifyRecordService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> uploadDishonestImage(UploadDishonestImageForm uploadDishonestImageForm) {
        HashMap<String, Object> resultMap = new HashMap<>();
        String lockKey = String.format(LockKeyConstant.WSKH_LOCK_TASK_DISHONEST_ID, uploadDishonestImageForm.getRequest_no());
        try {
            // 获取锁
            RLock lock = redissonClient.getLock(lockKey);
            if (lock.isLocked()) {
                log.info("上传失信记录图片未获取到锁[{}]", uploadDishonestImageForm.getRequest_no());
                resultMap.put(Fields.ERROR_NO, "-1");
                resultMap.put(Fields.ERROR_INFO, "当前无法上传图片，请等待");
                return resultMap;
            } else {
                redissonUtil.tryLock(lockKey, 30, 180, TimeUnit.SECONDS);
            }

            String file_record_id = uploadDishonestImageForm.getFile_record_id();
            // 将结果同步至大字段表
            Map<String, Object> params = new HashMap<>();
            params.put(Fields.OPERATOR_NO, uploadDishonestImageForm.getOperator_no());
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22388);
            params.put(Fields.BUSINESS_REMARK, "操作员手动上传失信记录图片");
            params.put(WskhFields.DISHONEST_ID, file_record_id);
            resultMap.put(WskhFields.DISHONEST_ID, file_record_id);
            requestFlowService.saveParamsRecord(uploadDishonestImageForm.getRequest_no(), params);
        } finally {
            redissonUtil.unlock(lockKey);
        }

        return resultMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void modifyDishonestRecordAndRemark(DishonestForm dishonestForm) {
        String request_no = dishonestForm.getRequest_no();
        String lockKey = String.format(LockKeyConstant.WSKH_LOCK_TASK_DISHONEST_ID, request_no);
        boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
        if (!isLock) {
            log.info("modifyDishonestRecordAndRemark未获取到锁，request_no={}", request_no);
            return;
        }
        try {
            AuditChangeForm auditChangeForm = new AuditChangeForm();
            auditChangeForm.setRequest_no(request_no);
            auditChangeForm.setFlow_task_id(dishonestForm.getTask_id());
            auditChangeForm.setOperator_no(dishonestForm.getOperator_no());
            auditChangeForm.setOperator_name(dishonestForm.getOperator_name());
            UserBaseInfo user_base_info = new UserBaseInfo();
            user_base_info.setDishonest_record(dishonestForm.getDishonest_record());
            user_base_info.setDishonest_record_remark(dishonestForm.getDishonest_record_remark());
            auditChangeForm.setUser_base_info(user_base_info);
            custModifyRecordService.saveParamsAndRecord(auditChangeForm);
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    /**
     * 查询失信记录的所有信息
     */
    @Override
    public DishonestResp getDishonestRecord(String request_no) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
        DishonestResp dishonestResp = new DishonestResp();
        dishonestResp.setDishonest_id(clobContentInfo.getDishonest_id());
        dishonestResp.setDishonest_record(clobContentInfo.getDishonest_record());
        dishonestResp.setDishonest_content(clobContentInfo.getDishonest_content());
        dishonestResp.setDishonest_record_remark(clobContentInfo.getDishonest_record_remark());
        dishonestResp.setRpc_file_id(clobContentInfo.getRpc_file_id());
        dishonestResp.setRpc_remark(clobContentInfo.getRpc_remark());
        dishonestResp.setRpc_option(clobContentInfo.getRpc_option());
        if (StringUtils.isNotBlank(clobContentInfo.getDishonest_content())) {
            List<DictInfo> dictListByDictCode = cacheDict.getDictListByDictCode(DicConstant.DISHONEST_RECORD);
            List<String> collect = dictListByDictCode.stream().map(DictInfo::getSub_code).collect(Collectors.toList());
            dishonestResp.setDishonest_record_list(collect);
        }
        List<DictInfo> dictListByDictCode = cacheDict.getDictListByDictCode(DicConstant.INITIAL_INVESTMENT_AMOUNT);
        String initial_investment_amount = clobContentInfo.getInitial_investment_amount();
        if (StringUtils.isNotEmpty(initial_investment_amount)) {
            for (DictInfo dictInfo : dictListByDictCode) {
                if (dictInfo.getSub_code().equals(initial_investment_amount)) {
                    initial_investment_amount = dictInfo.getSub_name();
                    break;
                }
            }
        }
        dishonestResp.setInitial_investment_amount(initial_investment_amount);
        return dishonestResp;
    }

    @Override
    public QueryDishonestResult getSecurityAlisaRpa(String request_no, String operator_no) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
        QueryDishonestResult queryDishonestResult = aiAuditAchieveService.getSecurityAlisaRpa(request_no, clobContentInfo.getClient_name(), clobContentInfo.getId_no());
        boolean invokeCrhRpa = false;
        if (StringUtils.isNotBlank(queryDishonestResult.getResult_info_gt())) {
            invokeCrhRpa = true;
        }
        String result = PropertySource.get(PropKeyConstant.WSKH_CRHRPA_MODEL_CONFIG, "1");
        if (invokeCrhRpa && StringUtils.equals(result, "1")) {
            if (StringUtils.equals(PropertySource.get(PropKeyConstant.WSKH_ISOPEN_CRHRPA, "0"), "1")) {
                DishonestResp dishonestResp = getCrhDishonestImage(request_no);
                if (StringUtils.isNotBlank(dishonestResp.getError_info())) {
                    queryDishonestResult.setResult_info_rpa(dishonestResp.getError_info());
                } else {
                    queryDishonestResult.setDishonest_id(dishonestResp.getDishonest_id());
                }
            } else {
                queryDishonestResult.setResult_info_rpa("请自动进入网址");
            }
        }
        queryDishonestResult.setDishonest_record_list(Lists.newArrayList("0", "1", "2", "3", "4", "5"));
        return queryDishonestResult;
    }

    @Override
    public DishonestResp getCrhDishonestImage(String request_no) {
        DishonestResp dishonestResp = new DishonestResp();
        String error_info = iDealRpaRecordService.dealUserRpaRecord(request_no);
        if (StringUtils.isNotBlank(error_info)) {
            dishonestResp.setError_info(error_info);
        } else {
            ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
            dishonestResp.setDishonest_id(clobContentInfo.getDishonest_id());
        }
        dishonestResp.setDishonest_record_list(Lists.newArrayList("0", "1", "2", "3", "4", "5"));
        return dishonestResp;
    }

    @Override
    public DishonestDetail queryDetailDishonestContent(String dishonest_content) {
        DishonestDetail dishonestDetail = new DishonestDetail();
        try {
            byte[] bytes = archiveService.electDownloadFile(dishonest_content);
            String content = new String(bytes, "utf-8");
            dishonestDetail.setContent(content);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            throw new BizException("-9997", "文件读取异常");
        }
        return dishonestDetail;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void uploadRpcMisInfo(BaseUser baseUser, UploadRcpMisRequest uploadRcpMisRequest) {
        AuditChangeForm auditChangeForm = new AuditChangeForm();
        auditChangeForm.setRequest_no(uploadRcpMisRequest.getRequest_no());
        auditChangeForm.setFlow_task_id(uploadRcpMisRequest.getTask_id());
        auditChangeForm.setOperator_no(baseUser.getStaff_no());
        auditChangeForm.setOperator_name(baseUser.getUser_name());
        UserRpcInfo user_rpc_info = new UserRpcInfo();
        user_rpc_info.setRpc_file_id(uploadRcpMisRequest.getRpc_file_id());
        user_rpc_info.setRpc_option(uploadRcpMisRequest.getRpc_option());
        user_rpc_info.setRpc_remark(uploadRcpMisRequest.getRpc_remark());
        auditChangeForm.setUser_rpc_info(user_rpc_info);
        custModifyRecordService.saveParamsAndRecord(auditChangeForm);

    }
}

package com.cairh.cpe.backend.form.resp;

import lombok.Data;

import java.util.List;

@Data
public class MoneyAntiInfo {

    /**
     * 开户营业部
     */
    private String branch_name;


    /**
     * 开户营业部省代码
     */
    private String branch_province_code;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 原始地址
     */
    private String initial_address;


    /**
     * 异地开户理由
     */
    private String choose_branch_reason;

    /**
     * 职业代码
     */
    private String profession_code;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 职业备注(选择职业理由)
     */
    private String choose_profession_reason;

    /**
     * 选择职业理由(可选择理由)
     */
    private List<String> choose_profession_reason_list;


    /**
     * 核实信息备注
     */
    private String profession_other;

    /**
     * 工作单位
     */
    private String work_unit;

    /**
     * 经营范围
     */
    private String work_range;

    /**
     * 性别 0 男  1 女
     */
    private String client_gender;

    private String choose_branch_reason_content; // 异地开户理由文本

    private String choose_profession_reason_content; // 选择职业理由文本
}

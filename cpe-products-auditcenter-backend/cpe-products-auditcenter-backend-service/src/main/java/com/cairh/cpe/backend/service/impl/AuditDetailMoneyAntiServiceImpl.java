package com.cairh.cpe.backend.service.impl;

import com.cairh.cpe.backend.form.req.GetProfessionReasonForm;
import com.cairh.cpe.backend.form.req.MoneyAntiModifyForm;
import com.cairh.cpe.backend.form.resp.MoneyAntiInfo;
import com.cairh.cpe.backend.service.IAuditCenterCommonService;
import com.cairh.cpe.backend.service.IAuditDetailMoneyAntiService;
import com.cairh.cpe.businflow.service.ICustModifyRecordService;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.IdKindEnum;
import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.dto.ProfessionReasonConfig;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.IDCardInfo;
import com.cairh.cpe.common.entity.clob.UserBaseInfo;
import com.cairh.cpe.common.entity.request.AuditChangeForm;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.AgeUtil;
import com.cairh.cpe.common.util.IdentifyUtils;
import com.cairh.cpe.common.util.PropertiesUtils;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;
import com.cairh.cpe.service.aiaudit.service.IAiAuditAchieveService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class AuditDetailMoneyAntiServiceImpl implements IAuditDetailMoneyAntiService {

    @Autowired
    private IRequestService requestService;
    @Autowired
    private CacheBranch cacheBranch;
    @Autowired
    private ICustModifyRecordService custModifyRecordService;
    @Autowired
    private IBusinFlowRequestService businFlowRequestService;
    @Autowired
    private IAiAuditAchieveService aiAuditAchieveService;
    @Autowired
    private IAuditCenterCommonService auditCenterCommonService;


    @Override
    public MoneyAntiInfo getMoneyAntiInfo(String request_no) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
        MoneyAntiInfo moneyAntiInfo = new MoneyAntiInfo();
        String client_gender = "";
        if (StringUtils.isNotBlank(clobContentInfo.getBranch_no())) {
            BranchInfo branchInfo = cacheBranch.getBranchByNo(clobContentInfo.getBranch_no());
            if (branchInfo != null) {
                moneyAntiInfo.setBranch_name(branchInfo.getBranch_name());
                moneyAntiInfo.setBranch_province_code(branchInfo.getProvince_code());
            }
        }

        if (clobContentInfo.getId_card_info() != null) {
            IDCardInfo idCardInfo = clobContentInfo.getId_card_info();
            client_gender = idCardInfo.getClient_gender();
            moneyAntiInfo.setClient_gender(client_gender);
            if (StringUtils.equalsAny(StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL), WskhConstant.BUSIN_TYPE_NORMAL)
                    && !StringUtils.equals(IdKindEnum.FOREIGN_PREV_PERMIT.getCode(), clobContentInfo.getId_kind())) {
                // 港澳居民来往内地通行证or台湾居民来往大陆通行证
                if (StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                    moneyAntiInfo.setAge(AgeUtil.ageUtil(StringUtils.isNotBlank(clobContentInfo.getBirthday()) ? clobContentInfo.getBirthday() :
                            IdentifyUtils.getIdCardBirthDay(clobContentInfo.getId_card_info().getId_no())));
                } else {
                    moneyAntiInfo.setAge(AgeUtil.ageUtil(IdentifyUtils.getIdCardBirthDay(idCardInfo.getId_no())));
                }
            } else if (StringUtils.isNotBlank(clobContentInfo.getBirthday())) {
                moneyAntiInfo.setAge(AgeUtil.ageUtil(clobContentInfo.getBirthday()));
            }
        }

        if (clobContentInfo.getUser_base_info() != null) {
            UserBaseInfo userBaseInfo = clobContentInfo.getUser_base_info();
            moneyAntiInfo.setAddress(userBaseInfo.getAddress());
            moneyAntiInfo.setInitial_address(userBaseInfo.getInitial_address());//原始地址
            moneyAntiInfo.setProfession_code(userBaseInfo.getProfession_code());
            moneyAntiInfo.setChoose_branch_reason(StringUtils.isNotBlank(userBaseInfo.getChoose_branch_reason()) ? userBaseInfo.getChoose_branch_reason() : "");
            moneyAntiInfo.setChoose_profession_reason(userBaseInfo.getChoose_profession_reason());
            moneyAntiInfo.setWork_unit(userBaseInfo.getWork_unit());
            moneyAntiInfo.setProfession_other(userBaseInfo.getProfession_other());
            moneyAntiInfo.setChoose_profession_reason_content(StringUtils.isNotBlank(clobContentInfo.getChoose_profession_reason_content()) ? clobContentInfo.getChoose_profession_reason_content() : "");
            moneyAntiInfo.setChoose_branch_reason_content(clobContentInfo.getChoose_branch_reason_content());
        }

        if (StringUtils.isBlank(moneyAntiInfo.getChoose_profession_reason())) {
            moneyAntiInfo.setChoose_profession_reason(clobContentInfo.getChoose_profession_reason_content());
        }
        if (StringUtils.isBlank(moneyAntiInfo.getChoose_branch_reason())) {
            moneyAntiInfo.setChoose_branch_reason(clobContentInfo.getChoose_branch_reason_content());
        }
        //设置选择职业理由的内容
        if (StringUtils.equalsAny(StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL), WskhConstant.BUSIN_TYPE_NORMAL)) {
            List<String> list = new ArrayList<>();
            int age = 0;
            if (clobContentInfo.getId_card_info() != null) {
                IDCardInfo idCardInfo = clobContentInfo.getId_card_info();
                client_gender = idCardInfo.getClient_gender();
                age = AgeUtil.ageUtil(StringUtils.isNotBlank(clobContentInfo.getBirthday()) ? clobContentInfo.getBirthday() :
                        IdentifyUtils.getIdCardBirthDay(clobContentInfo.getId_card_info().getId_no()));
            }
            // 获取配置信息
            String ageProfessionReasonCodeConfig = PropertySource.get(PropKeyConstant.WSKH_AGE_PROFESSION_REASON_CODE_CONFIG, "");
            if (StringUtils.isNotBlank(ageProfessionReasonCodeConfig)) {
                List<ProfessionReasonConfig> configList = PropertiesUtils.parseConfig(ageProfessionReasonCodeConfig);
                list = PropertiesUtils.evaluateConditions(age, moneyAntiInfo.getProfession_code(), client_gender, configList);
            }
            moneyAntiInfo.setChoose_profession_reason_list(list);
        }

        return moneyAntiInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<AiAuditRuleResp> auditRetry(MoneyAntiModifyForm moneyAntiModifyForm) {
        String request_no = moneyAntiModifyForm.getRequest_no();
        BusinFlowRequest businFlowRequest = businFlowRequestService.getById(request_no);

        AuditChangeForm auditChangeForm = new AuditChangeForm();

        auditChangeForm.setRequest_no(request_no);
        auditChangeForm.setFlow_task_id(moneyAntiModifyForm.getTask_id());
        auditChangeForm.setOperator_no(moneyAntiModifyForm.getOperator_no());
        auditChangeForm.setOperator_name(moneyAntiModifyForm.getOperator_name());
        auditChangeForm.setModify_link(businFlowRequest.getAnode_id());
        if (StringUtils.isBlank(moneyAntiModifyForm.getChoose_profession_reason())) {
            moneyAntiModifyForm.setChoose_profession_reason(moneyAntiModifyForm.getChoose_profession_reason_content());
        }
        if (StringUtils.isBlank(moneyAntiModifyForm.getChoose_branch_reason())) {
            moneyAntiModifyForm.setChoose_branch_reason(moneyAntiModifyForm.getChoose_branch_reason_content());
        }
        auditChangeForm.setChoose_profession_reason_content(moneyAntiModifyForm.getChoose_profession_reason_content());
        auditChangeForm.setChoose_branch_reason_content(moneyAntiModifyForm.getChoose_branch_reason_content());

        auditChangeForm.setAddress_modify_reason(moneyAntiModifyForm.getAddress_modify_reason());
        auditChangeForm.setProfession_code_modify_reason(moneyAntiModifyForm.getProfession_code_modify_reason());
        auditChangeForm.setWork_unit_modify_reason(moneyAntiModifyForm.getWork_unit_modify_reason());
        auditChangeForm.setChoose_profession_reason_modify_reason(moneyAntiModifyForm.getChoose_profession_reason_modify_reason());
        auditChangeForm.setChoose_branch_reason_modify_reason(moneyAntiModifyForm.getChoose_branch_reason_modify_reason());
        auditChangeForm.setModify_reason(moneyAntiModifyForm.getModify_reason());
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setAddress(moneyAntiModifyForm.getAddress());
        if (StringUtils.isNotBlank(moneyAntiModifyForm.getAddress())) {
            auditChangeForm.setTranslation_address(auditCenterCommonService.getTranslationAddress(moneyAntiModifyForm.getAddress()));
            auditChangeForm.setAlternative_address(auditCenterCommonService.getalternativeTranslationAddress(moneyAntiModifyForm.getAddress()));
        }
        userBaseInfo.setProfession_code(moneyAntiModifyForm.getProfession_code());
        userBaseInfo.setWork_unit(moneyAntiModifyForm.getWork_unit());
        userBaseInfo.setChoose_profession_reason(moneyAntiModifyForm.getChoose_profession_reason());
        userBaseInfo.setChoose_branch_reason(moneyAntiModifyForm.getChoose_branch_reason());
        userBaseInfo.setProfession_other(moneyAntiModifyForm.getProfession_other());
        auditChangeForm.setUser_base_info(userBaseInfo);

        custModifyRecordService.saveParamsAndRecord(auditChangeForm);
        return aiAuditAchieveService.againExecuteAiaudit(request_no, "9");
    }

    @Override
    public MoneyAntiInfo getProfessionReason(GetProfessionReasonForm getProfessionReasonForm) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(getProfessionReasonForm.getRequest_no());
        List<String> list = new ArrayList<>();
        if (StringUtils.equalsAny(StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL), WskhConstant.BUSIN_TYPE_NORMAL)) {
            String client_gender = "";
            int age = 0;
            if (clobContentInfo.getId_card_info() != null) {
                IDCardInfo idCardInfo = clobContentInfo.getId_card_info();
                client_gender = idCardInfo.getClient_gender();
                age = AgeUtil.ageUtil(StringUtils.isNotBlank(clobContentInfo.getBirthday()) ? clobContentInfo.getBirthday() :
                        IdentifyUtils.getIdCardBirthDay(clobContentInfo.getId_card_info().getId_no()));
            }

            // 获取配置信息
            String ageProfessionReasonCodeConfig = PropertySource.get(PropKeyConstant.WSKH_AGE_PROFESSION_REASON_CODE_CONFIG, "");
            if (StringUtils.isNotBlank(ageProfessionReasonCodeConfig)) {
                List<ProfessionReasonConfig> configList = PropertiesUtils.parseConfig(ageProfessionReasonCodeConfig);
                list = PropertiesUtils.evaluateConditions(age, getProfessionReasonForm.getProfession_code(), client_gender, configList);
            }
        }
        MoneyAntiInfo moneyAntiInfo = new MoneyAntiInfo();
        moneyAntiInfo.setChoose_profession_reason_list(list);
        return moneyAntiInfo;
    }

}

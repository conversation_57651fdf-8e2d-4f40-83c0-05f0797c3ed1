package com.cairh.cpe.backend.form.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 开户提交
 *
 * <AUTHOR>
 */
@Data
public class BusinessHandCreateForm {
    /**
     * 100400 个人开户
     * 100500 机构开户
     * 100600 产品开户
     */
    @NotBlank(message = "业务类型不能为空")
    private String busin_type;

    /**
     * md5鉴权
     */
    private String md5;

    /**
     * 客户名称  个人  为个人姓名，非个人为机构名称 或产品名称
     */
    @NotBlank(message = "客户名称不能为空")
    private String client_name;

    /**
     * 证件类型
     */
    @NotBlank(message = "证件类型不能为空")
    private String id_kind;

    /**
     * 证件号码   个人 为证件号码 ，非个人为机构证件号码
     */
    @NotBlank(message = "证件号码不能为空")
    private String id_no;

    /**
     * 证件照正面
     */
    private String photo_front;

    /**
     * 证件照反面
     */
    private String photo_back;


    /**
     * 如果是护照 那么需要用此字段接收参数
     */
    private String cert_file_path;

    /**
     * 开户类别
     */
    @NotBlank(message = "开户类别不能为空")
    private String client_category;

    /**
     * 营业部编号
     */
    @NotBlank(message = "营业部编码不能为空")
    private String branch_no;


    /**
     * 营业部名称
     */
    @NotBlank(message = "营业部名称不能为空")
    private String branch_name;

    /**
     * 经办人姓名
     */
    private String agent_name;

    /**
     * 经办人证件类型
     */
    private String agent_id_kind;

    /**
     * 经办人证件号码
     */
    private String agent_id_no;

    /**
     * 经办人证件照人像面
     */
    private String agent_photo_front;

    /**
     * 经办人证件照国徽面
     */
    private String agent_photo_back;

    /**
     * 业务申请编号
     */
    private String business_apply_no;

    /**
     * 手机号码
     */
    private String mobile_tel;
}

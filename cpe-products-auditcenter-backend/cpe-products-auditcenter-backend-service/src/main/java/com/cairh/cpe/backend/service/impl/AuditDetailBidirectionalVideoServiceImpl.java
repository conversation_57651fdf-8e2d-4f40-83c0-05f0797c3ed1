package com.cairh.cpe.backend.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.QueryOperatorInfoResponse;
import com.cairh.cpe.backend.form.resp.QueryZegoTokenResp;
import com.cairh.cpe.backend.service.IAuditDetailBidirectionalVideoService;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.service.IBusinFlowParamsService;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.base.rpc.IVBaseUserInfoDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseUserInfoQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseUserInfoQryResponse;
import com.cairh.cpe.esb.component.video.IEsbComponentVideoDubboService;
import com.cairh.cpe.esb.component.video.dto.req.VideoExitQueueInfoRequest;
import com.cairh.cpe.esb.component.video.dto.req.VideoJoinQueueRequest;
import com.cairh.cpe.esb.component.video.dto.req.VideoQryQueueInfoRequest;
import com.cairh.cpe.esb.component.video.dto.resp.VideoQryQueueInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description：双向视频服务实现
 * Author： slx
 * Date： 2024/5/14 下午3:44
 */
@Slf4j
@Service
public class AuditDetailBidirectionalVideoServiceImpl implements IAuditDetailBidirectionalVideoService {
    public final static String USER_GENDER_MAN = "先生";
    public final static String USER_GENDER_WOMAN = "女士";
    // 默认视频厂商
    private final static String DEFAULT_SERVICE_VENDER = "zego_h5_private_cloud";
    @DubboReference(lazy = true, check = false)
    private IEsbComponentVideoDubboService esbComponentVideoDubboService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private IBusinFlowParamsService businFlowParamsService;
    @Autowired
    private CacheDict cacheDict;
    @DubboReference(lazy = true, check = false)
    private IVBaseUserInfoDubboService baseUserInfoDubboService;
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;
    @DubboReference(check = false)
    private IEsbComponentVideoDubboService videoDubboService;
    @Resource
    private RedissonUtil redissonUtil;

    /**
     * 双向视频加入队列
     *
     * @param request 请求参数
     */
    @Override
    public void videoJoinQueue(JoinVideoRequest request) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, request.getTask_id());
        boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
        if (!isLock) {
            log.info("双向视频加入队列未获取到锁，task_id={}", request.getTask_id());
            return;
        }
        try {
            BusinFlowTask businFlowTask = getBusinFlowTask(request.getTask_id());
            Map<String, Object> businFlowParams = getBusinFlowParams(businFlowTask.getRequest_no());
            VideoJoinQueueRequest videoJoinQueueRequest = new VideoJoinQueueRequest();
            videoJoinQueueRequest.setSubsys_no(WskhConstant.SUBSYS_ID);
            videoJoinQueueRequest.setBusin_type(MapUtils.getString(businFlowParams, Fields.BUSIN_TYPE, StrUtil.SPACE));
            videoJoinQueueRequest.setBusin_name(cacheDict.getDictDesc(Fields.BUSIN_TYPE, MapUtils.getString(businFlowParams, Fields.BUSIN_TYPE, StrUtil.SPACE)));
            videoJoinQueueRequest.setOrgan_flag("");
            videoJoinQueueRequest.setApp_id(MapUtils.getString(businFlowParams, Fields.APP_ID, StrUtil.SPACE));
            videoJoinQueueRequest.setBranch_no(MapUtils.getString(businFlowParams, Fields.BRANCH_NO, StrUtil.SPACE));
            videoJoinQueueRequest.setClient_id("");
            videoJoinQueueRequest.setFund_account("");
            videoJoinQueueRequest.setUser_name(MapUtils.getString(businFlowParams, Fields.CLIENT_NAME, StrUtil.SPACE));
            videoJoinQueueRequest.setId_kind(MapUtils.getString(businFlowParams, Fields.ID_KIND, StrUtil.SPACE));
            videoJoinQueueRequest.setId_no(MapUtils.getString(businFlowParams, Fields.ID_NO, StrUtil.SPACE));
            videoJoinQueueRequest.setMobile_tel(MapUtils.getString(businFlowParams, Fields.MOBILE_TEL));
            videoJoinQueueRequest.setVideo_type(MapUtils.getString(businFlowParams, Fields.VIDEO_TYPE, WskhConstant.VIDEO_TYPE_2));
            videoJoinQueueRequest.setVideo_level(VideoConstant.DEFAULT_VIDEO_LEVEL);
            videoJoinQueueRequest.setService_vender(DEFAULT_SERVICE_VENDER);
            videoJoinQueueRequest.setUnique_id(businFlowTask.getSerial_id());
            videoJoinQueueRequest.setRequest_id(businFlowTask.getSerial_id());
            videoJoinQueueRequest.setChannel_code(MapUtils.getString(businFlowParams, Fields.CHANNEL_CODE));
            videoJoinQueueRequest.setUser_gender(getUserGenderInfo(businFlowParams));
            // 网厅业务办理-经办人信息
            videoJoinQueueRequest.setAgent_name(MapUtils.getString(businFlowParams, Fields.HAND_AGENT_NAME));
            videoJoinQueueRequest.setAgent_id_kind(MapUtils.getString(businFlowParams, Fields.HAND_AGENT_ID_KIND));
            videoJoinQueueRequest.setAgent_id_no(MapUtils.getString(businFlowParams, Fields.HAND_AGENT_ID_NO));
            videoJoinQueueRequest.setClient_category(MapUtils.getString(businFlowParams, Fields.HAND_CLIENT_CATEGORY));

            log.info("audit-joinQueue dubbo接口请求参数: {}", JSON.toJSONString(videoJoinQueueRequest));
            esbComponentVideoDubboService.joinQueue(videoJoinQueueRequest);
            log.info("audit-joinQueue dubbo接口响应参数: {}", "调用成功");
        } catch (Exception e) {
            log.error("双向视频加入队列异常，task_id={}", request.getTask_id(), e);
            throw new BizException(ErrorEnum.BIDIRECTIONAL_TASK_JOIN_QUEUE_ERROR.getValue(), e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    /**
     * 查询双向视频状态
     *
     * @param request 请求参数
     */
    @Override
    public VideoQryQueueInfoResponse queryVideoQueue(QueryVideoQueueRequest request) {
        BusinFlowTask businFlowTask = getBusinFlowTask(request.getTask_id());
        VideoQryQueueInfoRequest videoQryQueueInfoRequest = new VideoQryQueueInfoRequest();
        videoQryQueueInfoRequest.setSubsys_no(WskhConstant.SUBSYS_ID);
        videoQryQueueInfoRequest.setUnique_id(businFlowTask.getSerial_id());

        log.info("audit-qryQueueInfo dubbo接口请求参数: {}", JSON.toJSONString(videoQryQueueInfoRequest));
        if (StringUtils.equalsAny(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_INVALIDATE, FlowStatusConst.AUDIT_PASS, FlowStatusConst.AUDIT_NO_PASS)) {
            VideoQryQueueInfoResponse videoQryQueueInfoResponse = new VideoQryQueueInfoResponse();
            videoQryQueueInfoResponse.setTask_id(businFlowTask.getTask_id());
            videoQryQueueInfoResponse.setTask_status(businFlowTask.getTask_status());
            videoQryQueueInfoResponse.setTask_type(businFlowTask.getTask_type());
            if (StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_INVALIDATE)) {
                videoQryQueueInfoResponse.setOp_content(businFlowTask.getOp_content());
            }
            log.info("audit-qryQueueInfo dubbo接口响应参数: {}", JSON.toJSONString(videoQryQueueInfoResponse));
            return videoQryQueueInfoResponse;
        }
        VideoQryQueueInfoResponse esbVideoQryQueueInfoResponse
                = esbComponentVideoDubboService.qryQueueInfo(videoQryQueueInfoRequest);
        esbVideoQryQueueInfoResponse.setTask_id(businFlowTask.getTask_id());
        esbVideoQryQueueInfoResponse.setTask_status(businFlowTask.getTask_status());
        esbVideoQryQueueInfoResponse.setTask_type(businFlowTask.getTask_type());
        log.info("audit-qryQueueInfo dubbo接口响应参数: {}", JSON.toJSONString(esbVideoQryQueueInfoResponse));
        return esbVideoQryQueueInfoResponse;
    }

    /**
     * 退出双向视频队列
     *
     * @param request 请求参数
     */
    @Override
    public void exitVideoQueue(ExitVideoQueueRequest request) {
        BusinFlowTask businFlowTask = getBusinFlowTask(request.getTask_id());
        VideoExitQueueInfoRequest videoExitQueueInfoRequest = new VideoExitQueueInfoRequest();
        videoExitQueueInfoRequest.setSubsys_no(WskhConstant.SUBSYS_ID);
        videoExitQueueInfoRequest.setUnique_id(businFlowTask.getSerial_id());
        log.info("audit-exitQueueInfo dubbo接口请求参数: {}", JSON.toJSONString(videoExitQueueInfoRequest));
        esbComponentVideoDubboService.exitQueueInfo(videoExitQueueInfoRequest);
        log.info("audit-exitQueueInfo dubbo接口响应参数: {}", "成功退出视频队列");
    }

    @Override
    public QueryOperatorInfoResponse queryOperatorInfo(QueryOperatorInfoRequest request) {
        // 操作员信息
        VBaseUserInfoQryRequest userInfoQryRequest = new VBaseUserInfoQryRequest();
        userInfoQryRequest.setStaff_no(request.getOperator_no());
        log.info("baseUserQryUserInfo dubbo接口请求参数: {}", JSON.toJSONString(userInfoQryRequest));
        VBaseUserInfoQryResponse vBaseUserInfoQryResponse
                = baseUserInfoDubboService.baseUserQryUserInfo(userInfoQryRequest);
        log.info("baseUserQryUserInfo dubbo接口响应参数: {}", JSON.toJSONString(vBaseUserInfoQryResponse));

        QueryOperatorInfoResponse queryOperatorInfoResponse = new QueryOperatorInfoResponse();
        BeanUtils.copyProperties(vBaseUserInfoQryResponse, queryOperatorInfoResponse);
        return queryOperatorInfoResponse;
    }

    @Override
    public QueryZegoTokenResp refreshUserZegoToken(QueryVideoQueueRequest request) {
        BusinFlowTask businFlowTask = getBusinFlowTask(request.getTask_id());
        if (Objects.isNull(businFlowTask)) {
            log.error("BusinFlowTask not exist, task_id: {}", request.getTask_id());
            throw new BizException(ErrorEnum.AUDIT_TASK_NOT_EXIST.getValue(), ErrorEnum.AUDIT_TASK_NOT_EXIST.getDesc());
        }
        VideoUserStatus userStatus = new VideoUserStatus();
        String user_id = WskhConstant.SUBSYS_ID + businFlowTask.getSerial_id();

        if (Boolean.FALSE.equals(redisTemplate.hasKey(VideoConstant.COMPONENT_VIDEO_REDIS_VIDEO_USER_UNIFIED_PREFIX + user_id))) {
            log.error("video user not exist, user_id: {}", user_id);
            throw new BizException(ErrorEnum.BIDIRECTIONAL_QUEUE_NOT_EXIST.getValue(), ErrorEnum.BIDIRECTIONAL_QUEUE_NOT_EXIST.getDesc());
        }
//        if (!StringUtils.equalsAny(userStatus.getStatus(), VideoConstant.COMPONENT_VIDEO_REDIS_VIDEO_USER_STATUS_MATCHED,
//                VideoConstant.COMPONENT_VIDEO_REDIS_VIDEO_USER_STATUS_VIDEOING, VideoConstant.COMPONENT_VIDEO_REDIS_VIDEO_USER_STATUS_VIDEOED)) {
//            throw new BizException(String.format("status: %s illegal", userStatus.getStatus()));
//        }

        String token = videoDubboService.refreshUserZegoToken(WskhConstant.SUBSYS_ID, businFlowTask.getSerial_id(), userStatus.getStatus(), userStatus.getRemote_user_id(), userStatus.getZego_token(), userStatus.getRoom_id());
        QueryZegoTokenResp resp = new QueryZegoTokenResp();
        resp.setTask_id(request.getTask_id());
        resp.setZego_token(token);
        return resp;
    }

    /**
     * 获取任务数据
     *
     * @param taskId 任务编号
     * @return 参数
     */
    private BusinFlowTask getBusinFlowTask(String taskId) {
        return businFlowTaskService.getCurrTaskTypeByTaskId(taskId, TaskTypeEnum.AUDIT.getCode());
    }

    /**
     * 获取大字段表参数
     *
     * @param requestNo 请求编号
     * @return 参数
     */
    private Map<String, Object> getBusinFlowParams(String requestNo) {
        return businFlowParamsService.getParamContentById(requestNo);
    }

    /**
     * 获取客户性别信息
     *
     * @param params 用户信息
     * @return 先生 or 女士
     */
    private String getUserGenderInfo(Map<String, Object> params) {
        String gender = MapUtils.getString(params, Fields.USER_GENDER, StrUtil.SPACE);
        String idNo = MapUtils.getString(params, Fields.ID_NO, StrUtil.SPACE);
        if (StrUtil.isBlank(gender)) {
            gender = getGenderByIdCard(idNo);
        }
        if (StrUtil.equals(gender, "1")) {
            return USER_GENDER_MAN;
        }
        if (StrUtil.equals(gender, "2")) {
            return USER_GENDER_WOMAN;
        }
        return StrUtil.EMPTY;
    }

    public String getGenderByIdCard(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            return StrUtil.EMPTY;
        }
        // 获取第17位字符
        char genderChar = idCard.charAt(16);
        // 判断奇偶
        if ((genderChar - '0') % 2 == 0) {
            return "2";
        } else {
            return "1";
        }
    }
}

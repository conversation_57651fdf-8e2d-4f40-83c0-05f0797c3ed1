package com.cairh.cpe.backend.form.req;

import lombok.Data;

import java.util.List;

@Data
public class MoneyAntiModifyForm {

    private String operator_no;
    private String operator_name;

    /**
     * 业务编号
     */
    private String request_no;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 职业代码
     */
    private String profession_code;

    /**
     * 工作单位
     */
    private String work_unit;

    /**
     * 异地开户理由
     */
    private String choose_branch_reason;

    /**
     * 选择职业理由
     */
    private String choose_profession_reason;

    /**
     * 核实备注信息
     */
    private String profession_other;

    private String choose_branch_reason_content; // 异地开户理由文本

    private String choose_profession_reason_content; // 选择职业理由文本

    /**
     * 任务id
     */
    private String task_id;
    /**
     * 修改原因
     */
    private String address_modify_reason;
    private String choose_branch_reason_modify_reason;
    private String choose_profession_reason_modify_reason;
    private String profession_code_modify_reason;
    private String work_unit_modify_reason;
    private String modify_reason;
}

package com.cairh.cpe.backend.form.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = false)
@Data
public class ModifyRecordForm extends PageInfo {

    //条件营业部
    private String branch_no;

    private List<String> branch_nos;
    //可操作营业部
    private List<String> operator_branch;
    //客户姓名
    private String client_name;
    //客户手机号
    private String mobile_tel;
    //修改项目
    private String modify_item;
    //操作员编号
    private String operator_no;
    //操作员姓名
    private String operator_name;
    //创建时间开始时间
    private String modify_datetime_start;
    //创建时间结束时间
    private String modify_datetime_end;
    //分公司
    private String company_name;

}

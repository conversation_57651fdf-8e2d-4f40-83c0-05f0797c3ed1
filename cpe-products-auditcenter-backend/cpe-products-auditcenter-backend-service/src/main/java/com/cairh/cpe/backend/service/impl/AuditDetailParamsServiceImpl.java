package com.cairh.cpe.backend.service.impl;

import com.cairh.cpe.backend.service.AuditDetailParamsService;
import com.cairh.cpe.common.constant.Fields;
import com.cairh.cpe.common.service.IBusinFlowParamsService;
import com.cairh.cpe.common.service.IRequestFlowService;
import com.cairh.cpe.common.util.KHDateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class AuditDetailParamsServiceImpl implements AuditDetailParamsService {

    @Resource
    @Autowired
    private IBusinFlowParamsService businFlowParamsService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateParams(String requestNo, Map<String, Object> paramsMap) {
        Map<String, Object> contentMap = businFlowParamsService.getParamContentById(requestNo);
        contentMap.putAll(paramsMap);
        contentMap.put(Fields.UPDATE_DATETIME, KHDateUtil.formatDate(new Date(), KHDateUtil.DATE_TIME_FORMAT));
        businFlowParamsService.saveParams(requestNo, contentMap);
    }

}

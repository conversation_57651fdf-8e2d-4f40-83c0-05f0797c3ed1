package com.cairh.cpe.backend.form.resp;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ProfileImageResp {


    /**
     * 人脸识别分数（证通公安）
     */
    private String face_score;

    /**
     * 人脸识结果（证通公安）
     */
    private String face_score_desc;

    /**
     * 大头照
     */
    private String file_80;

    /**
     * 公安照
     */
    private String file_82;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 当前分数为中登or证通 1-中登 2-证通
     */
    private String score_type;

    /**
     * 当前时间  1-中登时间 2-非中登时间
     */
    private String work_time;

    /**
     * 结果类型 1-未认证 2-一致 3-不一致 4-待确认
     */
    private String result_type;

    /**
     * 数据标记 老数据为空 新数据为1
     */
    private String data_sign;
}

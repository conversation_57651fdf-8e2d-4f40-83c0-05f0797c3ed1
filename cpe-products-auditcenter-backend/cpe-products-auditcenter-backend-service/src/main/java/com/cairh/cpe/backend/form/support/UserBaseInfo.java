package com.cairh.cpe.backend.form.support;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class UserBaseInfo {

    @NotBlank(message = "busi_serial_no不能为空")
    private String busi_serial_no;

    /**
     * 手机号
     */
    private String mobile_tel;

    /**
     * 民族
     */
    private String nation_id;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 性别
     */
    private String user_gender;


    /**
     * 英文姓名
     */
    private String english_name;

    /**
     * 曾持有证件号
     */
    private String prev_id_number;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 职业 （可编辑）
     */
    private String profession_code;

    /**
     * 开户营业部
     */
    private String branch_no;

    /**
     * 真实开户营业部
     */
    private String real_branch_no;

    /**
     * 经常居住地址 （可编辑）
     */
    private String address;

    /**
     * 工作单位 （可编辑）
     */
    private String work_unit = "";

    /**
     * 异地开户理由
     */
    private String choose_branch_reason = "";

    /**
     * 选择职业理由
     */
    private String choose_profession_reason = "";

    /**
     * 失信记录
     */
    private String dishonest_record;

    /**
     * 失信记录备注信息
     */
    private String dishonest_record_remark;

    /**
     * 失信记录文本信息
     */
    private String dishonest_content;

    /**
     * 手机号归属地
     */
    private String mobile_location;

    /**
     * 渠道编号
     */
    private String channel_no;

    /**
     * 渠道名称
     */
    private String channel_name;

    /**
     * 推广关系编号
     */
    private String broker_code;

    /**
     * 推广关系（营销人员）
     */
    private String broker_name;

    /**
     * 活动编号
     */
    private String activity_no;

    /**
     * 活动名称
     */
    private String activity_name;

    /**
     * 营销团队
     */
    private String marketing_team;

    /**
     * 视频见证类型
     */
    private String video_type;

    /**
     * 原始地址
     */
    private String initial_address;

    /**
     * 原始地址是否修改
     */
    private String initial_address_modify_flag = "0";

    /**
     * 双向视频 视频类型
     */
    private String task_type;

    /**
     * 核实备注信息 （可编辑）
     */
    private String profession_other;

    /**
     * 开户渠道
     */
    private String open_channel;

    /**
     * 接入方式（h5等）
     */
    private String app_id;


    /**
     * 话术id
     */
    private String video_speech_id;


    /**
     * 审核流水id
     */
    private String spjz_id;


    /**
     * 开户流水id
     */
    private String pc_id;

    /**
     * 开户类型
     */
    private String busin_type;

    /**
     * 初始投资金额
     */
    private String initial_investment_amount;

    /**
     * 状态码
     */
    private String deal_status;

    /**
     * 处理结果
     */
    private String deal_info;

}

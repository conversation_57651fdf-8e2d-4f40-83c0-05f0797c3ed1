package com.cairh.cpe.backend.service.impl;

import com.cairh.cpe.backend.form.resp.VideoInfoResp;
import com.cairh.cpe.backend.service.IAuditDetailVideoInfoService;
import com.cairh.cpe.common.constant.Fields;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.service.IBusinFlowRecordService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.service.aiaudit.request.SpeechSourceReq;
import com.cairh.cpe.service.aiaudit.response.AuditBusinRecordQueryResp;
import com.cairh.cpe.service.aiaudit.response.QueryAuditBusinRecordResp;
import com.cairh.cpe.service.aiaudit.service.IAiAuditAchieveService;
import com.cairh.cpe.service.idverify.IVideoScripModelService;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class AuditDetailVideoInfoServiceImpl implements IAuditDetailVideoInfoService {

    @Autowired
    private IRequestService requestService;
    @Autowired
    private IVideoScripModelService videoScripModelService;
    @Autowired
    private IAiAuditAchieveService aiAuditAchieveService;
    @Autowired
    private IBusinFlowRecordService businFlowRecordService;


    /**
     * *获取视频和视频话术
     *
     * @param request_no
     * @return
     */
    @Override
    public VideoInfoResp getVideoInfo(String request_no) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
        VideoInfoResp videoInfoResp = new VideoInfoResp();
        videoInfoResp.setFile_8A(clobContentInfo.getFile_8A());
        List<SpeechSourceReq> speechSource = videoScripModelService.getSpeechSource(request_no);
        videoInfoResp.setVideo_script(speechSource);
        return videoInfoResp;
    }

    @Override
    public QueryAuditBusinRecordResp refreshVideo(BaseUser baseUser, BusinFlowRequest businFlowRequest) {
        // 操作流水
        Map<String, Object> recordMap = Maps.newHashMap();
        recordMap.put(Fields.REQUEST_STATUS, businFlowRequest.getRequest_status());
        recordMap.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22404);
        recordMap.put(Fields.ANODE_ID, businFlowRequest.getAnode_id());
        recordMap.put(Fields.OPERATOR_NO, baseUser.getStaff_no());
        recordMap.put(Fields.OPERATOR_NAME, baseUser.getUser_name());
        recordMap.put(Fields.BUSINESS_REMARK, "刷新视频以及音频");
        businFlowRecordService.saveBusinFlowRecord(businFlowRequest, recordMap);
        String request_no = businFlowRequest.getRequest_no();
        // 刷新视频
        aiAuditAchieveService.againExecuteAiaudit(request_no, "4");
        // 刷新音频
        aiAuditAchieveService.againExecuteAiaudit(request_no, "5");
        List<String> item_identity = new ArrayList<>();
        item_identity.add("4");
        item_identity.add("5");
        List<AuditBusinRecordQueryResp> list = new ArrayList<>();
        List<AuditBusinRecordQueryResp> auditBusinRecordQueryResps = aiAuditAchieveService.queryAuditBusinRecord(request_no, item_identity);
        if (CollectionUtils.isNotEmpty(auditBusinRecordQueryResps)) {
            list.addAll(auditBusinRecordQueryResps);
        }
        QueryAuditBusinRecordResp queryAuditBusinRecordResp = new QueryAuditBusinRecordResp();
        queryAuditBusinRecordResp.setAuditBusinRecordQueryRespList(list);
        return queryAuditBusinRecordResp;
    }

}

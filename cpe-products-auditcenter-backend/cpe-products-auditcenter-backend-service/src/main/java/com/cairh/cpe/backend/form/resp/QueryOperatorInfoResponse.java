package com.cairh.cpe.backend.form.resp;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 查询操作员响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class QueryOperatorInfoResponse {

    //员工号
    private String staff_no;

    //用户姓名
    private String user_name;

    //营业部编号
    private String branch_no;

    //手机号码
    private String mobile_tel;

    //电子邮箱
    private String e_mail;

    //地址
    private String address;

    //性别
    private String gender;

    //从业证书编号
    private String profession_cert;

    //从业证书失效日期
    private Integer profession_cert_enddate;

    //头像文件Id
    private String image_file_id;

    //头像base64
    private String base64_image;

    //固定电话
    private String phone_tel;

    //账号失效日期
    private Integer expire_enddate;

    //券商名称
    private String security_name;

    //券商电话
    private String security_servicephone;
}
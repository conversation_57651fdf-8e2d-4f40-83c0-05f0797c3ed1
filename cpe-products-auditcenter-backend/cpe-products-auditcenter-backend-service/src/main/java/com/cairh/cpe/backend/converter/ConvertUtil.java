package com.cairh.cpe.backend.converter;

import com.cairh.cpe.backend.form.req.TaskDetailRequest;
import com.cairh.cpe.backend.form.resp.HistoryTaskInfoResp;
import com.cairh.cpe.backend.form.resp.TaskDetailInfo;
import com.cairh.cpe.common.entity.His_BusinFlowTask;
import com.cairh.cpe.common.entity.His_UserQueryExtInfo;
import com.cairh.cpe.common.entity.Label;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class ConvertUtil {

    /**
     * 明细列表值转化
     *
     * @param info
     * @param request
     * @param branchInfoMap
     * @param labelMap
     * @param tabFlagFinished
     * @param expireDate
     * @return
     */
    public static TaskDetailInfo convertToTaskDetailInfoVO(TaskDetailInfo info,
                                                           TaskDetailRequest request,
                                                           Map<String, BranchInfo> branchInfoMap,
                                                           Map<String, Label> labelMap,
                                                           String tabFlagFinished,
                                                           Date expireDate) {
        if (!tabFlagFinished.equals(request.getComplete_flag())) {
            if (StringUtils.isBlank(info.getOperator_no()) || !request.getOperator_no().equals(info.getOperator_no())) {
                info.setShow_button("noshow");
            }
        }
        info.setCompany_name(branchInfoMap.getOrDefault(branchInfoMap.getOrDefault(info.getBranch_no(), new BranchInfo()).getUp_branch_no(), new BranchInfo())
                .getBranch_name());
        List<String> labelTypes = Lists.newArrayList();
        Arrays.stream(info.getMatch_labels().split(",")).filter(e -> labelMap.containsKey(e)).forEach(r -> {
            labelTypes.add(labelMap.get(r).getLabel_type());
        });
        info.setMatch_labels_types(labelTypes);
        if (!tabFlagFinished.equals(request.getComplete_flag()) && null != info.getDeal_datetime() && StringUtils.isNotBlank(info.getOperator_no())) {
            info.setNear_expiry(info.getDeal_datetime().before(expireDate));
        }
        info.setBusin_type_code(info.getBusin_type());
        info.setVideo_type_code(info.getVideo_type());
        info.setClient_category_code(info.getClient_category());
        return info;
    }

    public static TaskDetailInfo convertToMyTaskDetailInfoVO(TaskDetailInfo info,
                                                             TaskDetailRequest request,
                                                             Map<String, BranchInfo> branchInfoMap,
                                                             Map<String, Label> labelMap,
                                                             Date expireDate) {
        List<String> labelTypes = Lists.newArrayList();
        Arrays.stream(info.getMatch_labels().split(",")).filter(e -> labelMap.containsKey(e)).forEach(r -> {
            labelTypes.add(labelMap.get(r).getLabel_type());
        });
        info.setMatch_labels_types(labelTypes);
        info.setCompany_name(branchInfoMap.getOrDefault(branchInfoMap.getOrDefault(info.getBranch_no(), new BranchInfo()).getUp_branch_no(), new BranchInfo())
                .getBranch_name());
        if (!"1".equals(request.getIs_task_id_data()) && null != info.getDeal_datetime()) {
            info.setNear_expiry(info.getDeal_datetime().before(expireDate));
        }
        info.setBusin_type_code(info.getBusin_type());
        info.setVideo_type_code(info.getVideo_type());
        info.setClient_category_code(info.getClient_category());
        return info;
    }


    /**
     * 历史订单转换
     *
     * @param his_userQueryExtInfo
     * @param branchInfoMap
     * @return
     */
    public static HistoryTaskInfoResp convertToApplyVO(His_UserQueryExtInfo his_userQueryExtInfo,
                                                       Map<String, BranchInfo> branchInfoMap,
                                                       Map<String, His_BusinFlowTask> taskMap,
                                                       Map<String, String> taskStatusMap,
                                                       boolean applyList) {
        HistoryTaskInfoResp resp = new HistoryTaskInfoResp();
        BeanUtils.copyProperties(his_userQueryExtInfo, resp);
        resp.setCompany_name(branchInfoMap.getOrDefault(branchInfoMap.getOrDefault(his_userQueryExtInfo.getBranch_no(), new BranchInfo()).getUp_branch_no(), new BranchInfo())
                .getBranch_name());
        resp.setBranch_name(branchInfoMap.getOrDefault(his_userQueryExtInfo.getBranch_no(), new BranchInfo()).getBranch_name());
        resp.setBusin_type_code(his_userQueryExtInfo.getBusin_type());
        resp.setVideo_type_code(his_userQueryExtInfo.getVideo_type());
        resp.setClient_category_code(his_userQueryExtInfo.getClient_category());
        resp.setTask_id(taskMap.getOrDefault(his_userQueryExtInfo.getRequest_no(), new His_BusinFlowTask()).getTask_id());
        His_BusinFlowTask flowTask = taskMap.get(his_userQueryExtInfo.getRequest_no());
        if (null != flowTask) {
            resp.setRequest_status_name(taskStatusMap.getOrDefault(flowTask.getTask_type() + "-" + flowTask.getTask_status(), StringUtils.EMPTY));
        } else {
            return resp;
        }
        if (applyList) {
            resp.setSerial_id(flowTask.getSerial_id());
            resp.setRequest_no(his_userQueryExtInfo.getRequest_no());
        } else {
            resp.setRequest_no(flowTask.getRequest_no());
            resp.setSerial_id(his_userQueryExtInfo.getRequest_no());
        }
        return resp;
    }

}

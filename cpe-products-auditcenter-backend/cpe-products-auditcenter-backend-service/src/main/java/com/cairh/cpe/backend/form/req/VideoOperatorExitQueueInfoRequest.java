package com.cairh.cpe.backend.form.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description：
 * Author： slx
 * Date： 2024/6/26 上午10:49
 */
@Data
@Accessors(chain = true)
public class VideoOperatorExitQueueInfoRequest implements Serializable {

    private static final long serialVersionUID = 3206655205206968058L;
    /**
     * 系统编号 Y
     */
    @NotNull
    private String subsys_no;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 业务流水号(需保证唯一) Y
     */
    @NotNull
    private String unique_id;

    /**
     * 操作员编号
     */
    private String staff_no;

    /**
     * 退出原因
     */
    private String reason_msg;
}

package com.cairh.cpe.backend.form.resp;

import com.cairh.cpe.common.entity.Label;
import lombok.Data;

import java.util.List;

@Data
public class RecentRejectReasonResp {

    /**
     * 驳回次数
     */
    private String reject_num ;

    /**
     * 任务类型（audit 开户见证 review 开户复审 secondary_review 开户二次复核 ）
     */
    private String task_type ;
    private String task_type_status ;
    private String task_type_status_str ;
    /**
     * 见证人
     */
    private String audit_operator_name;
    private String audit_operator_no;
    private String review_operator_name;
    private String review_operator_no;
    private String double_operator_name;
    private String double_operator_no;

    /**
     * 上一次驳回原因
     */
    private String last_reject_reason ;


    /**
     * 接入方式（h5开户等）
     */
    private String app_id ;


    /**
     * 任务id
     */
    private String task_id ;

    private List<Label> labels ;

    /**
     * 开户渠道是否命中
     */
    private String branch_repeated;

    /**
     * 联系地址是否命中
     */
    private String address_repeated;

}

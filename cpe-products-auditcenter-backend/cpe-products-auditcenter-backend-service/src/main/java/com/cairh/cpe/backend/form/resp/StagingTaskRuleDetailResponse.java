package com.cairh.cpe.backend.form.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/3/12 09:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StagingTaskRuleDetailResponse {

    /**
     * 主键ID
     */
    private String serial_id;

    /**
     * 规则名称
     */
    private String rule_name;

    /**
     * 规则类型（1-暂存任务）
     */
    private String rule_type;

    /**
     * 规则开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date rule_datetime_start;

    /**
     * 规则结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date rule_datetime_end;

    /**
     * 顺序，优先级
     */
    private Integer order_no;

    /**
     * 可选规则
     */
    private String expression;

    /**
     * 状态（1-可用，0-禁用）
     */
    private String status;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 更新人
     */
    private String update_by;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date update_datetime;

}

package com.cairh.cpe.backend.form.resp;


import com.cairh.cpe.cache.annotation.DataConvert;
import com.cairh.cpe.common.backend.annotation.Desensitize;
import com.cairh.cpe.common.backend.masking.mode.DataMaskingMode;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class BusinFlowRecordResult {


    /**
     * 流水号
     */
    private String serial_id;
    // 所属营业部编号
    private String branch_no;
    // 所属营业部名称
    @DataConvert(code_type = "branch")
    private String branch_name;
    // 客户姓名
    private String client_name;
    // 客户手机号
    @Desensitize(mode = DataMaskingMode.MOBILE_TEL)
    private String mobile_tel;
    // 客户身份证号
    @Desensitize(mode = DataMaskingMode.ID_NO)
    private String id_no;
    /**
     * 证件类别
     */
    @DataConvert(code_dict = "id_kind")
    private String id_kind;
    // 接入方式
    @DataConvert(code_dict = "app_id")
    private String app_id;
    // 操作员编号
    private String operator_no;
    // 操作员名称
    @DataConvert(code_type = "user")
    private String operator_name;
    // 视频见证方式
    @DataConvert(code_dict = "video_type")
    private String video_type;
    // 业务标志
    @DataConvert(code_dict = "business_flag")
    private String business_flag;
    // 业务说明
    private String business_remark;
    /**
     * 渠道代码
     */
    private String channel_code;
    /**
     * 渠道名称
     */
    private String channel_name;

    /**
     * 开户渠道
     */
    private String open_channel;

    /**
     * 业务操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 业务类型
     */
    @DataConvert(code_dict = "busin_type")
    private String busin_type;

    /**
     * 业务状态
     */
    private String request_status;

    /**
     * 当前流程节点
     */
    private String anode_id;

    @DataConvert(code_type = "task_status")
    private String request_status_name;


    /**
     * 所属分公司
     */
    private String company_name;

    /**
     * 活动名称
     */
    private String activity_name;

    /**
     * 营销团队
     */
    private String marketing_team;

    /**
     * 业务类型-code
     */
    private String busin_type_code;


    public String getRequest_status_name() {
        if (request_status_name == null && anode_id !=null && request_status!=null) {
            if ("audit,review,secondary_review".contains(anode_id) && "1234".contains(request_status)) {
                return anode_id + "-" + request_status;
            } else {
                return request_status;
            }
        }
        return request_status_name;
    }



    public String getBranch_name() {
        if (branch_name == null) {
            return branch_no;
        }
        return branch_name;
    }

    public String getOperator_name() {
        if (operator_name == null) {
            return operator_no;
        }
        return operator_name;
    }

    public String getChannel_name() {
        if (channel_name == null) {
            return channel_code;
        }
        return channel_name;
    }
}

package com.cairh.cpe.backend.service;

import com.cairh.cpe.backend.form.req.BaseInfoForm;
import com.cairh.cpe.backend.form.resp.RecentRejectReasonResp;
import com.cairh.cpe.common.entity.response.BusinFlowRecordResp;

import java.util.List;

public interface IAuditDetailRejectReasonService {

    RecentRejectReasonResp getLastRejectReason(BaseInfoForm baseInfoForm);

    List<BusinFlowRecordResp> getHisRejectReason(String request_no);
}

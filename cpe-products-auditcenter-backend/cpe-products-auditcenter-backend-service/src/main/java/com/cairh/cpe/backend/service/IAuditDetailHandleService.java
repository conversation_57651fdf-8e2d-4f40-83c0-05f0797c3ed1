package com.cairh.cpe.backend.service;

import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.BidirectionalVideoChatLogInfo;
import com.cairh.cpe.backend.form.support.OnlineUserByEnRoleAndBranchSupportResponse;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;

import java.util.List;

public interface IAuditDetailHandleService {
    Result<String> dealAuditTask(BaseUser baseUser, DealAuditTaskForm dealAuditTask, BusinFlowTask businFlowTask);

    void auditPassTask(FinishAuditTaskForm finishAuditTaskForm, BusinFlowTask currentTask,BaseUser baseUser);

    BusinFlowTask auditNotPassTask(FinishAuditTaskForm finishAuditTaskForm,BaseUser baseUser);

    /**
     * 任务挂起
     *
     * @param suspendTaskForm
     */
    void suspendTask(SuspendTaskForm suspendTaskForm);

    OnlineUserByEnRoleAndBranchSupportResponse transferBranchTree(TransferBranchTreeForm transferBranchTreeForm);

    void transferTask(TransferTaskForm transferTaskForm);

    void uploadAuditParam(AuditVideoParmRequest videoParmRequest);

    void transferAccept(TransferAcceptForm transferAcceptForm);

    void transferRefuse(TransferRefuseForm transferRefuseForm);

    String checkNotBlank(FinishAuditTaskForm finishAuditTaskForm, ClobContentInfo clobContentInfo);

    /**
     * 保存双向视频对话记录
     *
     * @param request
     */
    void saveBidirectionalVideoChatLog(SaveBidirectionalVideoChatLogRequest request);

    /**
     * 查询双向视频对话记录
     *
     * @param request_no
     * @return
     */
    List<BidirectionalVideoChatLogInfo> queryBidirectionalVideoChatLog(String request_no);
}

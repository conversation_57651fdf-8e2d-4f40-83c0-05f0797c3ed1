package com.cairh.cpe.backend.form.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class UploadRcpMisRequest {

    @NotBlank(message = "业务流水号不能为空")
    private String request_no;

    @NotBlank(message = "task_id不能为空")
    private String task_id;

    /**
     * rpc_file_id  核查记录不能为空
     */
    private String rpc_file_id;

    /**
     * rpc备注
     */
    private String rpc_remark;

    /**
     * rpc_options 核查选项
     */
    private String rpc_option;

}

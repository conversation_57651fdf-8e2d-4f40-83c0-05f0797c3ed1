package com.cairh.cpe.backend.form.resp;

import com.cairh.cpe.common.entity.clob.IDCardInfo;
import com.cairh.cpe.common.entity.clob.UserBaseInfo;
import com.cairh.cpe.common.entity.clob.VideoInfo;
import com.cairh.cpe.common.entity.support.ArchfileinfoResp;
import lombok.Data;

@Data
public class UserAcBaseInfo {
    private String request_no; // 业务流水号

    private String face_score; // 人脸识别分数（证通分数）

    private String face_score_desc; // 证通分数说明

    private String file_6A; // 身份证正面图片链接

    private String file_6B; // 身份证反面图片链接

    private String file_80; // 免冠照链接

    private String file_82; // 公安头像链接

    private String file_8A; // 视频链接

    private String file_7C; // 辅助证件正面

    private String file_7D; // 辅助证件反面

    private String file_Bm; // 机构的营业执照

    private String file_7W; // 境内机构出具的就业证明

    private String file_7X; // 住宿登记证明表原件

    private String face_score_82_80; // 人脸比对分数（公安照和大头照）

    private String kh_face_score_82_80; // 开户人脸比对分数(公安和免冠照)

    private String id_kind;// 身份证类型

    private String id_no;// 身份证号码

    private String issued_depart;// 签发机关

    private String id_begindate;// 身份证开始日期

    private String id_enddate;// 身份证结束日期

    private String id_address;// 证件地址

    private String client_name;// 客户姓名

    private String mobile_tel;// 手机号

    private String nation_id; // 民族

    private String nationality; // 国籍

    private String user_gender; // 性别

    private String birthday; // 性别

    private String profession_code; // 职业

    private String branch_no; // 开户营业部

    private String english_name; // 英文姓名

    private String prev_id_number; // 曾持有证件号

    /**
     * 真实开户营业部
     */
    private String real_branch_no;

    /**
     * 初始投资金额
     */
    private String initial_investment_amount;

    private String address; // 经常居住地址

    private String work_unit; // 工作单位

    private String choose_branch_reason; // 异地开户理由

    private String choose_profession_reason; // 选择职业理由

    private String dishonest_record; // 失信记录

    private String dishonest_record_remark; // 失信记录备注信息

    private String dishonest_content; // 失信记录文件链接

    private String mobile_location; // 手机号归属地

    private String channel_code; // 渠道编号

    private String channel_name; // 渠道名称

    private String broker_code; // 推广关系编号（营销人员）

    private String broker_name; // 推广关系（营销人员）

    private String activity_no; // 活动编号

    private String activity_name; // 活动名称

    private String marketing_team; // 营销团队

    private String video_type; // 视频见证类型

    private String task_type; // 双向视频-任务类型

    private String initial_address;//原始地址

    private String initial_address_modify_flag;//原始地址是否修改

    private String profession_other;//核实备注信息

    private String open_channel;//开户渠道


    private String app_id;//接入方式（h5等）


    private String video_speech_id;//话术id


    private String spjz_id;//审核流水id


    private String pc_id;//开户流水id

    private String busi_serial_no;//就是pc_id

    private String busin_type;//开户类型

    private String auxiliary_id_kind;// 辅助证件类型
    private String auxiliary_id_no;// 辅助证件号码
    private String oversea_client_name;// 境外客户姓名
    private String oversea_id_kind;// 境外证件类型
    private String oversea_id_no;// 境外证件号码
    private String auxiliary_id_begindate;
    private String auxiliary_id_enddate;
    private String auxiliary_id_address;
    private String auxiliary_client_name; // 辅助证件客户姓名
    private String auxiliary_type; // 辅助证件类型

    //初始化进入详情页面的用户初始数据
    private String original_file_6A; //原始身份证人像面

    private String original_file_6B; //原始身份证国徽面

    private String original_file_7C; //原始辅助证件正面

    private String original_file_7D; //原始辅助证件反面

    private String original_file_Bm; //机构的营业执照

    private String original_file_7W; //境内机构出具的就业证明

    private String original_file_7X; //住宿登记证明表原件

    private String original_file_80; //原始大头照

    private String original_id_address;// 证件地址

    private String original_id_begindate;// 证件有效期开始日期YYYYMMDD

    private String original_id_enddate;// 证件有效期结束日期YYYYMMDD

    private String original_auxiliary_id_address;// 辅助证件地址

    private String original_auxiliary_id_begindate;// 辅助证件有效期开始日期YYYYMMDD

    private String original_auxiliary_id_enddate;// 辅助证件有效期结束日期YYYYMMDD

    private String original_auxiliary_client_name;// 辅助证件客户姓名

    private String original_auxiliary_type;// 辅助证件类型

    private String original_address;// 经常居住地址

    private String original_choose_branch_reason; // 异地开户理由

    private String original_choose_profession_reason; // 选择职业理由

    private String original_profession_code; // 职业

    private String original_work_unit; // 工作单位

    private String original_dishonest_record; // 失信记录

    private String original_dishonest_record_remark; // 失信记录备注信息

    private String original_profession_other; // 核查信息备注

    private String original_nationality;  // 国籍

    private String original_english_name;  // 英文姓名

    private String original_prev_id_number;  // 曾持有证件号

    private String original_birthday; // 原始出生日期

    private String translation_address; // 经常居住地址(字典翻译)

    private String alternative_address; // 经常居住地址(使用!~分隔的地址)


    private UserBaseInfo user_base_info;

    private IDCardInfo id_card_info;

    private VideoInfo user_video_info;

    private ArchfileinfoResp archfile_info;

    private String photo_front; // 证件照正面

    private String photo_back; // 证件照反面

    private String cert_file_path; // 如果是护照 那么需要用此字段接收参数

    private String agent_photo_front; // 经办人证件照人像面

    private String agent_photo_back; // 经办人证件照国徽面

    private String deal_status; // 状态码

    private String deal_info; // 处理结果

    private String permit_no; // 港澳台居民居住证-通行证号码

    private String original_photo_front; // 证件照正面

    private String original_photo_back; // 证件照反面

    private String original_agent_photo_front; // 经办人证件照人像面

    private String original_agent_photo_back; // 经办人证件照国徽面

}

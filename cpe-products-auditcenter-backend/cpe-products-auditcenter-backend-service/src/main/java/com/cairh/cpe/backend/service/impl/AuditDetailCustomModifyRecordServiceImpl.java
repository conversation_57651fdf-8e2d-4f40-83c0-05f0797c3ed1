package com.cairh.cpe.backend.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cairh.cpe.backend.service.IAuditDetailCustomModifyRecordService;
import com.cairh.cpe.businflow.entity.CustModifyRecord;
import com.cairh.cpe.businflow.service.ICustModifyRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AuditDetailCustomModifyRecordServiceImpl implements IAuditDetailCustomModifyRecordService {

    @Autowired
    private ICustModifyRecordService custModifyRecordService;


    @Override
    public List<CustModifyRecord> getUserModifyRecord(String request_no) {
        return custModifyRecordService.queryModifyRecordById(request_no);
    }
}

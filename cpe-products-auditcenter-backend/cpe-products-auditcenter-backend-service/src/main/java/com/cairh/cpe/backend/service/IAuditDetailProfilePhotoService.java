package com.cairh.cpe.backend.service;

import com.cairh.cpe.backend.form.req.HandProfileImageRetryForm;
import com.cairh.cpe.backend.form.req.HandVerifyPoliceAllForm;
import com.cairh.cpe.backend.form.req.ProfileImageRetryForm;
import com.cairh.cpe.backend.form.resp.ProfileImageResp;
import com.cairh.cpe.backend.form.resp.ProfilePhotosResp;
import com.cairh.cpe.backend.form.support.VerifyPoliceInfo;
import com.cairh.cpe.common.entity.response.VerifyPoliceAllResult;
import com.cairh.cpe.common.entity.response.VerifyPoliceResp;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;

import java.util.List;

public interface IAuditDetailProfilePhotoService {

    List<AiAuditRuleResp> profileImageRetry(ProfileImageRetryForm profileImageRetryForm);

    List<AiAuditRuleResp> handProfileImageRetry(HandProfileImageRetryForm profileImageRetryForm);

    ProfileImageResp getProfileImageAndScore(String request_no);

    List<ProfilePhotosResp> queryHisProfileImage(String request_no);

    VerifyPoliceResp refreshPolicePhoto(VerifyPoliceInfo verifyPoliceInfo);

    VerifyPoliceAllResult handVerifyPoliceAll(HandVerifyPoliceAllForm handVerifyPoliceAllForm);
}

package com.cairh.cpe.backend.service.impl;

import com.alibaba.fastjson2.JSON;
import com.cairh.cpe.backend.form.resp.DictBranchResult;
import com.cairh.cpe.backend.form.resp.DictResult;
import com.cairh.cpe.backend.service.IAuditCenterCommonService;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.cache.service.CacheCityCode;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.common.constant.DicConstant;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.response.BranchDictInfo;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.entity.response.CityInfo;
import com.cairh.cpe.common.entity.response.DictInfo;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.service.archive.request.FileInfo;
import com.cairh.cpe.service.archive.request.FileUriReq;
import com.cairh.cpe.service.archive.request.ImageInfo;
import com.cairh.cpe.service.archive.service.IArchiveService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class AuditCenterCommonServiceImpl implements IAuditCenterCommonService {

    @Autowired
    private IArchiveService archiveService;

    @Autowired
    private CacheDict cacheDict;

    @Autowired
    private CacheBranch cacheBranch;

    @Autowired
    private CacheCityCode cacheCityCode;




    @Override
    public String uploadBase64Image(String imageBase64) {
        ImageInfo imageInfo = new ImageInfo();
        imageInfo.setImage_name("common.png");
        imageInfo.setBase64_image(imageBase64);
        imageInfo.setRemark(WskhConstant.SUBSYS_ID);
        // 返回文件id
        return archiveService.electUploadImage(imageInfo);
    }

    @SneakyThrows
    @Override
    public String uploadFileImage(MultipartFile imageFile) {
        ImageInfo imageInfo = new ImageInfo();
        imageInfo.setImage_name("common.png");
        imageInfo.setBase64_image(Base64.encodeBase64String(IOUtils.toByteArray(imageFile.getInputStream())));
        imageInfo.setRemark(WskhConstant.SUBSYS_ID);
        // 返回文件id
        return archiveService.electUploadImage(imageInfo);
    }

    @Override
    public String uploadFileByUri(String FileUrl) { // 图片路径文件路径上传返回文件id（不下载）/
        FileUriReq fileUriReq = new FileUriReq();
        fileUriReq.setFile_path(FileUrl);
        fileUriReq.setRemark(WskhConstant.SUBSYS_ID);
        fileUriReq.setNot_down(true);
        // 返回文件id
        log.info("uploadFileByUri入参为[{}]", JSON.toJSONString(fileUriReq));
        return archiveService.electUploadFileByUri(fileUriReq);
    }


    @Override
    public String electUploadFile(byte[] file,String file_name) { // 上传失信记录文本内容
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFile(file);
        fileInfo.setFile_name(file_name+".txt");
        fileInfo.setRemark(WskhConstant.SUBSYS_ID);
        // 返回文件id
        log.info("electUploadFile入参为[{}]", JSON.toJSONString(fileInfo));
        return archiveService.electUploadFile(fileInfo);
    }

    @Override
    public List<DictResult> getDictionaryList(String dict_code_str) {
        List<DictResult> resultList = new ArrayList<>();
        String[] dictCodeArr = dict_code_str.split(",");

        for (String dictCode : dictCodeArr) {
            List<DictInfo> list = cacheDict.getDictListByDictCode(dictCode);
            // list = list.stream().filter(dict->StatusConstant.STATUS_VALID.equals(dict.getStatus())).collect(Collectors.toList());
            DictResult dictResult = new DictResult();
            dictResult.setDict_code(dictCode);
            dictResult.setDict_data(list);
            resultList.add(dictResult);
        }

        return resultList;
    }

    @Override
    public List<DictBranchResult> getBranchList(String branch_type) {
        List<DictBranchResult> resultList = new ArrayList<>();
        List<BranchInfo> branchList = cacheBranch.getAllBranchList();
        List<BranchDictInfo> list = new ArrayList<>();
        for(BranchInfo branchInfo :branchList){
            BranchDictInfo branchDictInfo = new BranchDictInfo();
            if (StringUtils.isNotBlank(branch_type)){
                if (StringUtils.equals(branch_type,branchInfo.getBranch_type())){
                    branchDictInfo.setDict_code("branch_no");
                    branchDictInfo.setDict_name("营业部");
                    branchDictInfo.setSub_code(branchInfo.getBranch_no());
                    branchDictInfo.setSub_name(branchInfo.getBranch_name());
                    branchDictInfo.setStatus(branchInfo.getStatus());
                    BaseBeanUtil.copyProperties(branchInfo,branchDictInfo);
                    list.add(branchDictInfo);
                }
            }else {
                if (!StringUtils.equals("4",branchInfo.getBranch_type())) {
                    branchDictInfo.setDict_code("branch_no");
                    branchDictInfo.setDict_name("营业部");
                    branchDictInfo.setSub_code(branchInfo.getBranch_no());
                    branchDictInfo.setSub_name(branchInfo.getBranch_name());
                    branchDictInfo.setStatus(branchInfo.getStatus());
                    BaseBeanUtil.copyProperties(branchInfo,branchDictInfo);
                    list.add(branchDictInfo);
                }
            }
        }
        DictBranchResult dictResult = new DictBranchResult();
        dictResult.setDict_data(list);
        dictResult.setDict_code("branch_no");
        resultList.add(dictResult);

        return resultList;
    }

    @Override
    public String getTranslationAddress(String address) {
        log.info("身份解析,address={}" ,address);
        String[] split = address.split("!~");
        //CHN!~360000!~360400!~360426!~唐镇创新中路
        if (split.length == 5) {
            String province_name = "";
            String city_name = "";
            String region_name = "";
            List<CityInfo> cityInfoList = cacheCityCode.getALlCityInfo();
            if (StringUtils.isNotBlank(split[1])) {
                province_name = cityInfoList.stream().filter(e-> e.getProvince_code().equals(split[1])).findFirst().orElse(new CityInfo()).getProvince_name();
            }
            if (StringUtils.isNotBlank(split[2])) {
                city_name = cityInfoList.stream().filter(e-> e.getCity_code().equals(split[2])).findFirst().orElse(new CityInfo()).getCity_name();
            }

            if (StringUtils.isNotBlank(split[3])) {
                region_name = cityInfoList.stream().filter(e-> e.getRegion_code().equals(split[3])).findFirst().orElse(new CityInfo()).getRegion_name();
            }

            String translation_address = province_name + city_name + region_name + split[4];
            return translation_address;
        } else {
            log.info("经常居住地址的格式不正确");
            return "经常居住地址的格式不正确";
        }
    }

    @Override
    public String getTranslationAddressCross(String address) {
        log.info("身份解析,address={}" ,address);
        String[] split = address.split("!~");
        //CHN!~360000!~360400!~360426!~唐镇创新中路
        if (split.length == 5) {
            String country = split[0];
            String dictDesc = cacheDict.getDictDesc(DicConstant.NATIONALITY_CODE, country);
            return dictDesc + split[4];
        } else {
            log.info("经常居住地址的格式不正确");
            return "经常居住地址的格式不正确";
        }
    }

    @Override
    public String getalternativeTranslationAddress(String address) {
        log.info("身份解析,address={}" ,address);
        String[] split = address.split("!~");
        //CHN!~360000!~360400!~360426!~唐镇创新中路
        if (split.length == 5) {
            String province_name = "";
            String city_name = "";
            String region_name = "";
            List<CityInfo> cityInfoList = cacheCityCode.getALlCityInfo();
            if (StringUtils.isNotBlank(split[1])) {
                province_name = cityInfoList.stream().filter(e-> e.getProvince_code().equals(split[1])).findFirst().orElse(new CityInfo()).getProvince_name();
            }
            if (StringUtils.isNotBlank(split[2])) {
                city_name = cityInfoList.stream().filter(e-> e.getCity_code().equals(split[2])).findFirst().orElse(new CityInfo()).getCity_name();
            }

            if (StringUtils.isNotBlank(split[3])) {
                region_name = cityInfoList.stream().filter(e-> e.getRegion_code().equals(split[3])).findFirst().orElse(new CityInfo()).getRegion_name();
            }
            String alternative_address = province_name + "!~" + city_name + "!~" + region_name + "!~" + split[4];
            return alternative_address;
        } else {
            log.info("经常居住地址的格式不正确");
            return "经常居住地址的格式不正确";
        }
    }

    public String getAddressCode(String address,String type) {
        String[] split = address.split("!~");
        //CHN!~360000!~360400!~360426!~唐镇创新中路
        String address_code ="";
        if (split.length == 5) {
            if (StringUtils.equals(type,"province")){
                address_code = split[1];
            }else if (StringUtils.equals(type,"city")){
                address_code = split[2];
            }else if (StringUtils.equals(type,"region")){
                address_code = split[3];
            }else {

            }
            return address_code;
        } else {
            log.info("经常居住地址的格式不正确");
            return "经常居住地址的格式不正确";
        }
    }

}

package com.cairh.cpe.backend.converter;

import com.cairh.cpe.backend.form.req.BusinessHandCreateForm;
import com.cairh.cpe.backend.form.resp.UserAcBaseInfo;
import com.cairh.cpe.backend.service.IAuditCenterCommonService;
import com.cairh.cpe.common.backend.util.BaseBeanUtil;
import com.cairh.cpe.context.convert.Converter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

@Slf4j
@Component
public class HandUserBaseInfoConvertUserAcBaseInfo implements Converter<BusinessHandCreateForm, UserAcBaseInfo> {

    @Autowired
    private IAuditCenterCommonService auditCenterCommonService;

    @Value("${account.submission.skipFtp:false}")
    private Boolean skipFtp;

    @Override
    public UserAcBaseInfo convert(BusinessHandCreateForm source, UserAcBaseInfo target) {
        BaseBeanUtil.copyProperties(source, target);
        // 将图片转换成文件id
        target.setPhoto_front(getFileRecordId(target.getPhoto_front()));
        target.setPhoto_back(getFileRecordId(target.getPhoto_back()));
        target.setAgent_photo_front(getFileRecordId(target.getAgent_photo_front()));
        target.setAgent_photo_back(getFileRecordId(target.getAgent_photo_back()));

        if (StringUtils.isNotBlank(target.getCert_file_path())) {
            List<String> filePathList = Arrays.asList(target.getCert_file_path().split(","));
            target.setCert_file_path(filePathList.stream()
                    .map(this::getFileRecordId)
                    .reduce((file1, file2) -> file1.isEmpty() ? file2 : file1 + "," + file2)
                    .orElse(""));
        }
        target.setOriginal_photo_front(target.getPhoto_front());
        target.setOriginal_photo_back(target.getPhoto_back());
        target.setOriginal_agent_photo_front(target.getAgent_photo_front());
        target.setOriginal_agent_photo_back(target.getAgent_photo_back());
        return target;
    }

    public String getFileRecordId(String fileUrl) {
        if (skipFtp) {
            return getSkipFileRecordId(fileUrl);
        } else {
            if (StringUtils.isNotBlank(fileUrl)) {
                return auditCenterCommonService.uploadFileByUri(fileUrl);
            }
        }
        return "";
    }

    public String getSkipFileRecordId(String fileUrl) {
        String filerecord_id = "";
        if (StringUtils.isBlank(fileUrl)) {
            return "";
        }
        try {
            File file = new File(fileUrl);
            if (file.exists()) {
                FileInputStream fileInputStream = new FileInputStream(file);
                byte[] bytes = new byte[(int) file.length()];
                fileInputStream.read(bytes);
                fileInputStream.close();
                String base64 = Base64.getEncoder().encodeToString(bytes);

                filerecord_id = auditCenterCommonService.uploadBase64Image(base64);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            return filerecord_id;
        }
    }
}

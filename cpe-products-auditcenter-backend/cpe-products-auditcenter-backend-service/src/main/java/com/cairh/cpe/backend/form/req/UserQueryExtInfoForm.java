package com.cairh.cpe.backend.form.req;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.cairh.cpe.common.util.SqlDateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = false)
@Data
public class UserQueryExtInfoForm extends PageInfo {

    //客户姓名
    private String client_name;
    //客户手机号
    private String mobile_tel;
    //客户身份证号
    private String id_no;

    /**
     * 推广关系 名称
     */
    private String broker_name;

    /**
     * 证件类别
     */
    private String id_kind;
    /**
     * 状态
     */
    private String request_status;

    //渠道编号
    private String channel_code;

    /**
     * 接入方式
     */
    private String app_id;

    /**
     * 接入方式
     */
    private List<String> app_ids;


    //审核操作员姓名和编号
    private String audit_operator_no;
    private String audit_operator_name;
    //复核操作员姓名和编号
    private String review_operator_no;
    private String review_operator_name;
    //二次复核操作员姓名和编号
    private String double_operator_no;
    private String double_operator_name;


    /**
     * * 活动编号
     */
    private String activity_no;

    /**
     * *活动名称（营销活动）
     */
    private String activity_name;

    /**
     * *视频见证方式
     */
    private String video_type;

    //申请时间开始
    private String request_datetime_start;
    //申请时间结束
    private String request_datetime_end;


    //条件营业部
    private String branch_no;
    //营业部多选功能
    private List<String> branch_nos;

    //分公司名称
    private String company_name;
    //分公司多选
    private List<String> company_names;
    /**
     * 业务类型
     */
    private String busin_type;


    /**
     * 业务流水号
     */
    private String request_no;
    /**
     * *手机号归属地
     */
    private String mobile_location;

    //审核通过时间开始
    private String audit_datetime_start;
    //审核通过时间结束
    private String audit_datetime_end;

    //复核通过时间开始
    private String review_datetime_start;
    //复核通过时间结束
    private String review_datetime_end;

    //复核通过时间开始
    private String double_datetime_start;
    //复核通过时间结束
    private String double_datetime_end;
    /**
     * 资金账户
     */
    private String fund_account;
    /**
     * 客户号
     */
    private String client_id;

    /**
     * 营销团队
     */
    private String marketing_team;

    /**
     * 业务类型（多选）
     */
    private List<String> busin_types;

    /**
     * 业务类型（多选）
     */
    private List<Integer> busin_types_int;


    /**
     * 业务状态(多选)
     */
    private List<String> request_status_list;

    //audit-1,review-2等等
    private List<String> request_status_type_list;

    /**
     * 任务类型
     */
    private List<String> task_types;

    /**
     * 开户渠道
     */
    private String open_channel;


    /**
     * 当前页数
     */
    private Integer cur_page = 1;

    /**
     * 每页数量
     */
    private Integer page_size = 10;

    /**
     * 排序方式
     */
    private List<OrderItem> order_item;

    /**
     * 可操作营业部
     */
    private List<String> operator_branch;

    /**
     * 审核通过时当前节点信息
     */
    private String end_node;

    /**
     * 当前流程节点
     */
    private String anode_id;

    public void setAudit_datetime_start(String audit_datetime_start) {
        this.audit_datetime_start = SqlDateUtil.getDateStartDetail(audit_datetime_start);
    }

    public void setAudit_datetime_end(String audit_datetime_end) {
        this.audit_datetime_end = SqlDateUtil.getDateStartDetail(audit_datetime_end);
    }

    public void setReview_datetime_start(String review_datetime_start) {
        this.review_datetime_start = SqlDateUtil.getDateStartDetail(review_datetime_start);
    }

    public void setReview_datetime_end(String review_datetime_end) {
        this.review_datetime_end = SqlDateUtil.getDateStartDetail(review_datetime_end);
    }

    public void setDouble_datetime_start(String double_datetime_start) {
        this.double_datetime_start = SqlDateUtil.getDateStartDetail(double_datetime_start);
    }

    public void setDouble_datetime_end(String double_datetime_end) {
        this.double_datetime_end = SqlDateUtil.getDateStartDetail(double_datetime_end);
    }

    public void setRequest_datetime_start(String request_datetime_start) {
        this.request_datetime_start = SqlDateUtil.getDateStartDetail(request_datetime_start);
    }

    public void setRequest_datetime_end(String request_datetime_end) {
        this.request_datetime_end = SqlDateUtil.getDateStartDetail(request_datetime_end);
    }
}

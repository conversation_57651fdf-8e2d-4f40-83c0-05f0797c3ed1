package com.cairh.cpe.backend.form.req;

import com.cairh.cpe.common.constant.WskhConstant;
import lombok.Data;

@Data
public class TransferTaskMessage {

    /**
     * 业务编号
     */
    private String request_no;

    private String flow_task_id;
    /**
     * 转交人编号
     */
    private String operator_no;

    private String transfer_operator_no;
    private String transfer_operator_name;

    /**
     * 任务类型
     */
    private String task_type;

    /**
     * 转交原因
     */
    private String message_content;

    /**
     * 消息类型
     */
    private String message_type = WskhConstant.TASK_TRANSFER;

    /**
     * 第一段持续时间(秒)
     */
    private Integer transfer_first_duration;

    /**
     * 第二段持续时间(秒)
     */
    private Integer transfer_second_duration;
}

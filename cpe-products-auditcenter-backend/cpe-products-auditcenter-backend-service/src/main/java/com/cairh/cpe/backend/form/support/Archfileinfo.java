package com.cairh.cpe.backend.form.support;

import lombok.Data;

@Data
public class Archfileinfo {

    /**
     * 人脸识别分数（证通分数）
     * 大头照与证件照片 非中登时间提交，才会将此字段传至见证
     */
    private String face_score;

    /**
     * 证通分数说明
     */
    private String face_score_desc;

    /**
     * 身份证正面图片链接
     */
    private String file_6A;

    /**
     * 身份证反面图片链接
     */
    private String file_6B;

    /**
     * 免冠照链接
     */
    private String file_80;

    /**
     * 公安头像地址
     */
    private String file_82;

    /**
     * 单项视频链接
     */
    private String file_8A;

    /**
     * 辅证件正面
     */
    private String file_7C;

    /**
     * 辅证件反面
     */
    private String file_7D;

    /**
     * 开户人脸比对分数(公安和免冠照)
     * 免冠照与公安照   中登时间会将此字段传至见证
     */
    private String kh_face_score_82_80;

    /**
     * 机构的营业执照
     */
    private String file_Bm;

    /**
     * 境内机构出具的就业证明
     */
    private String file_7W;

    /**
     * 住宿登记证明表原件
     */
    private String file_7X;
}

package com.cairh.cpe.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.backend.form.req.BaseInfoForm;
import com.cairh.cpe.backend.form.resp.RecentRejectReasonResp;
import com.cairh.cpe.backend.service.IAuditDetailRejectReasonService;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowRecordEnum;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.Label;
import com.cairh.cpe.common.entity.TaskReasonRecord;
import com.cairh.cpe.common.entity.UserQueryExtInfo;
import com.cairh.cpe.common.entity.request.BusinFlowRecordForm;
import com.cairh.cpe.common.entity.response.BusinFlowRecordResp;
import com.cairh.cpe.common.mapper.LabelMapper;
import com.cairh.cpe.common.service.IBusinFlowRecordService;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.ITaskReasonRecordService;
import com.cairh.cpe.common.service.IUserQueryExtInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AuditDetailRejectReasonServiceImpl implements IAuditDetailRejectReasonService {

    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private IBusinFlowRecordService businFlowRecordService;
    @Autowired
    private IUserQueryExtInfoService userQueryExtInfoService;

    @Resource
    private ITaskReasonRecordService taskReasonRecordService;

    @Autowired
    private LabelMapper labelMapper;

    @Autowired
    private CacheDict cacheDict;

    @Override
    public RecentRejectReasonResp getLastRejectReason(BaseInfoForm baseInfoForm) {
        RecentRejectReasonResp recentRejectReasonResp = new RecentRejectReasonResp();
        BusinFlowTask businFlowTask = businFlowTaskService.getById(baseInfoForm.getFlowtask_id());
        recentRejectReasonResp.setBranch_repeated(businFlowTask.getBranch_repeated());
        recentRejectReasonResp.setAddress_repeated(businFlowTask.getAddress_repeated());
        LambdaQueryWrapper<UserQueryExtInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserQueryExtInfo::getRequest_no, baseInfoForm.getRequest_no());
        List<UserQueryExtInfo> userQueryExtInfos = userQueryExtInfoService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(userQueryExtInfos)) {
            UserQueryExtInfo userQueryExtInfo = userQueryExtInfos.get(0);
            recentRejectReasonResp.setAudit_operator_no(userQueryExtInfo.getAudit_operator_no());
            recentRejectReasonResp.setAudit_operator_name(userQueryExtInfo.getAudit_operator_name());
            recentRejectReasonResp.setReview_operator_no(userQueryExtInfo.getReview_operator_no());
            recentRejectReasonResp.setReview_operator_name(userQueryExtInfo.getReview_operator_name());
            recentRejectReasonResp.setDouble_operator_no(userQueryExtInfo.getDouble_operator_no());
            recentRejectReasonResp.setDouble_operator_name(userQueryExtInfo.getDouble_operator_name());
        }
        LambdaQueryWrapper<BusinFlowTask> taskWrapper = new LambdaQueryWrapper<>();
        taskWrapper.eq(BusinFlowTask::getRequest_no, baseInfoForm.getRequest_no());
        taskWrapper.eq(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_NO_PASS);
        int reject_num = businFlowTaskService.count(taskWrapper);
        recentRejectReasonResp.setReject_num(reject_num + "");

        LambdaQueryWrapper<TaskReasonRecord> reasonWrapper = new LambdaQueryWrapper<>();
        reasonWrapper.eq(TaskReasonRecord::getRequest_no, baseInfoForm.getRequest_no());
        reasonWrapper.orderByDesc(TaskReasonRecord::getCreate_datetime);
        List<TaskReasonRecord> reasonRecordList = taskReasonRecordService.list(reasonWrapper);
        if (CollectionUtils.isNotEmpty(reasonRecordList)) {
            String task_id = reasonRecordList.get(0).getTask_id();
            recentRejectReasonResp.setLast_reject_reason(reasonRecordList.stream().filter(item -> StringUtils.equals(item.getTask_id(), task_id))
                    .map(TaskReasonRecord::getReason_desc)
                    .collect(Collectors.joining("")));
        }

        if (StringUtils.isNotBlank(businFlowTask.getMatch_labels())) {
            LambdaQueryWrapper<Label> labelWrapper = new LambdaQueryWrapper<>();
            List<String> collect = Arrays.stream(businFlowTask.getMatch_labels().split(","))
                    .map(String::trim)
                    .collect(Collectors.toList());
            labelWrapper.in(Label::getSerial_id, collect);
            List<Label> labels = labelMapper.selectList(labelWrapper);
            recentRejectReasonResp.setLabels(labels);
        }
        recentRejectReasonResp.setTask_id(businFlowTask.getSerial_id());
        recentRejectReasonResp.setTask_type(businFlowTask.getTask_type());
        recentRejectReasonResp.setTask_type_status(businFlowTask.getTask_type() + "-" + businFlowTask.getTask_status());
        recentRejectReasonResp.setTask_type_status_str(cacheDict.getDictDesc(WskhConstant.DICT_TASK_STATUS, recentRejectReasonResp.getTask_type_status()));
        return recentRejectReasonResp;
    }


    @Override
    public List<BusinFlowRecordResp> getHisRejectReason(String request_no) {
        BusinFlowRecordForm businFlowRecordForm = new BusinFlowRecordForm();
        businFlowRecordForm.setRequest_no(request_no);
        List<String> business_flags = new ArrayList<>();
        business_flags.add(FlowRecordEnum.B1003.getValue());
        business_flags.add(FlowRecordEnum.B1103.getValue());
        business_flags.add(FlowRecordEnum.B1203.getValue());
        businFlowRecordForm.setBusiness_flags(business_flags);
        List<BusinFlowRecordResp> businFlowRecordResps = businFlowRecordService.qryUserApplyRecord(businFlowRecordForm);
        return businFlowRecordResps;
    }
}

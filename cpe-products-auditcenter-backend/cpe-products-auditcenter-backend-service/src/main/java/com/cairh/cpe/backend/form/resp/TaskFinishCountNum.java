package com.cairh.cpe.backend.form.resp;

import lombok.Data;

@Data
public class TaskFinishCountNum { //统计任务 00:00:00到当前时间工作员所处理的任务数量

    /**
     * 总任务数
     */
    private String task_num = "0";

    /**
     * 见证任务数
     */
    private String audit_num = "0";

    /**
     * 复核任务数
     */
    private String review_num = "0";

    /**
     * 二次复核任务数
     */
    private String secondary_review_num = "0";



    /**
     * 见证通过数
     */
    private String audit_pass_num = "0";

    /**
     * 见证不通过数
     */
    private String audit_notpass_num = "0";

    /**
     * 复核通过数
     */
    private String review_pass_num = "0";

    /**
     * 复核不通过数
     */
    private String review_notpass_num = "0";

    /**
     * 二次复核通过数
     */
    private String secondary_review_pass_num = "0";

    /**
     * 二次复核不通过数
     */
    private String secondary_review_notpass_num = "0";

}

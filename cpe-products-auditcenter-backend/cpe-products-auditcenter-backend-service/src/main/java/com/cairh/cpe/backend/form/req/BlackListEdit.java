package com.cairh.cpe.backend.form.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class BlackListEdit {


    /**
     * 主键
     */
    @NotBlank(message = "主键不能为空")
    private String serial_id;

    /**
     * 关键字
     */
    @NotBlank(message = "内容不能为空")
    private String content;

}

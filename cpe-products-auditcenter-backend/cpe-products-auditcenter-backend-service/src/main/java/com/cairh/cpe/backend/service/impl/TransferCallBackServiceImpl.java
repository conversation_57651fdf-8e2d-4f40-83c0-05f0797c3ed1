package com.cairh.cpe.backend.service.impl;

import com.alibaba.fastjson.JSON;
import com.cairh.cpe.backend.service.ITransferCallBackService;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.entity.support.TaskMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
public class TransferCallBackServiceImpl implements ITransferCallBackService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public void notify(TaskMessage message) {
        redisTemplate.convertAndSend(Constant.CHANNEL_TRANSFER_TASK, JSON.toJSONString(message));
    }
}

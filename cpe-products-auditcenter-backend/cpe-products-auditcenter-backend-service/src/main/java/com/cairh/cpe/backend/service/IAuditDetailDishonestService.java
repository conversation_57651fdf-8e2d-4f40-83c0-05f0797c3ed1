package com.cairh.cpe.backend.service;

import com.cairh.cpe.backend.form.req.DishonestForm;
import com.cairh.cpe.backend.form.req.UploadDishonestImageForm;
import com.cairh.cpe.backend.form.req.UploadRcpMisRequest;
import com.cairh.cpe.backend.form.resp.DishonestDetail;
import com.cairh.cpe.backend.form.resp.DishonestResp;
import com.cairh.cpe.common.entity.response.QueryDishonestResult;
import com.cairh.cpe.context.BaseUser;

import java.util.Map;

public interface IAuditDetailDishonestService {

    /**
     * 上传失信记录
     */
    Map<String, Object> uploadDishonestImage(UploadDishonestImageForm uploadDishonestImageForm);

    /**
     * 失信记录以及备注修改
     */
    void modifyDishonestRecordAndRemark(DishonestForm dishonestForm);

    DishonestResp getDishonestRecord(String request_no);


    QueryDishonestResult getSecurityAlisaRpa(String request_no, String operator_no);

    DishonestResp getCrhDishonestImage(String request_no);

    DishonestDetail queryDetailDishonestContent(String dishonest_content);

    /**
     * 上传核查记录
     *
     * @param baseUser
     * @param uploadRcpMisRequest
     * @return
     */
    void uploadRpcMisInfo(BaseUser baseUser, UploadRcpMisRequest uploadRcpMisRequest);
}

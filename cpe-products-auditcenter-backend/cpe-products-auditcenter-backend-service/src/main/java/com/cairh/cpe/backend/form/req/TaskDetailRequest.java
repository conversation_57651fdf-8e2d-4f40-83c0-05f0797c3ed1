package com.cairh.cpe.backend.form.req;

import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.util.SqlDateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = false)
public class TaskDetailRequest extends PageBaseRequest {

    private String operator_no;

    private String operator_name;


    /**
     * 推广关系 名称
     */
    private String broker_name;

    /**
     * true 自动派发
     * false 停止派发
     */
    private String operator_status;

    private String user_role;


    private List<String> task_types_role;
    /**
     * 客户姓名
     */
    private String client_name;

    /**
     * 手机号码
     */
    private String mobile_tel;

    /**
     * 证件类型
     */
    private String id_kind;

    /**
     * 证件号码
     */
    private String id_no;

    /**
     * 渠道编码
     */
    private String channel_code;

    /**
     * 渠道名称
     */
    private String channel_name;

    /**
     * 活动码
     */
    private String activity_no;

    /**
     * 活动名称
     */
    private String activity_name;

    /**
     * 营销团队
     */
    private String marketing_team;

    /**
     * 见证人姓名
     */
    private String audit_operator_name;

    /**
     * 见证人工号
     */
    private String audit_operator_no;

    /**
     * 复核人姓名
     */
    private String review_operator_name;

    /**
     * 复核人工号
     */
    private String review_operator_no;

    /**
     * 二次复核人员姓名
     */
    private String double_operator_name;

    /**
     * 二次复核人员工号
     */
    private String double_operator_no;

    //审核通过时间开始
    private String audit_datetime_start;
    //审核通过时间结束
    private String audit_datetime_end;

    //复核通过时间开始
    private String review_datetime_start;
    //复核通过时间结束
    private String review_datetime_end;

    //复核通过时间开始
    private String double_datetime_start;
    //复核通过时间结束
    private String double_datetime_end;


    /**
     * 开户申请提交开始时间
     */
    private String request_datetime_start;


    /**
     * 开户申请提交结束时间
     */
    private String request_datetime_end;

    /**
     * 视频见证方式
     */
    private String video_type;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 业务类型（多选）
     */
    private List<String> busin_types;

    /**
     * 所属营业部
     */
    private String branch_no;

    /**
     * 任务状态
     */
    private String task_status;

    private List<String> task_status_type_list;


    /**
     * 可操作营业部
     */
    private List<String> operator_branch;

    /**
     * 是否开启智能派单（0 关闭 1 开启）
     */
    private String isOpenDispatch;


    /**
     * 接入方式
     */
    private String app_id;


    /**
     * 接入方式
     */
    private List<String> app_ids;


    /**
     * 开户渠道
     */
    private String open_channel;


    /**
     * 任务来源
     */
    private String task_source;

    /**
     * 任务提交时间开始 开始
     */
    private String task_create_datetime_start;

    /**
     * 任务提交时间结束 结束
     */
    private String task_create_datetime_end;


    /**
     * 处理时间 开始
     */
    private String deal_datetime_start;

    /**
     * 处理时间 结束
     */
    private String deal_datetime_end;

    /**
     * 处理人工号
     */
    private String deal_operator_no;

    /**
     * 处理人姓名
     */
    private String deal_operator_name;

    /**
     * 手动认领数据
     */
    private String manual_claim_data = "0";

    /**
     * 0 待处理tab
     * 1 已完成tab
     */
    private String is_task_id_data;

    /**
     * 任务中心
     * 0 - 待处理
     * 1 - 已完成
     * 2 - 暂存
     */
    private String complete_flag;

    /**
     * 1 姓名 2 手机号码  3 证件号码
     */
    private Integer queryType;

    private String request_no;

    private String client_category;


    /**
     * 外呼标识 0 -否 1-是
     */
    private String call_flag;

    /**
     * 标签列表
     */
    private String match_label;

    /**
     * 绿通
     */
    private String white_flag;

    /**
     * 推送状态
     */
    private String push_flag;

    /**
     * 推送状态-多个
     */
    private List<String> push_flags;

    public void setMatch_label(String match_label) {
        if (StringUtils.equalsAny(match_label, WskhConstant.WHITE_FLAG_LABEL)) {
            this.setWhite_flag("1");
        } else {
            this.match_label = match_label;
        }
    }


    public void setAudit_datetime_start(String audit_datetime_start) {
        this.audit_datetime_start = SqlDateUtil.getDateStartDetail(audit_datetime_start);
    }

    public void setAudit_datetime_end(String audit_datetime_end) {
        this.audit_datetime_end = SqlDateUtil.getDateStartDetail(audit_datetime_end);
    }

    public void setReview_datetime_start(String review_datetime_start) {
        this.review_datetime_start = SqlDateUtil.getDateStartDetail(review_datetime_start);
    }

    public void setReview_datetime_end(String review_datetime_end) {
        this.review_datetime_end = SqlDateUtil.getDateStartDetail(review_datetime_end);
    }

    public void setDouble_datetime_start(String double_datetime_start) {
        this.double_datetime_start = SqlDateUtil.getDateStartDetail(double_datetime_start);
    }

    public void setDouble_datetime_end(String double_datetime_end) {
        this.double_datetime_end = SqlDateUtil.getDateStartDetail(double_datetime_end);
    }

    public void setRequest_datetime_start(String request_datetime_start) {
        this.request_datetime_start = SqlDateUtil.getDateStartDetail(request_datetime_start);
    }

    public void setRequest_datetime_end(String request_datetime_end) {
        this.request_datetime_end = SqlDateUtil.getDateStartDetail(request_datetime_end);
    }


    public void setId_kind(String id_kind) {
        this.id_kind = id_kind;
    }


    public void setApp_id(String app_id) {
        this.app_id = app_id;
        if (StringUtils.isNotBlank(app_id)) {
            this.setApp_ids(Arrays.stream(app_id.split(",")).collect(Collectors.toList()));
        }
    }


    public void setBusin_type(String busin_type) {
        this.busin_type = busin_type;
        if (StringUtils.isNotBlank(busin_type)) {
            this.setBusin_types(Arrays.stream(busin_type.split(",")).collect(Collectors.toList()));
        }
    }


}

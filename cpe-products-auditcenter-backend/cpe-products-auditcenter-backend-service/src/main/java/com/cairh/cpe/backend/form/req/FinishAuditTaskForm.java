package com.cairh.cpe.backend.form.req;

import com.cairh.cpe.common.entity.clob.AuditReason;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class FinishAuditTaskForm {

    @NotBlank(message = "申请编号不能为空")
    private String request_no;

    @NotBlank(message = "任务编号不能为空")
    private String task_id;

    /**
     * 不通过原因
     */
    private List<AuditReason> reasons;

    /**
     * 删除智能审核不通过的不通过项
     */
    private String ai_nopass_reason_list;

    /**
     * 删除智能审核不通过的原因
     */
    private String ai_nopass_reason;

    /**
     * 删除智能审核不通过的其他原因
     */
    private String ai_nopass_reason_other;


    /**
     * 强制通过的理由
     */
    private String must_pass_reason;

    /**
     * 审批警示条数
     */
    private Integer audit_warning_num;


}

package com.cairh.cpe.backend.form.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = false)
@Data
public class BusinFlowRecordQueryForm extends PageInfo {
    //客户姓名
    private String client_name;
    //客户手机号
    private String mobile_tel;
    //客户身份证号
    private String id_no;


    private List<String> branch_nos;
    //可操作营业部
    private List<String> operator_branch;
    //流水类型  0用户流水 1审核流水 2办理流水
    private String record_type;
    //视频类型
    private String video_type;
    //操作员编号
    private String operator_no;
    //操作员姓名
    private String operator_name;
    //渠道编号
    private String channel_code;
    //渠道名称
    private String channel_name;
    //业务标志
    private String business_flag;
    //申请时间开始和
    private String create_datetime_start;
    //申请时间结束
    private String create_datetime_end;
    //申请编号
    private String request_no;
    /**
     * 推荐人编号
     */
    private String broker_code;
    /**
     * 证件类型
     */
    private String id_kind;

    /**
     * 业务状态
     */
    private String request_status;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 接入方式
     */
    private String app_id;


    /**
     * 开户渠道
     */
    private String open_channel;



    /**
     * 所属营业部
     */
    private String branch_no;

    /**
     * 活动名称
     */
    private String activity_name;


    /**
     * 营销团队
     */
    private String marketing_team;


    /**
     * 任务类型
     */
    private String task_type;


}

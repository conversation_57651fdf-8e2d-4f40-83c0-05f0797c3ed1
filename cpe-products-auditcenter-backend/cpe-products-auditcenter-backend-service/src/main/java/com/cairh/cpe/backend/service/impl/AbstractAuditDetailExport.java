package com.cairh.cpe.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.common.constant.WskhConstant;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * Description：见证详情导出抽象类
 * Author： slx
 * Date： 2024/4/8 14:49
 */
@Slf4j
public abstract class AbstractAuditDetailExport<Q, R, E> {

    /**
     * 执行数据查询并转换，通过wrapper查询
     *
     * @param query    查询对象
     * @param countNum 查询总数
     * @param wrapper  查询语句
     * @return 导出数据结果集
     */
    List<R> executeDataQueryAndTransformation(Q query, int countNum, LambdaQueryWrapper<E> wrapper, String sourceCode) throws Exception {
        List<R> allResults = new ArrayList<>();
        try {
            // 初始化缓存数据
            initCacheData();
            // 获取查询次数
            int queryTimes = (countNum + WskhConstant.DATA_EXPORT_PAGE_SIZE - 1) / WskhConstant.DATA_EXPORT_PAGE_SIZE;
            List<R> records = new ArrayList<>();
            for (int i = 1; i <= queryTimes; i++) {
                refreshQuery(records, query, wrapper);
                Page<R> list = getPageData(query, wrapper, sourceCode);
                records = list.getRecords();
                allResults.addAll(records);
            }
            allResults.forEach(result -> convertData(result, query));
        } catch (Exception e) {
            log.error("executeDataQueryAndTransformation is error ", e);
            throw new RuntimeException(e);
        }
        return allResults;
    }

    /**
     * 查询总数
     *
     * @param query      查询对象
     * @param wrapper    查询条件
     * @param sourceCode 来源code
     * @return 查询总数
     */
    abstract int queryCount(Q query, LambdaQueryWrapper<E> wrapper, String sourceCode);

    /**
     * 转换数据：结果返回，个别字段带有注解@DataConvert进行转换数据，导出数据过多时，耗时过长，采用分批处理
     *
     * @param result 返回结果对象
     * @param query  查询对象
     */
    abstract void convertData(R result, Q query);

    /**
     * 获取分页数据，带有wrapper查询条件
     *
     * @param query      查询对象
     * @param wrapper    查询条件
     * @param sourceCode 来源code
     * @return 分页数据
     */
    abstract Page<R> getPageData(Q query, LambdaQueryWrapper<E> wrapper, String sourceCode);


    /**
     * 刷新筛选条件
     * 每次查询1000条，获取最后一条数据的no，通过设置比对no条件进行下一次查询（request_no）
     *
     * @param records 查询结果集
     * @param query   查询对象
     * @param wrapper 查询条件
     */
    abstract LambdaQueryWrapper<E> refreshQuery(List<R> records, Q query, LambdaQueryWrapper<E> wrapper);

    /**
     * 初始化缓存数据
     */
    abstract void initCacheData();

}

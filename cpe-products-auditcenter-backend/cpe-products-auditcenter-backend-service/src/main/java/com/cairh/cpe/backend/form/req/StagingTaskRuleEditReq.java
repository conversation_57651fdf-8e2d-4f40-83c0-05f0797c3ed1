package com.cairh.cpe.backend.form.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 规则编辑请求
 *
 * <AUTHOR>
 * @since 2025/3/12 09:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StagingTaskRuleEditReq {

    /**
     * 规则唯一编号
     */
    @NotBlank(message = "规则唯一编号不能为空")
    private String serial_id;

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    private String rule_name;

    /**
     * 规则类型（1-暂存任务）
     */
    @NotBlank(message = "规则类型不能为空")
    private String rule_type;

    /**
     * 规则开始时间
     */
    @NotBlank(message = "不参与派单开始时间不能为空")
    private String rule_datetime_start;

    /**
     * 规则结束时间
     */
    @NotBlank(message = "不参与派单结束时间不能为空")
    private String rule_datetime_end;

    /**
     * 顺序，优先级
     */
    @NotBlank(message = "优先级不能为空")
    private Integer order_no;

    /**
     * 可选规则
     */
    @NotBlank(message = "不参与派单条件不能为空")
    private String expression;

    /**
     * 状态（1-可用，0-禁用）
     */
    @NotBlank(message = "状态不能为空")
    private String status;

    /**
     * 描述
     */
    private String description;

}

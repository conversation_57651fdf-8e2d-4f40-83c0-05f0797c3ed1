package com.cairh.cpe.backend.form.req;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PageBaseRequest {


    private static final int DEFAULT_PAGE_NUM = 1;

    private  static final int MIN_PAGE_NUM = 0 ;
    /**
     * 当前页数
     */
    private Integer cur_page = 1;

    /**
     * 每页数量
     */
    private Integer page_size = 10;


    private static  final int MAX_PAGE_SIZE = 1000;

    private static final int DEFAULT_PAGE_SIZE = 10;
    /**
     *排序方式
     */
    private List<OrderItem> order_item;



    public Integer getPageNum() {
        if (null == cur_page || cur_page.intValue() <= MIN_PAGE_NUM) {
            return DEFAULT_PAGE_NUM;
        }
        return cur_page;
    }

    public Integer getPageSize() {
        if (null == page_size || page_size.intValue() <= MIN_PAGE_NUM || page_size.intValue()  > MAX_PAGE_SIZE ) {
            return DEFAULT_PAGE_SIZE;
        }
        return page_size;
    }
}

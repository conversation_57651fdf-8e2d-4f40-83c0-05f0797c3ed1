package com.cairh.cpe.backend.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.UserQueryExtInfoForm;
import com.cairh.cpe.backend.form.resp.UserQueryExtInfoDetail;
import org.apache.ibatis.annotations.Param;

public interface UserQueryExtInfoBaseMapper {

    Integer countUserQueryExtInfo(@Param("queryForm") UserQueryExtInfoForm query);

    Page<UserQueryExtInfoDetail> selectUserQueryExtInfoListByPage(Page<UserQueryExtInfoDetail> page, @Param("queryForm") UserQueryExtInfoForm userQueryExtInfoForm);

    Page<UserQueryExtInfoDetail> selectUserQueryExtInfoExportListByPage(Page<UserQueryExtInfoDetail> page, @Param("queryForm") UserQueryExtInfoForm userQueryExtInfoForm);

}

package com.cairh.cpe.backend.form.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ValidateCitizenDocumentForm {

    private String operator_no;
    private String operator_name;

    /**
     * 业务编号
     */
    @NotBlank(message = "业务编号不能为空")
    private String request_no;

    /**
     * 证件开始日期
     */
    private String id_begindate;

    /**
     * 证件结束日期
     */
    private String id_enddate;

    /**
     * 任务id
     */
    @NotBlank(message = "task_id不能为空")
    private String task_id;

    /**
     * 英文姓名
     */
    private String english_name;

    /**
     * 曾拥有证件号
     */
    private String prev_id_number;

    /**
     * 国籍
     */
    private String nationality;

}

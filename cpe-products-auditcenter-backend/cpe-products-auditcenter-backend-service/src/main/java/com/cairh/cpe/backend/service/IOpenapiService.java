package com.cairh.cpe.backend.service;


import com.cairh.cpe.backend.form.req.GetAuditResultForm;
import com.cairh.cpe.common.entity.response.AuditResultResp;
import com.cairh.cpe.common.entity.response.SubmitAuditResp;
import com.cairh.cpe.context.Result;

import java.util.Map;

public interface IOpenapiService {

    SubmitAuditResp openapiSubmitAudit(Map<String, Object> params);

    AuditResultResp openapiGetAuditResult(GetAuditResultForm baseForm);

    /**
     * 创建复核记录
     *
     * @param taskId
     * @return
     */
    Result openapiSubmitBidirectionalAudit(String taskId);

}

package com.cairh.cpe.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.UserQueryExtInfoForm;
import com.cairh.cpe.backend.form.resp.UserQueryExtInfoDetail;
import com.cairh.cpe.backend.form.resp.UserQueryExtInfoResultExport;
import com.cairh.cpe.backend.mapper.UserQueryExtInfoBaseMapper;
import com.cairh.cpe.backend.service.IUserQueryExtInfoHandleService;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.common.constant.DicConstant;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.entity.UserQueryExtInfo;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.entity.response.DictInfo;
import com.cairh.cpe.common.service.IUserQueryExtInfoService;
import com.cairh.cpe.common.util.SqlDateUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.service.config.AiAuditThreadPoolTaskExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Description：客户查询扩展信息业务处理实现
 * Author： slx
 * Date： 2024/4/8 11:33
 */
@Slf4j
@Component
public class UserQueryExtInfoHandleServiceImpl extends AbstractAuditDetailExport<UserQueryExtInfoForm, UserQueryExtInfoResultExport, UserQueryExtInfo> implements IUserQueryExtInfoHandleService {

    private final Map<String, DictInfo> dictInfoMap = new ConcurrentHashMap<>(128);
    private final Map<String, BranchInfo> branchInfoMap = new ConcurrentHashMap<>(128);
    @Autowired
    private CacheBackendUser backendUser;
    @Autowired
    private CacheBranch cacheBranch;
    @Resource
    private AiAuditThreadPoolTaskExecutor aiAuditThreadPoolTaskExecutor;
    @Autowired
    private IUserQueryExtInfoService userQueryExtInfoService;
    @Resource
    private CacheDict cacheDict;
    @Resource
    private UserQueryExtInfoBaseMapper userQueryExtInfoBaseMapper;

    @Override
    public List<UserQueryExtInfoResultExport> exportUserInfoList(BaseUser baseUser, UserQueryExtInfoForm userQueryExtInfoForm) {
        // 权限验证
//        if (this.isPermissionDenied(baseUser)) {
//            throw new BizException("-9997", ResultMessageConstant.INSUFFICIENT_EXPORT_PERMISSIONS_MESSAGE);
//        }
        String operatorBranchNo = baseUser.getBranch_no();
        List<String> operatorBranchList = new ArrayList<>();
        // 非营运中心营业部
        if (!StringUtils.equals(WskhConstant.SPECIAL_BRANCH, operatorBranchNo)) {
            operatorBranchList = backendUser.getEnBranchNo(baseUser.getStaff_no());
        }
        // 勾选营业部
        if (StringUtils.isNotBlank(userQueryExtInfoForm.getBranch_no())) {
            List<String> branchNoList = Arrays.asList(userQueryExtInfoForm.getBranch_no().split(","));
            // 营运中心营业部,拥有所有营业部权限
            if (StringUtils.equals(WskhConstant.SPECIAL_BRANCH, operatorBranchNo)) {
                userQueryExtInfoForm.setBranch_nos(branchNoList);
            } else {
                // 获取营业部交集
                List<String> newBranchNoList = operatorBranchList.stream()
                        .filter(branchNoList::contains)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(newBranchNoList)) {
                    log.info("用户[{}]无权限查看该查询条件[{}]", baseUser.getStaff_no(), userQueryExtInfoForm.getBranch_no());
                    return Collections.emptyList();
                }
                userQueryExtInfoForm.setBranch_nos(newBranchNoList);
            }
        } else {
            userQueryExtInfoForm.setBranch_nos(operatorBranchList);
        }
        // 业务状态
        if (StringUtils.isNotBlank(userQueryExtInfoForm.getRequest_status())) {
            String[] taskTypeArr = userQueryExtInfoForm.getRequest_status().split("-");
            if (taskTypeArr.length == 2) {
                // 复核通过or二次复核通过 需通过end_node作查询条件
                if (StringUtils.equalsAny(userQueryExtInfoForm.getRequest_status(), WskhConstant.REVIEW_3, WskhConstant.SECONDARY_REVIEW_3)) {
                    userQueryExtInfoForm.setEnd_node(taskTypeArr[0]);
                } else {
                    userQueryExtInfoForm.setAnode_id(taskTypeArr[0]);
                }
                userQueryExtInfoForm.setRequest_status(taskTypeArr[1]);
            }
        }
        // 查询总数
        int countNum = this.queryCount(userQueryExtInfoForm, null, "");
        if (countNum == 0) {
            return Collections.emptyList();
        }
        // 计数
        if (countNum > 50000) {
            log.error("查询客户信息列表，导出数据超过50000条，请重新选择查询条件");
            throw new BizException("-9997", ErrorEnum.TOO_MUCH_DATA_MESSAGE.getDesc());
        }
        List<UserQueryExtInfoResultExport> userQueryExtInfoResultExportList;
        try {
            userQueryExtInfoResultExportList = super.executeDataQueryAndTransformation(userQueryExtInfoForm, countNum, null, "");
        } catch (Exception e) {
            log.error("查询客户信息列表，获取导出数据消息异常：", e);
            throw new BizException("-9997", ErrorEnum.FAILED_GET_EXPORT_DATA_MESSAGE.getDesc());
        }
        return userQueryExtInfoResultExportList;
    }

    @Override
    int queryCount(UserQueryExtInfoForm query, LambdaQueryWrapper<UserQueryExtInfo> wrapper, String sourceCode) {
        return userQueryExtInfoBaseMapper.countUserQueryExtInfo(query);
    }

    @Override
    Page<UserQueryExtInfoResultExport> getPageData(UserQueryExtInfoForm userQueryExtInfoForm, LambdaQueryWrapper<UserQueryExtInfo> wrapper, String sourceCode) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<UserQueryExtInfoDetail> page = new Page<>(1, WskhConstant.DATA_EXPORT_PAGE_SIZE);
        page.setSearchCount(false);
        Page<UserQueryExtInfoDetail> resultPage = userQueryExtInfoBaseMapper.selectUserQueryExtInfoExportListByPage(page, userQueryExtInfoForm);
        stopWatch.stop();
        log.info("UserQueryExtInfoResultExport导出分页查询耗时: {}", stopWatch.getTotalTimeMillis());
        Page<UserQueryExtInfoResultExport> exportPage = new Page<>();
        List<UserQueryExtInfoResultExport> userQueryExtInfoResultExportList = resultPage.getRecords().stream().map(item -> {
            UserQueryExtInfoResultExport userQueryExtInfoResultExport = new UserQueryExtInfoResultExport();
            BeanUtils.copyProperties(item, userQueryExtInfoResultExport);
            return userQueryExtInfoResultExport;
        }).collect(Collectors.toList());
        exportPage.setRecords(userQueryExtInfoResultExportList);
        return exportPage;
    }

    @Override
    LambdaQueryWrapper<UserQueryExtInfo> refreshQuery(List<UserQueryExtInfoResultExport> records, UserQueryExtInfoForm query, LambdaQueryWrapper<UserQueryExtInfo> wrapper) {
        if (CollectionUtils.isEmpty(records)) {
            return wrapper;
        }
        UserQueryExtInfoResultExport userQueryExtInfoResultExport = records.get(records.size() - 1);
        if (Objects.nonNull(userQueryExtInfoResultExport)) {
            query.setRequest_no(userQueryExtInfoResultExport.getRequest_no());
        }
        return wrapper;
    }

    @Override
    public void convertData(UserQueryExtInfoResultExport userQueryExtInfoResultExport, UserQueryExtInfoForm query) {

        // 分公司名称 company_name
        String up_branch_no = branchInfoMap.getOrDefault(userQueryExtInfoResultExport.getBranch_no(), new BranchInfo()).getUp_branch_no();
        String companyName = StringUtils.isEmpty(up_branch_no) ? userQueryExtInfoResultExport.getBranch_no() : branchInfoMap.getOrDefault(up_branch_no, new BranchInfo()).getBranch_name();
        userQueryExtInfoResultExport.setCompany_name(companyName);
        // 业务类型 code_dict = "busin_type"
        userQueryExtInfoResultExport.setBusin_type(dictInfoMap.getOrDefault(DicConstant.BUSIN_TYPE_DICT + userQueryExtInfoResultExport.getBusin_type(), new DictInfo()).getSub_name());
        // 分支机构 code_type = "branch"
        userQueryExtInfoResultExport.setBranch_no(branchInfoMap.getOrDefault(userQueryExtInfoResultExport.getBranch_no(), new BranchInfo()).getBranch_name());
        // 证件类型 code_dict = "id_kind"
        userQueryExtInfoResultExport.setId_kind(dictInfoMap.getOrDefault(DicConstant.ID_KIND_DICT + userQueryExtInfoResultExport.getId_kind(), new DictInfo()).getSub_name());
        // 性别 code_dict = "client_gender"
        userQueryExtInfoResultExport.setClient_gender(dictInfoMap.getOrDefault(DicConstant.CLIENT_GENDER_DICT + userQueryExtInfoResultExport.getClient_gender(), new DictInfo()).getSub_name());
        // 职业代码 code_dict = "profession_code"
        userQueryExtInfoResultExport.setProfession_code(dictInfoMap.getOrDefault(DicConstant.PROFESSION_CODE_DICT + userQueryExtInfoResultExport.getProfession_code(), new DictInfo()).getSub_name());
        // 视频见证方式 code_dict = "video_type"
        userQueryExtInfoResultExport.setVideo_type(dictInfoMap.getOrDefault(DicConstant.VIDEO_TYPE_DICT + userQueryExtInfoResultExport.getVideo_type(), new DictInfo()).getSub_name());
        // 任务状态 code_dict = "task_status" 字段task_type转换
        userQueryExtInfoResultExport.setRequest_status_name(getRequest_status_name(userQueryExtInfoResultExport.getRequest_status_name()));
        // 接入方式 code_dict = "app_id"
        userQueryExtInfoResultExport.setApp_id(dictInfoMap.getOrDefault(DicConstant.APP_ID_DICT + userQueryExtInfoResultExport.getApp_id(), new DictInfo()).getSub_name());
    }

    @Override
    void initCacheData() {
        // 数据准备
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.ID_KIND_DICT).stream().collect(Collectors.toMap(x -> DicConstant.ID_KIND_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.VIDEO_TYPE_DICT).stream().collect(Collectors.toMap(x -> DicConstant.VIDEO_TYPE_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.BUSIN_TYPE_DICT).stream().collect(Collectors.toMap(x -> DicConstant.BUSIN_TYPE_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.APP_ID_DICT).stream().collect(Collectors.toMap(x -> DicConstant.APP_ID_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.CLIENT_GENDER_DICT).stream().collect(Collectors.toMap(x -> DicConstant.CLIENT_GENDER_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.PROFESSION_CODE_DICT).stream().collect(Collectors.toMap(x -> DicConstant.PROFESSION_CODE_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.AUDIT_STATUS_DICT).stream().collect(Collectors.toMap(x -> DicConstant.AUDIT_STATUS_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.REVIEW_STATUS_DICT).stream().collect(Collectors.toMap(x -> DicConstant.REVIEW_STATUS_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.SECONDARY_REVIEW_STATUS_DICT).stream().collect(Collectors.toMap(x -> DicConstant.SECONDARY_REVIEW_STATUS_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.PROCESS_STATUS_DICT).stream().collect(Collectors.toMap(x -> DicConstant.PROCESS_STATUS_DICT + x.getSub_code(), x -> x)));
        branchInfoMap.putAll(cacheBranch.getAllBranchList().stream().collect(Collectors.toMap(BranchInfo::getBranch_no, x -> x)));
    }

    private String getRequest_status_name(String request_status_name) {
        String[] subArr = request_status_name.split("-");
        if (subArr.length >= 2) {
            switch (subArr[0]) {
                case FlowNodeConst.AUDIT:
                    return dictInfoMap.getOrDefault(DicConstant.AUDIT_STATUS_DICT + subArr[1], new DictInfo()).getSub_name();
                case FlowNodeConst.REVIEW:
                    return dictInfoMap.getOrDefault(DicConstant.REVIEW_STATUS_DICT + subArr[1], new DictInfo()).getSub_name();
                case FlowNodeConst.SECONDARY_REVIEW:
                    return dictInfoMap.getOrDefault(DicConstant.SECONDARY_REVIEW_STATUS_DICT + subArr[1], new DictInfo()).getSub_name();
                default:
                    return request_status_name;
            }
        }
        return dictInfoMap.getOrDefault(DicConstant.PROCESS_STATUS_DICT + request_status_name, new DictInfo()).getSub_name();
    }

    private LambdaQueryWrapper<UserQueryExtInfo> queryWrapper(UserQueryExtInfoForm userQueryExtInfo) {
        LambdaQueryWrapper<UserQueryExtInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserQueryExtInfo::getIs_snapshot, "0");
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getClient_name()), UserQueryExtInfo::getClient_name, userQueryExtInfo.getClient_name());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getMobile_tel()), UserQueryExtInfo::getMobile_tel, userQueryExtInfo.getMobile_tel());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getId_no()), UserQueryExtInfo::getId_no, userQueryExtInfo.getId_no());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getVideo_type()), UserQueryExtInfo::getVideo_type, userQueryExtInfo.getVideo_type());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getChannel_code()), UserQueryExtInfo::getChannel_code, userQueryExtInfo.getChannel_code());
        //申请时间
        wrapper.ge(StringUtils.isNotBlank(userQueryExtInfo.getRequest_datetime_start()), UserQueryExtInfo::getRequest_datetime, SqlDateUtil.getParseDateStartDetail(userQueryExtInfo.getRequest_datetime_start()));
        wrapper.le(StringUtils.isNotBlank(userQueryExtInfo.getRequest_datetime_end()), UserQueryExtInfo::getRequest_datetime, SqlDateUtil.getParseDateEndDetail(userQueryExtInfo.getRequest_datetime_end()));
        //审核通过时间
        wrapper.ge(StringUtils.isNotBlank(userQueryExtInfo.getAudit_datetime_start()), UserQueryExtInfo::getAudit_finish_datetime, SqlDateUtil.getParseDateStartDetail(userQueryExtInfo.getAudit_datetime_start()));
        wrapper.le(StringUtils.isNotBlank(userQueryExtInfo.getAudit_datetime_end()), UserQueryExtInfo::getAudit_finish_datetime, SqlDateUtil.getParseDateEndDetail(userQueryExtInfo.getAudit_datetime_end()));
        //复核通过时间
        wrapper.ge(StringUtils.isNotBlank(userQueryExtInfo.getReview_datetime_start()), UserQueryExtInfo::getReview_finish_datetime, SqlDateUtil.getParseDateStartDetail(userQueryExtInfo.getReview_datetime_start()));
        wrapper.le(StringUtils.isNotBlank(userQueryExtInfo.getReview_datetime_end()), UserQueryExtInfo::getReview_finish_datetime, SqlDateUtil.getParseDateEndDetail(userQueryExtInfo.getReview_datetime_end()));
        //二次复核通过时间
        wrapper.ge(StringUtils.isNotBlank(userQueryExtInfo.getDouble_datetime_start()), UserQueryExtInfo::getDouble_finish_datetime, SqlDateUtil.getParseDateStartDetail(userQueryExtInfo.getDouble_datetime_start()));
        wrapper.le(StringUtils.isNotBlank(userQueryExtInfo.getDouble_datetime_end()), UserQueryExtInfo::getDouble_finish_datetime, SqlDateUtil.getParseDateEndDetail(userQueryExtInfo.getDouble_datetime_end()));
        // 业务状态
        if (StringUtils.isNotBlank(userQueryExtInfo.getRequest_status())) {
            String[] taskTypeArr = userQueryExtInfo.getRequest_status().split("-");
            if (taskTypeArr.length == 2) {
                // 复核通过or二次复核通过 需通过end_node作查询条件
                if (StringUtils.equalsAny(userQueryExtInfo.getRequest_status(), WskhConstant.REVIEW_3, WskhConstant.SECONDARY_REVIEW_3)) {
                    wrapper.and(
                            l -> l.eq(UserQueryExtInfo::getEnd_node, taskTypeArr[0])
                                    .or().eq(UserQueryExtInfo::getAnode_id, taskTypeArr[0]));
                } else {
                    wrapper.eq(UserQueryExtInfo::getAnode_id, taskTypeArr[0]);
                }
                wrapper.eq(UserQueryExtInfo::getRequest_status, taskTypeArr[1]);
            }
        }
        wrapper.in(CollectionUtils.isNotEmpty(userQueryExtInfo.getBranch_nos()), UserQueryExtInfo::getBranch_no, userQueryExtInfo.getBranch_nos());
        wrapper.in(StringUtils.isNotBlank(userQueryExtInfo.getBusin_type()), UserQueryExtInfo::getBusin_type, userQueryExtInfo.getBusin_type());
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getAudit_operator_name()), UserQueryExtInfo::getAudit_operator_name, userQueryExtInfo.getAudit_operator_name());
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getReview_operator_name()), UserQueryExtInfo::getReview_operator_name, userQueryExtInfo.getReview_operator_name());
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getDouble_operator_name()), UserQueryExtInfo::getAudit_operator_name, userQueryExtInfo.getAudit_operator_name());
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getClient_id()), UserQueryExtInfo::getClient_id, userQueryExtInfo.getClient_id());
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getFund_account()), UserQueryExtInfo::getFund_account, userQueryExtInfo.getFund_account());
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getBroker_name()), UserQueryExtInfo::getBroker_name, userQueryExtInfo.getBroker_name());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getApp_id()), UserQueryExtInfo::getApp_id, userQueryExtInfo.getApp_id());
        //wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getRequest_status()), UserQueryExtInfo::getRequest_status, userQueryExtInfo.getRequest_status());
        // 审核 复核 二次复核操作员编号
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getAudit_operator_no()), UserQueryExtInfo::getAudit_operator_no, userQueryExtInfo.getAudit_operator_no());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getReview_operator_no()), UserQueryExtInfo::getReview_operator_no, userQueryExtInfo.getReview_operator_no());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getDouble_operator_no()), UserQueryExtInfo::getDouble_operator_no, userQueryExtInfo.getDouble_operator_no());
        //业务流水号
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getActivity_no()), UserQueryExtInfo::getActivity_no, userQueryExtInfo.getActivity_no());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getRequest_no()), UserQueryExtInfo::getRequest_no, userQueryExtInfo.getRequest_no());
        //活动名称activity_name
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getActivity_name()), UserQueryExtInfo::getActivity_name, userQueryExtInfo.getActivity_name());
        //活动名称marketing_team
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getMarketing_team()), UserQueryExtInfo::getMarketing_team, userQueryExtInfo.getMarketing_team());
        //开户渠道
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getOpen_channel()), UserQueryExtInfo::getOpen_channel, userQueryExtInfo.getOpen_channel());
        wrapper.orderByAsc(UserQueryExtInfo::getRequest_no);
        return wrapper;
    }
}

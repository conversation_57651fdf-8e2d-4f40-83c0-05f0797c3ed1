package com.cairh.cpe.backend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.StagingTaskRuleDetailResponse;
import com.cairh.cpe.backend.form.resp.StagingTaskRuleListResponse;
import com.cairh.cpe.backend.form.resp.StagingTaskRulePageResponse;
import com.cairh.cpe.context.BaseUser;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/11 16:18
 */
public interface IAuditStagingTaskRuleService {

    /**
     * 查询任务暂存规则分页数据
     *
     * @param request
     * @return
     */
    Page<StagingTaskRulePageResponse> page(StagingTaskRulePageReq request);


    /**
     * 查询任务暂存规则集合
     *
     * @param request
     * @return
     */
    List<StagingTaskRuleListResponse> list(StagingTaskRuleListReq request);

    /**
     * 查询任务暂存规则详情
     *
     * @param serial_id
     * @return
     */
    StagingTaskRuleDetailResponse detail(String serial_id);

    /**
     * 新增任务暂存规则
     *
     * @param baseUser
     * @param request
     * @return
     */
    void addRule(BaseUser baseUser, StagingTaskRuleAddReq request);

    /**
     * 编辑任务暂存规则
     *
     * @param baseUser
     * @param request
     * @return
     */
    void editRule(BaseUser baseUser, StagingTaskRuleEditReq request);

    /**
     * 编辑状态
     *
     * @param baseUser
     * @param request
     * @return
     */
    void updateStatus(BaseUser baseUser, StagingTaskRuleStatusReq request);

    /**
     * 删除任务暂存规则
     *
     * @param serial_id
     * @return
     */
    void deleteRule(String serial_id);
}

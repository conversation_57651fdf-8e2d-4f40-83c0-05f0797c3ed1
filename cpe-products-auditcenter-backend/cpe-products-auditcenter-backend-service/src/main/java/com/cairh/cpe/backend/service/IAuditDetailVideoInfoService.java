package com.cairh.cpe.backend.service;


import com.cairh.cpe.backend.form.resp.VideoInfoResp;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.service.aiaudit.response.QueryAuditBusinRecordResp;

public interface IAuditDetailVideoInfoService {

    VideoInfoResp getVideoInfo(String request_no);

    QueryAuditBusinRecordResp refreshVideo(BaseUser baseUser, BusinFlowRequest businFlowRequest);
}

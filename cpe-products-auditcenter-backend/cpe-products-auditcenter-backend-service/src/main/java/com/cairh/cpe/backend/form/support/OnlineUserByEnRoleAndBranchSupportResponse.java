package com.cairh.cpe.backend.form.support;

import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseQryOnlineUserByEnRoleAndBranchResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class OnlineUserByEnRoleAndBranchSupportResponse extends VBaseQryOnlineUserByEnRoleAndBranchResponse {

    /**
     * 实际用户列表
     */
    private List<OperatorAndBranchExtendInfo> operatorAndBranchInfoExtendList;
}

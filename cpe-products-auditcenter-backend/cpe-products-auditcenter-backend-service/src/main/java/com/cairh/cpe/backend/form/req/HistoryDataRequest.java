package com.cairh.cpe.backend.form.req;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class HistoryDataRequest extends PageInfo  {

    /**
     * 客户姓名
     */
    private String client_name;


    /**
     * 手机号码
     */
    private String mobile_tel;

    /**
     * 证件类型
     */
    private String id_kind;


    /**
     * 证件号码
     */
    private String id_no;


    /**
     * 渠道名称
     */
    private String channel_name;


    /**
     * 活动码
     */
    private String activity_no;


    /**
     * 营销团队
     */
    private String marketing_team;


    /**
     * 见证人姓名
     */
    private String audit_operator_name;


    /**
     * 见证人工号
     */
    private String audit_operator_no;


    /**
     * 复核人姓名
     */
    private String review_operator_name;

    /**
     * 复核人工号
     */
    private String review_operator_no;

    /**
     * 二次复核人工号
     */
    private String double_operator_no;

    /**
     * 二次复核人名称
     */
    private String double_operator_name;

    /**
     * 开户申请提交开始时间
     */
    private String request_datetime_start;


    /**
     * 开户申请提交结束时间
     */
    private String request_datetime_end;

    /**
     * 审核通过时间开始
     */
    private String audit_datetime_start;

    /**
     * 审核通过时间结束
     */
    private String audit_datetime_end;

    /**
     * 复核通过时间开始
     */
    private String review_datetime_start;

    /**
     * 复核通过时间结束
     */
    private String review_datetime_end;

    /**
     * 复核通过时间开始
     */
    private String double_datetime_start;

    /**
     * 复核通过时间结束
     */
    private String double_datetime_end;

    /**
     * 视频见证方式
     */
    private String video_type;

    /**
     * 业务类型
     */
    private String busin_type;


    /**
     * 所属营业部
     */
    private String branch_no;


    /**
     * 任务状态
     */
    private String request_status;



    /**
     * 快照类型
     */
    private String is_snapshot;

}

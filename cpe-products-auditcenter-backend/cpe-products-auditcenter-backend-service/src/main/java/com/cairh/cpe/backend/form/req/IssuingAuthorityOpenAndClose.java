package com.cairh.cpe.backend.form.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * Description：启用/禁用签发机关
 * Author： slx
 * Date： 2024/10/23 10:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class IssuingAuthorityOpenAndClose {

    /**
     * id
     */
    @NotBlank(message = "serial_id不能为空")
    private String serial_id;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空")
    private String status;
}

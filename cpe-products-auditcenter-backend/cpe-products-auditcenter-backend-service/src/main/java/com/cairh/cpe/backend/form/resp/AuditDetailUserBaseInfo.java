package com.cairh.cpe.backend.form.resp;

import com.cairh.cpe.cache.annotation.DataConvert;
import lombok.Data;

@Data
public class AuditDetailUserBaseInfo {

    /**
     * 渠道名称
     */
    private String channel_name;

    /**
     * 渠道编号
     */
    private String channel_code;

    /**
     * 活动名称
     */
    private String activity_name;

    /**
     * 活动码
     */
    private String activity_no;

    /**
     * 营销团队
     */
    private String marketing_team;

    /**
     * 手机号归属地
     */
    private String mobile_location;

    /**
     * 推广关系
     */
    private String broker_name;

    /**
     * 视频见证类型
     */
    private String video_type;

    /**
     * 开户渠道
     */
    private String open_channel;

    /**
     * 手机号码
     */
    private String mobile_tel;

    /**
     * 任务类型
     */
    private String task_type;

    /**
     * 业务类型
     */
    @DataConvert(code_dict = "busin_type")
    private String busin_type;
    private String busin_type_code;

    /**
     * 绿通标识
     */
    private String white_flag;

    private String task_id;

    private Integer age;

    private String id_kind;

    /**
     * 话术id
     */
    private String video_speech_id;

    /**
     * 当前时间  1-中登时间 2-非中登时间
     */
    private String work_time;
}

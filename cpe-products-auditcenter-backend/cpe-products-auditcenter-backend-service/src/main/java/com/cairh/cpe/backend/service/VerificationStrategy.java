package com.cairh.cpe.backend.service;

import com.cairh.cpe.backend.form.resp.VerificationResult;
import com.cairh.cpe.backend.form.support.VerifyPoliceInfo;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;

/**
 * 验证策略接口
 *
 * <AUTHOR>
 * @since 2025/5/7 10:37
 */
public interface VerificationStrategy {

    VerificationResult verify(ClobContentInfo clob, VerifyPoliceInfo info,
                              boolean isNewDataSign, boolean workTime);
}

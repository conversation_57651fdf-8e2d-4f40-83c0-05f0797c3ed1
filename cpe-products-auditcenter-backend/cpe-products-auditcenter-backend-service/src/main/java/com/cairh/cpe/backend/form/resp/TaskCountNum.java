package com.cairh.cpe.backend.form.resp;

import lombok.Data;

@Data
public class TaskCountNum { //统计任务 00:00:00到当前时间工作员所处理的任务数量
    /**
     * 总任务数
     */
    private String task_num = "0";

    /**
     * 待处理任务数
     */
    private String audit_pending_num = "0";

    /**
     * 见证任务数
     */
    private String audit_num = "0";

    /**
     * 复核任务数
     */
    private String review_num = "0";

    /**
     * 二次复核任务数
     */
    private String secondary_review_num = "0";

    /**
     * 外呼任务
     */
    private String out_call_num = "0";

    /**
     * 见证通过数
     */
    private String audit_pass_num = "0";

    /**
     * 见证不通过数
     */
    private String audit_notpass_num = "0";

    /**
     * 复核通过数
     */
    private String review_pass_num = "0";

    /**
     * 复核不通过数
     */
    private String review_notpass_num = "0";

    /**
     * 二次复核通过数
     */
    private String secondary_review_pass_num = "0";

    /**
     * 二次复核不通过数
     */
    private String secondary_review_notpass_num = "0";


    private String secondary_review_pend_num = "0";
    private String review_pend_num = "0";

    private String audit_pend_num = "0";

    /**
     * 暂存总数
     */
    private String staging_num = "0";

    /**
     * 被回收任务
     */
    private String recycle_num = "0";
}

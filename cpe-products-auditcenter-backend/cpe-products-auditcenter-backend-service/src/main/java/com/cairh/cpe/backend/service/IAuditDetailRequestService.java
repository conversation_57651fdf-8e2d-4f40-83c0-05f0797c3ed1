package com.cairh.cpe.backend.service;

import com.cairh.cpe.backend.form.req.BusinFlowRequestEditReq;
import com.cairh.cpe.backend.form.resp.HistoryApplyResp;
import com.cairh.cpe.common.entity.BusinFlowRequest;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IAuditDetailRequestService {


    /**
     * 获取历史申请记录
     * @param idNo
     * @return
     */
    List<HistoryApplyResp> applyHis(String idNo);

    /**
     * 根据申请单号获取申请单
     * @param requestNo
     * @return
     */
    BusinFlowRequest findByRequestNo(String requestNo);

    /**
     * 更新申请单状态
     * @param requestNo
     * @param req
     * @return
     */
    boolean updateRequest(String requestNo, BusinFlowRequestEditReq req);
}

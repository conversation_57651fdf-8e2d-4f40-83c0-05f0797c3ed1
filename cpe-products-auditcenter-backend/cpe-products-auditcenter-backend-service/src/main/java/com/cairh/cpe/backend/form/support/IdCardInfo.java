package com.cairh.cpe.backend.form.support;


import lombok.Data;

@Data
public class IdCardInfo {

    /**
     * 身份证类型
     */
    private String id_kind;

    /**
     * 身份证号码
     */
    private String id_no;

    /**
     * 签发机关
     */
    private String issued_depart;

    /**
     * 身份证开始日期
     */
    private String id_begindate;

    /**
     * 身份证结束日期
     */
    private String id_enddate;

    /**
     * 证件地址
     */
    private String id_address;

    /**
     * 客户姓名
     */
    private String client_name;

    /**
     * 辅助证件类型
     */
    private String auxiliary_id_kind;

    /**
     * 辅助证件号码
     */
    private String auxiliary_id_no;

    /**
     * 辅助证件开始日期
     */
    private String auxiliary_id_begindate;

    /**
     * 辅助证件结束日期
     */
    private String auxiliary_id_enddate;

    /**
     * 辅助证件地址
     */
    private String auxiliary_id_address;

    /**
     * 境外客户姓名
     */
    private String oversea_client_name;

    /**
     * 境外证件类型
     */
    private String oversea_id_kind;

    /**
     * 境外证件号码
     */
    private String oversea_id_no;

    /**
     * 港澳台居民居住证-通行证号码
     */
    private String permit_no;

    /**
     * 辅助证件客户姓名
     */
    private String auxiliary_client_name;

    /**
     * 辅助证件类型
     *  1(居住证)、2(就业证明+营业执照)、3(就业证明+统一社会信用代码证复印件)、4(住宿登记证明表原件)
     */
    private String auxiliary_type;

}

package com.cairh.cpe.backend.form.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class HistoryApplyResp {

    /**
     * 客户姓名
     */
    private String client_name;

    /**
     * 证件号码
     */
    private String id_no;


    /**
     * 渠道名称
     */
    private String branch_name;

    /**
     * 渠道名称
     */
    private String branch_no;

    /**
     * 当前流程节点
     */
    private String anode_id;


    /**
     * 手机号码
     */
    private String mobile_tel;


    /**
     * 渠道名称
     */
    private String channel_name;


    /**
     * 当前业务状态
     */
    private String request_status;

    /**
     * 申请状态名称
     */
    private String request_status_name;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date request_datetime;

}

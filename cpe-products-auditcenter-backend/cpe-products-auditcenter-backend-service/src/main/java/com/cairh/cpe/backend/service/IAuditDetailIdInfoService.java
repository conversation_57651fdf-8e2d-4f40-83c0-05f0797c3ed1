package com.cairh.cpe.backend.service;


import com.cairh.cpe.backend.form.req.HandInfoForm;
import com.cairh.cpe.backend.form.req.IdCardInfoForm;
import com.cairh.cpe.backend.form.req.ValidateCitizenDocumentForm;
import com.cairh.cpe.backend.form.resp.HandInfoResp;
import com.cairh.cpe.backend.form.resp.IdCardInfoResult;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;

import java.util.List;

public interface IAuditDetailIdInfoService {

    List<AiAuditRuleResp> idRetry(IdCardInfoForm idCardInfoForm, BusinFlowRequest businFlowRequest);

    List<AiAuditRuleResp> handIdRetry(IdCardInfoForm idCardInfoForm);

    IdCardInfoResult getIdInfo(IdCardInfoForm idCardInfoForm);


    /**
     * 网厅三要求
     *
     * @param handInfoForm
     * @return
     */
    HandInfoResp getHandInfo(HandInfoForm handInfoForm);

    void validateCitizenDocument(ValidateCitizenDocumentForm validateCitizenDocumentForm, BusinFlowRequest businFlowRequest);
}

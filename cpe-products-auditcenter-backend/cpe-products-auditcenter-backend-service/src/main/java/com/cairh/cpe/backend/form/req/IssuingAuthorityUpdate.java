package com.cairh.cpe.backend.form.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * Description：修改签发机关
 * Author： slx
 * Date： 2024/9/2 16:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class IssuingAuthorityUpdate {

    /**
     * id
     */
    @NotBlank(message = "serial_id不能为空")
    private String serial_id;

    /**
     * 省份编号
     */
    @NotBlank(message = "省份编号不能为空")
    private String province_no;

    /**
     * 签发机关名称
     */
    @NotBlank(message = "签发机关不能为空")
    private String authority_name;

    /**
     * 证件地址行政区划
     */
    @NotBlank(message = "证件地址行政区划不能为空")
    private String address_oragn;

    /**
     * 状态 8-可用 9-不可用
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
}

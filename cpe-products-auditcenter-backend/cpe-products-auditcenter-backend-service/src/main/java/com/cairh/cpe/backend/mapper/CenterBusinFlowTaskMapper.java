package com.cairh.cpe.backend.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.CountTaskForm;
import com.cairh.cpe.backend.form.req.TaskDetailRequest;
import com.cairh.cpe.backend.form.resp.*;
import org.apache.ibatis.annotations.Param;

public interface CenterBusinFlowTaskMapper {

    Page<TaskDetailInfo> selectCenterTaskListByPage(Page<TaskDetailInfo> page, @Param("queryForm") TaskDetailRequest taskDetailRequest);

    Page<TaskDetailInfo> selectCompleteCenterTaskListByPage(Page<TaskDetailInfo> page, @Param("queryForm") TaskDetailRequest taskDetailRequest);

    Page<TaskDetailInfo> selectStagingCenterTaskListByPage(Page<TaskDetailInfo> page, @Param("queryForm") TaskDetailRequest taskDetailRequest);

    Page<TaskStagingAndActivationInfo> selectStagingCenterTaskListByPageAndSerialId(Page<TaskStagingAndActivationInfo> page, @Param("queryForm") TaskDetailRequest taskDetailRequest);

    Page<TaskStagingAndActivationInfo> selectActivationCenterTaskListByPageAndSerialId(Page<TaskStagingAndActivationInfo> page, @Param("queryForm") TaskDetailRequest taskDetailRequest);

    TaskFinishCountNum countCenterTaskNum(@Param("queryForm") CountTaskForm countTaskForm);

    int countCallsNum(@Param("queryForm") CountTaskForm countTaskForm);

    TaskPendingCountNum countAuditPendingNum(@Param("queryForm") CountTaskForm countTaskForm);

    Page<TaskDetailInfoExport> selectCenterTaskList(Page<TaskDetailInfoExport> page, @Param("queryForm") TaskDetailRequest taskDetailRequest);

    Page<TaskDetailInfoExport> selectCompleteCenterTaskList(Page<TaskDetailInfoExport> page, @Param("queryForm") TaskDetailRequest taskDetailRequest);

    Integer countCenterTask(@Param("queryForm") TaskDetailRequest taskDetailRequest);

    Integer countCompleteCenterTask(@Param("queryForm") TaskDetailRequest taskDetailRequest);

    int countStagingOrActivationCenterTaskNum(@Param("queryForm") TaskDetailRequest taskDetailRequest);

}

package com.cairh.cpe.backend.form.resp;

import lombok.Data;

@Data
public class HandInfoResp {

    /**
     * 任务类型
     */
    private String busin_type;

    /**
     * 证件信息
     */
    private String id_kind;

    /**
     * 客户名称
     */
    private String client_name;

    /**
     * 手机号码
     */
    private String mobile_tel;

    /**
     * 证件号码
     */
    private String id_no;


    /**
     * 证件正面
     */
    private String photo_front;

    /**
     * 证件反面
     */
    private String photo_back;

    /**
     * 护照
     */
    private String cert_file_path;

    /**
     * 开户类别
     */
    private String client_category;

    /**
     * 营业部
     */
    private String branch_no;

    /**
     * 经办人名称
     */
    private String agent_name;

    /**
     * 经办人证件类型
     */
    private String agent_id_kind;

    /**
     * 经办人证件号码
     */
    private String agent_id_no;

    /**
     * 经办人证件人像面
     */
    private String agent_photo_front;

    /**
     * 经办人证件国徽面
     */
    private String agent_photo_back;

    /**
     * 视频ID
     */
    private String file_8A;


    /**
     * 免冠照
     */
    private String file_80;
}

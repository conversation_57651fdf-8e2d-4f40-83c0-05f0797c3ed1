package com.cairh.cpe.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cairh.cpe.backend.form.req.BusinFlowRequestEditReq;
import com.cairh.cpe.backend.form.resp.HistoryApplyResp;
import com.cairh.cpe.backend.service.IAuditDetailRequestService;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.His_UserQueryExtInfo;
import com.cairh.cpe.common.entity.His_BusinFlowRequest;
import com.cairh.cpe.common.entity.His_UserQueryExtInfo;
import com.cairh.cpe.common.entity.UserQueryExtInfo;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.mapper.HisBusinFlowRequestMapper;
import com.cairh.cpe.common.mapper.HisUserQueryExtInfoMapper;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.common.service.IUserQueryExtInfoService;
import com.cairh.cpe.common.util.BaseBeanUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
public class AuditDetailRequestServiceImpl implements IAuditDetailRequestService {

    @Resource
    private IBusinFlowRequestService businFlowRequestService;

    @Resource
    private IUserQueryExtInfoService userQueryExtInfoService;

    @Resource
    private CacheDict cacheDict;

    @Resource
    private CacheBranch cacheBranch;

    @Resource
    private HisUserQueryExtInfoMapper hisUserQueryExtInfoMapper;

    @Override
    public List<HistoryApplyResp> applyHis(String idNo) {
        Map<String, BranchInfo> branchInfoMap = cacheBranch.getAllBranchList().stream().collect(Collectors.toMap(BranchInfo::getBranch_no, x -> x));
        List<HistoryApplyResp> respList = convertAndSort(userQueryExtInfoService.list(createQueryWrapper(idNo)), branchInfoMap);
        List<HistoryApplyResp> hisRespList = convertAndSort(hisUserQueryExtInfoMapper.selectList(createQueryWrapperHis(idNo)), branchInfoMap);
        respList.addAll(hisRespList);
        return respList;
    }


    @Override
    public BusinFlowRequest findByRequestNo(String requestNo) {
        return businFlowRequestService.getById(requestNo);
    }

    @Override
    public boolean updateRequest(String requestNo, BusinFlowRequestEditReq req) {
        BusinFlowRequest request = businFlowRequestService.getById(requestNo);
        Assert.notNull(request, "BusinFlowRequest not find by request_no");
        if (FlowStatusConst.REQUEST_STATUS_AUDIT_PASS.equals(request.getRequest_status())
                || FlowStatusConst.REQUEST_STATUS_AUDIT_FAIL.equals(request.getRequest_status())) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<BusinFlowRequest> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(null != req.getRequest_status(), BusinFlowRequest::getRequest_status, req.getRequest_status());
        wrapper.set(null != req.getAnode_id(), BusinFlowRequest::getAnode_id, req.getAnode_id());
        wrapper.eq(BusinFlowRequest::getRequest_no, requestNo);
        return businFlowRequestService.update(wrapper);
    }

    private List<HistoryApplyResp> convertAndSort(List<?> sourceList, Map<String, BranchInfo> branchInfoMap) {
        return sourceList.stream()
                .map(item->{
                    return convertToHistoryApplyResp(item, branchInfoMap);
                })
                .sorted(Comparator.comparing(HistoryApplyResp::getRequest_datetime).reversed())
                .collect(Collectors.toList());
    }

    private LambdaQueryWrapper<UserQueryExtInfo> createQueryWrapper(String idNo) {
        LambdaQueryWrapper<UserQueryExtInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserQueryExtInfo::getId_no, idNo);
        wrapper.eq(UserQueryExtInfo::getIs_snapshot, "0");
        return wrapper;
    }

    private LambdaQueryWrapper<His_UserQueryExtInfo> createQueryWrapperHis(String idNo) {
        LambdaQueryWrapper<His_UserQueryExtInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(His_UserQueryExtInfo::getId_no, idNo);
        wrapper.eq(His_UserQueryExtInfo::getIs_snapshot, "0");
        return wrapper;
    }

    private HistoryApplyResp convertToHistoryApplyResp(Object item, Map<String, BranchInfo> branchInfoMap) {
        HistoryApplyResp applyResp = new HistoryApplyResp();
        if (item instanceof UserQueryExtInfo) {
            UserQueryExtInfo extInfo = (UserQueryExtInfo) item;
            BeanUtils.copyProperties(extInfo, applyResp);
        } else {
            His_UserQueryExtInfo hisInfo = (His_UserQueryExtInfo) item;
            BeanUtils.copyProperties(hisInfo, applyResp);
        }
        applyResp.setBranch_name(branchInfoMap.getOrDefault(applyResp.getBranch_no(), new BranchInfo()).getBranch_name());

        if (FlowNodeConst.END.equals(applyResp.getAnode_id())) {
            applyResp.setRequest_status_name("审核通过");
        } else if (FlowNodeConst.START.equals(applyResp.getAnode_id())) {
            applyResp.setRequest_status_name("待审核");
        }else {
            applyResp.setRequest_status_name(cacheDict.getDictDesc("request_status", applyResp.getAnode_id() + "-" + applyResp.getRequest_status()));
        }
        return applyResp;
    }

}

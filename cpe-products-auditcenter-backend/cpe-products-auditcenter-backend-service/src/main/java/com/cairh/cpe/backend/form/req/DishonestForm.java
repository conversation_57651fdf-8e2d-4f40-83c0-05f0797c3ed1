package com.cairh.cpe.backend.form.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 失信记录变更请求体
 *
 * <AUTHOR>
 * @since 2025/3/4 15:22
 */
@Data
public class DishonestForm implements Serializable {
    private static final long serialVersionUID = 1L;


    @NotBlank(message = "业务流水号不能为空")
    private String request_no;

    @NotBlank(message = "task_id不能为空")
    private String task_id;

    /**
     * 失信记录
     */
    private String dishonest_record;

    /**
     * 失信记录备注信息
     */
    private String dishonest_record_remark;

    /**
     * 操作人编号
     */
    private String operator_no;

    /**
     * 操作人名称
     */
    private String operator_name;

}

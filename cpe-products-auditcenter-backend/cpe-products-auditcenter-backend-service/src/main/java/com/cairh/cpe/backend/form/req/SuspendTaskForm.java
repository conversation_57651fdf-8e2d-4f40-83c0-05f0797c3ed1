package com.cairh.cpe.backend.form.req;


import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SuspendTaskForm {

    @NotBlank(message = "业务流水号不能为空")
    private String request_no;

    /**
     * 任务类型
     */
    @NotBlank(message = "任务类型不能为空")
    private String task_type;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;

    /**
     * 挂起理由字典
     */
    @NotBlank(message = "挂起原因必须选择")
    private String suspend_reason;

    /**
     * 任务id
     */
    private String task_id;

}

package com.cairh.cpe.backend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.StagingTaskCount;
import com.cairh.cpe.backend.form.resp.TaskCountNum;
import com.cairh.cpe.backend.form.resp.TaskDetailInfo;
import com.cairh.cpe.backend.form.resp.TaskDetailInfoExport;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;

import java.util.List;

public interface IWorkBusinFlowTaskService {

    /**
     * 通知派单系统。该操作员已上线
     *
     * @param operatbaorDispatchStatus
     */
    void dealOperatorDispatchStatus(OperatorDispatchStatus operatbaorDispatchStatus);


    /**
     * 批量回收
     *
     * @param businFlowTask
     */
    void batchRecoveryTask(BusinFlowTask businFlowTask);

    /**
     * 我的任务 明细列表
     *
     * @param taskDetailRequest
     * @return
     */
    Page<TaskDetailInfo> selectTaskListByPage(TaskDetailRequest taskDetailRequest);

    /**
     * 我的任务 明细列表-导出
     *
     * @param taskDetailRequest
     * @return
     */
    List<TaskDetailInfoExport> exportTaskList(TaskDetailRequest taskDetailRequest);

    /**
     * 我的任务统计任务
     */
    TaskCountNum countTaskNum(BaseUser baseUser);

    /**
     * 手动认领查询任务
     */
    Page<TaskDetailInfo> claimTaskQuery(TaskDetailForm taskDetailForm);

    /**
     * 手动认领任务
     */
    Result<String> claimTask(ClaimTaskForm claimTaskForm, String currentOperator);


    /**
     * 查询操作员是否在线
     */
    boolean queryOperatorStatus(String operator);

    /**
     * 任务回收
     *
     * @param batchRecoveryForm
     * @return
     */
    Result<String> batchRecovery(BatchRecoveryForm batchRecoveryForm);


    /**
     * 任务中心 明细查询
     *
     * @param taskDetailRequest
     * @return
     */
    Page<TaskDetailInfo> selectCenterTaskListByPage(TaskDetailRequest taskDetailRequest);


    /**
     * 任务中心 明细查询 导出
     *
     * @param baseUser
     * @param taskDetailRequest
     * @return
     */
    List<TaskDetailInfoExport> exportCenterTaskList(BaseUser baseUser, TaskDetailRequest taskDetailRequest);

    /**
     * 任务中心统计查询
     *
     * @param baseUser
     * @return
     */
    TaskCountNum countCenterTaskNum(BaseUser baseUser);

    /**
     * 更新任务
     *
     * @param serial_id
     * @param req
     * @return
     */
    boolean updateTask(String serial_id, BusinFlowTaskEditReq req);


    /**
     * 暂存任务
     *
     * @param baseUser
     * @param form
     * @return
     */
    void taskStaging(BaseUser baseUser, TaskStagingAndActivationForm form);

    /**
     * 释放任务
     *
     * @param baseUser
     * @param form
     * @return
     */
    void taskActivation(BaseUser baseUser, TaskStagingAndActivationForm form);

    /**
     * 批量暂存任务
     *
     * @param baseUser
     * @param taskDetailRequest
     * @return
     */
    StagingTaskCount taskBulkStaging(BaseUser baseUser, TaskDetailRequest taskDetailRequest);

    /**
     * 批量释放任务
     *
     * @param baseUser
     * @param taskDetailRequest
     * @return
     */
    StagingTaskCount taskBulkActivation(BaseUser baseUser, TaskDetailRequest taskDetailRequest);

}

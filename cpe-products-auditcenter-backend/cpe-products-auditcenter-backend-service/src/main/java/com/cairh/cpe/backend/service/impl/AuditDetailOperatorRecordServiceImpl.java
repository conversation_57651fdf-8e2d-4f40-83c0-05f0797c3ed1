package com.cairh.cpe.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.backend.service.IAuditDetailOperatorRecordService;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRecord;
import com.cairh.cpe.common.entity.response.AuditOperateRecord;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.entity.support.BackendUser;
import com.cairh.cpe.common.service.IBusinFlowRecordService;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.util.KHDateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AuditDetailOperatorRecordServiceImpl implements IAuditDetailOperatorRecordService {

    @Autowired
    private IBusinFlowRecordService businFlowRecordService;
    @Autowired
    private CacheBackendUser cacheBackendUser;
    @Autowired
    private CacheBranch cacheBranch;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;


    @Override
    public List<AuditOperateRecord> getOperatorRecord(String request_no) {
        List<AuditOperateRecord> resList = new ArrayList<>();
        List<BusinFlowRecord> list = businFlowRecordService.list(new LambdaQueryWrapper<>(BusinFlowRecord.class)
                .eq(BusinFlowRecord::getRequest_no, request_no)
                //.eq(BusinFlowRecord::getRecord_type, FlowStatusConst.AUDIT_RECORD_TYPE)
                .ne(BusinFlowRecord::getBusiness_flag, "0")
                .orderByDesc(BusinFlowRecord::getSerial_id));

        if (list != null && list.size() > 0) {
            list= list.stream().filter(li -> !StringUtils.equals(li.getBusiness_flag(), WskhConstant.BUSINESS_FLAG_22385)
                    && !StringUtils.equals(li.getBusiness_flag(), WskhConstant.BUSINESS_FLAG_22393)
                    && !StringUtils.equals(li.getBusiness_flag(), WskhConstant.BUSINESS_FLAG_22383)
                    && !StringUtils.equals(li.getBusiness_flag(), WskhConstant.BUSINESS_FLAG_22384)).collect(Collectors.toList());
            list.stream().forEach(record -> {
                AuditOperateRecord auditOperateRecord = new AuditOperateRecord();
                auditOperateRecord.setCreate_datetime(KHDateUtil.formatDate(record.getCreate_datetime(), KHDateUtil.DATE_TIME_FORMAT));
                auditOperateRecord.setBusiness_flag(record.getBusiness_flag());
                auditOperateRecord.setBusiness_remark(record.getBusiness_remark());
                auditOperateRecord.setOperator_no(record.getOperator_no());
                if (StringUtils.isNotBlank(record.getOperator_no())) {
                    if (StringUtils.equals(record.getOperator_no(),WskhConstant.SUPER_USER)){
                        auditOperateRecord.setOperator_no(WskhConstant.SUPER_USER);
                        auditOperateRecord.setOperator_name(WskhConstant.SUPER_USER);
                        auditOperateRecord.setBranch_name(WskhConstant.SUPER_BRANCH);
                    }
                    BackendUser backendUser = cacheBackendUser.getBackendUserByStaffNo(record.getOperator_no());
                    if (backendUser != null) {
                        auditOperateRecord.setOperator_name(backendUser.getUser_name());
                        auditOperateRecord.setBranch_no(backendUser.getBranch_no());
                        BranchInfo branchInfo = cacheBranch.getBranchByNo(backendUser.getBranch_no());
                        if (branchInfo != null) {
                            auditOperateRecord.setBranch_name(branchInfo.getBranch_name());
                        }
                    }
                }else {
                    auditOperateRecord.setOperator_no(WskhConstant.SUPER_USER);
                    auditOperateRecord.setOperator_name(WskhConstant.SUPER_USER);
                    auditOperateRecord.setBranch_name(WskhConstant.SUPER_BRANCH);
                }
                resList.add(auditOperateRecord);
            });
        }

        return resList;
    }
}

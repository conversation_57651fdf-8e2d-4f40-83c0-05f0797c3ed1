package com.cairh.cpe.backend.service;

import com.cairh.cpe.backend.form.resp.DictBranchResult;
import com.cairh.cpe.backend.form.resp.DictResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 通用服务
 */
public interface IAuditCenterCommonService {

    /**
     * 上传base64图片
     */
    String uploadBase64Image(String imageBase64);

    /**
     * 上传图片文件
     */
    String uploadFileImage(MultipartFile imageFile);

    /**
     * 上传失信记录文件 (文本文件) /上传图片文件
     */
    String uploadFileByUri(String FileUrl);

    String electUploadFile(byte[] file,String file_name);

    /**
     *查询字典
     * @param dict_code_str 可以多个，英文逗号分隔
     */
    List<DictResult> getDictionaryList(String dict_code_str);


    /**
     * 获取营业部列表
     */
    List<DictBranchResult> getBranchList(String branch_type);

    /**
     * 解析地址 经常居住地址(字典翻译)
     */
    String getTranslationAddress(String address);

    String getTranslationAddressCross(String address);

    /**
     * 经常居住地址(使用!~分隔的地址)
     * @param address
     * @return
     */
    String getalternativeTranslationAddress(String address);

    /**
     * 解析地址
     */
    String getAddressCode(String address,String type);




}

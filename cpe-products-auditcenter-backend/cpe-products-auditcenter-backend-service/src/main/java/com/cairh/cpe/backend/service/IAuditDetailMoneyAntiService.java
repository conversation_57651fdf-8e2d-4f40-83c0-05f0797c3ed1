package com.cairh.cpe.backend.service;

import com.cairh.cpe.backend.form.req.GetMoneyAntiInfoForm;
import com.cairh.cpe.backend.form.req.GetProfessionReasonForm;
import com.cairh.cpe.backend.form.req.MoneyAntiModifyForm;
import com.cairh.cpe.backend.form.resp.MoneyAntiInfo;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;

import java.util.List;

/**
 * 审核详情 - 反洗钱
 */
public interface IAuditDetailMoneyAntiService {

    /**
     * 取反洗钱信息
     */
    MoneyAntiInfo getMoneyAntiInfo(String request_no);

    List<AiAuditRuleResp> auditRetry(MoneyAntiModifyForm moneyAntiModifyForm);

    MoneyAntiInfo getProfessionReason(GetProfessionReasonForm getProfessionReasonForm);
}

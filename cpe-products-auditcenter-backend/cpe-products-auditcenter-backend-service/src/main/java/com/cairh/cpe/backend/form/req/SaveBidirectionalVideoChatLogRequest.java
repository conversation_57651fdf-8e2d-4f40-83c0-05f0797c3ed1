package com.cairh.cpe.backend.form.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 保存双向视频对话记录请求参数
 *
 * <AUTHOR>
 * @since 2024/12/3 18:34
 */
@Data
public class SaveBidirectionalVideoChatLogRequest {

    /**
     * request_no
     */
    @NotEmpty(message = "request_no不能为空")
    private String request_no;

    /**
     * 话术内容
     */
    @NotEmpty(message = "话术内容不能为空")
    private String words_content;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员名称
     */
    private String operator_name;

}

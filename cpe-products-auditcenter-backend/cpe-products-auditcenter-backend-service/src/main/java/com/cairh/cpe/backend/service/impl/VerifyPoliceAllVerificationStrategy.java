package com.cairh.cpe.backend.service.impl;

import com.alibaba.fastjson2.JSON;
import com.cairh.cpe.backend.form.resp.VerificationResult;
import com.cairh.cpe.backend.form.support.VerifyPoliceInfo;
import com.cairh.cpe.backend.service.VerificationStrategy;
import com.cairh.cpe.common.backend.util.BaseBeanUtil;
import com.cairh.cpe.common.constant.ClientCategoryEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.request.VerifyPoliceAllRequest;
import com.cairh.cpe.common.entity.response.VerifyPoliceAllResult;
import com.cairh.cpe.common.entity.response.VerifyPoliceResp;
import com.cairh.cpe.service.idverify.IComponentIdVerifyService;
import com.cairh.cpe.service.third.IElectThirdFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 港澳台居民居住证 or 新数据标签 or 非工作时间且标准业务&身份证or双向视频 策略
 * T2接口：11804254
 *
 * <AUTHOR>
 * @since 2025/5/7 10:42
 */
@Slf4j
@Service
public class VerifyPoliceAllVerificationStrategy implements VerificationStrategy {

    @Resource
    private IComponentIdVerifyService componentIdVerifyService;
    @Resource
    private IElectThirdFileService electThirdFileService;

    @Override
    public VerificationResult verify(ClobContentInfo clob, VerifyPoliceInfo info, boolean isNewDataSign, boolean workTime) {
        VerifyPoliceAllRequest req = new VerifyPoliceAllRequest();
        req.setOrgan_flag(WskhConstant.ORGAN_FLAG);
        req.setRealtime_flag("1");
        req.setBranch_no(clob.getBranch_no());
        req.setId_kind(clob.getId_kind());
        req.setId_no(clob.getId_no());
        req.setFull_name(clob.getClient_name());
        if (StringUtils.equalsAny(clob.getClient_category(), ClientCategoryEnum.CLIENT_INSTITUTION.getCode(), ClientCategoryEnum.CLIENT_PRODUCT.getCode())) {
            req.setId_kind(clob.getAgent_id_kind());
            req.setId_no(clob.getAgent_id_no());
            req.setFull_name(clob.getAgent_name());
        }
        req.setBase64_image(electThirdFileService.getFileThirdId(clob.getFile_80(), req.getId_no()));
        req.setCsdc_busi_kind(workTime ? "12" : "1");
        req.setOper_flag("3");
        log.info("【VerifyPoliceAllVerificationStrategy】无公安照请求入参：{}", JSON.toJSON(req));
        VerifyPoliceAllResult verifyPoliceAllResult = componentIdVerifyService.verifyPoliceAll(req);
        log.info("【VerifyPoliceAllVerificationStrategy】无公安照请求出参：{}", JSON.toJSON(verifyPoliceAllResult));
        if (Objects.isNull(verifyPoliceAllResult)) {
            return new VerificationResult(null, true, false);
        }
        // 中登时需要根据认证结果设置分数
        if (workTime) {
            verifyPoliceAllResult.setScore(WskhConstant.AUTH_RESULT_STATUS_SUCCESS.equals(verifyPoliceAllResult.getStatus())
                    ? WskhConstant.FACE_SCORE_SUCCESS : WskhConstant.FACE_SCORE_FAIL);
        }
        // 转换为VerifyPoliceResp返回
        VerifyPoliceResp resp = new VerifyPoliceResp();
        BaseBeanUtil.copyProperties(verifyPoliceAllResult, resp);
        return new VerificationResult(resp, true, false);
    }
}

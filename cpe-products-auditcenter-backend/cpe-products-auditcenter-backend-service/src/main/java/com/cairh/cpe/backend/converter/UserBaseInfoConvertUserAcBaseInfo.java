package com.cairh.cpe.backend.converter;

import com.cairh.cpe.backend.form.req.UserBaseInfoForm;
import com.cairh.cpe.backend.form.resp.UserAcBaseInfo;
import com.cairh.cpe.backend.service.IAuditCenterCommonService;
import com.cairh.cpe.common.backend.util.BaseBeanUtil;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.clob.IDCardInfo;
import com.cairh.cpe.common.entity.clob.UserBaseInfo;
import com.cairh.cpe.common.entity.clob.VideoInfo;
import com.cairh.cpe.common.entity.support.ArchfileinfoResp;
import com.cairh.cpe.context.convert.Converter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Base64;

@Slf4j
@Component
public class UserBaseInfoConvertUserAcBaseInfo implements Converter<UserBaseInfoForm, UserAcBaseInfo> {

    @Autowired
    private IAuditCenterCommonService auditCenterCommonService;

    @Value("${account.submission.skipFtp:false}")
    private Boolean skipFtp;

    @Override
    public UserAcBaseInfo convert(UserBaseInfoForm source, UserAcBaseInfo target) {
        BaseBeanUtil.copyProperties(source, target);
        //
        BaseBeanUtil.copyProperties(source.getUser_base_info(), target);
        BaseBeanUtil.copyProperties(source.getId_card_info(), target);
        BaseBeanUtil.copyProperties(source.getArch_file_info(), target);
        // 将图片转换成文件id
        target.setFile_6A(getFileRecordId(target.getFile_6A()));
        target.setFile_6B(getFileRecordId(target.getFile_6B()));
        target.setFile_80(getFileRecordId(target.getFile_80()));
        target.setFile_82(getFileRecordId(target.getFile_82()));
        target.setFile_7C(getFileRecordId(target.getFile_7C()));
        target.setFile_7D(getFileRecordId(target.getFile_7D()));
        target.setFile_Bm(getFileRecordId(target.getFile_Bm()));
        target.setFile_7W(getFileRecordId(target.getFile_7W()));
        target.setFile_7X(getFileRecordId(target.getFile_7X()));
        // 将失信记录文件转换成链接
        if (StringUtils.isNotBlank(target.getDishonest_content())) {
            target.setDishonest_content(getFileRecordId(target.getDishonest_content()));
        }
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        BaseBeanUtil.copyProperties(target, userBaseInfo);
        IDCardInfo idCardInfo = new IDCardInfo();
        BaseBeanUtil.copyProperties(target, idCardInfo);
        idCardInfo.setClient_gender(source.getUser_base_info().getUser_gender());
        idCardInfo.setPrev_id_number(source.getUser_base_info().getPrev_id_number());
        target.setInitial_investment_amount(source.getUser_base_info().getInitial_investment_amount());
        if (StringUtils.isBlank(target.getInitial_address())) {
            target.setInitial_address(auditCenterCommonService.getTranslationAddress(target.getAddress()));
            userBaseInfo.setInitial_address(target.getInitial_address());
        }
        VideoInfo videoInfo = new VideoInfo();
        videoInfo.setVideo_address(getFileRecordId(target.getFile_8A()));
        ArchfileinfoResp archfile_info = new ArchfileinfoResp();
        BaseBeanUtil.copyProperties(target, archfile_info);
        target.setArchfile_info(archfile_info);
        target.setUser_base_info(userBaseInfo);
        target.setId_card_info(idCardInfo);
        target.setUser_video_info(videoInfo);
        target.setFile_8A(videoInfo.getVideo_address());
        // 将原始地址保存在大字段中
        String address = target.getAddress();
        String busin_type = source.getUser_base_info().getBusin_type();
        if (StringUtils.equalsAny(busin_type, WskhConstant.BUSIN_TYPE_NORMAL)) {
            target.setTranslation_address(auditCenterCommonService.getTranslationAddress(address));
            target.setAlternative_address(auditCenterCommonService.getalternativeTranslationAddress(address));
        } else if (WskhConstant.CROSS_BUSIN_TYPE.equals(busin_type)) {
            target.setTranslation_address(auditCenterCommonService.getTranslationAddressCross(address));
            target.setAlternative_address(address);
        }
        // 填充原始数据
        target.setOriginal_file_6A(target.getFile_6A());
        target.setOriginal_file_6B(target.getFile_6B());
        target.setOriginal_file_80(target.getFile_80());
        target.setOriginal_file_7C(target.getFile_7C());
        target.setOriginal_file_7D(target.getFile_7D());
        target.setOriginal_file_Bm(target.getFile_Bm());
        target.setOriginal_file_7W(target.getFile_7W());
        target.setOriginal_file_7X(target.getFile_7X());
        target.setOriginal_id_address(target.getId_address());
        target.setOriginal_id_begindate(target.getId_begindate());
        target.setOriginal_id_enddate(target.getId_enddate());
        target.setOriginal_address(target.getAddress());
        target.setOriginal_choose_branch_reason(target.getChoose_branch_reason());
        target.setOriginal_choose_profession_reason(target.getChoose_profession_reason());
        target.setOriginal_profession_code(target.getProfession_code());
        target.setOriginal_work_unit(target.getWork_unit());
        target.setOriginal_dishonest_record(target.getDishonest_record());
        target.setOriginal_dishonest_record_remark(target.getDishonest_record_remark());
        target.setOriginal_profession_other(target.getProfession_other());
        target.setChannel_code(source.getUser_base_info().getChannel_no());
        target.setOriginal_auxiliary_id_address(target.getAuxiliary_id_address());
        target.setOriginal_auxiliary_id_begindate(target.getAuxiliary_id_begindate());
        target.setOriginal_auxiliary_id_enddate(target.getAuxiliary_id_enddate());
        target.setOriginal_auxiliary_client_name(target.getAuxiliary_client_name());
        target.setOriginal_auxiliary_type(target.getAuxiliary_type());
        target.setOriginal_nationality(target.getNationality());
        target.setOriginal_english_name(target.getEnglish_name());
        target.setOriginal_prev_id_number(target.getPrev_id_number());
        target.setOriginal_birthday(target.getBirthday());

        // 开户提供人像比对结果处理
        if (StringUtils.isNotBlank(target.getDeal_status())) {
            String score = WskhConstant.FACE_SCORE_FAIL;
            if (WskhConstant.STATUS_SUCCESS.equals(target.getDeal_status())) {
                score = WskhConstant.FACE_SCORE_SUCCESS;
            }
            target.setFace_score(score);
            target.setFace_score_82_80(score);
        }
        if (StringUtils.isNotBlank(target.getDeal_info())) {
            target.setFace_score_desc(target.getDeal_info());
        }
        return target;
    }

    public String getFileRecordId(String fileUrl) {
        if (skipFtp) {
            return getSkipFileRecordId(fileUrl);
        } else {
            if (StringUtils.isNotBlank(fileUrl)) {
                return auditCenterCommonService.uploadFileByUri(fileUrl);
            }
        }
        return "";
    }

    public String getSkipFileRecordId(String fileUrl) {
        String filerecord_id = "";
        if (StringUtils.isBlank(fileUrl)) {
            return "";
        }
        try {
            File file = new File(fileUrl);
            if (file.exists()) {
                FileInputStream fileInputStream = new FileInputStream(file);
                byte[] bytes = new byte[(int) file.length()];
                fileInputStream.read(bytes);
                fileInputStream.close();
                String base64 = Base64.getEncoder().encodeToString(bytes);

                filerecord_id = auditCenterCommonService.uploadBase64Image(base64);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            return filerecord_id;
        }
    }
}

package com.cairh.cpe.backend.form.resp;

import com.cairh.cpe.cache.annotation.DataConvert;
import lombok.Data;

@Data
public class IdCardInfoResult {

    /**
     * 证件地址
     */
    private String id_address;

    /**
     * 证件开始日期
     */
    private String id_begindate;

    /**
     * 证件结束日期
     */
    private String id_enddate;

    /**
     * 身份证号
     */
    private String id_no;

    /**
     * 客户姓名
     */
    private String client_name;


    /**
     * 签发年龄和有效期限匹配
     */
    private String issued_age_expiration_date;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 辅助证件类型
     */
    @DataConvert(code_dict = "id_kind")
    private String auxiliary_id_kind;

    /**
     * 辅助证件号码
     */
    private String auxiliary_id_no;

    /**
     * 境外客户姓名
     */
    private String oversea_client_name;

    /**
     * 境外证件类型
     */
    @DataConvert(code_dict = "id_kind")
    private String oversea_id_kind;

    /**
     * 境外证件号码
     */
    private String oversea_id_no;

    /**
     * 证件类型-code
     */
    private String id_kind_code;

    /**
     * 证件类型
     */
    @DataConvert(code_dict = "id_kind")
    private String id_kind;

    /**
     * 英文姓名
     */
    private String english_name;
    /**
     * 曾持有证件号
     */
    private String prev_id_number;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 性别
     */
    private String user_gender;

    /**
     * 开户是否变更数据
     */
    private String initial_address_modify_flag;

    /**
     * 辅助证件有效期开始日期
     */
    private String auxiliary_id_begindate;

    /**
     * 辅助证件有效期结束日期
     */
    private String auxiliary_id_enddate;

    /**
     * 辅助证件地址
     */
    private String auxiliary_id_address;

    /**
     * 港澳台居民居住证-通行证号码
     */
    private String permit_no;

    /**
     * 辅助证件客户姓名
     */
    private String auxiliary_client_name;

    /**
     * 辅助证件类型
     */
    private String auxiliary_type;
}

package com.cairh.cpe.backend.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.CountTaskForm;
import com.cairh.cpe.backend.form.req.TaskDetailRequest;
import com.cairh.cpe.backend.form.resp.TaskDetailInfo;
import com.cairh.cpe.backend.form.resp.TaskDetailInfoExport;
import com.cairh.cpe.backend.form.resp.TaskFinishCountNum;
import com.cairh.cpe.backend.form.resp.TaskPendingCountNum;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MybusinFlowTaskMapper {


    List<TaskDetailInfo> selectTaskList(@Param("queryForm") TaskDetailRequest taskDetailRequest);

    Page<TaskDetailInfo> selectTaskListByPage(Page<TaskDetailInfo> page, @Param("queryForm") TaskDetailRequest taskDetailRequest);

    Page<TaskDetailInfo> selectTaskListByPageByTaskId(Page<TaskDetailInfo> page, @Param("queryForm") TaskDetailRequest taskDetailRequest);

    TaskFinishCountNum countTaskNum(@Param("queryForm") CountTaskForm countTaskForm);

    TaskPendingCountNum countAuditPendingNum(@Param("queryForm") CountTaskForm countTaskForm);

    int countCallsNum(@Param("queryForm") CountTaskForm countTaskForm);

    Page<TaskDetailInfo> claimTaskQuery(Page<TaskDetailInfo> page, @Param("queryForm") TaskDetailRequest taskDetailRequest);

    int taskListCount(@Param("queryForm") TaskDetailRequest taskDetailRequest);

    Page<TaskDetailInfoExport> selectTaskAgentList(Page<TaskDetailInfoExport> page, @Param("queryForm") TaskDetailRequest taskDetailRequest);

}

package com.cairh.cpe.backend.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.backend.form.req.IdCardImageRetryForm;
import com.cairh.cpe.backend.form.resp.IdPhotoResp;
import com.cairh.cpe.backend.form.resp.IdPhotosResp;
import com.cairh.cpe.backend.service.IAuditDetailIdPhotoService;
import com.cairh.cpe.businflow.entity.CustModifyRecord;
import com.cairh.cpe.businflow.service.ICustModifyRecordService;
import com.cairh.cpe.common.constant.ClientCategoryEnum;
import com.cairh.cpe.common.constant.IdKindEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.UserIndependenceInfo;
import com.cairh.cpe.common.entity.request.AuditChangeForm;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.KHDateUtil;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;
import com.cairh.cpe.service.aiaudit.service.IAiAuditAchieveService;
import com.cairh.cpe.service.archive.service.IArchiveService;
import com.cairh.cpe.service.config.AiAuditThreadPoolTaskExecutor;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class AuditDetailIdPhotoServiceImpl implements IAuditDetailIdPhotoService {

    @Autowired
    private IRequestService requestService;
    @Autowired
    private IBusinFlowRequestService businFlowRequestService;
    @Autowired
    private IArchiveService archiveService;
    @Autowired
    private ICustModifyRecordService custModifyRecordService;
    @Autowired
    private IAiAuditAchieveService aiAuditAchieveService;
    @Resource
    private AiAuditThreadPoolTaskExecutor aiAuditThreadPoolTaskExecutor;


    @Override
    public String getIdPhotoFront(String request_no) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);

        return clobContentInfo.getFile_6A();
    }

    @Override
    public String getIdPhotoBack(String request_no) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);

        return clobContentInfo.getFile_6B();
    }

    @Override
    public List<IdPhotosResp> queryHisImage(String request_no) {
        ArrayList<IdPhotosResp> idPhotos = new ArrayList<>();
        LambdaQueryWrapper<CustModifyRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustModifyRecord::getRequest_no, request_no)
                .in(CustModifyRecord::getModify_item, "file_6A", "file_6B", "file_7C", "file_7D", "file_Bm", "file_7W", "file_7X")
                .orderByAsc(CustModifyRecord::getModify_datetime);
        List<CustModifyRecord> custModifyRecords = custModifyRecordService.list(wrapper);

        if (CollectionUtil.isNotEmpty(custModifyRecords)) {
            // 按照修改时间封装返回证件信息
            Map<String, List<CustModifyRecord>> groupedCustModifyRecordMap = custModifyRecords.stream().collect(Collectors.groupingBy(custModifyRecord -> KHDateUtil.formatDate(custModifyRecord.getModify_datetime(), KHDateUtil.DATE_TIME_FORMAT)));
            /*List<Map.Entry<String, List<CustModifyRecord>>> list = new ArrayList<>(groupedCustModifyRecordMap.entrySet());
            Collections.sort(list, new Comparator<Map.Entry<String, List<CustModifyRecord>>>() {
                @Override
                public int compare(Map.Entry<String, List<CustModifyRecord>> o1, Map.Entry<String, List<CustModifyRecord>> o2) {
                    return o1.getKey().compareTo(o2.getKey());
                }
            });*/
            Map<String, List<CustModifyRecord>> treeMap = new TreeMap<>(groupedCustModifyRecordMap);
            treeMap.forEach((modify_datetime, groupModifyRecords) -> {
                IdPhotosResp idPhotosResp = new IdPhotosResp();

                idPhotosResp.setModify_datetime(modify_datetime);
                Optional<CustModifyRecord> custModifyRecord6AOptional = groupModifyRecords.stream().filter(groupModifyRecord -> StrUtil.equals(groupModifyRecord.getModify_item(), "file_6A")).findFirst();
                if (custModifyRecord6AOptional.isPresent()) {
                    idPhotosResp.setFile_6A(custModifyRecord6AOptional.get().getModify_aftercontent());
                }
                Optional<CustModifyRecord> custModifyRecord6BOptional = groupModifyRecords.stream().filter(groupModifyRecord -> StrUtil.equals(groupModifyRecord.getModify_item(), "file_6B")).findFirst();
                if (custModifyRecord6BOptional.isPresent()) {
                    idPhotosResp.setFile_6B(custModifyRecord6BOptional.get().getModify_aftercontent());
                }
                Optional<CustModifyRecord> custModifyRecord7COptional = groupModifyRecords.stream().filter(groupModifyRecord -> StrUtil.equals(groupModifyRecord.getModify_item(), "file_7C")).findFirst();
                if (custModifyRecord7COptional.isPresent()) {
                    idPhotosResp.setFile_7C(custModifyRecord7COptional.get().getModify_aftercontent());
                }
                Optional<CustModifyRecord> custModifyRecord7DOptional = groupModifyRecords.stream().filter(groupModifyRecord -> StrUtil.equals(groupModifyRecord.getModify_item(), "file_7D")).findFirst();
                if (custModifyRecord7DOptional.isPresent()) {
                    idPhotosResp.setFile_7D(custModifyRecord7DOptional.get().getModify_aftercontent());
                }
                Optional<CustModifyRecord> custModifyRecordBmOptional = groupModifyRecords.stream().filter(groupModifyRecord -> StrUtil.equals(groupModifyRecord.getModify_item(), "file_Bm")).findFirst();
                if (custModifyRecordBmOptional.isPresent()) {
                    idPhotosResp.setFile_Bm(custModifyRecordBmOptional.get().getModify_aftercontent());
                }
                Optional<CustModifyRecord> custModifyRecord7WOptional = groupModifyRecords.stream().filter(groupModifyRecord -> StrUtil.equals(groupModifyRecord.getModify_item(), "file_7W")).findFirst();
                if (custModifyRecord7WOptional.isPresent()) {
                    idPhotosResp.setFile_7W(custModifyRecord7WOptional.get().getModify_aftercontent());
                }
                Optional<CustModifyRecord> custModifyRecord7XOptional = groupModifyRecords.stream().filter(groupModifyRecord -> StrUtil.equals(groupModifyRecord.getModify_item(), "file_7X")).findFirst();
                if (custModifyRecord7XOptional.isPresent()) {
                    idPhotosResp.setFile_7X(custModifyRecord7XOptional.get().getModify_aftercontent());
                }
                idPhotos.add(idPhotosResp);
            });
        }

        return idPhotos;
    }

    @Override
    public List<AiAuditRuleResp> imageRetryAndImageSave(IdCardImageRetryForm idCardImageRetryForm) {
        ClobContentInfo clob = requestService.getAllDataByRequestNo(idCardImageRetryForm.getRequest_no());
        UserIndependenceInfo userIndependenceInfo_one = new UserIndependenceInfo();
        //boolean needSave = false;
        //获取base64_6A
        if (!StringUtil.equals(idCardImageRetryForm.getFile_6A(), clob.getFile_6A())) {
            //needSave = true;
            userIndependenceInfo_one.setFile_6A(idCardImageRetryForm.getFile_6A());
        }
        if (!StringUtil.equals(idCardImageRetryForm.getFile_6B(), clob.getFile_6B())) {
            //needSave = true;
            userIndependenceInfo_one.setFile_6B(idCardImageRetryForm.getFile_6B());
        }
        if (!StringUtil.equals(idCardImageRetryForm.getFile_7C(), clob.getFile_7C())) {
            userIndependenceInfo_one.setFile_7C(idCardImageRetryForm.getFile_7C());
        }
        if (!StringUtil.equals(idCardImageRetryForm.getFile_7D(), clob.getFile_7D())) {
            userIndependenceInfo_one.setFile_7D(idCardImageRetryForm.getFile_7D());
        }
        if (!StringUtil.equals(idCardImageRetryForm.getFile_Bm(), clob.getFile_Bm())) {
            userIndependenceInfo_one.setFile_Bm(idCardImageRetryForm.getFile_Bm());
        }
        if (!StringUtil.equals(idCardImageRetryForm.getFile_7W(), clob.getFile_7W())) {
            userIndependenceInfo_one.setFile_7W(idCardImageRetryForm.getFile_7W());
        }
        if (!StringUtil.equals(idCardImageRetryForm.getFile_7X(), clob.getFile_7X())) {
            userIndependenceInfo_one.setFile_7X(idCardImageRetryForm.getFile_7X());
        }
        AuditChangeForm auditChangeForm = new AuditChangeForm();
        auditChangeForm.setRequest_no(idCardImageRetryForm.getRequest_no());
        auditChangeForm.setFlow_task_id(idCardImageRetryForm.getTask_id());
        auditChangeForm.setOperator_no(idCardImageRetryForm.getOperator_no());
        auditChangeForm.setOperator_name(idCardImageRetryForm.getOperator_name());
        auditChangeForm.setModify_link(clob.getAnode_id());
        UserIndependenceInfo userIndependenceInfo = new UserIndependenceInfo();
        userIndependenceInfo.setFile_6A(idCardImageRetryForm.getFile_6A());
        userIndependenceInfo.setFile_6B(idCardImageRetryForm.getFile_6B());
        userIndependenceInfo.setFile_7C(idCardImageRetryForm.getFile_7C());
        userIndependenceInfo.setFile_7D(idCardImageRetryForm.getFile_7D());
        userIndependenceInfo.setFile_Bm(idCardImageRetryForm.getFile_Bm());
        userIndependenceInfo.setFile_7W(idCardImageRetryForm.getFile_7W());
        userIndependenceInfo.setFile_7X(idCardImageRetryForm.getFile_7X());
        auditChangeForm.setIndependenceInfo(userIndependenceInfo);
        auditChangeForm.setIndependenceInfo_one(userIndependenceInfo_one);
        custModifyRecordService.saveParamsAndRecord(auditChangeForm);
        // 执行智能审核
        return executeAiAuditTasks(clob);
    }

    @Override
    public List<AiAuditRuleResp> handImageRetryAndImageSave(IdCardImageRetryForm idCardImageRetryForm) {
        ClobContentInfo clob = requestService.getAllDataByRequestNo(idCardImageRetryForm.getRequest_no());
        List<AiAuditRuleResp> aiAuditRuleResps = new ArrayList<>();
        if (StringUtils.equalsAny(IdKindEnum.ID_CARD.getCode(), clob.getId_kind(), clob.getAgent_id_kind())) {
            UserIndependenceInfo userIndependenceInfo_one = new UserIndependenceInfo();
            //获取base64_6A
            String photo_front = clob.getPhoto_front();
            String photo_back = clob.getPhoto_back();
            // 机构
            if (StringUtils.equalsAny(clob.getClient_category(), ClientCategoryEnum.CLIENT_INSTITUTION.getCode(), ClientCategoryEnum.CLIENT_PRODUCT.getCode())) {
                photo_front = clob.getAgent_photo_front();
                photo_back = clob.getAgent_photo_back();
            }
            if (!StringUtil.equals(idCardImageRetryForm.getFile_6A(), photo_front)) {
                userIndependenceInfo_one.setFile_6A(idCardImageRetryForm.getFile_6A());
            }
            if (!StringUtil.equals(idCardImageRetryForm.getFile_6B(), photo_back)) {
                userIndependenceInfo_one.setFile_6B(idCardImageRetryForm.getFile_6B());
            }
            AuditChangeForm auditChangeForm = new AuditChangeForm();
            auditChangeForm.setRequest_no(idCardImageRetryForm.getRequest_no());
            auditChangeForm.setFlow_task_id(idCardImageRetryForm.getTask_id());
            auditChangeForm.setOperator_no(idCardImageRetryForm.getOperator_no());
            auditChangeForm.setOperator_name(idCardImageRetryForm.getOperator_name());
            auditChangeForm.setModify_link(clob.getAnode_id());
            UserIndependenceInfo userIndependenceInfo = new UserIndependenceInfo();
            userIndependenceInfo.setFile_6A(idCardImageRetryForm.getFile_6A());
            userIndependenceInfo.setFile_6B(idCardImageRetryForm.getFile_6B());
            auditChangeForm.setIndependenceInfo(userIndependenceInfo);
            auditChangeForm.setIndependenceInfo_one(userIndependenceInfo_one);
            custModifyRecordService.saveParamsAndRecord(auditChangeForm);
            //智能审核
            return handExecuteAiAuditTasks(clob);
        }
        return aiAuditRuleResps;
    }

    @Override
    public IdPhotoResp getAllIdPhoto(String request_no) {
        IdPhotoResp idPhotoResp = new IdPhotoResp();
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
        idPhotoResp.setFile_6A(clobContentInfo.getFile_6A());
        idPhotoResp.setFile_6B(clobContentInfo.getFile_6B());
        idPhotoResp.setFile_7C(clobContentInfo.getFile_7C());
        idPhotoResp.setFile_7D(clobContentInfo.getFile_7D());
        idPhotoResp.setFile_Bm(clobContentInfo.getFile_Bm());
        idPhotoResp.setFile_7W(clobContentInfo.getFile_7W());
        idPhotoResp.setFile_7X(clobContentInfo.getFile_7X());
        return idPhotoResp;
    }

    /**
     * 执行智能审核任务
     */
    private List<AiAuditRuleResp> executeAiAuditTasks(ClobContentInfo clob) {
        List<AiAuditRuleResp> aiAuditRuleResps = new ArrayList<>();

        // 只处理普通业务类型
        if (!StringUtils.equalsAny(clob.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL)) {
            return aiAuditRuleResps;
        }
        String requestNo = clob.getRequest_no();

        // 创建异步任务列表
        List<CompletableFuture<List<AiAuditRuleResp>>> futures = new ArrayList<>();

        // 证件质量审核（外国人永居证除外）
        if (!StringUtils.equals(clob.getId_kind(), IdKindEnum.FOREIGN_PREV_PERMIT.getCode())) {
            futures.add(CompletableFuture.supplyAsync(() ->
                    aiAuditAchieveService.againExecuteAiaudit(requestNo, "a"), aiAuditThreadPoolTaskExecutor));
            futures.add(CompletableFuture.supplyAsync(() ->
                    aiAuditAchieveService.againExecuteAiaudit(requestNo, "b"), aiAuditThreadPoolTaskExecutor));
        }

        // 身份证和人脸识别审核
        futures.add(CompletableFuture.supplyAsync(() ->
                aiAuditAchieveService.againExecuteAiaudit(requestNo, "1"), aiAuditThreadPoolTaskExecutor));
        futures.add(CompletableFuture.supplyAsync(() ->
                aiAuditAchieveService.againExecuteAiaudit(requestNo, "3"), aiAuditThreadPoolTaskExecutor));

        // 等待所有任务完成并收集结果
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 合并所有审核结果
        futures.stream()
                .map(CompletableFuture::join)
                .filter(CollectionUtil::isNotEmpty)
                .forEach(aiAuditRuleResps::addAll);

        return aiAuditRuleResps;
    }

    /**
     * 执行网厅智能审核任务
     */
    private List<AiAuditRuleResp> handExecuteAiAuditTasks(ClobContentInfo clob) {
        List<AiAuditRuleResp> aiAuditRuleResps = new ArrayList<>();

        String requestNo = clob.getRequest_no();
        // 创建异步任务列表
        List<CompletableFuture<List<AiAuditRuleResp>>> futures = new ArrayList<>();

        // 证件质量审核（外国人永居证除外）
        if (!StringUtils.equals(clob.getId_kind(), IdKindEnum.FOREIGN_PREV_PERMIT.getCode())) {
            futures.add(CompletableFuture.supplyAsync(() ->
                    aiAuditAchieveService.handAgainExecuteAiaudit(requestNo, "a"), aiAuditThreadPoolTaskExecutor));
            futures.add(CompletableFuture.supplyAsync(() ->
                    aiAuditAchieveService.handAgainExecuteAiaudit(requestNo, "b"), aiAuditThreadPoolTaskExecutor));
        }

        // 身份证和人脸识别审核
        futures.add(CompletableFuture.supplyAsync(() ->
                aiAuditAchieveService.handAgainExecuteAiaudit(requestNo, "1"), aiAuditThreadPoolTaskExecutor));
        futures.add(CompletableFuture.supplyAsync(() ->
                aiAuditAchieveService.handAgainExecuteAiaudit(requestNo, "3"), aiAuditThreadPoolTaskExecutor));

        // 等待所有任务完成并收集结果
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 合并所有审核结果
        futures.stream()
                .map(CompletableFuture::join)
                .filter(CollectionUtil::isNotEmpty)
                .forEach(aiAuditRuleResps::addAll);

        return aiAuditRuleResps;
    }
}

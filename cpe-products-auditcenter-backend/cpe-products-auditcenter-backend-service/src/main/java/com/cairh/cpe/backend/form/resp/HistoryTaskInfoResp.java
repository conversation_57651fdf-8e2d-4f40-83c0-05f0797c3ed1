package com.cairh.cpe.backend.form.resp;

import com.cairh.cpe.cache.annotation.DataConvert;
import com.cairh.cpe.common.backend.annotation.Desensitize;
import com.cairh.cpe.common.backend.masking.mode.DataMaskingMode;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */

@Data
public class HistoryTaskInfoResp {


    /**
     * 节点ID
     */
    private String anode_id;


    /**
     * 客户姓名
     */
    private String client_name;

    /**
     * 推广关系名称
     */
    private String broker_name;

    /**
     * 手机号码
     */
    @Desensitize(mode = DataMaskingMode.MOBILE_TEL)
    private String mobile_tel;

    /**
     * 手机号归属地
     */
    private String mobile_location;


    @DataConvert(code_dict = "id_kind")
    private String id_kind;


    /**
     * 证件号码
     */
    @Desensitize(mode = DataMaskingMode.ID_NO)
    private String id_no;


    /**
     * 渠道编码
     */
    private String channel_code;

    /**
     * 渠道名称
     */
    private String channel_name;

    /**
     * 开户渠道
     */
    private String open_channel;


    /**
     * 活动码
     */
    private String activity_no;

    /**
     * 活动码
     */
    private String activity_name;


    /**
     * 营销团队
     */
    private String marketing_team;

    /**
     * 所属营业部
     */
    private String branch_no;


    /**
     * 营业部名称
     */
    private String branch_name;

    /**
     * 所属分公司
     */
    private String company_name;

    /**
     * 见证人姓名
     */
    private String audit_operator_name;


    /**
     * 见证人工号
     */
    private String audit_operator_no;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date audit_finish_datetime;

    /**
     * 复核人姓名
     */
    private String review_operator_name;

    /**
     * 复核人工号
     */
    private String review_operator_no;

    /**
     * 复核完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date review_finish_datetime;


    /**
     * 二次复核人姓名
     */
    private String double_operator_name;

    /**
     * 二次复核人工号
     */
    private String double_operator_no;

    /**
     * 二次复核完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date double_finish_datetime;


    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date request_datetime;



    /**
     * 视频见证方式
     */
    @DataConvert(code_dict = "video_type")
    private String video_type;

    /**
     * 视频见证方式
     */
    private String video_type_code;


    /**
     * 业务类型
     */
    @DataConvert(code_dict = "busin_type")
    private String busin_type;


    /**
     * 接入方式
     */
    @DataConvert(code_dict = "app_id")
    private String app_id;


    /**
     * 任务编号
     */
    private String serial_id;

    /**
     * 单次任务编号
     */
    private String task_id;


    /**
     * 申请编号
     */
    private String request_no;


    /**
     * 业务类型-code
     */
    private String busin_type_code;


    /**
     * 网厅业务办理-开户类别
     */
    @DataConvert(code_dict = "client_category")
    private String client_category;

    /**
     * 网厅业务办理-开户类别code
     */
    private String client_category_code;

    /**
     * 任务状态
     */
    private String request_status_name;
}

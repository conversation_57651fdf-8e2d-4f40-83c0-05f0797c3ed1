package com.cairh.cpe.backend.form.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 视频用户状态
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VideoUserStatus implements Serializable {

    private static final long serialVersionUID = 4403851955444472364L;

    // 系统编号
    private String subsys_no;

    // 唯一标识
    private String unique_id;

    // 状态
    private String status;

    // 坐席ID
    private String remote_user_id;

    // 即构token(即构作匹配时必传)
    private String zego_token;

    // 房间号
    private String room_id;

    //文件ID
    private String filerecord_id;
}

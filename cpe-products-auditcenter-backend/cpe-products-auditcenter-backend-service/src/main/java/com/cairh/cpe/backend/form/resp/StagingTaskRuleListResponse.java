package com.cairh.cpe.backend.form.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025/3/12 09:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StagingTaskRuleListResponse {

    /**
     * 主键ID
     */
    private String serial_id;

    /**
     * 规则名称
     */
    private String rule_name;

    /**
     * 规则类型
     */
    private String rule_type;

}

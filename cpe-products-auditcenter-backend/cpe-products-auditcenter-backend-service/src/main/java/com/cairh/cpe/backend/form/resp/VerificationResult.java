package com.cairh.cpe.backend.form.resp;

import com.cairh.cpe.common.entity.response.VerifyPoliceResp;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 验证结果
 *
 * <AUTHOR>
 * @since 2025/5/7 10:38
 */
@Data
@AllArgsConstructor
public class VerificationResult {

    /**
     * 公安认证结果
     */
    private VerifyPoliceResp response;

    /**
     * 是否是新数据
     */
    private boolean isNewDataSign;

    /**
     * 是否是工作时间
     */
    private boolean isWorkTime;
}

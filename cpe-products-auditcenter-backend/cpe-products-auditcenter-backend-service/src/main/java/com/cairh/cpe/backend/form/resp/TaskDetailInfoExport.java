package com.cairh.cpe.backend.form.resp;

import com.cairh.cpe.common.backend.annotation.Desensitize;
import com.cairh.cpe.common.backend.masking.mode.DataMaskingMode;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TaskDetailInfoExport {

    /**
     * 推送状态
     */
    private String push_flag;

    /**
     * 不允许的审核人
     */
    private String not_allow_auditor;

    /**
     * 真实任务类型
     */
    private String task_type_str;

    /**
     * 客户姓名
     */
    private String client_name;

    /**
     * 推广关系名称
     */
    private String broker_name;


    /**
     * 手机号码
     */
    @Desensitize(mode = DataMaskingMode.MOBILE_TEL)
    private String mobile_tel;

    /**
     * 手机号归属地
     */
    private String mobile_location;


    /**
     * 证件类型
     */
    private String id_kind;


    /**
     * 证件号码
     */
    @Desensitize(mode = DataMaskingMode.ID_NO)
    private String id_no;


    /**
     * 渠道编码
     */
    private String channel_code;

    /**
     * 渠道名称
     */
    private String channel_name;

    /**
     * 开户渠道
     */
    private String open_channel;

    /**
     * 是否vip渠道 0 不是  1 是
     */
    private String is_vip;


    /**
     * 活动码
     */
    private String activity_no;

    /**
     * 活动码
     */
    private String activity_name;


    /**
     * 任务状态
     */
    private String task_type;

    private String task_status;


    /**
     * 营销团队
     */
    private String marketing_team;


    /**
     * 所属营业部
     */
    private String branch_no;

    /**
     * 所属营业部
     */
    private String branch_name;


    /**
     * 所属分公司
     */
    private String company_name;


    /**
     * 审核人编号
     */
    private String operator_name;


    /**
     * 审核人
     */
    private String operator_no;


    /**
     * 见证人姓名
     */
    private String audit_operator_name;


    /**
     * 见证人工号
     */
    private String audit_operator_no;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date audit_finish_datetime;

    /**
     * 复核人姓名
     */
    private String review_operator_name;

    /**
     * 复核人工号
     */
    private String review_operator_no;

    /**
     * 复核完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date review_finish_datetime;


    /**
     * 二次复核人姓名
     */
    private String double_operator_name;

    /**
     * 二次复核人工号
     */
    private String double_operator_no;

    /**
     * 二次复核完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date double_finish_datetime;


    /**
     * 处理人工号
     */
    private String deal_operator_name;

    /**
     * 处理人姓名
     */
    private String deal_operator_no;


    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deal_datetime;


    /**
     * 任务提交时间
     */
    private String task_create_datetime;


    /**
     * 任务来源
     */
    private String task_source;


    /**
     * 开户申请提交时间
     */
    private String request_datetime;

    /**
     * 视频见证方式
     */
    private String video_type;

    /**
     * 业务类型
     */
    private String busin_type;


    /**
     * 接入方式
     */
    private String app_id;


    /**
     * 集中审核中心任务表的任务编号
     */
    private String serial_id;


    /**
     * 用户的业务流水号
     */
    private String request_no;


    /**
     * 展示 处理按钮  show展示  noshow不展示
     */
    private String show_button;


    private Date create_datetime;

    private String match_labels;

    /**
     * 是否绿通
     */
    private String white_flag;

    /**
     * 是否超时
     */
    private Boolean near_expiry = Boolean.FALSE;

    /**
     * 标签列表
     */
    private List<String> match_labels_types;

    /**
     * 业务类型-code
     */
    private String busin_type_code;

    /**
     * 开户渠道是否命中
     */
    private String branch_repeated;

    /**
     * 联系地址是否命中
     */
    private String address_repeated;

    /**
     * 网厅业务办理-开户类别
     */
    private String client_category;

    /**
     * 网厅业务办理-开户类别code
     */
    private String client_category_code;

}

package com.cairh.cpe.backend.service.impl;

import com.cairh.cpe.backend.service.IAuditDetailUserApplyRecordService;
import com.cairh.cpe.common.backend.util.BaseBeanUtil;
import com.cairh.cpe.common.entity.request.BusinFlowRecordForm;
import com.cairh.cpe.common.entity.response.BusinFlowRecordResp;
import com.cairh.cpe.common.service.IBusinFlowRecordService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class AuditDetailUserApplyRecordServiceImpl implements IAuditDetailUserApplyRecordService {

    @Autowired
    private IBusinFlowRecordService businFlowRecordService;


    @Override
    public List<com.cairh.cpe.backend.form.resp.BusinFlowRecordResp> getApplyRecord(BusinFlowRecordForm businFlowRecordForm) {
        businFlowRecordForm.setRecord_type("1");

        // 任务类型
        //List<String> task_types = Stream.of("audit", "review", "secondary_review").collect(Collectors.toList());
        List<String> task_types = Stream.of("review", "secondary_review").collect(Collectors.toList());
        businFlowRecordForm.setTask_types(task_types);
        // 完成办理业务标识
        //List<String> business_flags = Stream.of("1002", "1003", "1102", "1103", "1202", "1203").collect(Collectors.toList());
        List<String> business_flags = Stream.of("1102","1202").collect(Collectors.toList());
        businFlowRecordForm.setBusiness_flags(business_flags);

        List<BusinFlowRecordResp> businFlowRecordResps = businFlowRecordService.qryUserApplyRecord(businFlowRecordForm);
        List<BusinFlowRecordResp> collect = businFlowRecordResps.stream().filter(record -> StringUtils.contains(record.getBusi_content(), "\"request_status\":\"3\"")).collect(Collectors.toList());
        return BaseBeanUtil.copyToList(collect, com.cairh.cpe.backend.form.resp.BusinFlowRecordResp.class);
    }
}

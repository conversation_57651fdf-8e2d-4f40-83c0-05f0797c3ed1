package com.cairh.cpe.backend.form.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class HandVerifyPoliceAllForm {

    @NotBlank(message = "业务流水号不能为空")
    private String request_no;

    /**
     * 免冠照
     */
    @NotBlank(message = "file_80不能为空")
    private String file_80;

    /**
     * 任务id
     */
    @NotBlank(message = "唯一编号不能为空")
    private String task_id;
}

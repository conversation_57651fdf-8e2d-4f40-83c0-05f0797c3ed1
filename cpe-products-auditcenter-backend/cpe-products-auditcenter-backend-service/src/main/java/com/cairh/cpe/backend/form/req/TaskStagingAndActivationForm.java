package com.cairh.cpe.backend.form.req;


import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 任务暂存和激活入参
 *
 * <AUTHOR>
 * @since 2025/3/12 14:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TaskStagingAndActivationForm {


    /**
     * 任务请求编号
     */
    @NotBlank(message = "任务请求编号不能为空")
    private String request_no;

    /**
     * 任务编号
     */
    @NotBlank(message = "任务编号不能为空")
    private String task_id;

    /**
     * 任务唯一编号
     */
    @NotBlank(message = "任务唯一编号不能为空")
    private String flow_task_id;

}

package com.cairh.cpe.backend.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.api.aiaudit.resp.MaterialInfo;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.*;
import com.cairh.cpe.common.entity.response.AuditOperateRecord;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.service.aiaudit.response.QueryAuditBusinRecordResp;

import java.util.List;

public interface IAuditDetailHisInfoService {


    /**
     * 客户申请列表
     *
     * @param request
     * @return
     */
    Page<HistoryTaskInfoResp> requestPage(List<String> branchNos, HistoryDataRequest request);


    /**
     * 审核任务列表
     *
     * @param request
     * @return
     */
    Page<HistoryTaskInfoResp> taskPage(List<String> branchNos, HistoryDataRequest request);

    /**
     * 导出
     *
     * @param branchNos
     * @param request
     * @return
     */
    List<HistoryTaskInfoResp> export(List<String> branchNos, HistoryDataRequest request, boolean applyList);


    Result<AuditDetailUserBaseInfo> getUserSource(BaseInfoForm baseInfoForm);

    RecentRejectReasonResp getLastRejectReason(BaseInfoForm baseInfoForm);

    List<BusinFlowRecordResult> getHisRejectReason(String request_no);

    VideoInfoResp getVideoInfo(String request_no);

    String getIdPhotoFront(String request_no);

    String getIdPhotoBack(String request_no);

    ProfileImageResp getProfileImageAndScore(String request_no);

    IdCardInfoResult getIdInfo(IdCardInfoForm idCardInfoForm);

    MoneyAntiInfo getMoneyAntiInfo(String request_no);

    MoneyAntiInfo getProfessionReason(GetProfessionReasonForm getProfessionReasonForm);

    List<AuditOperateRecord> getOperatorRecord(String request_no);

    DishonestResp getDishonestRecord(String request_no);

    QueryAuditBusinRecordResp queryAuditBusinRecordV1(AuditQueryRecordReq auditQueryRecordReq);

    BusinFlowTaskPositionResp queryBusinFlowTaskPosition(String flowtask_id);

    MaterialInfo getUserMaterialInfo(String request_no);

    IdPhotoResp getAllIdPhoto(String request_no);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cairh.cpe.backend.mapper.CenterBusinFlowTaskMapper">
    <!--待处理-->
    <select id="selectCenterTaskListByPage" resultType="com.cairh.cpe.backend.form.resp.TaskDetailInfo">
        select /*+ index(b IDX_BFTASK_TASKSTATUS)*/
        'show' show_button,
        <include refid="task_field"/>
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.TASK_STATUS in ('1', '2', 'a', 'b')
        and push_flag != '7'
        <include refid="task_condition"/>
        order by u.request_datetime
    </select>

    <!--已完成-->
    <select id="selectCompleteCenterTaskListByPage" resultType="com.cairh.cpe.backend.form.resp.TaskDetailInfo">
        select
        'noshow' show_button,
        <include refid="task_field"/>
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.serial_id = u.REQUEST_NO
        where b.TASK_STATUS in ('3','4', '8')
        <include refid="task_condition"/>
        order by u.request_datetime
    </select>

    <!--暂存-->
    <select id="selectStagingCenterTaskListByPage" resultType="com.cairh.cpe.backend.form.resp.TaskDetailInfo">
        select /*+ index(b IDX_BFTASK_TASKSTATUS)*/
        'show' show_button,
        <include refid="task_field"/>
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.TASK_STATUS = '1'
        and b.push_flag = '7'
        <include refid="task_condition"/>
        order by u.request_datetime
    </select>

    <!--手动暂存-->
    <select id="selectStagingCenterTaskListByPageAndSerialId"
            resultType="com.cairh.cpe.backend.form.resp.TaskStagingAndActivationInfo">
        select /*+ index(b IDX_BFTASK_TASKSTATUS)*/
        b.serial_id,
        b.task_id,
        b.request_no,
        b.task_status,
        b.push_flag,
        b.not_allow_auditor
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.TASK_STATUS = '1'
        and b.video_type = '1'
        <include refid="task_condition"/>
    </select>

    <!--手动激活-->
    <select id="selectActivationCenterTaskListByPageAndSerialId"
            resultType="com.cairh.cpe.backend.form.resp.TaskStagingAndActivationInfo">
        select /*+ index(b IDX_BFTASK_TASKSTATUS)*/
        b.serial_id,
        b.task_id,
        b.request_no,
        b.task_status,
        b.push_flag,
        b.not_allow_auditor
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.TASK_STATUS = '1'
        <include refid="task_condition"/>
    </select>

    <!--手动暂存查询数量-->
    <select id="countStagingOrActivationCenterTaskNum" resultType="java.lang.Integer">
        select count(*)
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.TASK_STATUS = '1'
        and b.video_type = '1'
        <include refid="task_condition"/>
    </select>

    <!--待处理 查询全部，限制50000条-->
    <select id="selectCenterTaskList" resultType="com.cairh.cpe.backend.form.resp.TaskDetailInfoExport">
        select /*+ index(b IDX_BFTASK_TASKSTATUS)*/
        <include refid="task_field"/>
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.TASK_STATUS in ('1', '2', 'a', 'b')
        <include refid="task_condition"/>
        order by b.request_no
    </select>

    <!--已完成 查询全部，限制50000条-->
    <select id="selectCompleteCenterTaskList" resultType="com.cairh.cpe.backend.form.resp.TaskDetailInfoExport">
        select
        <include refid="task_field"/>
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.serial_id = u.REQUEST_NO
        where b.TASK_STATUS in ('3','4')
        <include refid="task_condition"/>
        order by b.request_no
    </select>

    <!--待处理 查询数量-->
    <select id="countCenterTask" resultType="java.lang.Integer">
        select count(1)
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.TASK_STATUS in ('1', '2', 'a', 'b')
        <include refid="task_condition"/>
    </select>

    <!--已完成 查询数量-->
    <select id="countCompleteCenterTask" resultType="java.lang.Integer">
        select count(1)
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.TASK_STATUS in ('3','4', '8')
        <include refid="task_condition"/>
    </select>


    <sql id="task_field">
        u.client_name,
        u.mobile_tel,
        u.broker_name,
        u.id_no,
        u.id_kind,
        u.channel_code,
        u.activity_no,
        u.activity_name,
        u.marketing_team,
        u.branch_no branch_name,
        u.branch_no,
        u.audit_operator_no,
        u.audit_operator_name,
        u.audit_finish_datetime,
        u.review_operator_no,
        u.review_operator_name,
        u.review_finish_datetime,
        u.double_operator_no,
        u.double_operator_name,
        u.double_finish_datetime,
        u.request_datetime,
        u.video_type,
        u.mobile_location,
        u.open_channel,
        u.channel_name,
        u.app_id,
        u.busin_type,
        u.client_category,
        b.serial_id,
        b.request_no,
        b.create_datetime,
        b.deal_datetime,
        b.white_flag,
        b.task_source,
        b.call_flag,
        b.task_status,
        b.task_type || '-' || b.task_status task_type,
        b.task_type task_type_code,
        b.operator_no,
        b.push_flag,
        b.not_allow_auditor,
        b.finish_datetime,
        b.match_labels,
        b.task_id
    </sql>

    <sql id="task_condition">
        <if test="queryForm.operator_branch !=null and queryForm.operator_branch.size()!=0">
            and u.branch_no IN
            <foreach collection="queryForm.operator_branch" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.request_datetime_start != null and queryForm.request_datetime_start!=''">
            and u.request_datetime &gt;= ${queryForm.request_datetime_start}
        </if>
        <if test="queryForm.request_datetime_end != null and queryForm.request_datetime_end!=''">
            and u.request_datetime &lt;= ${queryForm.request_datetime_end}
        </if>
        <if test="queryForm.audit_datetime_start != null and queryForm.audit_datetime_start!=''">
            and u.audit_finish_datetime &gt;= ${queryForm.audit_datetime_start}
        </if>
        <if test="queryForm.audit_datetime_end != null and queryForm.audit_datetime_end!=''">
            and u.audit_finish_datetime &lt;= ${queryForm.audit_datetime_end}
        </if>
        <if test="queryForm.review_datetime_start != null and queryForm.review_datetime_start!=''">
            and u.review_finish_datetime &gt;= ${queryForm.review_datetime_start}
        </if>
        <if test="queryForm.review_datetime_end != null and queryForm.review_datetime_end!=''">
            and u.review_finish_datetime &lt;= ${queryForm.review_datetime_end}
        </if>
        <if test="queryForm.double_datetime_start != null and queryForm.double_datetime_start!=''">
            and u.double_finish_datetime &gt;= ${queryForm.double_datetime_start}
        </if>
        <if test="queryForm.double_datetime_end != null and queryForm.double_datetime_end!=''">
            and u.double_finish_datetime &lt;= ${queryForm.double_datetime_end}
        </if>
        <if test="queryForm.open_channel != null and queryForm.open_channel!=''">
            and u.open_channel = #{queryForm.open_channel}
        </if>
        <if test="queryForm.video_type != null and queryForm.video_type!=''">
            and u.video_type = #{queryForm.video_type}
        </if>
        <if test="queryForm.client_name != null and queryForm.client_name!=''">
            and u.client_name= #{queryForm.client_name}
        </if>
        <if test="queryForm.id_kind !=null and queryForm.id_kind !=''">
            and u.id_kind = #{queryForm.id_kind}
        </if>
        <if test="queryForm.id_no != null and queryForm.id_no!=''">
            and u.id_no = #{queryForm.id_no}
        </if>
        <if test="queryForm.mobile_tel != null and queryForm.mobile_tel!=''">
            and u.mobile_tel = #{queryForm.mobile_tel}
        </if>
        <if test="queryForm.broker_name != null and queryForm.broker_name!=''">
            and u.broker_name like '%' || #{queryForm.broker_name} || '%'
        </if>
        <if test="queryForm.channel_code != null and queryForm.channel_code!=''">
            and u.channel_code = #{queryForm.channel_code}
        </if>
        <if test="queryForm.channel_name != null and queryForm.channel_name!=''">
            and u.channel_name like '%' || #{queryForm.channel_name} || '%'
        </if>
        <if test="queryForm.activity_no != null and queryForm.activity_no!=''">
            and u.activity_no = #{queryForm.activity_no}
        </if>
        <if test="queryForm.activity_name != null and queryForm.activity_name!=''">
            and u.activity_name like '%' || #{queryForm.activity_name} || '%'
        </if>
        <if test="queryForm.marketing_team != null and queryForm.marketing_team!=''">
            and u.marketing_team like '%' || #{queryForm.marketing_team} || '%'
        </if>
        <if test="queryForm.audit_operator_no != null and queryForm.audit_operator_no!=''">
            and u.audit_operator_no = #{queryForm.audit_operator_no}
        </if>
        <if test="queryForm.audit_operator_name != null and queryForm.audit_operator_name!=''">
            and u.audit_operator_name like '%' || #{queryForm.audit_operator_name} || '%'
        </if>
        <if test="queryForm.review_operator_no != null and queryForm.review_operator_no!=''">
            and u.review_operator_no = #{queryForm.review_operator_no}
        </if>
        <if test="queryForm.review_operator_name != null and queryForm.review_operator_name!=''">
            and u.review_operator_name like '%' || #{queryForm.review_operator_name} || '%'
        </if>
        <if test="queryForm.double_operator_no != null and queryForm.double_operator_no!=''">
            and u.double_operator_no = #{queryForm.double_operator_no}
        </if>
        <if test="queryForm.double_operator_name != null and queryForm.double_operator_name!=''">
            and u.double_operator_name like '%' || #{queryForm.double_operator_name} || '%'
        </if>
        <if test="queryForm.task_status_type_list!=null and queryForm.task_status_type_list.size()!=0">
            and b.task_type || '-' || b.task_status in
            <foreach collection="queryForm.task_status_type_list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.busin_types !=null and queryForm.busin_types.size()!=0">
            and u.busin_type IN
            <foreach collection="queryForm.busin_types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.app_ids !=null and queryForm.app_ids.size()!=0">
            and u.app_id IN
            <foreach collection="queryForm.app_ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.request_no != null and queryForm.request_no!=''">
            and b.request_no > #{queryForm.request_no}
        </if>
        <if test="queryForm.client_category != null and queryForm.client_category!=''">
            and u.client_category = #{queryForm.client_category}
        </if>
        <if test="queryForm.task_source != null and queryForm.task_source!=''">
            and b.task_source = #{queryForm.task_source}
        </if>
        <if test="queryForm.call_flag != null and queryForm.call_flag!=''">
            and b.call_flag = #{queryForm.call_flag}
        </if>
        <if test="queryForm.match_label != null and queryForm.match_label!=''">
            and b.match_labels like '%' || #{queryForm.match_label} || '%'
        </if>
        <if test="queryForm.white_flag != null and queryForm.white_flag!=''">
            and b.white_flag = #{queryForm.white_flag}
        </if>
        <if test="queryForm.push_flag != null and queryForm.push_flag!=''">
            and b.push_flag = #{queryForm.push_flag}
        </if>
        <if test="queryForm.push_flags !=null and queryForm.push_flags.size()!=0">
            and b.push_flag IN
            <foreach collection="queryForm.push_flags" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="countCenterTaskNum" resultType="com.cairh.cpe.backend.form.resp.TaskFinishCountNum">
        select count(1) as task_num,
        NVL(SUM(CASE WHEN b.task_type = 'audit' THEN 1 ELSE 0 END),0) AS audit_num,
        NVL(SUM(CASE WHEN b.task_type = 'review' THEN 1 ELSE 0 END),0) AS review_num,
        NVL(SUM(CASE WHEN b.task_type = 'secondary_review' THEN 1 ELSE 0 END),0) AS secondary_review_num,
        NVL(SUM(CASE WHEN b.task_type = 'audit' AND b.task_status = '3' THEN 1 ELSE 0 END),0) AS audit_pass_num,
        NVL(SUM(CASE WHEN b.task_type = 'audit' AND (b.task_status = '4' OR b.task_status = '8') THEN 1 ELSE 0 END),0)
        AS audit_notpass_num,
        NVL(SUM(CASE WHEN b.task_type = 'review' AND b.task_status = '3' THEN 1 ELSE 0 END),0) AS review_pass_num,
        NVL(SUM(CASE WHEN b.task_type = 'review' AND b.task_status = '4' THEN 1 ELSE 0 END),0) AS review_notpass_num,
        NVL(SUM(CASE WHEN b.task_type = 'secondary_review' AND b.task_status = '3' THEN 1 ELSE 0 END),0) AS
        secondary_review_pass_num,
        NVL(SUM(CASE WHEN b.task_type = 'secondary_review' AND b.task_status = '4' THEN 1 ELSE 0 END),0) AS
        secondary_review_notpass_num
        from businflowtask b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.finish_datetime >= TRUNC(SYSDATE)
        and u.branch_no IN
        <foreach collection="queryForm.operator_branch" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="countAuditPendingNum" resultType="com.cairh.cpe.backend.form.resp.TaskPendingCountNum">
        select count(1) AS audit_pending_num,
        NVL(SUM(CASE WHEN b.task_type = 'audit' THEN 1 ELSE 0 END),0) AS audit_pend_num,
        NVL(SUM(CASE WHEN b.task_type = 'review' THEN 1 ELSE 0 END),0)AS review_pend_num,
        NVL(SUM(CASE WHEN b.task_type = 'secondary_review' THEN 1 ELSE 0 END),0) AS secondary_review_pend_num,
        NVL(SUM(CASE WHEN b.push_flag = '7' THEN 1 ELSE 0 END),0) AS staging_num
        from businflowtask b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.task_status in ('1', '2', 'a', 'b')
        and u.branch_no IN
        <foreach collection="queryForm.operator_branch" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="countCallsNum" resultType="int">
        select count(1)
        from CALLDETAILS
        where call_status = '1' and is_answer = '1'
        and create_datetime >= TRUNC(SYSDATE)
        and client_branch_no IN
        <foreach collection="queryForm.operator_branch" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>
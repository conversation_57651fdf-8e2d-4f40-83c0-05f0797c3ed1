<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cairh.cpe.backend.mapper.UserQueryExtInfoBaseMapper">

    <!--客户查询列表查询-->
    <select id="selectUserQueryExtInfoListByPage" resultType="com.cairh.cpe.backend.form.resp.UserQueryExtInfoDetail">
        select
        <choose>
            <!-- 当request_datetime_start或request_datetime_end存在时指定索引 -->
            <when test="queryForm.request_datetime_start != null or queryForm.request_datetime_end != null">
                /*+ index(u IDX_USER_REQUESTDATE)*/
            </when>
        </choose>
        <include refid="userExt_field"/>
        from USERQUERYEXTINFO u
        where u.is_snapshot = '0'
        <include refid="userExt_condition"/>
        order by u.request_datetime desc
    </select>

    <!--客户查询导出查询-->
    <select id="selectUserQueryExtInfoExportListByPage" resultType="com.cairh.cpe.backend.form.resp.UserQueryExtInfoDetail">
        select /*+ index(u IDX_USER_REQUESTDATE)*/
        <include refid="userExt_field"/>
        from USERQUERYEXTINFO u
        where u.is_snapshot = '0'
        <include refid="userExt_condition"/>
        order by u.request_no
    </select>

    <!--客户查询导出获取数量-->
    <select id="countUserQueryExtInfo" resultType="java.lang.Integer">
        select count(*)
        from USERQUERYEXTINFO u
        where u.is_snapshot = '0'
        <include refid="userExt_condition"/>
    </select>

    <sql id="userExt_field">
        u.request_no,
        u.request_status,
        u.request_datetime,
        u.submit_datetime,
        u.mobile_tel,
        u.branch_no,
        u.client_id,
        u.fund_account,
        u.broker_name,
        u.broker_code,
        u.app_id,
        u.channel_code,
        u.channel_name,
        u.client_name,
        u.id_kind,
        u.id_no,
        u.client_gender,
        u.nation_id,
        u.issued_depart,
        u.id_begindate,
        u.id_enddate,
        u.id_address,
        u.address,
        u.zipcode,
        u.industry_type,
        u.degree_code,
        u.profession_code,
        u.identity_category,
        u.audit_operator_no,
        u.audit_operator_name,
        u.audit_finish_datetime,
        u.review_operator_no,
        u.review_operator_name,
        u.review_finish_datetime,
        u.double_operator_no,
        u.double_operator_name,
        u.double_finish_datetime,
        u.mobile_diffplace,
        u.anode_id,
        u.choose_branch_reason,
        u.choose_profession_reason,
        u.mobile_location,
        u.activity_no,
        u.activity_name,
        u.marketing_team,
        u.dishonest_record,
        u.dishonest_record_remark,
        u.dishonest_content,
        u.work_unit,
        u.client_tags,
        u.op_station,
        u.video_type,
        u.ai_audit_code,
        u.busin_type,
        u.video_operator_no,
        u.video_operator_name,
        u.open_channel,
        u.end_node,
        u.is_snapshot
    </sql>

    <sql id="userExt_condition">
        <if test="queryForm.request_datetime_start != null and queryForm.request_datetime_start!=''">
            and u.request_datetime &gt;= ${queryForm.request_datetime_start}
        </if>
        <if test="queryForm.request_datetime_end != null and queryForm.request_datetime_end!=''">
            and u.request_datetime &lt;= ${queryForm.request_datetime_end}
        </if>
        <if test="queryForm.branch_nos !=null and queryForm.branch_nos.size()!=0">
            and u.branch_no IN
            <foreach collection="queryForm.branch_nos" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.client_name != null and queryForm.client_name!=''">
            and u.client_name= #{queryForm.client_name}
        </if>
        <if test="queryForm.id_no != null and queryForm.id_no!=''">
            and u.id_no = #{queryForm.id_no}
        </if>
        <if test="queryForm.mobile_tel != null and queryForm.mobile_tel!=''">
            and u.mobile_tel = #{queryForm.mobile_tel}
        </if>
        <if test="queryForm.video_type != null and queryForm.video_type!=''">
            and u.video_type = #{queryForm.video_type}
        </if>
        <if test="queryForm.busin_type != null and queryForm.busin_type!=''">
            and u.busin_type = #{queryForm.busin_type}
        </if>
        <if test="queryForm.request_status != null and queryForm.request_status!=''">
            and u.request_status = #{queryForm.request_status}
        </if>
        <if test="queryForm.end_node != null and queryForm.end_node!=''">
            and (u.end_node = #{queryForm.end_node} or u.anode_id = #{queryForm.end_node})
        </if>
        <if test="queryForm.anode_id != null and queryForm.anode_id!=''">
            and u.anode_id = #{queryForm.anode_id}
        </if>
        <if test="queryForm.audit_operator_no != null and queryForm.audit_operator_no!=''">
            and u.audit_operator_no = #{queryForm.audit_operator_no}
        </if>
        <if test="queryForm.audit_operator_name != null and queryForm.audit_operator_name!=''">
            and u.audit_operator_name like '%' || #{queryForm.audit_operator_name} || '%'
        </if>
        <if test="queryForm.review_operator_no != null and queryForm.review_operator_no!=''">
            and u.review_operator_no = #{queryForm.review_operator_no}
        </if>
        <if test="queryForm.review_operator_name != null and queryForm.review_operator_name!=''">
            and u.review_operator_name like '%' || #{queryForm.review_operator_name} || '%'
        </if>
        <if test="queryForm.double_operator_no != null and queryForm.double_operator_no!=''">
            and u.double_operator_no = #{queryForm.double_operator_no}
        </if>
        <if test="queryForm.double_operator_name != null and queryForm.double_operator_name!=''">
            and u.double_operator_name like '%' || #{queryForm.double_operator_name} || '%'
        </if>
        <if test="queryForm.broker_name != null and queryForm.broker_name!=''">
            and u.broker_name like '%' || #{queryForm.broker_name} || '%'
        </if>
        <if test="queryForm.channel_code != null and queryForm.channel_code!=''">
            and u.channel_code = #{queryForm.channel_code}
        </if>
        <if test="queryForm.activity_no != null and queryForm.activity_no!=''">
            and u.activity_no = #{queryForm.activity_no}
        </if>
        <if test="queryForm.activity_name != null and queryForm.activity_name!=''">
            and u.activity_name like '%' || #{queryForm.activity_name} || '%'
        </if>
        <if test="queryForm.marketing_team != null and queryForm.marketing_team!=''">
            and u.marketing_team like '%' || #{queryForm.marketing_team} || '%'
        </if>
        <if test="queryForm.review_datetime_start != null and queryForm.review_datetime_start!=''">
            and u.review_finish_datetime &gt;= ${queryForm.review_datetime_start}
        </if>
        <if test="queryForm.review_datetime_end != null and queryForm.review_datetime_end!=''">
            and u.review_finish_datetime &lt;= ${queryForm.review_datetime_end}
        </if>
        <if test="queryForm.double_datetime_start != null and queryForm.double_datetime_start!=''">
            and u.double_finish_datetime &gt;= ${queryForm.double_datetime_start}
        </if>
        <if test="queryForm.double_datetime_end != null and queryForm.double_datetime_end!=''">
            and u.double_finish_datetime &lt;= ${queryForm.double_datetime_end}
        </if>
        <if test="queryForm.open_channel != null and queryForm.open_channel!=''">
            and u.open_channel = #{queryForm.open_channel}
        </if>
        <if test="queryForm.app_id != null and queryForm.app_id!=''">
            and u.app_id = #{queryForm.app_id}
        </if>
        <if test="queryForm.request_no != null and queryForm.request_no!=''">
            and u.request_no > #{queryForm.request_no}
        </if>
    </sql>
</mapper>
package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.form.req.BaseForm;
import com.cairh.cpe.backend.form.resp.VideoInfoResp;
import com.cairh.cpe.backend.service.IAuditDetailVideoInfoService;
import com.cairh.cpe.backend.util.ChannelVipHelper;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.service.aiaudit.response.QueryAuditBusinRecordResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 审核详情 - 视频信息
 */
@Slf4j
@RestController
@RequestMapping("auditDetailVideoInfo")
public class AuditDetailVideoInfoController {

    @Autowired
    private IAuditDetailVideoInfoService auditDetailVideoInfoService;
    @Autowired
    private ChannelVipHelper channelVipHelper;
    @Autowired
    private IBusinFlowRequestService businFlowRequestService;


    /**
     * 获取视频
     */
    @PostMapping("getVideoInfo")
    public Result<VideoInfoResp> getVideoInfo(@Validated @RequestBody BaseForm baseForm) {
        VideoInfoResp videoInfo = auditDetailVideoInfoService.getVideoInfo(baseForm.getRequest_no());

        videoInfo.setVip(channelVipHelper.isVip(baseForm.getRequest_no()));
        return Result.success(videoInfo);
    }

    /**
     * 视频以及语音刷新，重新进行智能审核
     */
    @PostMapping("refreshVideo")
    public Result<QueryAuditBusinRecordResp> refreshVideo(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody BaseForm baseForm) {
        String request_no = baseForm.getRequest_no();
        BusinFlowRequest businFlowRequest = businFlowRequestService.getById(request_no);
        if (null == businFlowRequest) {
            return Result.fail("参数传参有误");
        }
        if (!StringUtils.equals(businFlowRequest.getRequest_status(), FlowStatusConst.REQUEST_STATUS_AUDITING)) {
            return Result.fail(ErrorEnum.AUDIT_NOT_AUDITING_TASK.getValue(), ErrorEnum.AUDIT_NOT_AUDITING_TASK.getDesc());
        }
        return Result.success(auditDetailVideoInfoService.refreshVideo(baseUser, businFlowRequest));
    }
}

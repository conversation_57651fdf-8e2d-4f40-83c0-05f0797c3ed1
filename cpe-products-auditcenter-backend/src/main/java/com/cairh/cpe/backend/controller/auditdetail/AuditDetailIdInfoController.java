package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.form.req.HandInfoForm;
import com.cairh.cpe.backend.form.req.IdCardInfoForm;
import com.cairh.cpe.backend.form.req.ValidateCitizenDocumentForm;
import com.cairh.cpe.backend.form.resp.HandInfoResp;
import com.cairh.cpe.backend.form.resp.IdCardInfoResult;
import com.cairh.cpe.backend.service.IAuditDetailIdInfoService;
import com.cairh.cpe.common.constant.IdKindEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 审核详情 - 证件信息
 */
@Slf4j
@RestController
@RequestMapping("auditDetailIdInfo")
public class AuditDetailIdInfoController {

    @Autowired
    IAuditDetailIdInfoService auditDetailIdInfoService;

    @Resource
    private IBusinFlowRequestService businFlowRequestService;

    @Autowired
    private RedissonUtil redissonUtil;

    /**
     * 证件信息重新识别
     */
    @PostMapping("idRetry")
    public Result<List<AiAuditRuleResp>> idRetry(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody IdCardInfoForm idCardInfoForm) {
//        String lockKey = String.format(LockKeyConstant.WSKH_CUST_PARAM_REQUEST_NO, idCardInfoForm.getRequest_no());
//        boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
//        if (!isLock) {
//            return Result.fail(ErrorEnum.REDIS_TRY_LOCK_FAIL.getValue(), ErrorEnum.REDIS_TRY_LOCK_FAIL.getDesc());
//        }
//        try {
        idCardInfoForm.setOperator_no(baseUser.getStaff_no());
        idCardInfoForm.setOperator_name(baseUser.getUser_name());
        BusinFlowRequest businFlowRequest = businFlowRequestService.getById(idCardInfoForm.getRequest_no());
        if (null == businFlowRequest) {
            return Result.fail("参数传参有误");
        }
        if (!StringUtils.equals(businFlowRequest.getRequest_status(), FlowStatusConst.REQUEST_STATUS_AUDITING)) {
            return Result.fail("该任务状态不为审核中，无法进行idRetry，忽略本次请求");
        }
        return Result.success(auditDetailIdInfoService.idRetry(idCardInfoForm, businFlowRequest));
//        } finally {
//            redissonUtil.unlock(lockKey);
//        }
    }

    /**
     * 网厅业务办理-证件信息重新识别
     */
    @PostMapping("handIdRetry")
    public Result<List<AiAuditRuleResp>> handIdRetry(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody IdCardInfoForm idCardInfoForm) {
        BusinFlowRequest businFlowRequest = businFlowRequestService.getById(idCardInfoForm.getRequest_no());
        if (null == businFlowRequest) {
            return Result.fail("handIdRetry参数传参有误");
        }
        if (!StringUtils.equals(businFlowRequest.getRequest_status(), FlowStatusConst.REQUEST_STATUS_AUDITING)) {
            return Result.fail("该任务状态不为审核中，无法进行handIdRetry，忽略本次请求");
        }
        return Result.success(auditDetailIdInfoService.handIdRetry(idCardInfoForm));
    }

    /**
     * 获取证件信息
     */
    @PostMapping("getIdInfo")
    public Result<IdCardInfoResult> getIdInfo(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody IdCardInfoForm idCardInfoForm) {
        idCardInfoForm.setOperator_no(baseUser.getStaff_no());
        idCardInfoForm.setOperator_name(baseUser.getUser_name());

        IdCardInfoResult idCardInfoResult = auditDetailIdInfoService.getIdInfo(idCardInfoForm);
        return Result.success(idCardInfoResult);
    }

    /**
     * 网厅基础数据数据
     */
    @PostMapping("getHandInfo")
    public Result<HandInfoResp> getHandInfo(@Validated @RequestBody HandInfoForm handInfoForm) {
        return Result.success(auditDetailIdInfoService.getHandInfo(handInfoForm));
    }

    /**
     * 公民出入境证件信息核查
     */
    @PostMapping("validateCitizenDocument")
    public Result<String> validateCitizenDocument(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody ValidateCitizenDocumentForm validateCitizenDocumentForm) {
        validateCitizenDocumentForm.setOperator_no(baseUser.getStaff_no());
        validateCitizenDocumentForm.setOperator_name(baseUser.getUser_name());
        BusinFlowRequest businFlowRequest = businFlowRequestService.getById(validateCitizenDocumentForm.getRequest_no());
        if (null == businFlowRequest) {
            return Result.fail("参数传参有误");
        }
        if (!StringUtils.equalsAny(businFlowRequest.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL, WskhConstant.CROSS_BUSIN_TYPE) ||
                !StringUtils.equalsAny(businFlowRequest.getId_kind(), IdKindEnum.GA_PASS_CARD.getCode(), IdKindEnum.TAIWAN_PASS_CARD.getCode(),
                        IdKindEnum.FOREIGN_PREV_PERMIT.getCode(), IdKindEnum.GAT_RESIDENCE_PERMIT.getCode())) {
            return Result.fail("该业务类型或证件类型不支持公民出入境证件信息核查");
        }
        if (!StringUtils.equals(businFlowRequest.getRequest_status(), FlowStatusConst.REQUEST_STATUS_AUDITING)) {
            return Result.fail("该任务状态不为审核中，无法进行validateCitizenDocument");
        }
        auditDetailIdInfoService.validateCitizenDocument(validateCitizenDocumentForm, businFlowRequest);
        return Result.success("校验成功");
    }

}

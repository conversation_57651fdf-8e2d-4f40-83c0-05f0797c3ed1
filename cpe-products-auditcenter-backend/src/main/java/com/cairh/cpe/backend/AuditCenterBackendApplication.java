package com.cairh.cpe.backend;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@SpringBootApplication(scanBasePackages = {"com.cairh.cpe"})
@EnableCaching
@EnableDiscoveryClient
@MapperScan(basePackages = "com.cairh.cpe.**.mapper")
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
public class AuditCenterBackendApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuditCenterBackendApplication.class, args);
    }
}

package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.ProfileImageResp;
import com.cairh.cpe.backend.form.resp.ProfilePhotosResp;
import com.cairh.cpe.backend.form.support.VerifyPoliceInfo;
import com.cairh.cpe.backend.service.IAuditDetailProfilePhotoService;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.response.VerifyPoliceAllResult;
import com.cairh.cpe.common.entity.response.VerifyPoliceResp;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 审核详情 - 客户头像
 */
@Slf4j
@RestController
@RequestMapping("auditDetailProfilePhoto")
public class AuditDetailProfilePhotoController {

    @Autowired
    private IAuditDetailProfilePhotoService auditDetailProfilePhotoService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;

    @Resource
    private IRequestService requestService;

    @Autowired
    private RedissonUtil redissonUtil;

    /**
     * 获取客户的头像和公安照还有证通公安分数
     */
    @PostMapping("getProfileImageAndScore")
    public Result<ProfileImageResp> getProfileImageAndScore(@Validated @RequestBody BaseForm baseForm) {
        ProfileImageResp profileImageResp = auditDetailProfilePhotoService.getProfileImageAndScore(baseForm.getRequest_no());

        return Result.success(profileImageResp);
    }

    /**
     * 重新识别
     */
    @PostMapping("profileImageRetry")
    public Result<List<AiAuditRuleResp>> profileImageRetry(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody ProfileImageRetryForm profileImageRetryForm) {
//        String lockKey = String.format(LockKeyConstant.WSKH_CUST_PARAM_REQUEST_NO, profileImageRetryForm.getRequest_no());
//        boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
//        if (!isLock) {
//            return Result.fail(ErrorEnum.REDIS_TRY_LOCK_FAIL.getValue(), ErrorEnum.REDIS_TRY_LOCK_FAIL.getDesc());
//        }
//        try {
            profileImageRetryForm.setOperator_no(baseUser.getStaff_no());
            profileImageRetryForm.setOperator_name(baseUser.getUser_name());
            if (StringUtils.isNotBlank(profileImageRetryForm.getTask_id())) {
                BusinFlowTask task = businFlowTaskService.getById(profileImageRetryForm.getTask_id());
                if (null != task && !StringUtils.equals(task.getTask_status(), FlowStatusConst.AUDIT_AUDITING)) {
                    log.warn("该任务状态不为审核中，无法进行profileImageRetry，忽略本次请求");
                    return Result.success(new ArrayList<>());
                }
            }
            List<AiAuditRuleResp> aiAuditRuleResps = auditDetailProfilePhotoService.profileImageRetry(profileImageRetryForm);
            return Result.success(aiAuditRuleResps);
//        } finally {
//            redissonUtil.unlock(lockKey);
//        }
    }

    /**
     * 网厅业务办理-重新识别
     */
    @PostMapping("handProfileImageRetry")
    public Result<List<AiAuditRuleResp>> handProfileImageRetry(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody HandProfileImageRetryForm profileImageRetryForm) {
        profileImageRetryForm.setOperator_no(baseUser.getStaff_no());
        profileImageRetryForm.setOperator_name(baseUser.getUser_name());
        if (StringUtils.isNotBlank(profileImageRetryForm.getTask_id())) {
            BusinFlowTask task = businFlowTaskService.getById(profileImageRetryForm.getTask_id());
            if (null != task && !StringUtils.equals(task.getTask_status(), FlowStatusConst.AUDIT_AUDITING)) {
                log.warn("该任务状态不为审核中，无法进行handProfileImageRetry，忽略本次请求");
                return Result.success(new ArrayList<>());
            }
        }
        List<AiAuditRuleResp> aiAuditRuleResps = auditDetailProfilePhotoService.handProfileImageRetry(profileImageRetryForm);

        return Result.success(aiAuditRuleResps);
    }

    /**
     * 全要素验证并获取证通分
     */
    @PostMapping("handVerifyPoliceAll")
    public Result<VerifyPoliceAllResult> handVerifyPoliceAll(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody HandVerifyPoliceAllForm handVerifyPoliceAllForm) {
        if (StringUtils.isNotBlank(handVerifyPoliceAllForm.getTask_id())) {
            BusinFlowTask task = businFlowTaskService.getById(handVerifyPoliceAllForm.getTask_id());
            if (null != task && !StringUtils.equals(task.getTask_status(), FlowStatusConst.AUDIT_AUDITING)) {
                log.warn("该任务状态不为审核中，无法进行handVerifyPoliceAll，忽略本次请求");
                return Result.success();
            }
        }
        VerifyPoliceAllResult verifyPoliceAllResult = auditDetailProfilePhotoService.handVerifyPoliceAll(handVerifyPoliceAllForm);
        return Result.success(verifyPoliceAllResult);
    }

    /**
     * 查询历史客户头像
     */
    @PostMapping("queryHisProfileImage")
    public Result<List<ProfilePhotosResp>> queryHisProfileImage(@Validated @RequestBody BaseForm baseForm) {
        List<ProfilePhotosResp> profilePhotosResps = auditDetailProfilePhotoService.queryHisProfileImage(baseForm.getRequest_no());

        return Result.success(profilePhotosResps);
    }


    /**
     * 公安照刷新
     */
    @PostMapping("refreshPolicePhoto")
    public Result<VerifyPoliceResp> refreshPolicePhoto(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody VerifyPoliceForm verifyPoliceForm) {
        BusinFlowRequest businFlowRequest = requestService.getByRequestNo(verifyPoliceForm.getRequest_no());
        if (null == businFlowRequest) {
            return Result.fail("request_no参数有误，请重试");
        }
        VerifyPoliceInfo verifyPoliceInfo = new VerifyPoliceInfo();
        BeanUtils.copyProperties(businFlowRequest, verifyPoliceInfo);
        verifyPoliceInfo.setStaff_no(baseUser.getStaff_no());
        return Result.success(auditDetailProfilePhotoService.refreshPolicePhoto(verifyPoliceInfo));
    }
}

package com.cairh.cpe.backend.controller.auditdetail;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.api.aiaudit.resp.MaterialInfo;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.*;
import com.cairh.cpe.backend.service.IAuditDetailHisInfoService;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.common.entity.response.AuditOperateRecord;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.service.aiaudit.response.QueryAuditBusinRecordResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 历史订单
 */
@Slf4j
@RestController
@RequestMapping("auditDetailHisInfo")
public class AuditDetailHisInfoController {

    @Autowired
    private IAuditDetailHisInfoService auditDetailHisInfoService;

    @Autowired
    private CacheBackendUser backendUser;

    /**
     * 客户清单列表
     */
    @PostMapping("request/page")
    public Result<Page<HistoryTaskInfoResp>> requestPage(@AuthenticationPrincipal BaseUser baseUser, @RequestBody HistoryDataRequest request) {
        List<String> branchNos = backendUser.getEnBranchNo(baseUser.getStaff_no());
        if (CollectionUtils.isEmpty(branchNos)) {
            return Result.fail("没有相关营业全部");
        }
        return Result.success(auditDetailHisInfoService.requestPage(branchNos, request));
    }

    /**
     * 客户导出
     */
    @PostMapping("request/export")
    public Result<List<HistoryTaskInfoResp>> requestExport(@AuthenticationPrincipal BaseUser baseUser, @RequestBody HistoryDataRequest request) {
        List<String> branchNos = backendUser.getEnBranchNo(baseUser.getStaff_no());
        if (CollectionUtils.isEmpty(branchNos)) {
            return Result.fail("没有相关营业全部");
        }
        request.setIs_snapshot("0");
        return Result.success(auditDetailHisInfoService.export(branchNos, request, Boolean.TRUE));
    }

    /**
     * 任务清单列表
     *
     * @param request
     * @return
     */
    @PostMapping("task/page")
    public Result<Page<HistoryTaskInfoResp>> taskPage(@AuthenticationPrincipal BaseUser baseUser, @RequestBody HistoryDataRequest request) {
        List<String> branchNos = backendUser.getEnBranchNo(baseUser.getStaff_no());
        if (CollectionUtils.isEmpty(branchNos)) {
            return Result.fail("没有相关营业全部");
        }
        return Result.success(auditDetailHisInfoService.taskPage(branchNos, request));
    }

    /**
     * 任务导出
     */
    @PostMapping("task/export")
    public Result<List<HistoryTaskInfoResp>> taskExport(@AuthenticationPrincipal BaseUser baseUser, @RequestBody HistoryDataRequest request) {
        List<String> branchNos = backendUser.getEnBranchNo(baseUser.getStaff_no());
        if (CollectionUtils.isEmpty(branchNos)) {
            return Result.fail("没有相关营业全部");
        }
        request.setIs_snapshot("1");
        return Result.success(auditDetailHisInfoService.export(branchNos, request, Boolean.FALSE));
    }

    /**
     * 取用户来源
     */
    @PostMapping("getUserSource")
    public Result<AuditDetailUserBaseInfo> getUserSource(@Validated @RequestBody BaseInfoForm baseInfoForm) {
        return auditDetailHisInfoService.getUserSource(baseInfoForm);
    }

    /**
     * 查询历史驳回原因
     */
    @PostMapping("getHisRejectReason")
    public Result<List<BusinFlowRecordResult>> getHisRejectReason(@Validated @RequestBody BaseForm baseForm) {
        return Result.success(auditDetailHisInfoService.getHisRejectReason(baseForm.getRequest_no()));
    }

    /**
     * 获取最新一次驳回原因
     */
    @PostMapping("getLastRejectReason")
    public Result<RecentRejectReasonResp> getLastRejectReason(@Validated @RequestBody BaseInfoForm baseInfoForm) {
        RecentRejectReasonResp recentRejectReasonResp = auditDetailHisInfoService.getLastRejectReason(baseInfoForm);
        return Result.success(recentRejectReasonResp);
    }

    /**
     * 获取视频
     */
    @PostMapping("getVideoInfo")
    public Result<VideoInfoResp> getVideoInfo(@Validated @RequestBody BaseForm baseForm) {
        VideoInfoResp videoInfo = auditDetailHisInfoService.getVideoInfo(baseForm.getRequest_no());
        return Result.success(videoInfo);
    }

    /**
     * 取证件照正面
     */
    @PostMapping("getIdPhotoFront")
    public Result<String> getIdPhotoFront(@Validated @RequestBody GetIdPhotoFrontForm getIdPhotoFrontForm) {
        String idPhotoFront = auditDetailHisInfoService.getIdPhotoFront(getIdPhotoFrontForm.getRequest_no());
        return Result.success(idPhotoFront);
    }

    /**
     * 取证件照反面
     */
    @PostMapping("getIdPhotoBack")
    public Result<String> getIdPhotoBack(@Validated @RequestBody GetIdPhotoBackForm getIdPhotoBackForm) {
        String idPhotoBack = auditDetailHisInfoService.getIdPhotoBack(getIdPhotoBackForm.getRequest_no());
        return Result.success(idPhotoBack);
    }

    /**
     * 全部证件照片
     */
    @PostMapping("getAllIdPhoto")
    public Result<IdPhotoResp> getAllIdPhoto(@Validated @RequestBody GetIdPhotoBackForm getIdPhotoBackForm) {
        return Result.success(auditDetailHisInfoService.getAllIdPhoto(getIdPhotoBackForm.getRequest_no()));
    }

    /**
     * 获取客户的头像和公安照还有证通公安分数
     */
    @PostMapping("getProfileImageAndScore")
    public Result<ProfileImageResp> getProfileImageAndScore(@Validated @RequestBody BaseForm baseForm) {
        ProfileImageResp profileImageResp = auditDetailHisInfoService.getProfileImageAndScore(baseForm.getRequest_no());
        return Result.success(profileImageResp);
    }

    /**
     * 获取证件信息
     */
    @PostMapping("getIdInfo")
    public Result<IdCardInfoResult> getIdInfo(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody IdCardInfoForm idCardInfoForm) {
        idCardInfoForm.setOperator_no(baseUser.getStaff_no());
        idCardInfoForm.setOperator_name(baseUser.getUser_name());
        IdCardInfoResult idCardInfoResult = auditDetailHisInfoService.getIdInfo(idCardInfoForm);
        return Result.success(idCardInfoResult);
    }

    /**
     * 取反洗钱信息
     */
    @PostMapping("getMoneyAntiInfo")
    public Result<MoneyAntiInfo> getMoneyAntiInfo(@Validated @RequestBody GetMoneyAntiInfoForm getMoneyAntiInfoForm) {
        MoneyAntiInfo moneyAntiInfo = auditDetailHisInfoService.getMoneyAntiInfo(getMoneyAntiInfoForm.getRequest_no());
        return Result.success(moneyAntiInfo);
    }

    /**
     * 获取选择职业理由的范围
     */
    @PostMapping("getProfessionReason")
    public Result<MoneyAntiInfo> getProfessionReason(@Validated @RequestBody GetProfessionReasonForm getProfessionReasonForm) {
        MoneyAntiInfo moneyAntiInfo = auditDetailHisInfoService.getProfessionReason(getProfessionReasonForm);
        return Result.success(moneyAntiInfo);
    }

    /**
     * 取操作流水
     */
    @PostMapping("getOperatorRecord")
    public Result<List<AuditOperateRecord>> getOperatorRecord(@Validated @RequestBody GetOperatorRecordForm getOperatorRecordForm) {
        List<AuditOperateRecord> auditOperateRecords = auditDetailHisInfoService.getOperatorRecord(getOperatorRecordForm.getRequest_no());
        return Result.success(auditOperateRecords);
    }

    /**
     * 获取失信记录
     */
    @PostMapping("getDishonestRecord")
    public Result<DishonestResp> getDishonestRecord(@Validated @RequestBody BaseForm baseForm) {
        DishonestResp dishonestResp = auditDetailHisInfoService.getDishonestRecord(baseForm.getRequest_no());
        return Result.success(dishonestResp);
    }

    /**
     * 查询智能审核结果
     */
    @PostMapping("queryAuditBusinRecord")
    public Result<QueryAuditBusinRecordResp> queryAuditBusinRecord(@Validated @RequestBody AuditQueryRecordReq auditQueryRecordReq) {
        QueryAuditBusinRecordResp queryAuditBusinRecordResp = auditDetailHisInfoService.queryAuditBusinRecordV1(auditQueryRecordReq);
        return Result.success(queryAuditBusinRecordResp);
    }

    /**
     * 查询任务所在位置信息
     */
    @PostMapping("queryBusinFlowTaskPosition")
    public Result<BusinFlowTaskPositionResp> queryBusinFlowTaskPosition(@Validated @RequestBody BusinFlowTaskPositionReq businFlowTaskPositionReq) {
        BusinFlowTaskPositionResp businFlowTaskPositionResp = auditDetailHisInfoService.queryBusinFlowTaskPosition(businFlowTaskPositionReq.getFlowtask_id());
        return Result.success(businFlowTaskPositionResp);
    }

    /**
     * 审核报告素材
     */
    @PostMapping("getAuditReportMaterial")
    public Result<MaterialInfo> getAuditReportMaterial(@Validated @RequestBody BaseForm baseForm) {
        MaterialInfo materialInfo = auditDetailHisInfoService.getUserMaterialInfo(baseForm.getRequest_no());
        return Result.success(materialInfo);
    }
}

package com.cairh.cpe.backend.controller.query;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.common.entity.RuleConfiguration;
import com.cairh.cpe.common.service.IRuleConfigurationService;
import com.cairh.cpe.context.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * 规则关系
 *
 * <AUTHOR>
 * @since 2025/3/11 16:26
 */
@RequestMapping("ruleConfiguration")
@RestController
public class RuleConfigurationController {

    @Resource
    private IRuleConfigurationService ruleConfigurationService;

    /**
     * 规则列表
     */
    @PostMapping("list")
    public Result<List<RuleConfiguration>> list() {
        LambdaQueryWrapper<RuleConfiguration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByAsc(RuleConfiguration::getOrder_no);
        return Result.success(ruleConfigurationService.list(lambdaQueryWrapper));
    }

}

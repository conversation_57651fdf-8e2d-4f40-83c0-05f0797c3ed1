package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.form.req.BaseForm;
import com.cairh.cpe.backend.form.req.BaseInfoForm;
import com.cairh.cpe.backend.form.resp.BusinFlowRecordResult;
import com.cairh.cpe.backend.form.resp.RecentRejectReasonResp;
import com.cairh.cpe.backend.service.IAuditDetailRejectReasonService;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.response.BusinFlowRecordResp;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 审核详情 - 驳回原因
 */
@Slf4j
@RestController
@RequestMapping("auditDetailRejectReason")
public class AuditDetailRejectReasonController {

    @Autowired
    private IAuditDetailRejectReasonService auditDetailRejectReasonService;

    @Autowired
    private IBusinFlowRequestService businFlowRequestService;


    /**
     * 获取最新一次驳回原因
     */
    @PostMapping("getLastRejectReason")
    public Result<RecentRejectReasonResp> getLastRejectReason (@Validated @RequestBody BaseInfoForm baseInfoForm) {
        return Result.success(auditDetailRejectReasonService.getLastRejectReason(baseInfoForm));
    }

    /**
     * 查询历史驳回原因
     */
    @PostMapping("getHisRejectReason")
    public Result<List<BusinFlowRecordResult>> getHisRejectReason (@Validated @RequestBody BaseForm baseForm) {

        List<BusinFlowRecordResp> list = auditDetailRejectReasonService.getHisRejectReason(baseForm.getRequest_no());
        BusinFlowRequest byId = businFlowRequestService.getById(baseForm.getRequest_no());
        List<BusinFlowRecordResult> listResult = new ArrayList<>();
        for (BusinFlowRecordResp businFlowRecordResp : list) {
            BusinFlowRecordResult businFlowRecordResult = new BusinFlowRecordResult();
            businFlowRecordResp.setRequest_status(byId.getRequest_status());
            BaseBeanUtil.copyProperties(businFlowRecordResp,businFlowRecordResult);
            listResult.add(businFlowRecordResult);
        }
        return Result.success(listResult);
    }

}

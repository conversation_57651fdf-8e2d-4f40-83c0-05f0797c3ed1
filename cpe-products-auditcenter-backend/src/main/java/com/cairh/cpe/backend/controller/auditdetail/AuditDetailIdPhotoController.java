package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.form.req.BaseForm;
import com.cairh.cpe.backend.form.req.GetIdPhotoBackForm;
import com.cairh.cpe.backend.form.req.GetIdPhotoFrontForm;
import com.cairh.cpe.backend.form.req.IdCardImageRetryForm;
import com.cairh.cpe.backend.form.resp.IdPhotoResp;
import com.cairh.cpe.backend.form.resp.IdPhotosResp;
import com.cairh.cpe.backend.service.IAuditDetailIdPhotoService;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 审核详情 - 证件照
 */
@Slf4j
@RestController
@RequestMapping("auditDetailIdPhoto")
public class AuditDetailIdPhotoController {

    @Autowired
    private IAuditDetailIdPhotoService auditDetailIdPhotoService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;

    /**
     * 取证件照正面
     */
    @PostMapping("getIdPhotoFront")
    public Result<String> getIdPhotoFront(@Validated @RequestBody GetIdPhotoFrontForm getIdPhotoFrontForm) {
        String idPhotoFront = auditDetailIdPhotoService.getIdPhotoFront(getIdPhotoFrontForm.getRequest_no());

        return Result.success(idPhotoFront);
    }

    /**
     * 取证件照反面
     */
    @PostMapping("getIdPhotoBack")
    public Result<String> getIdPhotoBack(@Validated @RequestBody GetIdPhotoBackForm getIdPhotoBackForm) {
        String idPhotoBack = auditDetailIdPhotoService.getIdPhotoBack(getIdPhotoBackForm.getRequest_no());

        return Result.success(idPhotoBack);
    }

    /**
     * 全部证件照片
     */
    @PostMapping("getAllIdPhoto")
    public Result<IdPhotoResp> getAllIdPhoto(@Validated @RequestBody GetIdPhotoBackForm getIdPhotoBackForm) {
        return Result.success(auditDetailIdPhotoService.getAllIdPhoto(getIdPhotoBackForm.getRequest_no()));
    }

    /**
     * 查询历史证照
     */
    @PostMapping("queryHisImage")
    public Result<List<IdPhotosResp>> queryHisImage(@Validated @RequestBody BaseForm baseForm) {
        List<IdPhotosResp> list = auditDetailIdPhotoService.queryHisImage(baseForm.getRequest_no());

        return Result.success(list);
    }

    /**
     * 照片重试
     */
    @PostMapping("imageRetryAndImageSave")
    public Result<List<AiAuditRuleResp>> imageRetryAndImageSave(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody IdCardImageRetryForm idCardImageRetryForm) {
        idCardImageRetryForm.setOperator_no(baseUser.getStaff_no());
        idCardImageRetryForm.setOperator_name(baseUser.getUser_name());
        if (StringUtils.isNotBlank(idCardImageRetryForm.getTask_id())) {
            BusinFlowTask task = businFlowTaskService.getById(idCardImageRetryForm.getTask_id());
            if (null != task && !StringUtils.equals(task.getTask_status(), FlowStatusConst.AUDIT_AUDITING)) {
                throw new BizException(ErrorEnum.ALREADY_DEAL_TASK_TIP.getValue(), ErrorEnum.ALREADY_DEAL_TASK_TIP.getDesc());
            }
        }
        List<AiAuditRuleResp> aiAuditRuleResps = auditDetailIdPhotoService.imageRetryAndImageSave(idCardImageRetryForm);
        return Result.success(aiAuditRuleResps);
    }

    /**
     * 网厅业务办理-照片重试
     */
    @PostMapping("handImageRetryAndImageSave")
    public Result<List<AiAuditRuleResp>> handImageRetryAndImageSave(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody IdCardImageRetryForm idCardImageRetryForm) {
        idCardImageRetryForm.setOperator_no(baseUser.getStaff_no());
        idCardImageRetryForm.setOperator_name(baseUser.getUser_name());
        if (StringUtils.isNotBlank(idCardImageRetryForm.getTask_id())) {
            BusinFlowTask task = businFlowTaskService.getById(idCardImageRetryForm.getTask_id());
            if (null != task && !StringUtils.equals(task.getTask_status(), FlowStatusConst.AUDIT_AUDITING)) {
                throw new BizException(ErrorEnum.ALREADY_DEAL_TASK_TIP.getValue(), ErrorEnum.ALREADY_DEAL_TASK_TIP.getDesc());
            }
        }
        List<AiAuditRuleResp> aiAuditRuleResps = auditDetailIdPhotoService.handImageRetryAndImageSave(idCardImageRetryForm);
        return Result.success(aiAuditRuleResps);
    }
}

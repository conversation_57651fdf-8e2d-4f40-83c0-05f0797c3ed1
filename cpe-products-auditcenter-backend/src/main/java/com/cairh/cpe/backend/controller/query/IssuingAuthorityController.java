package com.cairh.cpe.backend.controller.query;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.RedisKeyConstant;
import com.cairh.cpe.common.entity.IssuingAuthority;
import com.cairh.cpe.common.service.IIssuingAuthorityService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.db.config.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Description：签发机关Controller
 * Author： slx
 * Date： 2024/9/2 16:19
 */
@Slf4j
@RestController
@RequestMapping("issuingAuthority")
public class IssuingAuthorityController {

    @Autowired
    private IIssuingAuthorityService issuingAuthorityService;
    @Resource
    private IdGenerator idGenerator;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 查询签发机关
     */
    @PostMapping("list")
    public Result<Page<IssuingAuthority>> list(@Validated @RequestBody IssuingAuthorityList issuingAuthorityList) {
        Page<IssuingAuthority> page = new Page<>(issuingAuthorityList.getCur_page(), issuingAuthorityList.getPage_size());
        LambdaQueryWrapper<IssuingAuthority> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(issuingAuthorityList.getProvince_no()), IssuingAuthority::getProvince_no, issuingAuthorityList.getProvince_no());
        wrapper.like(StringUtils.isNotBlank(issuingAuthorityList.getAuthority_name()), IssuingAuthority::getAuthority_name, issuingAuthorityList.getAuthority_name());
        wrapper.like(StringUtils.isNotBlank(issuingAuthorityList.getAddress_oragn()), IssuingAuthority::getAddress_oragn, issuingAuthorityList.getAddress_oragn());
        wrapper.eq(StringUtils.isNotBlank(issuingAuthorityList.getStatus()), IssuingAuthority::getStatus, issuingAuthorityList.getStatus());
        wrapper.orderByDesc(IssuingAuthority::getCreate_datetime);
        return Result.success(issuingAuthorityService.page(page, wrapper));
    }

    /**
     * 新增签发机关
     */
    @PostMapping("add")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> add(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody IssuingAuthorityAdd issuingAuthorityAdd) {
        if (checkIssuingAuthorityNameRepeat(issuingAuthorityAdd.getAuthority_name(), "")) {
            return Result.fail(ErrorEnum.ISSUING_AUTHORITY_SAVE_ERROR.getValue(), ErrorEnum.ISSUING_AUTHORITY_SAVE_ERROR.getDesc());
        }
        IssuingAuthority issuingAuthority = new IssuingAuthority();
        BeanUtils.copyProperties(issuingAuthorityAdd, issuingAuthority);
        issuingAuthority.setSerial_id(idGenerator.nextUUID(null));
        issuingAuthority.setCreate_by(baseUser.getStaff_no());
        issuingAuthority.setUpdate_by(baseUser.getStaff_no());
        issuingAuthority.setRemark(StringUtils.isNotBlank(issuingAuthority.getRemark()) ? issuingAuthority.getRemark() : StrUtil.SPACE);
        issuingAuthorityService.save(issuingAuthority);
        redisTemplate.delete(RedisKeyConstant.WSKH_ISSUING_AUTHORITY_ORGAN);
        return Result.success();
    }

    /**
     * 修改签发机关
     */
    @PostMapping("update")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> update(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody IssuingAuthorityUpdate issuingAuthorityUpdate) {
        if (checkIssuingAuthorityNameRepeat(issuingAuthorityUpdate.getAuthority_name(), issuingAuthorityUpdate.getSerial_id())) {
            return Result.fail(ErrorEnum.ISSUING_AUTHORITY_SAVE_ERROR.getValue(), ErrorEnum.ISSUING_AUTHORITY_SAVE_ERROR.getDesc());
        }
        IssuingAuthority issuingAuthority = new IssuingAuthority();
        BeanUtils.copyProperties(issuingAuthorityUpdate, issuingAuthority);
        issuingAuthority.setUpdate_by(baseUser.getStaff_no());
        issuingAuthority.setUpdate_datetime(new Date());
        issuingAuthority.setRemark(StringUtils.isNotBlank(issuingAuthority.getRemark()) ? issuingAuthority.getRemark() : StrUtil.SPACE);
        issuingAuthorityService.updateById(issuingAuthority);
        redisTemplate.delete(RedisKeyConstant.WSKH_ISSUING_AUTHORITY_ORGAN);
        return Result.success();
    }

    /**
     * 启用/禁用签发机关
     */
    @PostMapping("openAndClose")
    public Result<Boolean> openAndClose(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody IssuingAuthorityOpenAndClose issuingAuthorityOpenAndClose) {
        IssuingAuthority issuingAuthority = new IssuingAuthority();
        BeanUtils.copyProperties(issuingAuthorityOpenAndClose, issuingAuthority);
        issuingAuthority.setUpdate_by(baseUser.getStaff_no());
        issuingAuthority.setUpdate_datetime(new Date());
        issuingAuthorityService.updateById(issuingAuthority);
        redisTemplate.delete(RedisKeyConstant.WSKH_ISSUING_AUTHORITY_ORGAN);
        return Result.success();
    }

    /**
     * 删除签发机关-物理删除
     */
    @PostMapping("delete")
    public Result<Boolean> delete(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody IssuingAuthorityDelete issuingAuthorityDelete) {
        IssuingAuthority issuingAuthority = new IssuingAuthority();
        BeanUtils.copyProperties(issuingAuthorityDelete, issuingAuthority);
        issuingAuthorityService.removeById(issuingAuthority.getSerial_id());
        redisTemplate.delete(RedisKeyConstant.WSKH_ISSUING_AUTHORITY_ORGAN);
        return Result.success();
    }

    /**
     * 校验签发机关名称重复
     *
     * @param authority_name
     * @param serial_id
     */
    private boolean checkIssuingAuthorityNameRepeat(String authority_name, String serial_id) {
        LambdaQueryWrapper<IssuingAuthority> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(IssuingAuthority::getSerial_id);
        wrapper.eq(IssuingAuthority::getAuthority_name, authority_name);
        List<IssuingAuthority> list = issuingAuthorityService.list(wrapper);
        if (list.isEmpty()) {
            return false;
        }
        int selectCount = (int) list.stream().filter(issuingAuthority -> !issuingAuthority.getSerial_id().equals(serial_id)).count();
        return selectCount == 1;
    }
}

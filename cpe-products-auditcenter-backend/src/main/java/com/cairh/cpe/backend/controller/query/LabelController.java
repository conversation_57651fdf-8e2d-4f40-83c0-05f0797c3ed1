package com.cairh.cpe.backend.controller.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.GetLogInfoForm;
import com.cairh.cpe.backend.form.req.LabelMatchReq;
import com.cairh.cpe.backend.form.req.PageBaseRequest;
import com.cairh.cpe.common.constant.RedisKeyConstant;
import com.cairh.cpe.common.entity.Label;
import com.cairh.cpe.common.service.ILabelService;
import com.cairh.cpe.context.Result;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequestMapping("label")
public class LabelController {

    @Autowired
    private ILabelService labelService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 标签数据分页查询
     */
    @RequestMapping("queryLabelList")
    public Result<Page<Label>> queryLabelList(@RequestBody PageBaseRequest request) {
        Page<Label> page = new Page<>(request.getCur_page(), request.getPage_size());
        if (CollectionUtils.isNotEmpty(request.getOrder_item())) {
            page.setOrders(request.getOrder_item());
        }
        Page<Label> labelList = labelService.queryLabelList(page);
        return Result.success(labelList);
    }
    /**
     * 标签详情
     */
    @RequestMapping("queryLabelDetail")
    public Result<Label> queryLabelDetail(@RequestBody Label lable) {
        if (lable.getSerial_id() == null) {
            return Result.fail("标签id不能为空");
        }
        Label label = labelService.getById(lable.getSerial_id());
        return Result.success(label);
    }

    /**
     * 标签新增
     */
    @RequestMapping("addLabel")
    public Result<Boolean> addLabel(@RequestBody Label label) {
        if (label == null) {
            return Result.fail("标签数据不能为空");
        } else {
            if (label.getLabel_name() == null) {
                return Result.fail("标签名称不能为空");
            }
            if (label.getLabel_type() == null) {
                return Result.fail("标签类型不能为空");
            }
            if (label.getRegular_expression() == null) {
                return Result.fail("标签正则表达式不能为空");
            }
        }
        boolean save = labelService.addLabel(label);
        cleanCache();
        return Result.success(save);
    }
    /**
     * 标签修改
     */
    @RequestMapping("updateLabel")
    public Result<Boolean> updateLabel(@RequestBody Label label) {
        if (label.getSerial_id() == null) {
            return Result.fail("标签id不能为空");
        }
        boolean update = labelService.updateLabel(label);
        cleanCache();
        return Result.success(update);
    }
    /**
     * 标签删除
     */
    @RequestMapping("deleteLabel")
    public Result<Boolean> deleteLabel(@RequestBody Label label) {
        if (label.getSerial_id() == null) {
            return Result.fail("标签id不能为空");
        }
        boolean remove = labelService.removeById(label.getSerial_id());
        cleanCache();
        return Result.success(remove);
    }

    @RequestMapping("getMatchLabel")
    public Result<List<Label>> getMatchLabel(@RequestBody LabelMatchReq req) {
        List<Label> matchLabel = labelService.getMatchLabel(req.getRequest_no(), Maps.newHashMap());
        return Result.success(matchLabel);
    }


    @RequestMapping("getLogInfo")
    public Result<String> getLogInfo(@RequestBody GetLogInfoForm getLogInfoForm) {
        String logInfo="getLogInfo:content="+getLogInfoForm.getContent();
        log.info(logInfo);
        return Result.success(logInfo);
    }


    /**
     * 清空缓存
     */
    private void cleanCache() {
        //删除缓存
        CompletableFuture.runAsync(()->{
            log.info("label 缓存被清理");
            redisTemplate.delete(RedisKeyConstant.WSKH_AC_LABEL);
        });
    }
}

package com.cairh.cpe.backend.controller.query;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.UserQueryExtInfoForm;
import com.cairh.cpe.backend.form.resp.UserQueryExtInfoDetail;
import com.cairh.cpe.backend.form.resp.UserQueryExtInfoResult;
import com.cairh.cpe.backend.form.resp.UserQueryExtInfoResultExport;
import com.cairh.cpe.backend.mapper.UserQueryExtInfoBaseMapper;
import com.cairh.cpe.backend.service.IUserQueryExtInfoHandleService;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.common.constant.DicConstant;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.UserQueryExtInfo;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.entity.response.DictInfo;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.IUserQueryExtInfoService;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 用户开户查询
 */
@Slf4j
@RestController
@RequestMapping("userqueryexinfo")
public class UserQueryExtInfoController {

    @Autowired
    private IUserQueryExtInfoService userQueryExtInfoService;
    @Autowired
    private CacheBranch cacheBranch;
    @Autowired
    private CacheBackendUser backendUser;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private IUserQueryExtInfoHandleService userQueryExtInfoHandleService;
    @Resource
    private CacheDict cacheDict;
    @Resource
    private UserQueryExtInfoBaseMapper userQueryExtInfoBaseMapper;

    /**
     * 用户开户查询列表
     */
    @RequestMapping("queryUserInfoList")
    public Result<Page<UserQueryExtInfoResult>> queryUserInfoList(@AuthenticationPrincipal BaseUser baseUser, @RequestBody UserQueryExtInfoForm userQueryExtInfoForm) {
        // 1. 参数转换和校验
        String operatorBranchNo = baseUser.getBranch_no();
        List<String> operatorBranchList = new ArrayList<>();
        // 非营运中心营业部
        if (!StringUtils.equals(WskhConstant.SPECIAL_BRANCH, operatorBranchNo)) {
            operatorBranchList = backendUser.getEnBranchNo(baseUser.getStaff_no());
        }
        // 勾选营业部
        if (StringUtils.isNotBlank(userQueryExtInfoForm.getBranch_no())) {
            List<String> branchNoList = Arrays.asList(userQueryExtInfoForm.getBranch_no().split(","));
            // 营运中心营业部,拥有所有营业部权限
            if (StringUtils.equals(WskhConstant.SPECIAL_BRANCH, operatorBranchNo)) {
                userQueryExtInfoForm.setBranch_nos(branchNoList);
            } else {
                // 获取营业部交集
                List<String> newBranchNoList = operatorBranchList.stream()
                        .filter(branchNoList::contains)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(newBranchNoList)) {
                    log.info("用户[{}]无权限查看该查询条件[{}]", baseUser.getStaff_no(), userQueryExtInfoForm.getBranch_no());
                    return Result.success(new Page<>());
                }
                userQueryExtInfoForm.setBranch_nos(newBranchNoList);
            }
        } else {
            userQueryExtInfoForm.setBranch_nos(operatorBranchList);
        }
        // 业务状态
        if (StringUtils.isNotBlank(userQueryExtInfoForm.getRequest_status())) {
            String[] taskTypeArr = userQueryExtInfoForm.getRequest_status().split("-");
            if (taskTypeArr.length == 2) {
                // 复核通过or二次复核通过 需通过end_node作查询条件
                if (StringUtils.equalsAny(userQueryExtInfoForm.getRequest_status(), WskhConstant.REVIEW_3, WskhConstant.SECONDARY_REVIEW_3)) {
                    userQueryExtInfoForm.setEnd_node(taskTypeArr[0]);
                } else {
                    userQueryExtInfoForm.setAnode_id(taskTypeArr[0]);
                }
                userQueryExtInfoForm.setRequest_status(taskTypeArr[1]);
            }
        }
        // 2. 分页查询
        Page<UserQueryExtInfoDetail> queryPage = userQueryExtInfoBaseMapper.selectUserQueryExtInfoListByPage(
                new Page<>(userQueryExtInfoForm.getCur_page(), userQueryExtInfoForm.getPage_size()), userQueryExtInfoForm);
        if (CollectionUtils.isEmpty(queryPage.getRecords())) {
            return Result.success(new Page<>());
        }

        // 3. 获取流程任务ID映射
        List<String> requestNoList = queryPage.getRecords().stream().map(UserQueryExtInfoDetail::getRequest_no).collect(Collectors.toList());
        LambdaQueryWrapper<BusinFlowTask> businFlowTaskQueryWrapper = new LambdaQueryWrapper<>();
        businFlowTaskQueryWrapper.select(BusinFlowTask::getRequest_no, BusinFlowTask::getSerial_id, BusinFlowTask::getCreate_datetime);
        businFlowTaskQueryWrapper.in(BusinFlowTask::getRequest_no, requestNoList);
        // 获取最新一条数据
        Map<String, BusinFlowTask> flowtaskIdMap = businFlowTaskService.list(businFlowTaskQueryWrapper).stream()
                .collect(Collectors.toMap(
                        BusinFlowTask::getRequest_no,
                        task -> task,
                        (existing, replacement)
                                -> existing.getCreate_datetime().before(replacement.getCreate_datetime()) ? replacement : existing
                ));

        // 4. 转换结果
        // 数据准备
        Map<String, BranchInfo> branchInfoMap = cacheBranch.getAllBranchList().stream().collect(Collectors.toMap(BranchInfo::getBranch_no, x -> x));
        Map<String, DictInfo> dictInfoMap = new HashMap<>(128);
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.BUSIN_TYPE_DICT).stream().collect(Collectors.toMap(x -> DicConstant.BUSIN_TYPE_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.VIDEO_TYPE_DICT).stream().collect(Collectors.toMap(x -> DicConstant.VIDEO_TYPE_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.BUSIN_TYPE_DICT).stream().collect(Collectors.toMap(x -> DicConstant.BUSIN_TYPE_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.APP_ID_DICT).stream().collect(Collectors.toMap(x -> DicConstant.APP_ID_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.CLIENT_GENDER_DICT).stream().collect(Collectors.toMap(x -> DicConstant.CLIENT_GENDER_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.PROFESSION_CODE_DICT).stream().collect(Collectors.toMap(x -> DicConstant.PROFESSION_CODE_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.AUDIT_STATUS_DICT).stream().collect(Collectors.toMap(x -> DicConstant.AUDIT_STATUS_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.REVIEW_STATUS_DICT).stream().collect(Collectors.toMap(x -> DicConstant.REVIEW_STATUS_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.SECONDARY_REVIEW_STATUS_DICT).stream().collect(Collectors.toMap(x -> DicConstant.SECONDARY_REVIEW_STATUS_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.PROCESS_STATUS_DICT).stream().collect(Collectors.toMap(x -> DicConstant.PROCESS_STATUS_DICT + x.getSub_code(), x -> x)));
        branchInfoMap.putAll(cacheBranch.getAllBranchList().stream().collect(Collectors.toMap(BranchInfo::getBranch_no, x -> x)));

        List<UserQueryExtInfoResult> userQueryExtInfoResults = queryPage.getRecords().stream()
                .map(record -> {
                    UserQueryExtInfoResult result = new UserQueryExtInfoResult();
                    BaseBeanUtil.copyProperties(record, result);
                    result.setFlowtask_id(flowtaskIdMap.getOrDefault(record.getRequest_no(), new BusinFlowTask()).getSerial_id());
                    result.setBusin_type_code(record.getBusin_type());
                    result.setVideo_type_code(record.getVideo_type());
                    // 转换数据
                    convertData(result, dictInfoMap, branchInfoMap);
                    return result;
                })
                .collect(Collectors.toList());
        // 5. 返回结果
        Page<UserQueryExtInfoResult> pageResult = new Page<>();
        pageResult.setTotal(queryPage.getTotal());
        pageResult.setRecords(userQueryExtInfoResults);
        return Result.success(pageResult);

    }

    @GetMapping("find/{request_no}")
    public Result<UserQueryExtInfo> findByRequestNo(@PathVariable("request_no") String request_no) {
        return Result.success(userQueryExtInfoService.findExtInfo(request_no));
    }

    @PostMapping("exportUserInfoList")
    public Result<List<UserQueryExtInfoResultExport>> exportUserInfoList(@AuthenticationPrincipal BaseUser baseUser, @RequestBody UserQueryExtInfoForm userQueryExtInfoForm) {
        List<UserQueryExtInfoResultExport> list = userQueryExtInfoHandleService.exportUserInfoList(baseUser, userQueryExtInfoForm);
        return Result.success(list);
    }

    private void convertData(UserQueryExtInfoResult userQueryExtInfoResult, Map<String, DictInfo> dictInfoMap, Map<String, BranchInfo> branchInfoMap) {
        // 分公司名称 company_name
        String up_branch_no = branchInfoMap.getOrDefault(userQueryExtInfoResult.getBranch_no(), new BranchInfo()).getUp_branch_no();
        String companyName = StringUtils.isEmpty(up_branch_no) ? userQueryExtInfoResult.getBranch_no() : branchInfoMap.getOrDefault(up_branch_no, new BranchInfo()).getBranch_name();
        userQueryExtInfoResult.setCompany_name(companyName);
        // 业务类型 code_dict = "busin_type"
        userQueryExtInfoResult.setBusin_type(dictInfoMap.getOrDefault(DicConstant.BUSIN_TYPE_DICT + userQueryExtInfoResult.getBusin_type(), new DictInfo()).getSub_name());
        // 分支机构 code_type = "branch"
        userQueryExtInfoResult.setBranch_no(branchInfoMap.getOrDefault(userQueryExtInfoResult.getBranch_no(), new BranchInfo()).getBranch_name());
        // 证件类型 code_dict = "id_kind"
        userQueryExtInfoResult.setId_kind(dictInfoMap.getOrDefault(DicConstant.ID_KIND_DICT + userQueryExtInfoResult.getId_kind(), new DictInfo()).getSub_name());
        // 性别 code_dict = "client_gender"
        userQueryExtInfoResult.setClient_gender(dictInfoMap.getOrDefault(DicConstant.CLIENT_GENDER_DICT + userQueryExtInfoResult.getClient_gender(), new DictInfo()).getSub_name());
        // 职业代码 code_dict = "profession_code"
        userQueryExtInfoResult.setProfession_code(dictInfoMap.getOrDefault(DicConstant.PROFESSION_CODE_DICT + userQueryExtInfoResult.getProfession_code(), new DictInfo()).getSub_name());
        // 视频见证方式 code_dict = "video_type"
        userQueryExtInfoResult.setVideo_type(dictInfoMap.getOrDefault(DicConstant.VIDEO_TYPE_DICT + userQueryExtInfoResult.getVideo_type(), new DictInfo()).getSub_name());
        // 任务状态 code_dict = "task_status" 字段task_type转换
        userQueryExtInfoResult.setRequest_status_name(getRequest_status_name(userQueryExtInfoResult.getRequest_status_name(), dictInfoMap));
        // 接入方式 code_dict = "app_id"
        userQueryExtInfoResult.setApp_id(dictInfoMap.getOrDefault("app_id" + userQueryExtInfoResult.getApp_id(), new DictInfo()).getSub_name());
    }

    private String getRequest_status_name(String request_status_name, Map<String, DictInfo> dictInfoMap) {
        String[] subArr = request_status_name.split("-");
        if (subArr.length >= 2) {
            switch (subArr[0]) {
                case FlowNodeConst.AUDIT:
                    return dictInfoMap.getOrDefault(DicConstant.AUDIT_STATUS_DICT + subArr[1], new DictInfo()).getSub_name();
                case FlowNodeConst.REVIEW:
                    return dictInfoMap.getOrDefault(DicConstant.REVIEW_STATUS_DICT + subArr[1], new DictInfo()).getSub_name();
                case FlowNodeConst.SECONDARY_REVIEW:
                    return dictInfoMap.getOrDefault(DicConstant.SECONDARY_REVIEW_STATUS_DICT + subArr[1], new DictInfo()).getSub_name();
                default:
                    return request_status_name;
            }
        }
        return dictInfoMap.getOrDefault(DicConstant.PROCESS_STATUS_DICT + request_status_name, new DictInfo()).getSub_name();
    }

}

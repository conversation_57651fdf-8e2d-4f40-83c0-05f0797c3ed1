package com.cairh.cpe.backend.controller.auditdetail;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cairh.cpe.backend.form.req.UserQueryExtInfoEditReq;
import com.cairh.cpe.common.entity.UserQueryExtInfo;
import com.cairh.cpe.common.service.IUserQueryExtInfoService;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("auditDetailUserQueryExt")
public class AuditDetailUserQueryExtController {

    @Resource
    private IUserQueryExtInfoService userQueryExtInfoService;



    /**
     * 编辑用户查询扩展信息
     */
    @PostMapping("edit/{requestNo}")
    public Result<Boolean> updateUserQueryExtInfo(@PathVariable("requestNo") String requestNo,
                                                  @RequestBody UserQueryExtInfoEditReq req) {
        Assert.notNull(requestNo, "requestNo not find");
        UserQueryExtInfo userQueryExtInfo =  userQueryExtInfoService.getById(requestNo);
        Assert.notNull(userQueryExtInfo, "userQueryExtInfo not find by request_no");
        LambdaUpdateWrapper<UserQueryExtInfo> wrapper = new LambdaUpdateWrapper();
        wrapper.set(null != req.getAnode_id() ,UserQueryExtInfo::getAnode_id,req.getAnode_id());
        wrapper.set(null != req.getRequest_status() ,UserQueryExtInfo::getRequest_status,req.getRequest_status());
        wrapper.set(null != req.getAudit_operator_no() ,UserQueryExtInfo::getAudit_operator_no,req.getAudit_operator_no());
        wrapper.set(null != req.getAudit_operator_name() ,UserQueryExtInfo::getAudit_operator_name,req.getAudit_operator_name());
        wrapper.set(null != req.getDouble_operator_no() ,UserQueryExtInfo::getDouble_operator_no,req.getDouble_operator_no());
        wrapper.set(null != req.getDouble_operator_name() ,UserQueryExtInfo::getDouble_operator_name,req.getDouble_operator_name());
        wrapper.set(null != req.getReview_operator_no() ,UserQueryExtInfo::getReview_operator_no,req.getReview_operator_no());
        wrapper.set(null != req.getReview_operator_name() ,UserQueryExtInfo::getReview_operator_name,req.getReview_operator_name());
        wrapper.eq(UserQueryExtInfo::getRequest_no,requestNo);
        return Result.success(userQueryExtInfoService.update(wrapper));

    }


}

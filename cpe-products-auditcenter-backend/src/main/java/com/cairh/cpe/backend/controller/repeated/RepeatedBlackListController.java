package com.cairh.cpe.backend.controller.repeated;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.BlackListAdd;
import com.cairh.cpe.backend.form.req.BlackListEdit;
import com.cairh.cpe.backend.form.req.BlackListReq;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.constant.RedisKeyConstant;
import com.cairh.cpe.common.entity.RepeatedBlackList;
import com.cairh.cpe.common.service.IRepeatedBlacklistService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;

/**
 * <AUTHOR>
 */

@Slf4j
@RestController
@RequestMapping("blacklist")
public class RepeatedBlackListController {


    @Resource
    private IRepeatedBlacklistService repeatedBlacklistService;


    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @RequestMapping("page")
    public Result<Page<RepeatedBlackList>> queryLabelList(@RequestBody BlackListReq request) {
        Page<RepeatedBlackList> page = new Page<>(request.getCur_page(), request.getPage_size());
        LambdaQueryWrapper<RepeatedBlackList> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(request.getKeyword()),RepeatedBlackList::getContent,request.getKeyword());
        wrapper.eq(StringUtils.isNotBlank(request.getBlack_status()),RepeatedBlackList::getBlack_status,request.getBlack_status());
        wrapper.orderByDesc(RepeatedBlackList::getCreate_datetime);
        return Result.success(repeatedBlacklistService.page(page,wrapper));
    }


    @PostMapping("save")
    public Result<Boolean> save(@AuthenticationPrincipal BaseUser baseUser,@Valid @RequestBody BlackListAdd blackListAdd) {
        Date date = new Date();
        RepeatedBlackList blackList = new RepeatedBlackList();
        blackList.setBlack_status(Constant.STATUS_NORMAL);
        blackList.setContent(blackListAdd.getContent());
        blackList.setCreate_by(baseUser.getUser_name());
        blackList.setCreate_datetime(date);
        blackList.setModify_by(baseUser.getUser_name());
        blackList.setModify_datetime(date);
        repeatedBlacklistService.save(blackList);
        String redisKey = RedisKeyConstant.WSKH_REPEATED_BLACKLIST;
        redisTemplate.delete(redisKey);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("edit")
    public Result<Boolean> edit(@AuthenticationPrincipal BaseUser baseUser,@Valid @RequestBody BlackListEdit blackListEdit) {
        LambdaUpdateWrapper<RepeatedBlackList> wrapper = new LambdaUpdateWrapper();
        wrapper.set(RepeatedBlackList::getModify_by, baseUser.getUser_name());
        wrapper.set(RepeatedBlackList::getContent, blackListEdit.getContent());
        wrapper.set(RepeatedBlackList::getModify_datetime, new Date());
        wrapper.eq(RepeatedBlackList::getSerial_id, blackListEdit.getSerial_id());
        repeatedBlacklistService.update(wrapper);
        String redisKey = RedisKeyConstant.WSKH_REPEATED_BLACKLIST;
        redisTemplate.delete(redisKey);
        return Result.success(Boolean.TRUE);
    }


    @PostMapping("delete/{serial_id}")
    public Result<Boolean> delete(@PathVariable("serial_id") String serial_id) {
        repeatedBlacklistService.removeById(serial_id);
        String redisKey = RedisKeyConstant.WSKH_REPEATED_BLACKLIST;
        redisTemplate.delete(redisKey);
        return Result.success(Boolean.TRUE);
    }


}

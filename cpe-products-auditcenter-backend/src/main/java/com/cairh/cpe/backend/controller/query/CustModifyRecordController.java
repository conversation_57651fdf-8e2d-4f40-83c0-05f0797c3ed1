package com.cairh.cpe.backend.controller.query;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.ModifyRecordForm;
import com.cairh.cpe.businflow.entity.request.ModifyRecordReq;
import com.cairh.cpe.businflow.entity.response.ModifyRecordResult;
import com.cairh.cpe.businflow.service.ICustModifyRecordService;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 客户资料修改
 */
@Slf4j
@RestController
@RequestMapping("custModifyRecord")
public class CustModifyRecordController {

    @Autowired
    private ICustModifyRecordService custModifyRecordService;

    @Autowired
    private CacheBranch cacheBranch;

    @Autowired
    private CacheBackendUser backendUser;

    /**
     * 客户资料修改记录查询列表
     */
    @RequestMapping("queryModifyRecord")
    public Result<Page<ModifyRecordResult>> queryModifyRecord(@AuthenticationPrincipal BaseUser baseUser,@RequestBody ModifyRecordForm modifyRecordForm) {
        ModifyRecordReq modifyRecordReq = new ModifyRecordReq();
        BeanUtil.copyProperties(modifyRecordForm,modifyRecordReq);
        //用户可操作营业部
        modifyRecordReq.setOperator_branch(backendUser.getEnBranchNo(baseUser.getStaff_no()));
        Page<ModifyRecordResult> page = new Page<>(modifyRecordReq.getCur_page(), modifyRecordReq.getPage_size());
        Page<ModifyRecordResult> resultPage = custModifyRecordService.selectModifyRecordListByPage(page, modifyRecordReq);
        Map<String, BranchInfo> branchInfoMap = cacheBranch.getAllBranchList().stream().collect(Collectors.toMap(BranchInfo::getBranch_no, x -> x));
        List<ModifyRecordResult> resultList = resultPage.getRecords().stream().map(item ->{
            item.setCompany_name(branchInfoMap.getOrDefault(branchInfoMap.getOrDefault(item.getBranch_no(), new BranchInfo()).getUp_branch_no(), new BranchInfo())
                    .getBranch_name());
            item.setBranch_name(branchInfoMap.getOrDefault(item.getBranch_no(),new BranchInfo()).getBranch_name());
            item.setBusin_type_code(item.getBusin_type());
            return item;
        }).collect(Collectors.toList());
        resultPage.setRecords(resultList);
        return Result.success(resultPage);
    }


}

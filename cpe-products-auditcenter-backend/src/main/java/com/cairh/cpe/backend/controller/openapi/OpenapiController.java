package com.cairh.cpe.backend.controller.openapi;

import com.cairh.cpe.backend.constant.BackendApiUrlConstant;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.OpenApiResp;
import com.cairh.cpe.backend.form.resp.QueryOperatorInfoResponse;
import com.cairh.cpe.backend.form.resp.QueryZegoTokenResp;
import com.cairh.cpe.backend.form.resp.UserAcBaseInfo;
import com.cairh.cpe.backend.form.support.IdCardInfo;
import com.cairh.cpe.backend.form.support.UserBaseInfo;
import com.cairh.cpe.backend.service.IAuditDetailBidirectionalVideoService;
import com.cairh.cpe.backend.service.IOpenapiService;
import com.cairh.cpe.businflow.entity.CustModifyRecord;
import com.cairh.cpe.businflow.service.ICustModifyRecordService;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.request.AuditChangeForm;
import com.cairh.cpe.common.entity.response.AuditResultResp;
import com.cairh.cpe.common.entity.response.SubmitAuditResp;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.util.BeanMapUtil;
import com.cairh.cpe.common.util.Md5Utils;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.video.dto.resp.VideoQryQueueInfoResponse;
import com.cairh.cpe.service.auditcenter.service.IBidirectionalVideoHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 国泰对接
 */
@Slf4j
@RestController
@RequestMapping("authless/openapi")
public class OpenapiController {

    @Autowired
    IOpenapiService openapiService;
    @Autowired
    ICustModifyRecordService custModifyRecordService;
    @Autowired
    private ConverterApplyFactory converterApplyFactory;
    @Autowired
    private IAuditDetailBidirectionalVideoService auditDetailBidirectionalVideoService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private IBidirectionalVideoHandlerService bidirectionalVideoHandlerService;

    @Resource
    private RedissonUtil redissonUtil;

    /**
     * 国泰提交审核信息
     */
    @PostMapping(BackendApiUrlConstant.OPENAI_SUBMIT_AUDIT)
    public Result<OpenApiResp> submitAudit(@Validated @RequestBody UserBaseInfoForm userBaserInfoForm) {
        String openMd5 = PropertySource.get(PropKeyConstant.WSKH_ISOPEN_MD5_AUTHENTICATION, "0");
        if (openMd5.equals("1")) {
            String md5 = userBaserInfoForm.getMd5();
            if (StringUtils.isBlank(md5)) {
                log.error("鉴权秘钥不能为空,busi_serial_no={}", userBaserInfoForm.getUser_base_info().getBusi_serial_no());
                return Result.fail("鉴权秘钥不能为空");
            }
            IdCardInfo id_card_info = userBaserInfoForm.getId_card_info();
            UserBaseInfo user_base_info = userBaserInfoForm.getUser_base_info();
            String local_md5 = id_card_info.getId_kind() + id_card_info.getId_no() + user_base_info.getBranch_no() + user_base_info.getBusin_type();
            local_md5 = Md5Utils.encryptParams(local_md5);
            if (!md5.equals(local_md5)) {
                log.error("鉴权秘钥不正确,busi_serial_no={}", userBaserInfoForm.getUser_base_info().getBusi_serial_no());
                return Result.fail("鉴权秘钥不正确");
            }
        }
        UserAcBaseInfo convert = converterApplyFactory.convert(userBaserInfoForm, new UserAcBaseInfo());
        String lockKey = LockKeyConstant.WSKH_AC_SUBMIT_CONTROL + convert.getBusi_serial_no();

        boolean isLock = redissonUtil.tryLock(lockKey, 0, 40, TimeUnit.SECONDS);
        if (!isLock) {
            return Result.fail(ErrorEnum.REDIS_TRY_LOCK_FAIL.getValue(), ErrorEnum.REDIS_TRY_LOCK_FAIL.getDesc());
        }
        try {
            Map<String, Object> params = new HashMap<>(BeanMapUtil.beanToMap(convert));
            // 转换成params的形式
            // 清空大字段部分数据
            params.put("not_auditor", "");
            params.put(WskhFields.FINISH_NODE, "");
            SubmitAuditResp submitAuditResp = openapiService.openapiSubmitAudit(params);
            //将开户证件照片和客户头像直接保存到客户修改记录表开始
            AuditChangeForm auditChangeForm = new AuditChangeForm();
            auditChangeForm.setModify_link(FlowNodeConst.AUDIT);
            auditChangeForm.setRequest_no(submitAuditResp.getRequest_no());
            auditChangeForm.setBusin_type(convert.getBusin_type());
            auditChangeForm.setFlow_task_id(submitAuditResp.getFlowtask_id());
            List<CustModifyRecord> custModifyRecords = new ArrayList<>();
            if (StringUtils.isNotBlank(convert.getFile_6A())) {
                CustModifyRecord file_6A = custModifyRecordService.createRecord("file_6A", convert.getFile_6A(), convert.getFile_6A(), auditChangeForm);
                custModifyRecords.add(file_6A);
            }
            if (StringUtils.isNotBlank(convert.getFile_6B())) {
                CustModifyRecord file_6B = custModifyRecordService.createRecord("file_6B", convert.getFile_6B(), convert.getFile_6B(), auditChangeForm);
                custModifyRecords.add(file_6B);
            }
            if (StringUtils.isNotBlank(convert.getFile_7C())) {
                CustModifyRecord file_7C = custModifyRecordService.createRecord("file_7C", convert.getFile_7C(), convert.getFile_7C(), auditChangeForm);
                custModifyRecords.add(file_7C);
            }
            if (StringUtils.isNotBlank(convert.getFile_7D())) {
                CustModifyRecord file_7D = custModifyRecordService.createRecord("file_7D", convert.getFile_7D(), convert.getFile_7D(), auditChangeForm);
                custModifyRecords.add(file_7D);
            }
            if (StringUtils.isNotBlank(convert.getFile_80())) {
                CustModifyRecord file_80 = custModifyRecordService.createRecord("file_80", convert.getFile_80(), convert.getFile_80(), auditChangeForm);
                custModifyRecords.add(file_80);
            }
            if (StringUtils.isNotBlank(convert.getFile_Bm())) {
                CustModifyRecord file_Bm = custModifyRecordService.createRecord("file_Bm", convert.getFile_Bm(), convert.getFile_Bm(), auditChangeForm);
                custModifyRecords.add(file_Bm);
            }
            if (StringUtils.isNotBlank(convert.getFile_7W())) {
                CustModifyRecord file_7W = custModifyRecordService.createRecord("file_7W", convert.getFile_7W(), convert.getFile_7W(), auditChangeForm);
                custModifyRecords.add(file_7W);
            }
            if (StringUtils.isNotBlank(convert.getFile_7X())) {
                CustModifyRecord file_7X = custModifyRecordService.createRecord("file_7X", convert.getFile_7X(), convert.getFile_7X(), auditChangeForm);
                custModifyRecords.add(file_7X);
            }
            custModifyRecordService.saveBatch(custModifyRecords);
            //将开户证件照片和客户头像直接保存到客户修改记录表结束
            OpenApiResp openApiResp = new OpenApiResp();
            openApiResp.setRequest_no(submitAuditResp.getRequest_no());
            openApiResp.setFlowtask_id(submitAuditResp.getFlowtask_id());
            openApiResp.setTask_id(submitAuditResp.getTask_id());
            return Result.success(openApiResp);
        } catch (Exception e) {
            log.error("开户提交异常,异常数据={}", convert.getBusi_serial_no(), e);
            return Result.fail(e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    /**
     * 国泰获取审核结果
     */
    @PostMapping(BackendApiUrlConstant.OPENAI_GETAUDIT_RESULT)
    public Result<AuditResultResp> getAuditResult(@Validated @RequestBody GetAuditResultForm baseForm) {
        String openMd5 = PropertySource.get(PropKeyConstant.WSKH_ISOPEN_MD5_AUTHENTICATION, "0");
        if (openMd5.equals("1")) {//开启则校验
            String md5 = baseForm.getMd5();
            if (StringUtils.isBlank(md5)) {//md5 =MD5（证件类别+证件编号+营业部编码+业务类别）转大写
                return Result.fail("鉴权秘钥不能为空");
            }
            String local_md5 = Md5Utils.encryptParams(baseForm.getTask_id());
            if (!md5.equals(local_md5)) {
                return Result.fail("鉴权秘钥不正确");
            }
        }
        log.info("查询审核结果开始 taskId={}", baseForm.getTask_id());
        AuditResultResp auditResultResp = openapiService.openapiGetAuditResult(baseForm);
        log.info("查询审核结果结束 taskId={}", baseForm.getTask_id());
        return Result.success(auditResultResp);
    }

    /**
     * 国泰提交双向视频复核信息
     */
    @PostMapping(BackendApiUrlConstant.OPENAI_SUBMIT_BIDIRECTIONAL_AUDIT)
    public Result submitAuditBidirectional(@Validated @RequestBody UserBidirectionalInfoForm userBidirectionalInfoForm) {
        Result result = md5VideoAuthentication(userBidirectionalInfoForm.getMd5(), userBidirectionalInfoForm.getTask_id());
        if (!result.isSuccess()) {
            return Result.fail(result.getError().getError_info());
        }
        // 创建复核任务
        return openapiService.openapiSubmitBidirectionalAudit(userBidirectionalInfoForm.getTask_id());
    }

    /**
     * 双向视频入队列
     */
    @PostMapping(BackendApiUrlConstant.OPENAI_BIDIRECTIONAL_JOIN_QUEUE)
    public Result<String> joinQueue(@Validated @RequestBody JoinVideoRequest request) {
        Result result = md5VideoAuthentication(request.getMd5(), request.getTask_id());
        if (!result.isSuccess()) {
            return Result.fail(result.getError().getError_info());
        }
        auditDetailBidirectionalVideoService.videoJoinQueue(request);
        return Result.success();
    }

    /**
     * 双向视频队列状态查询
     */
    @PostMapping(value = BackendApiUrlConstant.OPENAI_BIDIRECTIONAL_QUERY_QUEUE)
    public Result<VideoQryQueueInfoResponse> queryQueue(@Validated @RequestBody QueryVideoQueueRequest request) {
        Result result = md5VideoAuthentication(request.getMd5(), request.getTask_id());
        if (!result.isSuccess()) {
            return Result.fail(result.getError().getError_info());
        }
        return Result.success(auditDetailBidirectionalVideoService.queryVideoQueue(request));
    }

    /**
     * 退出双向视频队列状态
     */
    @PostMapping(value = BackendApiUrlConstant.OPENAI_BIDIRECTIONAL_EXIT_QUEUE)
    public Result exitQueue(@Validated @RequestBody ExitVideoQueueRequest request) {
        Result result = md5VideoAuthentication(request.getMd5(), request.getTask_id());
        if (!result.isSuccess()) {
            return Result.fail(result.getError().getError_info());
        }
        BusinFlowTask businFlowTask =
                businFlowTaskService.getCurrTaskTypeByTaskId(request.getTask_id(), TaskTypeEnum.AUDIT.getCode());
        if (businFlowTask != null) {
            if (StringUtils.equals(FlowStatusConst.AUDIT_INVALIDATE, businFlowTask.getTask_status())) {
                return Result.success();
            }
            auditDetailBidirectionalVideoService.exitVideoQueue(request);
            log.info("[任务作废标记]exitQueue双向视频退出双向视频队列 request_no = {} task_id = {}", businFlowTask.getRequest_no(), businFlowTask.getTask_id());
            bidirectionalVideoHandlerService.clearBidirectionalVideoTask(businFlowTask, request.getReason_msg());
        }
        return Result.success();
    }


    /**
     * 查询操作员信息   这个应该是不用的接口
     */
    @PostMapping(value = BackendApiUrlConstant.OPENAI_BIDIRECTIONAL_OPERATOR_INFO)
    public Result<QueryOperatorInfoResponse> queryQueue(@Validated @RequestBody QueryOperatorInfoRequest request) {
        return Result.success(auditDetailBidirectionalVideoService.queryOperatorInfo(request));
    }

    /**
     * zego token刷新
     */
    @PostMapping(value = BackendApiUrlConstant.OPENAI_BIDIRECTIONAL_ZEGO_TOKEN)
    public Result<QueryZegoTokenResp> refreshUserZegoToken(@Validated @RequestBody QueryVideoQueueRequest request) {
        Result result = md5VideoAuthentication(request.getMd5(), request.getTask_id());
        if (!result.isSuccess()) {
            return Result.fail(result.getError().getError_info());
        }
        QueryZegoTokenResp queryZegoTokenResp = auditDetailBidirectionalVideoService.refreshUserZegoToken(request);
        return Result.success(queryZegoTokenResp);
    }


    private Result md5VideoAuthentication(String md5, String taskId) {
        String openMd5 = PropertySource.get(PropKeyConstant.WSKH_ISOPEN_MD5_AUTHENTICATION, "0");
        //开启则校验
        if ("1".equals(openMd5)) {
            //md5 =MD5（taskId）转大写
            if (StringUtils.isBlank(md5)) {
                return Result.fail("鉴权秘钥不能为空");
            }
            String localMd5 = Md5Utils.encryptParams(taskId);
            if (!md5.equals(localMd5)) {
                return Result.fail("鉴权秘钥不正确");
            }
        }
        return Result.success();
    }
}

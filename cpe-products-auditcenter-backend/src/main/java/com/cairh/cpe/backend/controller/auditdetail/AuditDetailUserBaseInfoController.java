package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.form.req.BaseInfoForm;
import com.cairh.cpe.backend.form.resp.AuditDetailUserBaseInfo;
import com.cairh.cpe.backend.service.IAuditDetailUserBaseInfoService;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 审核详情 - 用户基本信息
 */
@Slf4j
@RestController
@RequestMapping("auditDetailUserBaseInfo")
public class AuditDetailUserBaseInfoController {

    @Autowired
    private IAuditDetailUserBaseInfoService auditDetailUserBaseInfoService;


    /**
     * 取用户来源
     */
    @PostMapping("getUserSource")
    public Result<AuditDetailUserBaseInfo> getUserSource(@Validated @RequestBody BaseInfoForm baseInfoForm) {
        Result<AuditDetailUserBaseInfo> auditDetailUserBaseInfo = auditDetailUserBaseInfoService.getUserSource(baseInfoForm);

        return auditDetailUserBaseInfo;
    }
}

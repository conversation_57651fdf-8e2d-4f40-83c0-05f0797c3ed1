package com.cairh.cpe.backend.controller.query;

import com.cairh.cpe.backend.form.resp.HistoryApplyResp;
import com.cairh.cpe.backend.service.IAuditDetailRequestService;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.context.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("businFlowRequest")
public class BusinFlowRequestController {

    @Resource
    private IAuditDetailRequestService auditDetailRequestService;

    @GetMapping("apply/his/{idNo}")
    public Result<List<HistoryApplyResp>> applyHis(@PathVariable("idNo") String idNo) {
        return Result.success(auditDetailRequestService.applyHis(idNo));
    }


    @GetMapping("find/{request_no}")
    public Result<BusinFlowRequest> findByRequestNo(@PathVariable("request_no") String request_no) {
        return Result.success(auditDetailRequestService.findByRequestNo(request_no));
    }


}

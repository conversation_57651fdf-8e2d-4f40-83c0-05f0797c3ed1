package com.cairh.cpe.backend.controller.query;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.TaskDetailRequest;
import com.cairh.cpe.backend.form.req.TaskStagingAndActivationForm;
import com.cairh.cpe.backend.form.resp.StagingTaskCount;
import com.cairh.cpe.backend.form.resp.TaskCountNum;
import com.cairh.cpe.backend.form.resp.TaskDetailInfo;
import com.cairh.cpe.backend.form.resp.TaskDetailInfoExport;
import com.cairh.cpe.backend.service.IWorkBusinFlowTaskService;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.esb.component.elect.IEsbComponentElectDubboService;
import com.cairh.cpe.esb.component.elect.dto.req.ElectDownloadFileRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectDownloadFileResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 任务中心查询
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("businFlowTask")
public class BusinFlowTaskCenterController {

    @Resource
    private IWorkBusinFlowTaskService workBusinFlowTaskService;

    @Resource
    private IBusinFlowTaskService businFlowTaskService;

    @DubboReference(check = false)
    private IEsbComponentElectDubboService esbComponentElectDubboService;

    @Autowired
    private RedissonUtil redissonUtil;


    /**
     * 任务中心-列表展示
     */
    @PostMapping("queryCenterTaskDetail")
    public Result<Page<TaskDetailInfo>> queryCenterTaskDetail(@AuthenticationPrincipal BaseUser baseUser, @RequestBody TaskDetailRequest taskDetailRequest) {
        taskDetailRequest.setOperator_no(baseUser.getStaff_no());
        taskDetailRequest.setOperator_name(baseUser.getUser_name());
        taskDetailRequest.setUser_role(baseUser.getUser_role());
        Page<TaskDetailInfo> list = workBusinFlowTaskService.selectCenterTaskListByPage(taskDetailRequest);
        return Result.success(list);
    }


    /**
     * 导出中心任务列表
     *
     * @param baseUser          当前操作的用户信息，包含员工编号、员工姓名和用户角色等信息，通过@AuthenticationPrincipal注解自动获取。
     * @param taskDetailRequest 任务详细信息请求参数，通过@RequestBody注解从请求体中获取。
     *                          该参数会被用来设定任务查询的条件，同时会记录操作人的信息。
     * @return 返回任务详情信息的分页结果，封装在Result<Page<TaskDetailInfo>>中。
     * 若查询成功，Result的success方法将返回分页信息；若有异常，则返回相应的错误信息。
     */
    @PostMapping("exportCenterTaskList")
    public Result<List<TaskDetailInfoExport>> exportCenterTaskList(@AuthenticationPrincipal BaseUser baseUser, @RequestBody TaskDetailRequest taskDetailRequest) {
        taskDetailRequest.setOperator_no(baseUser.getStaff_no());
        taskDetailRequest.setOperator_name(baseUser.getUser_name());
        taskDetailRequest.setUser_role(baseUser.getUser_role());
        try {
            List<TaskDetailInfoExport> list = workBusinFlowTaskService.exportCenterTaskList(baseUser, taskDetailRequest);
            return Result.success(list);
        } catch (BizException e) {
            return Result.fail(e.getError_no(), e.getError_info());
        }
    }

    /**
     * 任务统计接口
     */
    @PostMapping("countCenterTaskNum")
    public Result<TaskCountNum> countCenterTaskNum(@AuthenticationPrincipal BaseUser baseUser) {
        TaskCountNum taskCountNum = workBusinFlowTaskService.countCenterTaskNum(baseUser);
        return Result.success(taskCountNum);
    }


    @GetMapping("find/{request_no}")
    public Result<List<BusinFlowTask>> findByRequestNo(@PathVariable("request_no") String request_no) {
        LambdaQueryWrapper<BusinFlowTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusinFlowTask::getRequest_no, request_no);
        wrapper.orderByDesc(BusinFlowTask::getSerial_id);
        return Result.success(businFlowTaskService.list(wrapper));
    }

    /**
     * 话术播报语音文件输出
     */
    @GetMapping("authless/talkVoiceDownload/{filerecord_id}")
    public void talkVoiceDownload(@PathVariable("filerecord_id") String filerecord_id, HttpServletResponse response) {
        ElectDownloadFileRequest request = new ElectDownloadFileRequest();
        request.setFilerecord_id(filerecord_id);
        ElectDownloadFileResponse electDownloadFileResponse = esbComponentElectDubboService.electDownloadFile(request);
        Assert.notNull(electDownloadFileResponse, "话术语音文件不存在filerecord_id = " + filerecord_id);

        response.addHeader("Content-Disposition", "attachment;filename=" + electDownloadFileResponse.getFileName());
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            outputStream.write(electDownloadFileResponse.getFile());
            outputStream.flush();
        } catch (IOException e) {
            log.error("话术播报语音文件输出流异常 filerecord_id ={}", filerecord_id, e);
        }
    }

    /**
     * 任务手动暂存
     */
    @RequestMapping("staging")
    public Result<String> taskStaging(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody TaskStagingAndActivationForm form) {
        // 任务暂存开关
        boolean open_result = PropertySource.getBoolean(PropKeyConstant.WSKH_TASK_STAGING_SWITCH);
        if (!open_result) {
            return Result.fail("暂存任务开关未开启");
        }
        log.info("开始执行任务暂存, request_no: {} flow_task_id: {}, operator: {}", form.getRequest_no(), form.getFlow_task_id(), baseUser.getStaff_no());
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, form.getRequest_no());
        try {
            boolean isLock = redissonUtil.tryLock(lockKey, 0, 8, TimeUnit.SECONDS);
            if (!isLock) {
                throw new BizException(ErrorEnum.REDIS_TRY_LOCK_FAIL.getValue(), ErrorEnum.REDIS_TRY_LOCK_FAIL.getDesc());
            }
            workBusinFlowTaskService.taskStaging(baseUser, form);
        } catch (Exception e) {
            log.error("任务暂存失败, request_no: {} flow_task_id: {}", form.getRequest_no(), form.getFlow_task_id(), e);
            return Result.fail("任务暂存失败: " + e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
        return Result.success();
    }

    /**
     * 任务手动释放
     */
    @RequestMapping("activation")
    public Result<String> taskActivation(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody TaskStagingAndActivationForm form) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, form.getRequest_no());
        try {
            boolean isLock = redissonUtil.tryLock(lockKey, 0, 8, TimeUnit.SECONDS);
            if (!isLock) {
                throw new BizException(ErrorEnum.REDIS_TRY_LOCK_FAIL.getValue(), ErrorEnum.REDIS_TRY_LOCK_FAIL.getDesc());
            }
            workBusinFlowTaskService.taskActivation(baseUser, form);
        } catch (Exception e) {
            log.error("任务释放失败, request_no: {} flow_task_id: {}", form.getRequest_no(), form.getFlow_task_id(), e);
            return Result.fail("任务释放失败: " + e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
        return Result.success();
    }

    /**
     * 任务批量手动暂存
     */
    @RequestMapping("bulkStaging")
    public Result<StagingTaskCount> taskBulkStaging(@AuthenticationPrincipal BaseUser baseUser, @RequestBody TaskDetailRequest taskDetailRequest) {
        // 任务暂存开关
        boolean open_result = PropertySource.getBoolean(PropKeyConstant.WSKH_TASK_STAGING_SWITCH);
        if (!open_result) {
            return Result.fail("暂存任务开关未开启");
        }
        return Result.success(workBusinFlowTaskService.taskBulkStaging(baseUser, taskDetailRequest));
    }

    /**
     * 任务批量手动释放
     */
    @RequestMapping("bulkActivation")
    public Result<StagingTaskCount> taskBulkActivation(@AuthenticationPrincipal BaseUser baseUser, @RequestBody TaskDetailRequest taskDetailRequest) {
        return Result.success(workBusinFlowTaskService.taskBulkActivation(baseUser, taskDetailRequest));
    }

}

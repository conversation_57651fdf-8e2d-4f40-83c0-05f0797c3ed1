package com.cairh.cpe.backend.controller.query;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.common.entity.BusinFlowParams;
import com.cairh.cpe.common.service.IBusinFlowParamsService;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("flowparams")
public class BusinFlowParamsController {

    @Autowired
    private IBusinFlowParamsService businFlowParamsService;

    @GetMapping("list/{request_no}")
    public Result<String> findByRequestNo(@PathVariable("request_no") String request_no) {
        LambdaQueryWrapper<BusinFlowParams> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusinFlowParams::getRequest_no,request_no);
        wrapper.orderByAsc(BusinFlowParams::getOrder_no);
        List<BusinFlowParams> params =  businFlowParamsService.list(wrapper);
        return Result.success(JSON.toJSONString(params));
    }

}

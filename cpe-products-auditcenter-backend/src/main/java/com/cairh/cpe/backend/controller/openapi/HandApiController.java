package com.cairh.cpe.backend.controller.openapi;

import com.alibaba.fastjson.JSON;
import com.cairh.cpe.backend.constant.BackendApiUrlConstant;
import com.cairh.cpe.backend.form.req.BusinessCentralizedQueryForm;
import com.cairh.cpe.backend.form.req.BusinessHandCreateForm;
import com.cairh.cpe.backend.form.resp.CreateResult;
import com.cairh.cpe.backend.form.resp.HallBackInfo;
import com.cairh.cpe.backend.service.IHandApiService;
import com.cairh.cpe.common.constant.ClientCategoryEnum;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.IdKindEnum;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.util.IdentifyUtils;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("authless")
public class HandApiController {

    @Resource
    private RedissonUtil redissonUtil;

    @Resource
    private IHandApiService handApiService;

    @PostMapping(BackendApiUrlConstant.HAND_SUBMIT_AUDIT)
    public Result<CreateResult> create(@Validated @RequestBody BusinessHandCreateForm businessHandForm) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_ID_NO, businessHandForm.getId_no());
        boolean isLock = redissonUtil.tryLock(lockKey, 0, 8, TimeUnit.SECONDS);
        if (!isLock) {
            return Result.fail(ErrorEnum.REDIS_TRY_LOCK_FAIL.getValue(), ErrorEnum.REDIS_TRY_LOCK_FAIL.getDesc());
        }
        try {
            Result result = checkSubmit(businessHandForm);
            if (!result.isSuccess()) {
                return result;
            }
            return Result.success(handApiService.create(businessHandForm));
        } catch (Exception e) {
            log.error("网厅提交异常,异常数据={}", JSON.toJSONString(businessHandForm), e);
            return Result.fail(e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    @PostMapping(BackendApiUrlConstant.CENTRALIZED_QUERY_AUDIT)
    public Result<List<HallBackInfo>> centralizedQuery(@Validated @RequestBody BusinessCentralizedQueryForm queryForm) {
        return Result.success(handApiService.centralizedQuery(queryForm));
    }

    /**
     * 数据校验
     *
     * @param businessHandForm
     * @return
     */
    private Result<Void> checkSubmit(BusinessHandCreateForm businessHandForm) {
        if (StringUtils.equalsAny(businessHandForm.getClient_category(), ClientCategoryEnum.CLIENT_INSTITUTION.getCode(), ClientCategoryEnum.CLIENT_PRODUCT.getCode())) {
            if (!ObjectUtils.allNotNull(businessHandForm.getAgent_name(), businessHandForm.getAgent_id_no(),
                    businessHandForm.getAgent_id_kind(), businessHandForm.getAgent_photo_front(), businessHandForm.getAgent_photo_back())) {
                return Result.fail("经办人信息不能为空");
            }
            if (!IdKindEnum.ID_CARD.getCode().equals(businessHandForm.getAgent_id_kind())) {
                return Result.fail("经办人证件类型只支持身份证");
            }
            if (!IdentifyUtils.verifyIdentity(businessHandForm.getAgent_id_no())) {
                return Result.fail("经办人证件号码错误");
            }
        } else {
            //个人户
            if (IdKindEnum.ID_CARD.getCode().equals(businessHandForm.getId_kind()) && !IdentifyUtils.verifyIdentity(businessHandForm.getId_no())) {
                return Result.fail("身份证证件号码错误");
            }
        }
        return Result.success();
    }
}

package com.cairh.cpe.backend.controller.query;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.BusinFlowRecordQueryForm;
import com.cairh.cpe.backend.form.resp.BusinFlowRecordResult;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.entity.BusinFlowRecord;
import com.cairh.cpe.common.entity.request.BusinflowRecordReq;
import com.cairh.cpe.common.entity.response.BusinFlowRecordResp;
import com.cairh.cpe.common.service.IBusinFlowRecordService;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.common.util.SqlDateUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.service.config.AiAuditThreadPoolTaskExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 业务流水查询
 */
@Slf4j
@RestController
@RequestMapping("businFlowRecord")
public class BusinFlowRecordController {

    @Autowired
    private IBusinFlowRecordService businFlowRecordService;
    @Autowired
    private CacheBranch cacheBranch;
    @Autowired
    private CacheBackendUser backendUser;

    @Value("${cpe.ac.max-export:500000}")
    private Integer max_export;

    @Value("${cpe.ac.each-export:2000}")
    private Integer each_export;

    @Autowired
    AiAuditThreadPoolTaskExecutor aiAuditThreadPoolTaskExecutor;
    /**
     * 业务流水查询列表
     */
    @RequestMapping("queryAuditTask")
    public Result<Page<BusinFlowRecordResult>> queryAuditTask(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BusinFlowRecordQueryForm recordQueryForm) {
        BusinflowRecordReq businflowRecordReq = new BusinflowRecordReq();
        BeanUtil.copyProperties(recordQueryForm,businflowRecordReq);
        //用户可操作营业部
        businflowRecordReq.setOperator_branch(backendUser.getEnBranchNo(baseUser.getStaff_no()));
        Page<BusinFlowRecordResp> page = new Page<>(businflowRecordReq.getCur_page(), businflowRecordReq.getPage_size());
        Page<BusinFlowRecordResp> list = businFlowRecordService.selectRecordListByPage(page, businflowRecordReq);
        //对象转换
        Page<BusinFlowRecordResult> pageResult = new Page<BusinFlowRecordResult>();
        BaseBeanUtil.copyProperties(list,pageResult);
        List<BusinFlowRecordResult> BusinFlowRecordResults = new ArrayList<>();
        for (BusinFlowRecordResp record : list.getRecords()) {
            BusinFlowRecordResult businFlowRecordResult = new BusinFlowRecordResult();
            record.setCompany_name(cacheBranch.getUpCompanyName(record.getBranch_no()));
            BaseBeanUtil.copyProperties(record,businFlowRecordResult);
            businFlowRecordResult.setBusin_type_code(record.getBusin_type());
            BusinFlowRecordResults.add(businFlowRecordResult);
        }
        pageResult.setRecords(BusinFlowRecordResults);
        return Result.success(pageResult);
    }

    /**
     * 导出
     * @param recordQueryForm
     */
    @PostMapping("export")
    public Result<List<BusinFlowRecordResult>> export(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BusinFlowRecordQueryForm recordQueryForm) {
        BusinflowRecordReq businflowRecordReq = new BusinflowRecordReq();
        BeanUtil.copyProperties(recordQueryForm,businflowRecordReq);
        //用户可操作营业部
        businflowRecordReq.setOperator_branch(backendUser.getEnBranchNo(baseUser.getStaff_no()));
        Page<BusinFlowRecordResp> page = new Page<>(1, each_export,Boolean.TRUE);
        Page<BusinFlowRecordResp> respPage = businFlowRecordService.selectRecordListByPage(page, businflowRecordReq);
        long total = respPage.getTotal();
        if (total == 0L) {
            return Result.success(new ArrayList<>());
        }
        if (total > max_export) {
            throw new BizException("-9997", ErrorEnum.TOO_MUCH_DATA_MESSAGE.getDesc());
        }
        List<BusinFlowRecordResult> respList  = new CopyOnWriteArrayList<>();
        respList.addAll(respPage.getRecords().stream().map(item -> convertResp(item)).collect(Collectors.toList()));
        if (total > each_export) {
            int totalPages = (int) Math.ceil((double) total / each_export);
            List<CompletableFuture<Void>> futureList = new ArrayList<>();
            for (int i = 2; i <= totalPages; i++) {
                int currentPage = i;
                    // 设置分页参数
                // 创建新的查询对象，避免线程间共享
                BusinflowRecordReq asyncReq = new BusinflowRecordReq();
                BeanUtil.copyProperties(recordQueryForm, asyncReq);
                asyncReq.setOperator_branch(businflowRecordReq.getOperator_branch());
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    Page<BusinFlowRecordResp> asyncResult = businFlowRecordService.selectRecordListByPage(new Page<>(currentPage, each_export, Boolean.FALSE), asyncReq);
                    respList.addAll(asyncResult.getRecords().stream().map(item -> convertResp(item)).collect(Collectors.toList()));
                    log.info("业务流水查询导出数据 currentPage ={},number={}",currentPage,asyncResult.getRecords().size());
                }, aiAuditThreadPoolTaskExecutor);
                futureList.add(future);
            }
            CompletableFuture<Void> future = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
            future.join();
        }
        return Result.success(respList);
    }
    @GetMapping("list/{request_no}")
    public Result<List<BusinFlowRecord>> findByRequestNo(@PathVariable("request_no") String request_no) {
        LambdaQueryWrapper<BusinFlowRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusinFlowRecord::getRequest_no, request_no);
        wrapper.orderByDesc(BusinFlowRecord::getSerial_id);
        return Result.success(businFlowRecordService.list(wrapper));
    }


    /**
     * 实体类转化
     * @param recordResp
     * @return
     */
    private BusinFlowRecordResult convertResp(BusinFlowRecordResp recordResp) {
        BusinFlowRecordResult businFlowRecordResult = new BusinFlowRecordResult();
        BaseBeanUtil.copyProperties(recordResp,businFlowRecordResult);
        businFlowRecordResult.setCompany_name(cacheBranch.getUpCompanyName(recordResp.getBranch_no()));
        businFlowRecordResult.setBusin_type_code(recordResp.getBusin_type());
        return businFlowRecordResult;
    }

}

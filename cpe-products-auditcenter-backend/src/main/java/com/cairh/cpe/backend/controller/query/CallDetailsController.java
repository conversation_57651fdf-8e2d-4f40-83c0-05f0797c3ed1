package com.cairh.cpe.backend.controller.query;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.backend.form.resp.CallParametersResp;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.CallConstant;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.CallDetails;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.entity.response.CallUuiResp;
import com.cairh.cpe.common.entity.support.BackendUser;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.ICallDetailsService;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.common.util.http.RestTemplateUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("callDetails")
public class CallDetailsController {

    @Resource
    private ICallDetailsService iCallDetailsService;

    @Resource
    private RedissonUtil redissonUtil;

    @Resource
    private RestTemplateUtil restTemplateUtil;


    @Resource
    private IBusinFlowTaskService businFlowTaskService;

    @Resource
    private CacheBranch cacheBranch;

    @Resource
    private CacheBackendUser cacheBackendUser;

    @Value("${dx.audio.url:http://*************:8282/audiodownload}")
    private String dxAudioUrl;

    @GetMapping("allList/{request_no}")
    public Result<List<CallDetails>> list(@PathVariable("request_no") String request_no) {
        LambdaQueryWrapper<CallDetails> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CallDetails::getRequest_no, request_no);
        wrapper.in(CallDetails::getCall_status, Lists.newArrayList(CallConstant.CALL_STATUS_INIT,CallConstant.CALL_STATUS_FINISH));
        wrapper.orderByDesc(CallDetails::getSerial_id);
        return Result.success(iCallDetailsService.list(wrapper));
    }

    @GetMapping("list/{flowTaskId}")
    public Result<List<CallDetails>> findByRequestNo(@PathVariable("flowTaskId") String flowTaskId) {
        BusinFlowTask businFlowTask = businFlowTaskService.getById(flowTaskId);
        LambdaQueryWrapper<CallDetails> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CallDetails::getRequest_no, businFlowTask.getRequest_no());
        wrapper.in(CallDetails::getCall_status, Lists.newArrayList(CallConstant.CALL_STATUS_INIT,CallConstant.CALL_STATUS_FINISH));
        wrapper.orderByDesc(CallDetails::getSerial_id);
        return Result.success(iCallDetailsService.list(wrapper));
    }


    /**
     * 获取拨打前的参数
     * @param flowTaskId
     * @param baseUser
     * @return
     */
    @PostMapping("sendCall/{flowTaskId}")
    public Result<CallParametersResp> sendCallRequest(@PathVariable("flowTaskId") String flowTaskId,
                                                        @AuthenticationPrincipal BaseUser baseUser) {
        String lockKey = String.format(LockKeyConstant.CALL_FLOW_TASK_ID, flowTaskId);
        boolean isLock = redissonUtil.tryLock(lockKey, 0, 8, TimeUnit.SECONDS);
        if (!isLock) {
            return Result.fail(ErrorEnum.REDIS_TRY_LOCK_FAIL.getValue(),ErrorEnum.REDIS_TRY_LOCK_FAIL.getDesc());
        }
        try {
            BackendUser backendUser = cacheBackendUser.getBackendUserByStaffNo(baseUser.getStaff_no());
            Map<String, BranchInfo> branchInfoMap = cacheBranch.getAllBranchList().stream().collect(Collectors.toMap(BranchInfo::getBranch_no, x -> x));
            CallParametersResp resp = new CallParametersResp();
            CallUuiResp callUuiResp = iCallDetailsService.sendCallRequest(flowTaskId,baseUser,branchInfoMap,backendUser,
                    PropertySource.get(PropKeyConstant.WSKH_CALL_TEST_PHONE,""));
            resp.setUui(callUuiResp.getUui());
            resp.setSerial_id(callUuiResp.getSerial_id());
            resp.setBizId(flowTaskId);
            resp.setStationId(CallConstant.CALL_STAFF_PREFIX + baseUser.getStaff_no());
            return Result.success(resp);
        }finally {
            redissonUtil.unlock(lockKey);
        }
    }

    /**
     * 获取拨打前的参数
     * @param serial_id
     * @return
     */
    @PostMapping("cancelCall/{serial_id}")
    public Result<Boolean> cancelCall(@PathVariable("serial_id") String serial_id) {
        return Result.success(iCallDetailsService.cancelCall(serial_id));
    }

    /**
     * 文件下载
     * @param serial_id
     * @param response
     */
    @GetMapping("authless/audioDownload/{serial_id}")
    public void audioDownload(@PathVariable("serial_id") String serial_id, HttpServletResponse response) {
        CallDetails callDetails =  iCallDetailsService.getById(serial_id);
        Assert.notNull(callDetails,"录音信息未找到callId = " + serial_id);
        //文件地址
        String clientUrl = dxAudioUrl + "?pathname=" + callDetails.getRecordingFileName();
        ResponseEntity<byte[]> responseEntity = restTemplateUtil.restTemplate().exchange(clientUrl, HttpMethod.GET, null, byte[].class);
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error("状态码异常: {}", responseEntity.getStatusCodeValue());
            return;
        }
        response.setContentType("audio/wav");
        response.addHeader("Content-Disposition", "attachment;filename=" + callDetails.getCallId() + ".wav");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            outputStream.write(responseEntity.getBody());
            outputStream.flush();
        } catch (IOException e) {
            log.error("查看录音,输出流异常 callId ={}",callDetails.getCallId(), e);
        }
    }

}

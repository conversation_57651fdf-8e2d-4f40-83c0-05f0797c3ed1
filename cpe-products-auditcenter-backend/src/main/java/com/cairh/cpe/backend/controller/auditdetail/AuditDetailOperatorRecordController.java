package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.form.req.GetMoneyAntiInfoForm;
import com.cairh.cpe.backend.form.req.GetOperatorRecordForm;
import com.cairh.cpe.backend.form.resp.MoneyAntiInfo;
import com.cairh.cpe.backend.service.IAuditDetailOperatorRecordService;
import com.cairh.cpe.common.entity.response.AuditOperateRecord;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 审核详情 - 客服操作流水
 */
@Slf4j
@RestController
@RequestMapping("auditDetailOperatorRecord")
public class AuditDetailOperatorRecordController {

    @Autowired
    private IAuditDetailOperatorRecordService auditDetailOperatorRecordService;


    /**
     * 取操作流水
     */
    @PostMapping("getOperatorRecord")
    public Result<List<AuditOperateRecord>> getOperatorRecord(@Validated @RequestBody GetOperatorRecordForm getOperatorRecordForm) {
        List<AuditOperateRecord> auditOperateRecords = auditDetailOperatorRecordService.getOperatorRecord(getOperatorRecordForm.getRequest_no());

        return Result.success(auditOperateRecords);
    }
}

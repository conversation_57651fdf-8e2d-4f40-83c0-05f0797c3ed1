package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.DishonestDetail;
import com.cairh.cpe.backend.form.resp.DishonestResp;
import com.cairh.cpe.backend.service.IAuditCenterCommonService;
import com.cairh.cpe.backend.service.IAuditDetailDishonestService;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.Fields;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.constant.WskhFields;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.response.QueryDishonestResult;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 审核详情 - 失信记录
 */
@RestController
@RequestMapping("auditDetailDishonest")
public class AuditDetailDishonestController {

    @Autowired
    private IAuditDetailDishonestService auditDetailDishonestService;
    @Autowired
    private IAuditCenterCommonService auditCenterCommonService;
    @Resource
    private RedissonUtil redissonUtil;
    @Resource
    private IBusinFlowTaskService businFlowTaskService;


    /**
     * 失信记录以及备注修改
     */
    @PostMapping("modifyDishonestRecordAndRemark")
    public Result<String> modifyDishonestRecordAndRemark(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody DishonestForm dishonestForm) {
        // 数据校验
        BusinFlowTask businFlowTask = businFlowTaskService.getById(dishonestForm.getTask_id());
        if (businFlowTask == null) {
            throw new BizException(ErrorEnum.AUDIT_TASK_NOT_EXIST.getValue(), ErrorEnum.AUDIT_TASK_NOT_EXIST.getDesc());
        }
        if (!StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_AUDITING)) {
            throw new BizException(ErrorEnum.ALREADY_DEAL_TASK_TIP.getValue(), ErrorEnum.ALREADY_DEAL_TASK_TIP.getDesc());
        }
        dishonestForm.setOperator_no(baseUser.getStaff_no());
        dishonestForm.setOperator_name(baseUser.getUser_name());
        auditDetailDishonestService.modifyDishonestRecordAndRemark(dishonestForm);
        return Result.success();
    }


    /**
     * 上传失信记录图片
     */
    @PostMapping("uploadDishonestImage")
    public Result<String> uploadDishonestImage(@RequestPart MultipartFile image_file) {
        String file_record_id = auditCenterCommonService.uploadFileImage(image_file);
        return Result.success(file_record_id);
    }


    /**
     * 记录上传失信记录图片信息
     */
    @PostMapping("recordUploadDishonestImageInfo")
    public Result<String> recordUploadDishonestImageInfo(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody UploadDishonestImageForm uploadDishonestImageForm) {
        uploadDishonestImageForm.setOperator_no(baseUser.getStaff_no());
        uploadDishonestImageForm.setOperator_name(baseUser.getUser_name());

        Map<String, Object> resultMap = auditDetailDishonestService.uploadDishonestImage(uploadDishonestImageForm);
        if (StringUtils.equals("-1", MapUtils.getString(resultMap, Fields.ERROR_NO))) {
            return Result.fail(MapUtils.getString(resultMap, Fields.ERROR_INFO));
        } else {
            return Result.success(MapUtils.getString(resultMap, WskhFields.DISHONEST_ID));
        }

    }

    /**
     * 上传rcpMis 信息
     */
    @PostMapping("recordUploadRcpMisInfo")
    public Result<Boolean> recordUploadRcpMisInfo(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody UploadRcpMisRequest uploadRcpMisRequest) {
        String lockKey = String.format(LockKeyConstant.WSKH_RPC_MIS_REQUEST_NO, uploadRcpMisRequest.getRequest_no());
        boolean isLock = redissonUtil.tryLock(lockKey, 0, 8, TimeUnit.SECONDS);
        if (!isLock) {
            return Result.fail(ErrorEnum.REDIS_TRY_LOCK_FAIL.getValue(), ErrorEnum.REDIS_TRY_LOCK_FAIL.getDesc());
        }
        try {
            auditDetailDishonestService.uploadRpcMisInfo(baseUser, uploadRcpMisRequest);
            return Result.success();
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    /**
     * 获取失信记录
     */
    @PostMapping("getDishonestRecord")
    public Result<DishonestResp> getDishonestRecord(@Validated @RequestBody BaseForm baseForm) {
        DishonestResp dishonestResp = auditDetailDishonestService.getDishonestRecord(baseForm.getRequest_no());

        return Result.success(dishonestResp);
    }


    /**
     * 失信记录自动查询接口调用
     */
    @PostMapping("getSecurityAlisaRpa")
    public Result<QueryDishonestResult> getSecurityAlisaRpa(@Validated @RequestBody BaseForm baseForm, @AuthenticationPrincipal BaseUser baseUser) {
        QueryDishonestResult result = auditDetailDishonestService.getSecurityAlisaRpa(baseForm.getRequest_no(), baseUser.getStaff_no());
        return Result.success(result);

    }

    /**
     * crh_rpa接口调用
     */
    @PostMapping("getCrhDishonestImage")
    public Result<DishonestResp> getCrhDishonestImage(@Validated @RequestBody BaseForm baseForm) {
        DishonestResp dishonestResp = auditDetailDishonestService.getCrhDishonestImage(baseForm.getRequest_no());

        return Result.success(dishonestResp);
    }


    /**
     * 查看详情
     */
    @PostMapping("queryDetailDishonestContent")
    public Result<DishonestDetail> queryDetailDishonestContent(@Validated @RequestBody DishonestQueryDetail dishonestQueryDetail) {
        DishonestDetail dishonestResp = auditDetailDishonestService.queryDetailDishonestContent(dishonestQueryDetail.getDishonest_content());
        return Result.success(dishonestResp);
    }
}

package com.cairh.cpe.backend.controller.operate;

import com.cairh.cpe.backend.form.req.BackAuditTaskReq;
import com.cairh.cpe.common.service.IDealExceptionAuditDataService;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("resetAudit")
public class ResetAuditController {

    @Resource
    private IDealExceptionAuditDataService dealExceptionAuditDataService;

    /**
     * 任务回退
     * @param backAuditTaskReq
     * @return
     */
    @PostMapping("backAuditTask")
    public Result<Boolean> backAuditTask(@RequestBody BackAuditTaskReq backAuditTaskReq) {
        return Result.success(dealExceptionAuditDataService.backlExceptionAuditData(backAuditTaskReq.getSerial_id(),
                backAuditTaskReq.getRequest_no()));
    }


}

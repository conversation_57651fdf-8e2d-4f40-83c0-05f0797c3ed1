package com.cairh.cpe.backend.controller.auditdetail;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.BidirectionalVideoChatLogInfo;
import com.cairh.cpe.backend.form.resp.DealAuditTaskResp;
import com.cairh.cpe.backend.form.support.OnlineUserByEnRoleAndBranchSupportResponse;
import com.cairh.cpe.backend.service.IAuditDetailBidirectionalVideoService;
import com.cairh.cpe.backend.service.IAuditDetailHandleService;
import com.cairh.cpe.backend.service.ITransferCallBackService;
import com.cairh.cpe.backend.service.IWorkBusinFlowTaskService;
import com.cairh.cpe.businflow.entity.request.AuditResultNotifyReq;
import com.cairh.cpe.businflow.service.IAuditCallbackService;
import com.cairh.cpe.cache.component.ComponentWorkTimeService;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.dto.CheckBeforeMessage;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.clob.AuditReason;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.support.TaskMessage;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.service.IUserQueryExtInfoService;
import com.cairh.cpe.common.util.ParamsSavingUtils;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.esb.component.video.IEsbComponentVideoDubboService;
import com.cairh.cpe.esb.component.video.dto.req.VideoAnswerUserRequest;
import com.cairh.cpe.service.aiaudit.service.IAiAuditAchieveService;
import com.cairh.cpe.service.auditcenter.service.IBidirectionalVideoHandlerService;
import com.cairh.cpe.service.config.AiAuditThreadPoolTaskExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 审核处理
 */
@Slf4j
@RestController
@RequestMapping("auditDetailHandle")
public class AuditDetailHandleController {

    @Autowired
    IUserQueryExtInfoService userQueryExtInfoService;
    @Autowired
    private IAuditDetailHandleService auditDetailHandleService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private IRequestService requestService;
    @Autowired
    private IAuditCallbackService auditCallbackService;
    @Autowired
    private ITransferCallBackService transferCallBackService;
    @Resource
    private IWorkBusinFlowTaskService mybusinFlowTaskService;
    @Autowired
    private RedissonUtil redissonUtil;
    @Resource
    private AiAuditThreadPoolTaskExecutor aiAuditThreadPoolTaskExecutor;
    @DubboReference
    private IEsbComponentVideoDubboService componentVideoDubboService;

    @Autowired
    private IBidirectionalVideoHandlerService bidirectionalVideoHandlerService;
    @Autowired
    private IAuditDetailBidirectionalVideoService auditDetailBidirectionalVideoService;
    @Autowired
    private IAiAuditAchieveService aiAuditAchieveService;
    @Autowired
    private ComponentWorkTimeService componentWorkTimeService;

    /**
     * 开始处理
     */
    @PostMapping("dealAuditTask")
    public Result<String> dealAuditTask(@AuthenticationPrincipal BaseUser baseUser,
                                        @Validated @RequestBody DealAuditTaskForm dealAuditTask) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, dealAuditTask.getRequest_no());
        boolean isLock = redissonUtil.tryLock(lockKey, 2, 8, TimeUnit.SECONDS);
        if (!isLock) {
            return Result.fail(ErrorEnum.REDIS_TRY_LOCK_FAIL.getValue(), ErrorEnum.REDIS_TRY_LOCK_FAIL.getDesc());
        }
        try {
            BusinFlowTask businFlowTask = businFlowTaskService.getById(dealAuditTask.getFlow_task_id());
            if (businFlowTask == null) {
                return Result.fail(ErrorEnum.DATA_NOT_EXIST.getValue(), ErrorEnum.DATA_NOT_EXIST.getDesc());
            }
            Result<String> result = checkAuditTask(baseUser, dealAuditTask, businFlowTask);
            if (!result.isSuccess()) {
                return result;
            }
            processBidirectionalVideo(baseUser, dealAuditTask, businFlowTask);
            return auditDetailHandleService.dealAuditTask(baseUser, dealAuditTask, businFlowTask);
        } catch (Exception e) {
            log.error("操作员={}开始处理任务request_no={}异常", baseUser.getStaff_no(), dealAuditTask.getRequest_no(), e);
            return Result.fail(e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }


    private void processBidirectionalVideo(BaseUser baseUser, DealAuditTaskForm dealAuditTask, BusinFlowTask businFlowTask) {
        try {
            BusinFlowRequest businFlowRequest = requestService.getByRequestNo(businFlowTask.getRequest_no());
            if ((StringUtils.equalsAny(businFlowRequest.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL) &&
                    StringUtils.equals(businFlowTask.getVideo_type(), WskhConstant.VIDEO_TYPE_2) ||
                    ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, businFlowRequest.getBusin_type()))
                    && StringUtils.equals(businFlowTask.getTask_type(), TaskTypeEnum.AUDIT.getCode())) {
                VideoAnswerUserRequest request = new VideoAnswerUserRequest();
                request.setUnique_id(businFlowTask.getSerial_id());
                request.setOperator_no(businFlowTask.getOperator_no());
                request.setSubsys_no("24");
                request.setOperator_no(baseUser.getStaff_no());
                componentVideoDubboService.answerUserVideo(request);
            }
        } catch (Exception e) {
            log.error("操作员={}处理双向视频任务request_no={}异常", baseUser.getStaff_no(), dealAuditTask.getRequest_no(), e);
            log.info("[任务作废标记]processBidirectionalVideo双向视频处理任务，请求comp组件异常进行任务作废 request_no = {} task_id = {}", businFlowTask.getRequest_no(), businFlowTask.getTask_id());
            bidirectionalVideoHandlerService.clearBidirectionalVideoTask(businFlowTask, VideoConstant.BIDIRECTIONAL_CLEAR_TASK_QUEUE_EXCEPTION);
            String message = e.getMessage() + ",任务已作废";
            throw new BizException(ErrorEnum.BIDIRECTIONAL_INVALIDATE_TASK_ERROR.getValue(), message);
        }
    }


    /**
     * 审核通过前置校验
     * face_score_82_80  腾讯得分
     * face_score   证通得分
     */
    @PostMapping("checkBeforeAuditPass")
    public Result<DealAuditTaskResp> checkBeforeAuditPass(@Validated @RequestBody FinishAuditTaskForm finishAuditTaskForm) {
        BusinFlowRequest flowRequest = requestService.getByRequestNo(finishAuditTaskForm.getRequest_no());
        BusinFlowTask currentTask = businFlowTaskService.getById(finishAuditTaskForm.getTask_id());
        if (currentTask == null) {
            return Result.fail("-9997", "该任务不存在！");
        }
        // 双向视频
        if (StringUtils.equals(currentTask.getVideo_type(), WskhConstant.VIDEO_TYPE_2)) {
            ClobContentInfo contentInfo = requestService.getAllDataByRequestNo(finishAuditTaskForm.getRequest_no());
            if (StringUtils.isBlank(contentInfo.getFile_8A())) {
                return Result.fail("-9997", "请录制视频！");
            }
        }
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(finishAuditTaskForm.getRequest_no());
        if (!ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, clobContentInfo.getBusin_type())) {
            Result result = validateAge(clobContentInfo);
            if (!result.isSuccess()) {
                return Result.fail(result.getError().getError_no(), result.getError().getError_info());
            }
        }

        // 网厅非中登时间，需要提示手动刷新获取证通分才可以通过
        if ((ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, flowRequest.getBusin_type())
                && StringUtils.equalsAny(IdKindEnum.ID_CARD.getCode(), clobContentInfo.getId_kind(), clobContentInfo.getAgent_id_kind()))
                && !componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_ZD_GA) && StringUtils.isBlank(clobContentInfo.getFace_score())) {
            return Result.fail("-9997", "当前非中登时间，请手动刷新获取证通分！");
        }
        // 外国人永居证校验三要素
        String fail_message = "";
        String weak_fail_message = "";
        if (StringUtils.equals(flowRequest.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL) &&
                StringUtils.equals(clobContentInfo.getId_kind(), IdKindEnum.FOREIGN_PREV_PERMIT.getCode())) {
            String checkFlag = PropertySource.get(PropKeyConstant.WSKH_CHECK_PERMANENT_ID_INTERCEPT_CONFIG, "0");
            String permanentIdInfoCheckResult = clobContentInfo.getPermanent_id_info_check_result();
            if (StringUtils.equals(checkFlag, "1")) {
                if (!StringUtils.equals(permanentIdInfoCheckResult, "1")) {
                    fail_message = "身份验证失败，请修改证件信息后重新校验！";
                }
            } else {
                if (StringUtils.isEmpty(permanentIdInfoCheckResult)) {
                    weak_fail_message = "人脸识别结果未获取，请点击刷新按钮获取，如需继续通过，请选择或填写原因";
                } else if (!StringUtils.equals(permanentIdInfoCheckResult, "1")) {
                    weak_fail_message = "人脸识别失败，请重新获取结果，如继续通过，需填写通过原因";
                }
            }
        }
        //强制拦截
        if (StringUtils.equalsAny(flowRequest.getBusin_type(), WskhConstant.CROSS_BUSIN_TYPE)) {
            if (StringUtils.isBlank(clobContentInfo.getRpc_file_id())) {
                fail_message = "请上传RCPMIS查询结果截图！";
            }
            if (StringUtils.isBlank(clobContentInfo.getRpc_option())) {
                fail_message = "请选择RCPMIS查询结果！";
            }
            if (StringUtils.equals("6", clobContentInfo.getRpc_option()) && StringUtils.isBlank(clobContentInfo.getRpc_remark())) {
                fail_message = "请在备注栏填写详细的RCPMIS查询结果信息！";
            }
            // 存在禁止办理的不允许审核通过
            if (StringUtils.equalsAny(clobContentInfo.getRpc_option(), "1", "2", "3")) {
                fail_message = "RCPMIS查询结果类型不符合新开要求！";
            }
        }
        if (StringUtils.isNotBlank(fail_message)) {
            return Result.fail("-9997", fail_message);
        }
        DealAuditTaskResp dealAuditTaskResp = new DealAuditTaskResp();
        // 外国人永居证不需要校验分数
        if (StringUtils.equalsAny(flowRequest.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL) &&
                !StringUtils.equals(clobContentInfo.getId_kind(), IdKindEnum.FOREIGN_PREV_PERMIT.getCode()) ||
                (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, flowRequest.getBusin_type()) &&
                        StringUtils.equalsAny(IdKindEnum.ID_CARD.getCode(), clobContentInfo.getId_kind(), clobContentInfo.getAgent_id_kind()))) {
            CheckBeforeMessage checkBeforeMessage;
            // 是否新数据标签
            boolean isHandleNewData = StringUtils.equals(clobContentInfo.getData_sign(), WskhConstant.DATA_SIGN_STATUS);
            if (isHandleNewData) {
                checkBeforeMessage = newCheckBeforeMessage(clobContentInfo, finishAuditTaskForm.getMust_pass_reason());
            } else {
                // 港澳台居民居住证&港澳台通行证异常提示
                if (StringUtils.equals(WskhConstant.BUSIN_TYPE_NORMAL, clobContentInfo.getBusin_type()) &&
                        StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.GAT_RESIDENCE_PERMIT.getCode(),
                                IdKindEnum.GA_PASS_CARD.getCode(), IdKindEnum.TAIWAN_PASS_CARD.getCode())) {
                    checkBeforeMessage = check_zhongtong(clobContentInfo.getFace_score(), finishAuditTaskForm.getMust_pass_reason(), clobContentInfo.getBusin_type());
                } else {
                    checkBeforeMessage = oldCheckBeforeMessage(flowRequest, currentTask, clobContentInfo, finishAuditTaskForm.getMust_pass_reason());
                }
            }
            if (StringUtils.isNotBlank(checkBeforeMessage.getFail_message())) {
                return Result.fail("-9997", checkBeforeMessage.getFail_message());
            }
            if (StringUtils.isNotBlank(checkBeforeMessage.getWeak_fail_message())) {
                dealAuditTaskResp.setIntercept_type("weak_intercept");
                dealAuditTaskResp.setFail_message(checkBeforeMessage.getWeak_fail_message());
            }
        }
        // 外国人永居证开关未开启，增加弱拦截
        if (StringUtils.isNotBlank(weak_fail_message)) {
            dealAuditTaskResp.setIntercept_type("weak_intercept");
            dealAuditTaskResp.setFail_message(StringUtils.isNotBlank(dealAuditTaskResp.getFail_message())
                    ? weak_fail_message + "，" + dealAuditTaskResp.getFail_message() : weak_fail_message);
        }
        String not_blank_message = auditDetailHandleService.checkNotBlank(finishAuditTaskForm, clobContentInfo);
        if (StringUtils.isNotBlank(not_blank_message)) {
            dealAuditTaskResp.setIntercept_type("strong_intercept");
            dealAuditTaskResp.setFail_message(not_blank_message);
        }
        return Result.success(dealAuditTaskResp);
    }

    private Result validateAge(ClobContentInfo clobContentInfo) {
        try {
            // 用户基础信息中的年龄，年龄为空不处理，人工干预
            int age = Optional.of(clobContentInfo.getUser_base_info().getAge()).get();
            // 年龄小于最小限制18
            if (age < Constant.AUDIT_AGE_MIN) {
                return Result.fail("-9997", ErrorEnum.AGE_RESTRICTIONS_LESS_18_MESSAGE.getDesc());
            }
            // 年龄大于最大限制70
            if (age > Constant.AUDIT_AGE_MAX) {
                return Result.fail("-9997", ErrorEnum.AGE_RESTRICTIONS_MORE_70_MESSAGE.getDesc());
            }
        } catch (Exception e) {
            log.error("审核通过前置校验[BUSINFLOWPARAMS]的request_no：{},产生异常：{}", clobContentInfo.getRequest_no(), e.getMessage(), e);
        }
        return Result.success();
    }


    /**
     * 审核通过
     */
    @PostMapping("auditPassTask")
    public Result<String> auditPassTask(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody FinishAuditTaskForm finishAuditTaskForm) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, finishAuditTaskForm.getRequest_no());
        boolean isLock = redissonUtil.tryLock(lockKey, 2, 18, TimeUnit.SECONDS);
        if (!isLock) {
            return Result.fail(ErrorEnum.REDIS_TRY_LOCK_FAIL.getValue(), ErrorEnum.REDIS_TRY_LOCK_FAIL.getDesc());
        }
        try {
            BusinFlowTask task = businFlowTaskService.getById(finishAuditTaskForm.getTask_id());
            if (null == task) {
                return Result.fail(ErrorEnum.AUDIT_NOT_FIND_TASK.getValue(), ErrorEnum.AUDIT_NOT_FIND_TASK.getDesc());
            }
            if (StringUtils.equals(task.getTask_status(), FlowStatusConst.AUDIT_PASS)) {
                return Result.success();
            }
            if (!StringUtils.equals(task.getTask_status(), FlowStatusConst.AUDIT_AUDITING)) {
                return Result.fail(ErrorEnum.AUDIT_NOT_AUDITING_TASK.getValue(), ErrorEnum.AUDIT_NOT_AUDITING_TASK.getDesc());
            }
            // 处理审核需要推送派单为已推送状态
            boolean open_result = PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH, "0").equals("1");
            if (open_result && !StringUtils.equals(task.getPush_flag(), WskhConstant.ALREADY_PUSH_FLAG)) {
                return Result.fail(ErrorEnum.AUDIT_NOT_PUSH_DIS.getValue(), ErrorEnum.AUDIT_NOT_PUSH_DIS.getDesc());
            }

            if (!StringUtils.equals(task.getOperator_no(), baseUser.getStaff_no())) {
                return Result.fail(ErrorEnum.AUDIT_TRANSFER_NEW_TASK.getValue(), String.format(ErrorEnum.AUDIT_TRANSFER_NEW_TASK.getDesc(), task.getOperator_no()));
            }

            BusinFlowRequest flowRequest = requestService.getByRequestNo(finishAuditTaskForm.getRequest_no());
            auditDetailHandleService.auditPassTask(finishAuditTaskForm, task, baseUser);
            // 审核成功后的处理
            auditTaskAfterHandle(flowRequest.getBusin_type(), task);
            return Result.success();
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    /**
     * 审核驳回
     */
    @PostMapping("auditNotPassTask")
    public Result<String> auditNotPassTask(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody FinishAuditTaskForm finishAuditTaskForm) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, finishAuditTaskForm.getRequest_no());
        boolean isLock = redissonUtil.tryLock(lockKey, 2, 15, TimeUnit.SECONDS);
        if (!isLock) {
            return Result.fail(ErrorEnum.REDIS_TRY_LOCK_FAIL.getValue(), ErrorEnum.REDIS_TRY_LOCK_FAIL.getDesc());
        }
        try {
            BusinFlowTask task = businFlowTaskService.getById(finishAuditTaskForm.getTask_id());
            if (null == task) {
                return Result.fail(ErrorEnum.AUDIT_NOT_FIND_TASK.getValue(), ErrorEnum.AUDIT_NOT_FIND_TASK.getDesc());
            }
            if (StringUtils.equals(task.getTask_status(), FlowStatusConst.AUDIT_NO_PASS)) {
                return Result.success();
            }
            if (StringUtils.equals(task.getTask_status(), FlowStatusConst.AUDIT_PASS)) {
                return Result.fail(ErrorEnum.AUDIT_ALREADY_TASK.getValue(), ErrorEnum.AUDIT_ALREADY_TASK.getDesc());
            }
            // 处理审核需要推送派单为已推送状态
            boolean open_result = PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH, "0").equals("1");
            if (open_result && !StringUtils.equals(task.getPush_flag(), WskhConstant.ALREADY_PUSH_FLAG)) {
                return Result.fail(ErrorEnum.AUDIT_NOT_PUSH_DIS.getValue(), ErrorEnum.AUDIT_NOT_PUSH_DIS.getDesc());
            }

            if (!StringUtils.equals(task.getTask_status(), FlowStatusConst.AUDIT_AUDITING)) {
                return Result.fail(ErrorEnum.AUDIT_NOT_AUDITING_TASK.getValue(), ErrorEnum.AUDIT_NOT_AUDITING_TASK.getDesc());
            }
            if (!StringUtils.equals(task.getOperator_no(), baseUser.getStaff_no())) {
                return Result.fail(ErrorEnum.AUDIT_TRANSFER_NEW_TASK.getValue(), String.format(ErrorEnum.AUDIT_TRANSFER_NEW_TASK.getDesc(), task.getOperator_no()));
            }

            for (AuditReason reason : finishAuditTaskForm.getReasons()) {
                ParamsSavingUtils.noNullStringAttr(reason);
            }
            log.info("审核驳回业务开始------ requestNo ={}", finishAuditTaskForm.getRequest_no());
            BusinFlowTask currTaskType = auditDetailHandleService.auditNotPassTask(finishAuditTaskForm, baseUser);
            log.info("审核驳回业务结束------ requestNo ={}", finishAuditTaskForm.getRequest_no());
            aiAuditThreadPoolTaskExecutor.execute(() -> {
                log.info("审核驳回回调国泰通知开始------ requestNo ={}", finishAuditTaskForm.getRequest_no());
                AuditResultNotifyReq auditResultNotifyReq = new AuditResultNotifyReq();
                auditResultNotifyReq.setRequest_no(finishAuditTaskForm.getRequest_no());
                auditResultNotifyReq.setAnode_id(currTaskType.getTask_type());
                auditResultNotifyReq.setHandle_type("auditNotPass");
                auditResultNotifyReq.setTask_id(currTaskType.getTask_id());
                auditResultNotifyReq.setFlowtask_id(currTaskType.getSerial_id());
                auditCallbackService.auditResultNotify(auditResultNotifyReq);
                log.info("审核驳回回调国泰通知结束------ requestNo ={}", finishAuditTaskForm.getRequest_no());
            });
        } finally {
            redissonUtil.unlock(lockKey);
        }
        return Result.success();
    }


    /**
     * 挂起
     */
    @PostMapping("suspendTask")
    public Result<String> suspendTask(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody SuspendTaskForm suspendTaskForm) {
        checkTask(suspendTaskForm.getTask_id(), baseUser.getStaff_no());
        suspendTaskForm.setOperator_no(baseUser.getStaff_no());
        suspendTaskForm.setOperator_name(baseUser.getUser_name());
        auditDetailHandleService.suspendTask(suspendTaskForm);
        return Result.success();
    }

    /**
     * 转交人员机构树
     */
    @PostMapping("transferBranchTree")
    public Result<OnlineUserByEnRoleAndBranchSupportResponse> transferBranchTree(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody TransferBranchTreeForm transferBranchTreeForm) {
        checkTask(transferBranchTreeForm.getTask_id(), baseUser.getStaff_no());
        OnlineUserByEnRoleAndBranchSupportResponse onlineUserByEnRoleAndBranchSupportResponse = auditDetailHandleService.transferBranchTree(transferBranchTreeForm);
        return Result.success(onlineUserByEnRoleAndBranchSupportResponse);
    }

    /**
     * 转交
     */
    @PostMapping("transferTask")
    public Result<String> transferTask(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody TransferTaskForm transferTaskForm) {
        transferTaskForm.setOperator_no(baseUser.getStaff_no());
        transferTaskForm.setOperator_name(baseUser.getUser_name());
        auditDetailHandleService.transferTask(transferTaskForm);
        return Result.success();
    }

    /**
     * 双向视频-视频录制上传地址
     */
    @PostMapping("uploadAuditParam")
    public Result<String> uploadAuditParam(@Validated @RequestBody AuditVideoParmRequest videoParmRequest) {
        auditDetailHandleService.uploadAuditParam(videoParmRequest);
        return Result.success();
    }

    /**
     * 转交接受
     */
    @PostMapping("transferAccept")
    public Result<String> transferAccept(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody TransferAcceptForm transferAcceptForm) {
        transferAcceptForm.setOperator_no(baseUser.getStaff_no());
        transferAcceptForm.setOperator_name(baseUser.getUser_name());

        auditDetailHandleService.transferAccept(transferAcceptForm);

        TaskMessage message = new TaskMessage();
        message.setOperator_no(transferAcceptForm.getTransfer_operator_no());
        message.setRequest_no(transferAcceptForm.getRequest_no());
        message.setMessage_content("转交接受！");
        message.setFlow_task_id(transferAcceptForm.getFlow_task_id());
        transferCallBackService.notify(message);

        return Result.success();
    }

    /**
     * 转交拒绝
     */
    @PostMapping("transferRefuse")
    public Result<String> transferRefuse(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody TransferRefuseForm transferRefuseForm) {
        transferRefuseForm.setOperator_no(baseUser.getStaff_no());
        transferRefuseForm.setOperator_name(baseUser.getUser_name());

        auditDetailHandleService.transferRefuse(transferRefuseForm);

        TaskMessage message = new TaskMessage();
        message.setOperator_no(transferRefuseForm.getTransfer_operator_no());
        message.setRequest_no(transferRefuseForm.getRequest_no());
        message.setMessage_content("对方拒绝接受！");
        message.setFlow_task_id(transferRefuseForm.getFlow_task_id());
        transferCallBackService.notify(message);

        return Result.success();
    }

    @PostMapping("editTask/{serial_id}")
    public Result<Boolean> editTask(@PathVariable("serial_id") String serial_id,
                                    @RequestBody BusinFlowTaskEditReq req) {
        return Result.success(mybusinFlowTaskService.updateTask(serial_id, req));
    }

    /**
     * 坐席中断视频见证
     */
    @PostMapping("interruptUserVideo")
    public Result<Void> interruptUserVideo(@AuthenticationPrincipal BaseUser baseUser, @Valid @RequestBody VideoOperatorExitQueueInfoRequest request) {
        BusinFlowTask businFlowTask = businFlowTaskService.getById(request.getUnique_id());
        if (businFlowTask != null) {
            if (StringUtils.equals(FlowStatusConst.AUDIT_INVALIDATE, businFlowTask.getTask_status())) {
                return Result.success();
            }
            ExitVideoQueueRequest exitVideoQueueRequest = new ExitVideoQueueRequest();
            exitVideoQueueRequest.setTask_id(businFlowTask.getTask_id());
            auditDetailBidirectionalVideoService.exitVideoQueue(exitVideoQueueRequest);
            log.info("[任务作废标记]interruptUserVideo双向视频坐席中断视频见证 request_no = {} task_id = {}", businFlowTask.getRequest_no(), businFlowTask.getTask_id());
            bidirectionalVideoHandlerService.clearBidirectionalVideoTask(businFlowTask, request.getReason_msg());
        }
        return Result.success();
    }

    /**
     * 双向视频见证中任务心跳接口
     */
    @PostMapping("heartbeatBidirectionalTask")
    public Result<String> heartbeatBidirectionalTask(@AuthenticationPrincipal BaseUser baseUser,
                                                     @Validated @RequestBody HeartbeatBidirectionalTaskForm heartbeatBidirectionalTaskForm) {
        try {
            log.warn("heartbeatBidirectionalTask request_no={}, serial_id={}", heartbeatBidirectionalTaskForm.getRequest_no(), heartbeatBidirectionalTaskForm.getSerial_id());
            BusinFlowTask businFlowTask = businFlowTaskService.getById(heartbeatBidirectionalTaskForm.getSerial_id());
            if (!StringUtils.equals(businFlowTask.getVideo_type(), WskhConstant.VIDEO_TYPE_2)) {
                return Result.fail("该任务非双向视频任务");
            }
            if (!StringUtils.equals(businFlowTask.getTask_type(), TaskTypeEnum.AUDIT.getCode())) {
                return Result.fail("该任务非见证任务");
            }
            if (!StringUtils.equalsAny(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_AUDITING)) {
                return Result.fail("该任务非处理中任务");
            }
            bidirectionalVideoHandlerService.heartbeatBidirectionalTask(heartbeatBidirectionalTaskForm.getSerial_id());
            return Result.success();
        } catch (Exception e) {
            log.error("heartbeatBidirectionalTask error request_no={} serial_id={}", heartbeatBidirectionalTaskForm.getRequest_no(), heartbeatBidirectionalTaskForm.getSerial_id(), e);
            return Result.fail("-99", e.getMessage());
        }
    }

    /**
     * 保存-双向视频对话记录表
     */
    @PostMapping("saveBidirectionalVideoChatLog")
    public Result<String> saveBidirectionalVideoChatLog(@AuthenticationPrincipal BaseUser baseUser, @RequestBody SaveBidirectionalVideoChatLogRequest request) {
        request.setOperator_no(baseUser.getStaff_no());
        request.setOperator_name(baseUser.getUser_name());
        auditDetailHandleService.saveBidirectionalVideoChatLog(request);
        return Result.success();
    }

    /**
     * 查询-双向视频对话记录表
     */
    @GetMapping("query/{request_no}")
    public Result<List<BidirectionalVideoChatLogInfo>> queryBidirectionalVideoChatLog(@PathVariable("request_no") String request_no) {
        return Result.success(auditDetailHandleService.queryBidirectionalVideoChatLog(request_no));
    }

    private Result<String> checkAuditTask(BaseUser baseUser, DealAuditTaskForm dealAuditTask, BusinFlowTask businFlowTask) {
        //check 数据
        if (StringUtils.isNotBlank(businFlowTask.getNot_allow_auditor()) && businFlowTask.getNot_allow_auditor().contains(baseUser.getStaff_no())) {
            return Result.fail(ErrorEnum.AUDIT_TASK_NOT_ALLOW_AUDITOR.getValue(), ErrorEnum.AUDIT_TASK_NOT_ALLOW_AUDITOR.getDesc());
        }
        // 双向视频见证作废任务状态提示
        if (StringUtils.equals(businFlowTask.getVideo_type(), WskhConstant.VIDEO_TYPE_2)
                && StringUtils.equals(businFlowTask.getTask_type(), TaskTypeEnum.AUDIT.getCode())
                && StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_INVALIDATE)) {
            return Result.fail(ErrorEnum.BIDIRECTIONAL_AUDIT_INVALIDATE_TASK.getValue(), ErrorEnum.BIDIRECTIONAL_AUDIT_INVALIDATE_TASK.getDesc());
        }

        // -10086 会关闭页面所以修改 error_code  修改为其他编号
        if (!StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.REQUEST_STATUS_PENDING_AUDIT)
                && !StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_SUSPEND)
                && !StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.REQUEST_STATUS_AUDITING)) {
            return Result.fail(ErrorEnum.AUDIT_ALREADY_TASK.getValue(), ErrorEnum.AUDIT_ALREADY_TASK.getDesc());
        }

        // 任务审核中，不可重复处理
        if (StringUtils.equals(businFlowTask.getVideo_type(), WskhConstant.VIDEO_TYPE_2)
                && StringUtils.equals(businFlowTask.getTask_type(), TaskTypeEnum.AUDIT.getCode())
                && StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_AUDITING)) {
            return Result.fail(ErrorEnum.AUDIT_ALREADY_TASK.getValue(), ErrorEnum.AUDIT_ALREADY_TASK.getDesc());
        }

        // 处理审核需要推送派单为已推送状态
        boolean open_result = PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH, "0").equals("1");
        if (open_result && !StringUtils.equals(businFlowTask.getPush_flag(), WskhConstant.ALREADY_PUSH_FLAG)) {
            return Result.fail(ErrorEnum.AUDIT_NOT_PUSH_DIS.getValue(), ErrorEnum.AUDIT_NOT_PUSH_DIS.getDesc());
        }

        if (StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.REQUEST_STATUS_PENDING_AUDIT)) {
            LambdaQueryWrapper<BusinFlowTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BusinFlowTask::getOperator_no, baseUser.getStaff_no())
                    .eq(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_AUDITING);
            List<BusinFlowTask> list = businFlowTaskService.list(wrapper);
            if (CollectionUtil.isNotEmpty(list)) {
                return Result.fail(ErrorEnum.AUDIT_EXIST_OTHER_PENDING_TASK.getValue(), ErrorEnum.AUDIT_EXIST_OTHER_PENDING_TASK.getDesc());
            }
        }
        //调用认领接口
        if (baseUser.getUser_role().contains(UserRoleConstant.manual_claim_role) &&
                PropertySource.getBoolean(PropKeyConstant.WSKH_ISOPEN_DISPATCH) && null != dealAuditTask.getOperator_status() && !dealAuditTask.getOperator_status()) {
            if (StringUtils.isBlank(businFlowTask.getOperator_no()) && FlowStatusConst.AUDIT_PENDING.equals(businFlowTask.getTask_status())) {
                ClaimTaskForm claimTaskForm = new ClaimTaskForm();
                claimTaskForm.setSerial_id(businFlowTask.getSerial_id());
                claimTaskForm.setOperator_no(baseUser.getStaff_no());
                claimTaskForm.setOperator_name(baseUser.getUser_name());
                claimTaskForm.setPop_msg_model("manual");
                return mybusinFlowTaskService.claimTask(claimTaskForm, baseUser.getStaff_no());
            }
        }
        return Result.success();
    }

    private void checkTask(String taskId, String staff_no) {
        Assert.notNull(taskId, "taskId cannot be null");
        BusinFlowTask task = businFlowTaskService.getById(taskId);
        Assert.notNull(task, "BusinFlowTask not null by  taskId");
        // 双向视频不验证任务状态和任务审核人
        if (StringUtils.equals(task.getVideo_type(), WskhConstant.VIDEO_TYPE_2)) {
            return;
        }
        Assert.isTrue(StringUtils.equals(task.getTask_status(), FlowStatusConst.AUDIT_AUDITING), "该任务不再审核中");
        Assert.isTrue(StringUtils.equals(task.getOperator_no(), staff_no), "该任务审核人不是自己");
    }

    /**
     * 审核通过后操作
     *
     * @param busin_type
     * @param task
     */
    private void auditTaskAfterHandle(String busin_type, BusinFlowTask task) {
        aiAuditThreadPoolTaskExecutor.execute(() -> {
            if (!ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, busin_type)) {
                AuditResultNotifyReq auditResultNotifyReq = new AuditResultNotifyReq();
                auditResultNotifyReq.setRequest_no(task.getRequest_no());
                auditResultNotifyReq.setAnode_id(task.getTask_type());
                auditResultNotifyReq.setHandle_type("auditPassTask");
                auditResultNotifyReq.setFlowtask_id(task.getSerial_id());
                auditResultNotifyReq.setTask_id(task.getTask_id());
                auditCallbackService.auditResultNotify(auditResultNotifyReq);
                // 双向视频-100058 视频类型为2
                if (StringUtils.equals(busin_type, WskhConstant.BUSIN_TYPE_NORMAL) &&
                        StringUtils.equals(task.getVideo_type(), WskhConstant.VIDEO_TYPE_2) &&
                        StringUtils.equalsAny(task.getTask_type(), TaskTypeEnum.AUDIT.getCode())) {
                    log.info("见证通过，双向视频-复核智能审核相关数据开始! request_no={} busin_type={}", task.getRequest_no(), busin_type);
                    aiAuditAchieveService.againExecuteAiaudit(task.getRequest_no(), "4");
                    aiAuditAchieveService.againExecuteAiaudit(task.getRequest_no(), "5");
                    log.info("见证通过，双向视频-复核智能审核相关数据结束! request_no={} busin_type={}", task.getRequest_no(), busin_type);
                }
            } else {
                // 见证
                if (StringUtils.equalsAny(task.getTask_type(), TaskTypeEnum.AUDIT.getCode())) {
                    log.info("见证通过，网厅业务办理-复核智能审核相关数据开始! request_no={} busin_type={}", task.getRequest_no(), busin_type);
                    aiAuditAchieveService.handAgainExecuteAiaudit(task.getRequest_no(), "4");
                    aiAuditAchieveService.handAgainExecuteAiaudit(task.getRequest_no(), "5");
                    log.info("见证通过，网厅业务办理-复核智能审核相关数据结束! request_no={} busin_type={}", task.getRequest_no(), busin_type);
                }
            }
        });
    }


    /**
     * 开关前的逻辑
     *
     * @param flowRequest
     * @param currentTask
     * @param clobContentInfo
     * @param must_pass_reason
     * @return
     */
    private CheckBeforeMessage oldCheckBeforeMessage(BusinFlowRequest flowRequest,
                                                     BusinFlowTask currentTask,
                                                     ClobContentInfo clobContentInfo,
                                                     String must_pass_reason) {
        String scores = PropertySource.get(PropKeyConstant.WSKH_ZD_SCORE_CONFIDENCE_RANGE, "60,75");
        String scores_zt = PropertySource.get(PropKeyConstant.WSKH_ZT_SCORE_CONFIDENCE_RANGE, "40,60");
        String[] scoreArr = scores.split(",");
        int minScore = Integer.parseInt(scoreArr[0]);
        int maxScore = Integer.parseInt(scoreArr[1]);
        int minScore_zt = Integer.parseInt(scores_zt.split(",")[0]);
        int maxScore_zt = Integer.parseInt(scores_zt.split(",")[1]);

        int face_score = StringUtils.isNotBlank(clobContentInfo.getFace_score()) ? Double.valueOf(clobContentInfo.getFace_score()).intValue() : 0;
        int face_score_82_80 = StringUtils.isNotBlank(clobContentInfo.getFace_score_82_80()) ? Double.valueOf(clobContentInfo.getFace_score_82_80()).intValue() : -1;
        int kh_face_score_82_80 = StringUtils.isNotBlank(clobContentInfo.getKh_face_score_82_80()) ? Double.valueOf(clobContentInfo.getKh_face_score_82_80()).intValue() : -1;

        String fail_message = "";
        String weak_fail_message = "";
        // 100058、100300以及网厅业务办理-个人身份证、机构身份证
        if (StringUtils.equalsAny(flowRequest.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL) ||
                (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, flowRequest.getBusin_type()) &&
                        StringUtils.equalsAny(IdKindEnum.ID_CARD.getCode(), clobContentInfo.getId_kind(), clobContentInfo.getAgent_id_kind()))) {
            switch (currentTask.getTask_type()) {
                case FlowNodeConst.AUDIT:
                    if (StringUtils.isNotBlank(clobContentInfo.getFile_82())) {//判断腾讯分数
                        if (face_score_82_80 >= 0) {
                            if (face_score_82_80 < minScore && !(face_score_82_80 == 0 && (kh_face_score_82_80 == -1 || kh_face_score_82_80 >= maxScore))) {//校验腾讯分数
                                if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, flowRequest.getBusin_type())
                                        || StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                                    weak_fail_message = "请填写分数过低强制通过的原因";
                                } else {
                                    fail_message = "公安头像人脸识别分数过低，不允许通过";
                                }
                            } else if (face_score_82_80 < maxScore && StringUtils.isBlank(must_pass_reason)) {
                                weak_fail_message = "请填写分数过低强制通过的原因";
                            }
                        }
                    } else {//判断证通分数
                        if (face_score < minScore_zt) {
                            if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, flowRequest.getBusin_type())
                                    || StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                                weak_fail_message = "请填写分数过低强制通过的原因";
                            } else {
                                fail_message = "证通低于" + minScore_zt + "分无法通过";
                            }
                        } else if (face_score < maxScore_zt && StringUtils.isBlank(must_pass_reason)) {
                            weak_fail_message = "请填写分数过低强制通过的原因";
                        }
                    }
                    break;
                case FlowNodeConst.REVIEW:
                    boolean isopenEmergency = "1".equals(PropertySource.get(PropKeyConstant.WSKH_ISOPEN_EMERGENCY_CHANNEL, "0"));
                    if (StringUtils.isNotBlank(clobContentInfo.getFile_82())) {
                        if (face_score_82_80 >= 0) {
                            if (face_score_82_80 < maxScore && face_score_82_80 >= minScore && StringUtils.isBlank(must_pass_reason)) {
                                weak_fail_message = "请填写分数过低强制通过的原因";
                            } else if (face_score_82_80 < minScore && !(face_score_82_80 == 0 && (kh_face_score_82_80 == -1 || kh_face_score_82_80 >= maxScore))) {
                                if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, flowRequest.getBusin_type())
                                        || StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                                    weak_fail_message = "请填写分数过低强制通过的原因";
                                } else {
                                    fail_message = "公安头像人脸识别分数过低，不允许通过";
                                }
                            }
                        }
                    } else if (isopenEmergency) {
                        String openChannel = PropertySource.get(PropKeyConstant.WSKH_SPECIA_CHANNEL_OPEN, " ");
                        String businType = PropertySource.get(PropKeyConstant.WSKH_SPECIA_BUSIN_TYPE_OPEN, " ");
                        // 特殊渠道&特殊业务类型为空
                        if (StringUtils.isBlank(openChannel) && StringUtils.isBlank(businType)) {
                            // 判断证通分数
                            if (face_score < maxScore_zt) {
                                if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, flowRequest.getBusin_type())
                                        || StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                                    weak_fail_message = "请填写分数过低强制通过的原因";
                                } else {
                                    fail_message = "证通低于" + maxScore_zt + "分无法通过";
                                }
                            }
                        } else {
                            boolean specialOpen = ("," + PropertySource.get(PropKeyConstant.WSKH_SPECIA_CHANNEL_OPEN, "0") + ",").contains("," + clobContentInfo.getOpen_channel() + ",");
                            boolean businTypeBool = ("," + PropertySource.get(PropKeyConstant.WSKH_SPECIA_BUSIN_TYPE_OPEN, "0") + ",").contains("," + clobContentInfo.getBusin_type() + ",");
                            // 特殊渠道or特殊业务类型不为空，但均不包含当前申请渠道或业务类型
                            if (!specialOpen && !businTypeBool) {
                                if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, flowRequest.getBusin_type())
                                        || StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                                    weak_fail_message = "请填写分数过低强制通过的原因";
                                } else {
                                    fail_message = "无公安部头像，不允许通过，请中登时间刷新头像后进行审核";
                                }
                            } else {
                                // 判断证通分数
                                if (face_score < maxScore_zt) {
                                    if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, flowRequest.getBusin_type())
                                            || StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                                        weak_fail_message = "请填写分数过低强制通过的原因";
                                    } else {
                                        fail_message = "证通低于" + maxScore_zt + "分无法通过";
                                    }
                                }
                            }
                        }
                    } else {
                        // 网厅 或者 港澳台通行证 无需验证公安头像
                        if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, flowRequest.getBusin_type())
                                || StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                            weak_fail_message = "请填写分数过低强制通过的原因";
                        } else {
                            fail_message = "无公安部头像，不允许通过，请中登时间刷新头像后进行审核";
                        }
                    }
                    break;
                case FlowNodeConst.SECONDARY_REVIEW:
                    if (StringUtils.isNotBlank(clobContentInfo.getFile_82())) {
                        if (face_score_82_80 >= 0) {
                            if (face_score_82_80 < maxScore && face_score_82_80 >= minScore && StringUtils.isBlank(must_pass_reason)) {
                                weak_fail_message = "请填写分数过低强制通过的原因";
                            } else if (face_score_82_80 < minScore && !(face_score_82_80 == 0 && (kh_face_score_82_80 == -1 || kh_face_score_82_80 >= maxScore))) {
                                if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, flowRequest.getBusin_type())
                                        || StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                                    weak_fail_message = "请填写分数过低强制通过的原因";
                                } else {
                                    fail_message = "公安头像人脸识别分数过低，不允许通过";
                                }
                            }
                        }
                    }
                    break;
            }
        }
        return new CheckBeforeMessage(fail_message, weak_fail_message);
    }

    /**
     * 开关前的逻辑
     *
     * @param clobContentInfo
     * @return
     */
    private CheckBeforeMessage newCheckBeforeMessage(ClobContentInfo clobContentInfo,
                                                     String must_pass_reason) {
        String fail_message = "";
        String weak_fail_message = "";
        String busin_type = clobContentInfo.getBusin_type();
        if (StringUtils.isBlank(clobContentInfo.getScore_type())) {
            if (StringUtils.isBlank(clobContentInfo.getFace_score_82_80()) && StringUtils.isBlank(clobContentInfo.getFace_score())) {
                if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, busin_type)) {
                    weak_fail_message = "请填写分数过低强制通过的原因";
                } else {
                    fail_message = "请刷新公安照，然后在进行审核";
                }
                return new CheckBeforeMessage(fail_message, weak_fail_message);
            }
            if (StringUtils.isNotBlank(clobContentInfo.getFace_score_82_80()) && !StringUtils.equals(clobContentInfo.getFace_score_82_80(), "100")) {
                if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, busin_type)) {
                    weak_fail_message = "请填写分数过低强制通过的原因";
                } else {
                    fail_message = "人脸比对不一致，不允许通过";
                }
                return new CheckBeforeMessage(fail_message, weak_fail_message);
            }
            if (StringUtils.isNotBlank(clobContentInfo.getFace_score())) {
                return check_zhongtong(clobContentInfo.getFace_score(), must_pass_reason, busin_type);
            }
        }
        // 中登校验
        if (StringUtils.equals(clobContentInfo.getScore_type(), "1") && !StringUtils.equals(clobContentInfo.getFace_score_82_80(), "100")) {
            if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, busin_type)) {
                weak_fail_message = "请填写分数过低强制通过的原因";
            } else {
                fail_message = "人脸比对不一致，不允许通过";
            }
            return new CheckBeforeMessage(fail_message, weak_fail_message);
        }
        if (StringUtils.equals(clobContentInfo.getScore_type(), "2")) {
            // 证通校验
            return check_zhongtong(clobContentInfo.getFace_score(), must_pass_reason, busin_type);
        }
        return new CheckBeforeMessage(fail_message, weak_fail_message);
    }

    private CheckBeforeMessage check_zhongtong(String face_score_str, String must_pass_reason, String busin_type) {
        String fail_message = "";
        String weak_fail_message = "";
        String scores_zt = PropertySource.get(PropKeyConstant.WSKH_ZT_SCORE_CONFIDENCE_RANGE, "40,60");
        int minScore_zt = Integer.parseInt(scores_zt.split(",")[0]);
        int maxScore_zt = Integer.parseInt(scores_zt.split(",")[1]);
        int face_score = StringUtils.isNotBlank(face_score_str) ? Double.valueOf(face_score_str).intValue() : 0;
        if (face_score < minScore_zt) {
            if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, busin_type)) {
                weak_fail_message = "请填写分数过低强制通过的原因";
            } else {
                fail_message = "证通低于" + minScore_zt + "分无法通过";
            }
        } else if (face_score < maxScore_zt && StringUtils.isBlank(must_pass_reason)) {
            weak_fail_message = "请填写分数过低强制通过的原因";
        }
        return new CheckBeforeMessage(fail_message, weak_fail_message);
    }
}

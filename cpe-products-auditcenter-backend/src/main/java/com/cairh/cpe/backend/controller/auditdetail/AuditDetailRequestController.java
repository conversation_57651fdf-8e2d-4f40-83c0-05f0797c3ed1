package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.form.req.BusinFlowRequestEditReq;
import com.cairh.cpe.backend.service.IAuditDetailRequestService;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("auditDetailRequest")
public class AuditDetailRequestController {

    @Resource
    private IAuditDetailRequestService auditDetailRequestService;

    @PostMapping("edit/{requestNo}")
    public Result<Boolean> updateRequest(@PathVariable("requestNo") String requestNo,
                                         @RequestBody BusinFlowRequestEditReq req) {
        return Result.success(auditDetailRequestService.updateRequest(requestNo, req));
    }
}

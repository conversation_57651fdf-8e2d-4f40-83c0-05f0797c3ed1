package com.cairh.cpe.backend.controller.common;

import com.cairh.cpe.backend.form.req.DictionaryForm;
import com.cairh.cpe.backend.form.req.QueryBranchForm;
import com.cairh.cpe.backend.form.req.UploadImageForm;
import com.cairh.cpe.backend.form.resp.DictBranchResult;
import com.cairh.cpe.backend.form.resp.DictResult;
import com.cairh.cpe.backend.service.IAuditCenterCommonService;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.esb.component.elect.IEsbComponentElectDubboService;
import com.cairh.cpe.esb.component.elect.dto.req.ElectDownloadFileRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectDownloadFileResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.Optional;

/**
 * 通用服务
 */
@Slf4j
@RestController
@RequestMapping("auditCenterCommon")
public class AuditCenterCommonController {

    @Autowired
    private IAuditCenterCommonService auditCenterCommonService;

    @DubboReference(check = false)
    private IEsbComponentElectDubboService componentElectDubboService;


    /**
     * 图像base64 上传
     */
    @PostMapping("uploadBase64Image")
    public Result<String> uploadBase64Image(@Validated @RequestBody UploadImageForm uploadImageForm) {
        String fileRecordId = auditCenterCommonService.uploadBase64Image(uploadImageForm.getImage_data());

        return Result.success(fileRecordId);
    }

    /**
     * 图像file 上传
     */
    @PostMapping("uploadFileImage")
    public Result<String> uploadFileImage(@RequestPart MultipartFile imageFile) {
        String fileRecordId = auditCenterCommonService.uploadFileImage(imageFile);

        return Result.success(fileRecordId);
    }

    /**
     * 资源下载
     */
    @GetMapping("authless/downloadResource/{fileId}")
    public ResponseEntity<InputStreamResource> downloadResource(@PathVariable String fileId) {
        ElectDownloadFileRequest electDownloadFileRequest = new ElectDownloadFileRequest();
        electDownloadFileRequest.setFilerecord_id(fileId);
        ElectDownloadFileResponse electDownloadFileResponse = componentElectDubboService.electDownloadFile(electDownloadFileRequest);

        Optional<MediaType> mediaType = MediaTypeFactory.getMediaType(electDownloadFileResponse.getFileName());
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept-Ranges", "bytes");
        headers.setContentLength(electDownloadFileResponse.getFile().length);
        return ResponseEntity
                .ok()
                .headers(headers)
                .contentType(mediaType.isPresent() ? mediaType.get() : MediaType.APPLICATION_OCTET_STREAM)
                .body(new InputStreamResource(new ByteArrayInputStream(electDownloadFileResponse.getFile())));
    }

    /**
     * 字典查询
     */
    @PostMapping(value = "queryByDictCode")
    public Result<List<DictResult>> queryByDictCode(@Validated @RequestBody DictionaryForm dictionaryForm) {

        List<DictResult> res = auditCenterCommonService.getDictionaryList(dictionaryForm.getDictCode());
        return Result.success(res);
    }


    /**
     * 获取营业部列表  branch_type  传2 分公司 不传 所属营业部
     */
    @PostMapping(value = "queryBranch")
    public Result<List<DictBranchResult>> queryBranch(@Validated @RequestBody QueryBranchForm queryBranchForm) {

        List<DictBranchResult> res = auditCenterCommonService.getBranchList(queryBranchForm.getBranch_type());
        return Result.success(res);
    }

}

package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.common.service.IBusinFlowRecordService;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("auditDetailRecord")
public class AuditDetailRecordController {

    @Resource
    private IBusinFlowRecordService businFlowRecordService;



    @PostMapping("delete/{serial_id}")
    public Result<Boolean> deleteRecord(@PathVariable("serial_id") String serial_id) {
        return Result.success(businFlowRecordService.removeById(serial_id));
    }


}

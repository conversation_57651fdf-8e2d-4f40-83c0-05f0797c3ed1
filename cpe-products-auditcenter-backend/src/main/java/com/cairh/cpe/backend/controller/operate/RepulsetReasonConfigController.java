package com.cairh.cpe.backend.controller.operate;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.RectificationReasonForm;
import com.cairh.cpe.backend.form.req.RepulsetReasonAddInitForm;
import com.cairh.cpe.backend.form.req.RepulsetReasonChildren;
import com.cairh.cpe.backend.form.req.RepulsetReasonConfigForm;
import com.cairh.cpe.backend.form.resp.RepulsetReasonConfigResult;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.common.constant.DicConstant;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.RepulsetReasonConfig;
import com.cairh.cpe.common.entity.request.ReasonGroupLImitReq;
import com.cairh.cpe.common.entity.request.RectificationReasonReq;
import com.cairh.cpe.common.entity.request.RepulsetReasonBatchForm;
import com.cairh.cpe.common.entity.response.DictInfo;
import com.cairh.cpe.common.entity.response.NotAllowReasonGroup;
import com.cairh.cpe.common.entity.response.RepulsetReasonConfigQueryRes;
import com.cairh.cpe.common.service.IRepulsetReasonConfigService;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.common.util.SqlDateUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.db.config.IdGenerator;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 驳回原因配置
 */
@Slf4j
@RestController
@RequestMapping("repulsetReasonConfig")
public class RepulsetReasonConfigController {

    @Autowired
    private IRepulsetReasonConfigService repulsetReasonConfigService;
    @Resource
    private IdGenerator idGenerator;
    @Autowired
    private CacheDict cacheDict;

    /**
     * 驳回原因查询列表
     */
    @RequestMapping("queryReasonConfigList")
    public Result<Page<RepulsetReasonConfigResult>> queryReasonConfigList(@Validated @RequestBody RectificationReasonForm rectificationReasonForm) {

        RectificationReasonReq rectificationReasonReq = new RectificationReasonReq();
        BeanUtil.copyProperties(rectificationReasonForm, rectificationReasonReq);
        List<DictInfo> dictInfos = cacheDict.getDictListByDictCode(DicConstant.REJECT_REASON_GROUP);
        Page<RepulsetReasonConfigQueryRes> list = repulsetReasonConfigService.selectRectificationReasonListByPage(rectificationReasonReq, dictInfos);
        //对象转换
        Page<RepulsetReasonConfigResult> pageResult = new Page<RepulsetReasonConfigResult>();
        BaseBeanUtil.copyProperties(list, pageResult);
        List<RepulsetReasonConfigResult> RepulsetReasonConfigResults = new ArrayList<>();
        for (RepulsetReasonConfigQueryRes record : list.getRecords()) {
            RepulsetReasonConfigResult repulsetReasonConfigResult = new RepulsetReasonConfigResult();
            BaseBeanUtil.copyProperties(record, repulsetReasonConfigResult);
            RepulsetReasonConfigResults.add(repulsetReasonConfigResult);
        }
        pageResult.setRecords(RepulsetReasonConfigResults);
        return Result.success(pageResult);
    }


    /**
     * 驳回原因单条数据查询
     */
    @RequestMapping("queryReasonConfigById")
    public Result<RepulsetReasonConfigQueryRes> queryReasonConfigById(@Validated @RequestBody RectificationReasonForm rectificationReasonForm) {
        RepulsetReasonConfig byId = repulsetReasonConfigService.getById(rectificationReasonForm.getSerial_id());
        RepulsetReasonConfigQueryRes res = new RepulsetReasonConfigQueryRes();
        BaseBeanUtil.copyProperties(byId, res);
        LambdaQueryWrapper<RepulsetReasonConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RepulsetReasonConfig::getUn_serial_id, byId.getSerial_id());
        wrapper.orderByAsc(RepulsetReasonConfig::getCause_name);
        List<RepulsetReasonConfig> list = repulsetReasonConfigService.list(wrapper);
        res.setChildren_config(list);
        return Result.success(res);
    }

    @RequestMapping("batchAddReasonConfig")
    public Result<Integer> batchAddReasonConfig(@AuthenticationPrincipal BaseUser baseUser,
                                                @Validated @RequestBody RepulsetReasonBatchForm batchForm) {
        boolean hasDuplicates = batchForm.getCause_children().stream().map(chil -> chil.getCause_name())
                .collect(Collectors.groupingBy(n -> n, Collectors.counting()))
                .values()
                .stream()
                .anyMatch(count -> count > 1);
        if (hasDuplicates) {
            return Result.fail("存在相同的驳回原因名称，请重新添加");
        }
        boolean contentDuplicates = batchForm.getCause_children().stream().map(chil -> chil.getCause_content())
                .collect(Collectors.groupingBy(n -> n, Collectors.counting()))
                .values()
                .stream()
                .anyMatch(count -> count > 1);
        if (contentDuplicates) {
            return Result.fail("存在相同的驳回原因内容，请重新添加");
        }
        return Result.success(repulsetReasonConfigService.batchAddReasonConfig(baseUser, batchForm));
    }

    /**
     * 驳回原因新增保存
     */
    @RequestMapping("addOrUpdateReasonConfig")
    public Result<String> addOrUpdateReasonConfig(@AuthenticationPrincipal BaseUser baseUser,
                                                  @Validated @RequestBody RepulsetReasonConfigForm repulsetReasonConfigForm) {
        //校验规则
        List<RepulsetReasonChildren> children = repulsetReasonConfigForm.getCause_children();
        if (CollectionUtils.isNotEmpty(children)) {
            boolean hasDuplicates = children.stream().map(chil -> chil.getCause_name())
                    .collect(Collectors.groupingBy(n -> n, Collectors.counting()))
                    .values()
                    .stream()
                    .anyMatch(count -> count > 1);
            if (hasDuplicates) {
                return Result.fail("存在相同的驳回原因名称，请重新添加");
            }
            boolean contentDuplicates = children.stream().map(chil -> chil.getCause_content())
                    .collect(Collectors.groupingBy(n -> n, Collectors.counting()))
                    .values()
                    .stream()
                    .anyMatch(count -> count > 1);
            if (contentDuplicates) {
                return Result.fail("存在相同的驳回原因内容，请重新添加");
            }
        }
        //新增
        Date date = new Date();
        if (StringUtils.isBlank(repulsetReasonConfigForm.getSerial_id())) {
            RepulsetReasonConfig queryReasonConfig = new RepulsetReasonConfig();
            BeanUtil.copyProperties(repulsetReasonConfigForm, queryReasonConfig);
            String serial_id = idGenerator.nextUUID(null);
            queryReasonConfig.setSerial_id(serial_id);
            queryReasonConfig.setCreate_by(baseUser.getStaff_no());
            queryReasonConfig.setCreate_datetime(date);
            queryReasonConfig.setUpdate_datetime(date);
            queryReasonConfig.setUpdate_by(baseUser.getStaff_no());
            queryReasonConfig.setOrder_no(getOrderNo());
            SqlDateUtil.setDefaultValue(queryReasonConfig);
            repulsetReasonConfigService.save(queryReasonConfig);

            List<RepulsetReasonConfig> configList = repulsetReasonConfigForm.getCause_children()
                    .stream().map(item -> {
                        RepulsetReasonConfig addReasonConfig = new RepulsetReasonConfig();
                        addReasonConfig.setUpdate_datetime(date);
                        addReasonConfig.setUpdate_by(baseUser.getStaff_no());
                        addReasonConfig.setCreate_datetime(date);
                        addReasonConfig.setCreate_by(baseUser.getStaff_no());
                        addReasonConfig.setUn_serial_id(serial_id);
                        addReasonConfig.setUpdate_datetime(new Date());
                        addReasonConfig.setBusin_type(queryReasonConfig.getBusin_type());
                        addReasonConfig.setSerial_id(idGenerator.nextUUID(null));
                        addReasonConfig.setCause_group(queryReasonConfig.getCause_group());
                        addReasonConfig.setAudit_type(queryReasonConfig.getAudit_type());
                        addReasonConfig.setAnode_id(queryReasonConfig.getAnode_id());
                        addReasonConfig.setCause_name(item.getCause_name());
                        addReasonConfig.setCause_content(item.getCause_content());
                        return addReasonConfig;
                    }).collect(Collectors.toList());
            repulsetReasonConfigService.saveBatch(configList);
            return Result.success();
        }

        List<String> serialIds = children.stream().filter(e -> StringUtils.isNotBlank(e.getSerial_id())).map(RepulsetReasonChildren::getSerial_id).collect(Collectors.toList());
        //更新操作
        RepulsetReasonConfig reasonConfig = repulsetReasonConfigService.getById(repulsetReasonConfigForm.getSerial_id());
        LambdaQueryWrapper<RepulsetReasonConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RepulsetReasonConfig::getUn_serial_id, repulsetReasonConfigForm.getSerial_id());
        List<RepulsetReasonConfig> oldList = repulsetReasonConfigService.list(wrapper);
        if (CollectionUtils.isNotEmpty(oldList)) {
            // 更新操作删除不存在的
            List<String> delList = oldList.stream().filter(item -> !serialIds.contains(item.getSerial_id())).map(RepulsetReasonConfig::getSerial_id).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(delList)) {
                repulsetReasonConfigService.removeByIds(delList);
            }
        }
        //更新中存在新增的数据
        List<RepulsetReasonConfig> addList = children.stream().filter(e -> StringUtils.isBlank(e.getSerial_id())).map(item -> {
            RepulsetReasonConfig editReasonConfig = new RepulsetReasonConfig();
            editReasonConfig.setSerial_id(idGenerator.nextUUID(null));
            editReasonConfig.setCause_group(reasonConfig.getCause_group());
            editReasonConfig.setAudit_type(reasonConfig.getAudit_type());
            editReasonConfig.setAnode_id(reasonConfig.getCause_group());
            editReasonConfig.setCause_name(item.getCause_name());
            editReasonConfig.setCause_content(item.getCause_content());
            editReasonConfig.setUn_serial_id(reasonConfig.getSerial_id());
            editReasonConfig.setBusin_type(reasonConfig.getBusin_type());
            editReasonConfig.setUpdate_datetime(date);
            editReasonConfig.setUpdate_by(baseUser.getStaff_no());
            editReasonConfig.setCreate_datetime(date);
            editReasonConfig.setCreate_by(baseUser.getStaff_no());
            return editReasonConfig;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addList)) {
            repulsetReasonConfigService.saveBatch(addList);
        }

        //更新中存在修改的数据
        children.stream().filter(e -> StringUtils.isNotBlank(e.getSerial_id())).forEach(item -> {
            LambdaUpdateWrapper<RepulsetReasonConfig> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(StringUtils.isNotBlank(item.getCause_name()), RepulsetReasonConfig::getCause_name, item.getCause_name());
            updateWrapper.set(StringUtils.isNotBlank(item.getCause_content()), RepulsetReasonConfig::getCause_content, item.getCause_content());
            updateWrapper.eq(RepulsetReasonConfig::getSerial_id, item.getSerial_id());
            repulsetReasonConfigService.update(updateWrapper);
        });

        return Result.success();
    }

    @PostMapping("addInit")
    public Result<String> addInit(@AuthenticationPrincipal BaseUser baseUser,
                                  @Validated @RequestBody RepulsetReasonAddInitForm initForm) {
        LambdaQueryWrapper<RepulsetReasonConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RepulsetReasonConfig::getBusin_type, initForm.getTarget_busin_type());
        if (repulsetReasonConfigService.count(wrapper) > 0) {
            return Result.fail("该业务类型的驳回原因已经被初始化完成");
        }
        LambdaQueryWrapper<RepulsetReasonConfig> normalWrapper = new LambdaQueryWrapper<>();
        normalWrapper.eq(RepulsetReasonConfig::getBusin_type, initForm.getSource_busin_type());
        normalWrapper.orderByAsc(RepulsetReasonConfig::getUn_serial_id);
        List<RepulsetReasonConfig> normalList = repulsetReasonConfigService.list(normalWrapper);
        Date date = new Date();
        Map<String, String> attributesMap = Maps.newHashMap();
        normalList.stream().peek(item -> {
            String pkId = idGenerator.nextUUID(null);
            if (StringUtils.isBlank(item.getUn_serial_id())) {
                attributesMap.put(item.getSerial_id(), pkId);
            } else {
                item.setUn_serial_id(attributesMap.get(item.getUn_serial_id()));
            }
            item.setSerial_id(pkId);
            item.setBusin_type(initForm.getTarget_busin_type());
            item.setUpdate_datetime(date);
            item.setCreate_datetime(date);
            item.setCreate_by(baseUser.getStaff_no());
            item.setUpdate_by(baseUser.getStaff_no());
        }).collect(Collectors.toList());
        repulsetReasonConfigService.saveBatch(normalList);
        return Result.success();

    }

    /**
     * 驳回原因删除
     */
    @RequestMapping("deleteReasonConfig")
    public Result<String> deleteReasonConfig(@Validated @RequestBody RectificationReasonForm rectificationReasonForm) {
        LambdaQueryWrapper<RepulsetReasonConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RepulsetReasonConfig::getUn_serial_id, rectificationReasonForm.getSerial_id());
        repulsetReasonConfigService.remove(wrapper);
        repulsetReasonConfigService.removeById(rectificationReasonForm.getSerial_id());
        return Result.success();
    }

    /**
     * 添加驳回原因（原因分组限制）
     */
    @RequestMapping("reasonGroupLimit")
    public Result<List<NotAllowReasonGroup>> reasonGroupLimit(@Validated @RequestBody ReasonGroupLImitReq req) {
        List<NotAllowReasonGroup> list = repulsetReasonConfigService.reasonGroupLimit(req);
        return Result.success(list);
    }


    private Integer getOrderNo() {
        LambdaQueryWrapper<RepulsetReasonConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(RepulsetReasonConfig::getOrder_no);
        List<RepulsetReasonConfig> list = repulsetReasonConfigService.list(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            Integer order_no = list.get(0).getOrder_no();
            int new_order_no = order_no.intValue() + 1;
            return new_order_no;
        }
        return 0;
    }
}

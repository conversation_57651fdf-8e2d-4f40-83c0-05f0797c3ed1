package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.form.req.PopupRequest;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;


/**
 * 弹框控制
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("auditPopup")
public class AuditPopupController {

    @Autowired
    private RedissonUtil redissonUtil;

    @PostMapping("control")
    public Result<Boolean> control(@RequestBody PopupRequest request) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_POPUP_LOCK_KEY, request.getPopupKey());
        boolean isLock = redissonUtil.tryLock(lockKey, 0, 3, TimeUnit.SECONDS);
        if (!isLock) {
            log.info("Lock failed, popupKey: {}", request.getPopupKey());
            return Result.success(Boolean.FALSE);
        }
        return Result.success(Boolean.TRUE);
    }
}

package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.form.req.GetCustomModifyRecordForm;
import com.cairh.cpe.backend.service.IAuditDetailCustomModifyRecordService;
import com.cairh.cpe.businflow.entity.CustModifyRecord;
import com.cairh.cpe.businflow.entity.response.ModifyRecordResult;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 审核详情 - 客户资料修改流水
 */
@Slf4j
@RestController
@RequestMapping("auditDetailCustomModifyRecord")
public class AuditDetailCustomModifyRecordController {

    @Autowired
    private IAuditDetailCustomModifyRecordService auditDetailCustomModifyRecordService;


    /**
     * 客户资料修改流水
     */
    @PostMapping("getCustomModifyRecord")
    public Result<List<ModifyRecordResult>> getCustomModifyRecord(@Validated @RequestBody GetCustomModifyRecordForm getCustomModifyRecordForm) {
        List<ModifyRecordResult> resultList = auditDetailCustomModifyRecordService.getUserModifyRecord(getCustomModifyRecordForm.getRequest_no())
                .stream().filter(item-> StringUtils.isNotBlank(item.getOperator_no()))
                .map(item->{
                    item.setModify_item_centent(item.getModify_item());
                    ModifyRecordResult modifyRecordResult = new ModifyRecordResult();
                    BaseBeanUtil.copyProperties(item,modifyRecordResult);
                    return modifyRecordResult;
                })
                .collect(Collectors.toList());;
        return Result.success(resultList);
    }
}

package com.cairh.cpe.backend.controller.auditdetail;

import com.alibaba.fastjson2.JSON;
import com.cairh.cpe.backend.form.req.GetMoneyAntiInfoForm;
import com.cairh.cpe.backend.form.req.GetProfessionReasonForm;
import com.cairh.cpe.backend.form.req.MoneyAntiModifyForm;
import com.cairh.cpe.backend.form.resp.MoneyAntiInfo;
import com.cairh.cpe.backend.service.IAuditDetailMoneyAntiService;
import com.cairh.cpe.cache.service.CacheCityCode;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.request.AddressAssociationForm;
import com.cairh.cpe.common.entity.response.AddressAssociationQryResp;
import com.cairh.cpe.common.entity.support.ProvinceChildren;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;
import com.cairh.cpe.service.idverify.IAddressAssociationQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 审核详情 - 反洗钱
 */
@Slf4j
@RestController
@RequestMapping("auditDetailMoneyAnti")
public class AuditDetailMoneyAntiController {

    @Autowired
    private IAuditDetailMoneyAntiService auditDetailMoneyAntiService;

    @Autowired
    private IAddressAssociationQuery addressAssociationQuery;

    @Autowired
    private CacheCityCode cacheCityCode;

    @Autowired
    private IRequestService requestService;

    @Autowired
    private IBusinFlowTaskService businFlowTaskService;

    @Autowired
    private RedissonUtil redissonUtil;

    /**
     * 取反洗钱信息
     */
    @PostMapping("getMoneyAntiInfo")
    public Result<MoneyAntiInfo> getMoneyAntiInfo(@Validated @RequestBody GetMoneyAntiInfoForm getMoneyAntiInfoForm) {
        MoneyAntiInfo moneyAntiInfo = auditDetailMoneyAntiService.getMoneyAntiInfo(getMoneyAntiInfoForm.getRequest_no());

        return Result.success(moneyAntiInfo);
    }

    /**
     * 获取选择职业理由的范围
     */
    @PostMapping("getProfessionReason")
    public Result<MoneyAntiInfo> getProfessionReason(@Validated @RequestBody GetProfessionReasonForm getProfessionReasonForm) {
        MoneyAntiInfo moneyAntiInfo = auditDetailMoneyAntiService.getProfessionReason(getProfessionReasonForm);

        return Result.success(moneyAntiInfo);
    }

    /**
     * 审核重试
     */
    @PostMapping("auditRetry")
    public Result<List<AiAuditRuleResp>> auditRetry(@AuthenticationPrincipal BaseUser baseUser, @Validated @RequestBody MoneyAntiModifyForm getMoneyAntiInfoForm) {
//        String lockKey = String.format(LockKeyConstant.WSKH_CUST_PARAM_REQUEST_NO, getMoneyAntiInfoForm.getRequest_no());
//        boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
//        if (!isLock) {
//            return Result.fail(ErrorEnum.REDIS_TRY_LOCK_FAIL.getValue(), ErrorEnum.REDIS_TRY_LOCK_FAIL.getDesc());
//        }
//        try {
            if (StringUtils.isNotBlank(getMoneyAntiInfoForm.getTask_id())) {
                BusinFlowTask task = businFlowTaskService.getById(getMoneyAntiInfoForm.getTask_id());
                if (null != task && !StringUtils.equals(task.getTask_status(), FlowStatusConst.AUDIT_AUDITING)) {
                    throw new BizException(ErrorEnum.ALREADY_DEAL_TASK_TIP.getValue(), ErrorEnum.ALREADY_DEAL_TASK_TIP.getDesc());
                }
            }
            MoneyAntiModifyForm moneyAntiModifyForm = new MoneyAntiModifyForm();
            moneyAntiModifyForm.setOperator_no(baseUser.getStaff_no());
            moneyAntiModifyForm.setOperator_name(baseUser.getUser_name());
            //修改原因
            moneyAntiModifyForm.setAddress_modify_reason(getMoneyAntiInfoForm.getAddress_modify_reason());
            moneyAntiModifyForm.setChoose_branch_reason_modify_reason(getMoneyAntiInfoForm.getChoose_branch_reason_modify_reason());
            moneyAntiModifyForm.setChoose_profession_reason_modify_reason(getMoneyAntiInfoForm.getChoose_profession_reason_modify_reason());
            moneyAntiModifyForm.setProfession_code_modify_reason(getMoneyAntiInfoForm.getProfession_code_modify_reason());
            moneyAntiModifyForm.setWork_unit_modify_reason(getMoneyAntiInfoForm.getWork_unit_modify_reason());
            moneyAntiModifyForm.setRequest_no(getMoneyAntiInfoForm.getRequest_no());
            moneyAntiModifyForm.setAddress(getMoneyAntiInfoForm.getAddress());
            moneyAntiModifyForm.setProfession_code(getMoneyAntiInfoForm.getProfession_code());
            moneyAntiModifyForm.setWork_unit(getMoneyAntiInfoForm.getWork_unit());
            moneyAntiModifyForm.setChoose_branch_reason(getMoneyAntiInfoForm.getChoose_branch_reason());
            moneyAntiModifyForm.setChoose_profession_reason(getMoneyAntiInfoForm.getChoose_profession_reason());
            moneyAntiModifyForm.setProfession_other(getMoneyAntiInfoForm.getProfession_other());
            moneyAntiModifyForm.setChoose_profession_reason_content(getMoneyAntiInfoForm.getChoose_profession_reason_content());
            moneyAntiModifyForm.setChoose_branch_reason_content(getMoneyAntiInfoForm.getChoose_branch_reason_content());
            moneyAntiModifyForm.setModify_reason(getMoneyAntiInfoForm.getModify_reason());
            if (StringUtils.isBlank(moneyAntiModifyForm.getChoose_profession_reason())) {
                moneyAntiModifyForm.setChoose_profession_reason(moneyAntiModifyForm.getChoose_profession_reason_content());
            }
            if (StringUtils.isBlank(moneyAntiModifyForm.getChoose_branch_reason())) {
                moneyAntiModifyForm.setChoose_branch_reason(moneyAntiModifyForm.getChoose_branch_reason_content());
            }

            List<AiAuditRuleResp> aiAuditRuleResps = auditDetailMoneyAntiService.auditRetry(moneyAntiModifyForm);
            return Result.success(aiAuditRuleResps);
//        } finally {
//            redissonUtil.unlock(lockKey);
//        }
    }


    /**
     * 地址联想
     */
    @PostMapping("addressAssociation")
    public Result<List<String>> addressAssociation(@Validated @RequestBody AddressAssociationForm addressAssociationForm) {
        //转译城市和区县
        addressAssociationForm.setCity(cacheCityCode.getCityName(addressAssociationForm.getCity()));
        String district = addressAssociationForm.getDistrict();
        if (StringUtils.isNotBlank(district)) {
            addressAssociationForm.setDistrict(cacheCityCode.getRegionName(addressAssociationForm.getDistrict()));
        }
        // 获取开户渠道和用户所属营业部
        ClobContentInfo allDataByRequestNo = requestService.getAllDataByRequestNo(addressAssociationForm.getRequest_no());
        addressAssociationForm.setBranch_no(StringUtils.isNotBlank(allDataByRequestNo.getReal_branch_no()) ? allDataByRequestNo.getReal_branch_no() : allDataByRequestNo.getBranch_no());
        addressAssociationForm.setKhqd(allDataByRequestNo.getOpen_channel());
        if (StringUtils.isBlank(allDataByRequestNo.getOpen_channel())) {
            addressAssociationForm.setKhqd("JH");
        }
        log.info("地址联想的入参为:[{}]", JSON.toJSONString(addressAssociationForm));
        List<AddressAssociationQryResp> addressAssociationQryResps = addressAssociationQuery.addressAssociationQuery(addressAssociationForm);
        List<String> keyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addressAssociationQryResps)) {
            keyList = addressAssociationQryResps.stream().map(address -> address.getKey_prefix()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(keyList)) {
                keyList = keyList.stream().distinct().filter(list -> StringUtils.isNotEmpty(list)).collect(Collectors.toList());
            }
        }
        return Result.success(keyList);
    }


    /**
     * 获取所有省市区
     */
    @PostMapping("getAllCityTree")
    public Result<Map<String, Object>> addressAssociation() {
        Map<String, Object> map = new HashMap<>();
        List<ProvinceChildren> allCityTree = cacheCityCode.getAllCityTree();
        map.put("city_info", allCityTree);
        return Result.success(map);
    }


}

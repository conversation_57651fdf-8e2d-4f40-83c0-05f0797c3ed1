package com.cairh.cpe.backend.controller.repeated;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.WhiteListAdd;
import com.cairh.cpe.backend.form.req.WhiteListEdit;
import com.cairh.cpe.backend.form.req.WhiteListReq;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.constant.RedisKeyConstant;
import com.cairh.cpe.common.entity.RepeatedWhiteList;
import com.cairh.cpe.common.service.IRepeatedWhitelistService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("whitelist")
public class RepeatedWhiteListController {


    @Resource
    private IRepeatedWhitelistService repeatedWhitelistService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @RequestMapping("page")
    public Result<Page<RepeatedWhiteList>> queryLabelList(@RequestBody WhiteListReq request) {
        Page<RepeatedWhiteList> page = new Page<>(request.getCur_page(), request.getPage_size());
        LambdaQueryWrapper<RepeatedWhiteList> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(request.getKeyword()),RepeatedWhiteList::getContent,request.getKeyword());
        wrapper.eq(StringUtils.isNotBlank(request.getWhite_status()),RepeatedWhiteList::getWhite_status,request.getWhite_status());
        wrapper.orderByDesc(RepeatedWhiteList::getCreate_datetime);
        return Result.success(repeatedWhitelistService.page(page,wrapper));
    }


    @PostMapping("save")
    public Result<Boolean> save(@AuthenticationPrincipal BaseUser baseUser, @RequestBody WhiteListAdd whiteListAdd) {
        String redisKey = RedisKeyConstant.WSKH_REPEATED_WHITELIST;
        Date date = new Date();
        RepeatedWhiteList whiteList = new RepeatedWhiteList();
        whiteList.setWhite_status(Constant.STATUS_NORMAL);
        whiteList.setContent(whiteListAdd.getContent());
        whiteList.setCreate_by(baseUser.getUser_name());
        whiteList.setCreate_datetime(date);
        whiteList.setModify_by(baseUser.getUser_name());
        whiteList.setModify_datetime(date);
        repeatedWhitelistService.save(whiteList);
        redisTemplate.delete(redisKey);
        return Result.success(Boolean.TRUE);
    }



    @PostMapping("edit")
    public Result<Boolean> edit(@AuthenticationPrincipal BaseUser baseUser,@Valid @RequestBody WhiteListEdit whiteListEdit) {
        LambdaUpdateWrapper<RepeatedWhiteList> wrapper = new LambdaUpdateWrapper();
        wrapper.set(RepeatedWhiteList::getContent, whiteListEdit.getContent());
        wrapper.set(RepeatedWhiteList::getModify_by, baseUser.getUser_name());
        wrapper.set(RepeatedWhiteList::getModify_datetime, new Date());
        wrapper.eq(RepeatedWhiteList::getSerial_id, whiteListEdit.getSerial_id());
        repeatedWhitelistService.update(wrapper);
        String redisKey = RedisKeyConstant.WSKH_REPEATED_WHITELIST;
        redisTemplate.delete(redisKey);
        return Result.success(Boolean.TRUE);
    }


    @PostMapping("delete/{serial_id}")
    public Result<Boolean> delete(@PathVariable("serial_id") String serial_id) {
        repeatedWhitelistService.removeById(serial_id);
        String redisKey = RedisKeyConstant.WSKH_REPEATED_WHITELIST;
        redisTemplate.delete(redisKey);
        return Result.success(Boolean.TRUE);
    }

}

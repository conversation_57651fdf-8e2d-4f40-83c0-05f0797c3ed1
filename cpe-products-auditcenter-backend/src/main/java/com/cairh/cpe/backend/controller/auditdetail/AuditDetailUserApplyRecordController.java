package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.form.resp.BusinFlowRecordResp;
import com.cairh.cpe.backend.service.IAuditDetailUserApplyRecordService;
import com.cairh.cpe.common.entity.request.BusinFlowRecordForm;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 审核详情 - 客户历史申请记录
 */
@Slf4j
@RestController
@RequestMapping("auditDetailUserApplyRecord")
public class AuditDetailUserApplyRecordController {

    @Autowired
    private IAuditDetailUserApplyRecordService auditDetailUserApplyRecordService;


    /**
     * 取历史申请记录
     */
    @PostMapping("getUserApplyRecord")
    public Result<List<BusinFlowRecordResp>> getUserApplyRecord(@Validated @RequestBody BusinFlowRecordForm businFlowRecordForm) {
        List<BusinFlowRecordResp> businFlowRecordResps = auditDetailUserApplyRecordService.getApplyRecord(businFlowRecordForm);

        return Result.success(businFlowRecordResps);
    }
}

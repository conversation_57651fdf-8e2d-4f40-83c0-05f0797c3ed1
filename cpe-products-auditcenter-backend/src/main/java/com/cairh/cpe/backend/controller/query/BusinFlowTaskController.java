package com.cairh.cpe.backend.controller.query;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.TaskCountNum;
import com.cairh.cpe.backend.form.resp.TaskDetailInfo;
import com.cairh.cpe.backend.form.resp.TaskDetailInfoExport;
import com.cairh.cpe.backend.service.IWorkBusinFlowTaskService;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.constant.UserRoleConstant;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 我的任务查询
 */
@Slf4j
@RestController
@RequestMapping("businFlowTask")
public class BusinFlowTaskController {

    @Autowired
    private IWorkBusinFlowTaskService mybusinFlowTaskService;

    @Resource
    private RedissonUtil redissonUtil;


    /**
     * 开始派发/停止派发
     */
    @PostMapping("dealOperatorDispatchStatus")
    public Result<String> dealOperatorDispatchStatus(@AuthenticationPrincipal BaseUser baseUser, @RequestBody OperatorDispatchStatus operatbaorDispatchStatus) {

        operatbaorDispatchStatus.setOperator_no(baseUser.getStaff_no());
        operatbaorDispatchStatus.setOperator_name(baseUser.getUser_name());
        mybusinFlowTaskService.dealOperatorDispatchStatus(operatbaorDispatchStatus);
        return Result.success();

    }

    /**
     * 手动认领任务查询接口
     */
    @PostMapping("claimTaskQuery")
    public Result<Page<TaskDetailInfo>> claimTaskQuery(@AuthenticationPrincipal BaseUser baseUser, @RequestBody TaskDetailForm taskDetailForm) {
        String queryKeyWord = taskDetailForm.getQueryKeyWord();
        if (StringUtils.isBlank(queryKeyWord)) {
            //返回空数据
            Page<TaskDetailInfo> page = new Page<>(taskDetailForm.getCur_page(), taskDetailForm.getPage_size());
            return Result.success(page);
        }
        taskDetailForm.setOperator_no(baseUser.getStaff_no());
        taskDetailForm.setOperator_name(baseUser.getUser_name());
        taskDetailForm.setUser_role(baseUser.getUser_role());
        Page<TaskDetailInfo> list = mybusinFlowTaskService.claimTaskQuery(taskDetailForm);
        return Result.success(list);
    }

    /**
     * 认领接口
     */
    @PostMapping("claimTask")
    public Result<String> claimTask(@AuthenticationPrincipal BaseUser baseUser, @RequestBody ClaimTaskForm claimTaskForm) {
        claimTaskForm.setOperator_no(baseUser.getStaff_no());
        claimTaskForm.setOperator_name(baseUser.getUser_name());
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, claimTaskForm.getRequest_no());
        //转交接收
        boolean isLock = redissonUtil.tryLock(lockKey, 2, 10, TimeUnit.SECONDS);
        if (!isLock) {
            return Result.fail(ErrorEnum.AUDIT_NOT_ACQUIRED_LOCK.getValue(),
                    String.format(ErrorEnum.AUDIT_NOT_ACQUIRED_LOCK.getDesc()));
        }
        try {
            return mybusinFlowTaskService.claimTask(claimTaskForm, baseUser.getStaff_no());
        } catch (Exception e) {
            return Result.fail(ErrorEnum.AUDIT_CLAIM_ERROR.getValue(), e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    /**
     * 任务详情展示
     */
    @PostMapping("queryTaskDetail")
    public Result<Page<TaskDetailInfo>> queryTaskDetail(@AuthenticationPrincipal BaseUser baseUser, @RequestBody TaskDetailRequest taskDetailRequest) {
        if (StringUtils.isBlank(baseUser.getUser_role())) {
            return Result.success(new Page<>(taskDetailRequest.getCur_page(), taskDetailRequest.getPage_size()));
        }
        String user_role = baseUser.getUser_role();
        taskDetailRequest.setOperator_no(baseUser.getStaff_no());
        taskDetailRequest.setOperator_name(baseUser.getUser_name());
        taskDetailRequest.setUser_role(user_role);
        String operator_status = taskDetailRequest.getOperator_status();
        if (user_role.contains(UserRoleConstant.manual_claim_role) && PropertySource.getBoolean(PropKeyConstant.WSKH_ISOPEN_DISPATCH) && "false".equals(operator_status)) {
            taskDetailRequest.setManual_claim_data("1");
        }
        Page<TaskDetailInfo> list = mybusinFlowTaskService.selectTaskListByPage(taskDetailRequest);
        return Result.success(list);
    }


    /**
     * 任务统计接口
     */
    @PostMapping("countTaskNum")
    public Result<TaskCountNum> countTaskNum(@AuthenticationPrincipal BaseUser baseUser) {
        TaskCountNum taskCountNum = mybusinFlowTaskService.countTaskNum(baseUser);
        return Result.success(taskCountNum);
    }

    /**
     * 查询操作员是否在线
     */
    @PostMapping("queryOperatorStatus")
    public Result<Boolean> queryOperatorStatus(@AuthenticationPrincipal BaseUser baseUser) {
        boolean result = mybusinFlowTaskService.queryOperatorStatus(baseUser.getStaff_no());
        return Result.success(result);
    }

    /**
     * 批量回收
     */
    @PostMapping("batchRecovery")
    public Result<String> batchRecovery(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BatchRecoveryForm batchRecoveryForm) {
        batchRecoveryForm.setOperator_no(baseUser.getStaff_no());
        batchRecoveryForm.setOperator_name(baseUser.getUser_name());
        return mybusinFlowTaskService.batchRecovery(batchRecoveryForm);
    }

    /**
     * 导出我的任务-我的代办列表
     *
     * @param baseUser          当前操作的用户信息，包含员工编号、员工姓名和用户角色等信息，通过@AuthenticationPrincipal注解自动获取。
     * @param taskDetailRequest 任务详细信息请求参数，通过@RequestBody注解从请求体中获取。
     *                          该参数会被用来设定任务查询的条件，同时会记录操作人的信息。
     * @return 返回任务详情信息的分页结果，封装在Result<Page<TaskDetailInfo>>中。
     * 若查询成功，Result的success方法将返回分页信息；若有异常，则返回相应的错误信息。
     */
    @PostMapping("exportTaskList")
    public Result<List<TaskDetailInfoExport>> exportTaskList(@AuthenticationPrincipal BaseUser baseUser, @RequestBody TaskDetailRequest taskDetailRequest) {
        if (StringUtils.isBlank(baseUser.getUser_role())) {
            return Result.success(Collections.emptyList());
        }
        String user_role = baseUser.getUser_role();
        taskDetailRequest.setOperator_no(baseUser.getStaff_no());
        taskDetailRequest.setOperator_name(baseUser.getUser_name());
        taskDetailRequest.setUser_role(user_role);
        String operator_status = taskDetailRequest.getOperator_status();
        if (user_role.contains(UserRoleConstant.manual_claim_role) && PropertySource.getBoolean(PropKeyConstant.WSKH_ISOPEN_DISPATCH) && "false".equals(operator_status)) {
            taskDetailRequest.setManual_claim_data("1");
        }
        try {
            List<TaskDetailInfoExport> list = mybusinFlowTaskService.exportTaskList(taskDetailRequest);
            return Result.success(list);
        } catch (BizException e) {
            return Result.fail(e.getError_no(), e.getError_info());
        }
    }

}

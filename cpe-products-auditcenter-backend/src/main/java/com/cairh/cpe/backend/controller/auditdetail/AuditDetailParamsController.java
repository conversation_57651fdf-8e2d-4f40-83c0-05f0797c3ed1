package com.cairh.cpe.backend.controller.auditdetail;

import com.cairh.cpe.backend.service.AuditDetailParamsService;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("auditDetailParams")
public class AuditDetailParamsController {


    @Resource
    private AuditDetailParamsService auditDetailParamsService;

    @PostMapping("edit/{requestNo}")
    public Result<Boolean> editParams(@PathVariable("requestNo") String requestNo,
                                         @RequestBody Map<String, Object> paramsMap) {
        auditDetailParamsService.updateParams(requestNo, paramsMap);
        return Result.success(Boolean.TRUE);
    }


}

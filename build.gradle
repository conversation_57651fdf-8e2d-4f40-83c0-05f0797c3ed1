plugins {
    id 'io.spring.dependency-management' version '1.0.11.RELEASE' apply false
    id 'org.springframework.boot' version '2.6.15' apply false
    id 'com.github.johnrengelman.shadow' version '2.0.4' apply false
    id 'com.github.ben-manes.versions' version "0.38.0"
    id 'se.patrikerdes.use-latest-versions' version '0.2.16'
    id 'idea'
    id 'eclipse'
}

ext {
    artifactProjects = [
            project(":cpe-products-auditcenter-api")
    ]
    springCloudVersion = "2021.0.3"
}

allprojects {
    apply plugin: 'java'
    apply plugin: 'eclipse-wtp'
    apply plugin: 'java-library'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'com.github.ben-manes.versions'
    apply plugin: 'se.patrikerdes.use-latest-versions'

    group 'com.cairh'
    version '1.5'

    repositories {
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven {
            allowInsecureProtocol = true
            url 'http://maven.cairenhui.com/nexus/content/repositories/crh_dev'
        }
        maven {
            allowInsecureProtocol = true
            url 'http://maven.cairenhui.com/nexus/content/repositories/thirdparty'
        }
        maven {
            allowInsecureProtocol = true
            url 'http://maven.cairenhui.com/nexus/content/repositories/3rd_gtja'
        }
    }

    tasks.withType(JavaCompile) {
        options.encoding = "UTF-8"
        options.compilerArgs << '-parameters'
        options.compilerArgs << "-Xlint:-unchecked" << "-Xlint:-deprecation" << '-Xlint:-varargs'
    }

    jar {
        manifest {
            attributes('Manifest-Version': project.version,
                    'Implementation-Git-Version': getGitVersion(),
                    'Implementation-Time': buildTime("yyyy-MM-dd HH:mm"),
                    'Implementation-Title': project.name,
                    'Implementation-Vendor': '杭州财人汇网络股份有限公司',
                    'Implementation-Vendor-Id': 'CRH',
                    'Build-Author-Id': getAuthor(),
                    'Build-Author-Email': getAuthorEmail())
        }
    }

    test {
        useJUnitPlatform()
    }

    dependencies {
        compileOnly('org.projectlombok:lombok')
        annotationProcessor('org.projectlombok:lombok')
        annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
        testImplementation('org.junit.jupiter:junit-jupiter-api')
        testRuntimeOnly('org.junit.jupiter:junit-jupiter-engine')
    }

    configurations.all {
        exclude group: 'log4j', module: 'log4j'
        resolutionStrategy {
            eachDependency { DependencyResolveDetails details ->
                if (details.requested.group == 'org.springframework') {
                    details.useVersion "5.3.21"
                }
                if (details.requested.group == 'org.apache.dubbo') {
                    details.useVersion "3.1.11"
                }
            }

            cacheDynamicVersionsFor 0, 'seconds'
            cacheChangingModulesFor 0, 'seconds'
        }
    }

    dependencyManagement {
        imports {
            mavenBom 'com.cairh:cpe-starter-parent:0.3.161'
            mavenBom 'com.cairh:cpe-framework-infra-starter-parent:0.3.161'
        }

        dependencies {
            dependency 'com.cairh:cpe-common-backend:0.1.57'
            dependency 'com.cairh:cpe-esb-basedata-server-api:0.1.31-gt3'
            dependency 'com.cairh:cpe-esb-component-server-api:0.1.19-gj+'
            dependency 'com.cairh:cpe-aiaudit-server-api:0.0.1-38'
            dependency 'com.cairh:cpe-products-dispatch-server-api:0.1.0-16'
            dependency('com.cairh:cpe-auth:0.3.157')
            dependency 'com.cairh:cpe-job-core:0.0.1-beta.169'

            dependency 'com.baomidou:mybatis-plus-boot-starter:3.4.2'
            dependency 'com.github.jeffreyning:mybatisplus-plus:1.6.0-RELEASE'

            dependency 'com.alibaba.spring:spring-context-support:1.0.11'
            dependency 'org.hibernate:hibernate-validator:6.2.5.Final'

            dependency 'com.fasterxml.jackson.core:jackson-databind:2.13.2'
            dependency 'com.fasterxml.jackson.core:jackson-core:2.13.2'
            dependency 'com.fasterxml.jackson.core:jackson-annotations:2.13.2'

            dependency 'com.oceanbase:oceanbase-client:2.4.0'
            // 安全漏洞添加
            dependency 'org.apache.commons:commons-text:1.10.0'
            dependency 'org.apache.kafka:kafka-clients:3.4.0'

            // tongweb
            dependency 'com.gtja:tongweb-embed:7.0.E.2_P1'
            dependency 'com.gtja:tongweb-spring-boot-starter:2.x.0.RELEASE_P1'
            dependency 'com.gtja:tongweb-spring-boot-websocket:2.x.0.RELEASE'
        }
    }
}

configure(artifactProjects) { project ->
    apply from: "$rootDir/gradle/publications.gradle"
}

def static buildTime(String time) {
    def date = new Date()
    def formattedDate = date.format(time)
    return formattedDate
}

def static getGitVersion() {
    return 'git rev-parse --short HEAD'.execute().text.trim()
}

def static getAuthor() {
    return 'git config user.name'.execute().text.trim()
}

def static getAuthorEmail() {
    return 'git config user.email'.execute().text.trim()
}
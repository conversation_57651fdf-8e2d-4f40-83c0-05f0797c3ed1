package com.cairh.cpe.common.entity.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AddressAssociationForm {

    private String request_no;   //业务流水号
    private String identity_type;   //身份类别
    private String gt_zywlx;        //子业务类型
    private String TERMINAL_INFO;   //终端信息
    private String op_entrust_way;  //发起渠道
    private String id;              //发起人
    @NotBlank
    private String key;             //要提示的关键词，长度必须大于1个字符
    @NotBlank
    private String city;            //对应城市 经常居住地址的城市名称
    private String district;        //中文区县 经常居住地址的区县名称
    private String khqd;            //开户渠道
    private String branch_no;        //用户所属营业部
}

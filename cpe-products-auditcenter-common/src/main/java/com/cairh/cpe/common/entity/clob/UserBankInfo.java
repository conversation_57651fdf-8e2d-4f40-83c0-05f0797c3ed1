package com.cairh.cpe.common.entity.clob;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserBankInfo implements Serializable {

    private String bank_no;// 银行编号

    private String bank_account;// 银行账号

    private String bank_password;// 银行密码

    private String sign_flag;// 1 一步式，2两步式

    private String bank_id_kind;// 银行卡证件类型

    private String bank_mobile;// 银行预留手机号

    private String capital_mode;// 资金方式
}

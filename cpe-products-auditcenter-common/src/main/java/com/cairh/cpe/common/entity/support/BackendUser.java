package com.cairh.cpe.common.entity.support;

import lombok.Data;

import java.io.Serializable;

/**
 * 后台操作员用户
 */
@Data
public class BackendUser implements Serializable {

    // 员工号
    private String staff_no;

    // 用户姓名
    private String user_name;

    // 营业部编号
    private String branch_no;

    // 手机号码
    private String mobile_tel;

    // 电子邮箱
    private String e_mail;

    // 地址
    private String address;

    // 允许操作的营业部
    private String en_branch_nos;

    // 角色
    private String en_roles;

    // 性别
    private String gender;

    // 从业证书编号
    private String profession_cert;

    // 从业证书失效日期
    private Integer profession_cert_enddate;

    // 头像文件Id
    private String image_file_id;

    // 固定电话
    private String phone_tel;

    // 账号失效日期
    private Integer expire_enddate;

    // 限定的ip
    private String bind_ip;

    // 限定的mac
    private String bind_mac;

    // user_id
    private String user_id;
}

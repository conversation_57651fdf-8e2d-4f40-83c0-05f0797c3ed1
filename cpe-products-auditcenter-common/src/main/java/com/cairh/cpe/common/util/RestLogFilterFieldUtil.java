package com.cairh.cpe.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Set;

@Slf4j
public class RestLogFilterFieldUtil {

    public static String jsonFilterField(String requestBody) {
        if (StringUtils.isNotBlank(requestBody)) {
            try {
                if (isJson(requestBody)) {
                    JSONObject jsonObject = filterObj(JSON.parseObject(requestBody));
                    return jsonObject.toJSONString();
                }

                if (isArrayJson(requestBody)) {
                    JSONArray jsonArray = filterArray(JSON.parseArray(requestBody));
                    return jsonArray.toJSONString();
                }
                return requestBody;
            } catch (Exception e) {
                log.error("jsonFilterField error:", e);
                return "";
            }
        }
        return "";
    }


    private static JSONObject filterObj(JSONObject jsonObject) {
        JSONObject jsonObj = new JSONObject();
        if (jsonObject != null) {
            Set<Map.Entry<String, Object>> keySet = jsonObject.entrySet();
            for (Map.Entry<String, Object> entry : keySet) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (value instanceof JSONObject) {
                    jsonObj.put(key, filterObj((JSONObject) value));
                } else if (value instanceof JSONArray) {
                    jsonObj.put(key, filterArray((JSONArray) value));
                } else if (value instanceof Integer) {
                    jsonObj.put(key, value);
                } else {
                    jsonObj.put(key, replaceClobContent(key, String.valueOf(value)));
                }
            }
        }
        return jsonObj;
    }


    private static JSONArray filterArray(JSONArray jsonArray) {
        JSONArray jsonArrayNew = new JSONArray();
        for (Object object : jsonArray) {
            if (object instanceof JSONObject) {
                jsonArrayNew.add(filterObj((JSONObject) object));
            } else if (object instanceof String) {
                jsonArrayNew.add(replaceClobContent("", String.valueOf(object)));
            } else if (object instanceof Integer) {
                jsonArrayNew.add(object);
            }
        }
        return jsonArrayNew;
    }

    /**
     * 将clob字段打印出来，小于等于20，则直接打印出来，大于20，则取前10位和后10位
     */
    public static String replaceClobContent(String key, String content) {
        if (content == null || content.length() <= 500) {
            return content;
        } else {
            return content.substring(0, 10) + "***" + content.substring(content.length() - 10);
        }
    }

    public static boolean isJson(String str) {
        boolean result = false;
        if (StringUtils.isNotBlank(str)) {
            str = str.trim();
            if (str.startsWith("{") && str.endsWith("}")) {
                result = true;
            }
        }
        return result;
    }

    public static boolean isArrayJson(String str) {
        boolean result = false;
        if (StringUtils.isNotBlank(str)) {
            str = str.trim();
            if (str.startsWith("[") && str.endsWith("]")) {
                result = true;
            }
        }
        return result;
    }
}

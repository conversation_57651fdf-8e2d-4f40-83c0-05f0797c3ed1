package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.TaskReasonRecord;
import com.cairh.cpe.common.entity.clob.AuditReason;
import com.cairh.cpe.context.BaseUser;

import java.util.List;


/**
 * Description：针对表【TASKREASONRECORD(驳回流水表)】的数据库操作Service
 * Author： slx
 * Date： 2024/5/6 上午11:21
 */
public interface ITaskReasonRecordService extends IService<TaskReasonRecord> {

    void saveTaskReasonRecord(BusinFlowTask businFlowTask, List<AuditReason> reasons, BaseUser baseUser);
}

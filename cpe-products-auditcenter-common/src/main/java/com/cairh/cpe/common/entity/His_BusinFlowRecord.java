package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * his业务流程申请记录表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("His_BusinFlowRecord")
public class His_BusinFlowRecord implements Serializable {

    /**
     * 受理编号
     */
    private String request_no;

    /**
     * 流水号
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 业务状态
     */
    private String request_status;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;

    /**
     * 分支机构
     */
    private String branch_no;

    /**
     * 流程节点
     */
    private String anode_id;

    /**
     * 流水类型  0申请流水 1审核流水 2打回流水
     */
    private String record_type;

    /**
     * 业务标志
     */
    private String business_flag;

    /**
     * 业务说明
     */
    private String business_remark;

    /**
     * 数据内容
     */
    private String busi_content;


    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date tohis_datetime;


}

package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.entity.BusinFlowRequest;

import java.util.List;


/**
 * @program: cpe-products-wskh
 * @description: 业务流程申请对像
 * @author: syy
 * @create: 2022-01-19 15:25
 **/
public interface IBusinFlowRequestService extends IService<BusinFlowRequest> {

    /**
     * 根据身份证查询业务申请记录
     *
     * @param id_no 身份证编号
     * @return id_no、request_no
     */
    List<BusinFlowRequest> getBusinFlowRequestList(String id_no);

}

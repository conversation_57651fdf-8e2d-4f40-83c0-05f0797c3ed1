package com.cairh.cpe.common.entity.clob;

import lombok.Data;

import java.io.Serializable;

@Data
public class RevenueInfo implements Serializable {

    private String revenue_inmate_type;// 个人税收居民身份类别 1，2，3

    private String last_name;// 姓（英文或拼音）

    private String first_name;// 名（英文或拼音）

    private String revenue_birthday;// 出生日期

    private String revenue_birthplace_flag;// 出生地标识 境内 1、境外 0

    private String revenue_birthplace;// 出生地境内

    private String revenue_birthplace_country;//

    private String revenue_birthplace_ssq_code;//

    private String revenue_birthplace_e;// 出生地境外

    private String revenue_address_flag;// 现居住地标识 境内 1、境外

    private String revenue_address;// 现居住地境内

    private String revenue_address_e;// 现居住地境外

    private String revenue_address_country;//

    private String revenue_address_ssq_code;//

    private String nationality;// 税收居民国

    private String identify_number;// 纳税人识别号

    private String identify_number_flag;// 是否有纳税号1有，0没有

    private String no_identify_number_reason;// 无法提供纳税人识别号原因[字典] </br>0 居民国（地区）不发放纳税人识别号 </br>1 账户持有人未能取得纳税人识别号

    private String no_identify_number_remark;// 无法提供纳税人识别号具体原因

    private String revenue_nationality;// 税收居民国 多个之前用'｜'分格

    private String revenue_identifynumber;// 纳税人识别号多个之前用'｜'分格

    private String revenue_nationality1;// 税收居民国 1

    private String revenue_nationality2;// 税收居民国 2

    private String revenue_nationality3;// 税收居民国 3

    private String revenue_identifynumber1;// 纳税人识别号1

    private String revenue_identifynumber2;// 纳税人识别号2

    private String revenue_identifynumber3;// 纳税人识别号3
}

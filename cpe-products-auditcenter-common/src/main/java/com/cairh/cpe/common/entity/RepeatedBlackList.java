package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("REPEATEDBLACKLIST")
public class RepeatedBlackList {

    /**
     * 主键ID
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 黑名单内容
     */
    @TableField("content")
    private String content;


    /**
     * 黑名单状态  0 -禁用 1-启动
     */
    @TableField("black_status")
    private String black_status;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 修改人
     */
    @TableField("modify_by")
    private String modify_by;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @TableField("create_datetime")
    private Date create_datetime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @TableField("modify_datetime")
    private Date modify_datetime;


}

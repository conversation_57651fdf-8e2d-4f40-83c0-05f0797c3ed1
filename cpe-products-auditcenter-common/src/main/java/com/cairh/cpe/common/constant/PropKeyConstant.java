package com.cairh.cpe.common.constant;

public class PropKeyConstant {

    public static final String DB_TYPE = "cpe.db.id-generator.db-type";
    /**
     * PC站点格式
     */
    public static final String WSKH_PC_OPSTATION_PATTERN = "wskh.pc.opstation.pattern";
    /**
     * android 站点格式
     */
    public static final String WSKH_ANDROID_OPSTATION_PATTERN = "wskh.android.opstation.pattern";
    /**
     * ios 站点格式
     */
    public static final String WSKH_IOS_OPSTATION_PATTERN = "wskh.ios.opstation.pattern";
    /**
     * h5 站点格式
     */
    public static final String WSKH_H5_OPSTATION_PATTERN = "wskh.h5.opstation.pattern";

    /**
     * 短信验证码安全控制方式
     */
    public static final String NOTICE_ENCRYPT_METHOD = "notice.encrypt.method";

    /**
     * 手机号码验证规则表达式
     */
    public static final String WSKH_REG_MOBILE_PREFIX = "wskh.reg.mobile.prefix";
    /**
     * 验证码倒计时时间
     */
    public static final String WSKH_REG_VALIDATECODE_TIME = "wskh.reg.validatecode.time";
    /**
     * 超级短信验证码
     */
    public static final String WSKH_REG_SUPER_CODE = "wskh.reg.super.code";
    /**
     * 手机号码注册白名单
     */
    public static final String WSKH_REG_MOBILE_WHITELIST = "wskh.reg.mobile.whitelist";
    /**
     * 号码发送登录短信次数上限
     */
    public static final String WSKH_LOGINSMS_TRYLIMIT_ONEMOBILEONEDAY = "wskh.loginsms.trylimit.onemobileoneday";
    /**
     * 设备发送登录短信次数上限
     */
    public static final String WSKH_LOGINSMS_TRYLIMIT_ONEDEVICEONEDAY = "wskh.loginsms.trylimit.onedeviceoneday";
    /**
     * 号码免图形验证码发送登录短信次数上限
     */
    public static final String WSKH_LOGINSMS_TRYLIMIT_MOBILENOIMGCODE = "wskh.loginsms.trylimit.mobilenoimgcode";
    /**
     * 设备免图形验证码发送登录短信次数上限
     */
    public static final String WSKH_LOGINSMS_TRYLIMIT_DEVICENOIMGCODE = "wskh.loginsms.trylimit.devicenoimgcode";


    /**
     * 输入手机号后是否校验手机号占用情况
     */
    public static final String USER_PHNUMBER_UNIQUE_VERIFY = "user.phnumber.unique.verify";
    /**
     * 发送语音验证码之前是否先发送短信验证码
     */
    public static final String WSKH_VOICECODE_BEFORE_SMSCODE = "wskh.voicecode.before.smscode";

    /**
     * 身份证唯一性是否需要校验     说明：身份证唯一性校验, 校验该用户在账户系统中是否已经开户
     */
    public static final String WSKH_USER_UNIQUE_VERIFY = "wskh.user.unique.verify";

    /**
     * 客户信息记录已存在提示信息     说明：{0}表示客户号，{1}表示账户状态，{2}表示用户姓名，{3}表示客户热线，{4}营业部名称
     */
    public static final String WSKH_USER_EXISTS_TIP = "wskh.user.exists.tip";

    /**
     * 允许开户年龄范围     说明：例：18-60
     */
    public static final String WSKH_USER_AGE_LIMITS = "wskh.user.age.limits";

    /**
     * 网上开户用户柜台接口
     */
    public static final String INTERFACE_OOS_COUNTER_USER_BEAN_REF = "interface.oos.counter.user.bean.ref";
    /**
     * 用户职业字典
     */
    public static final String WSKH_USER_CAREER_BASEDICTIONARY = "wskh.user.career.basedictionary";
    /**
     * 基本资料页面联系地址标题
     */
    public static final String WSKH_TITLENAME_OF_ADDRESS = "wskh.titlename.of.address";
    /**
     * 开户最大年龄限制
     */
    public static final String OOSWEB_AGE_CHECK = "oosweb.age.check";
    /**
     * 限制开户说明（16岁以下）
     */
    public static final String WSKH_UNDER_16YEARS_AGE_PROMPT = "wskh.under.16years.age.prompt";
    /**
     * 限制开户说明（16≤周岁<18）
     */
    public static final String WSKH_BETWEEN_16AND18YEARS_AGE_PROMPT = "wskh.between.16and18years.age.prompt";
    /**
     * 限制开户说明（最大年龄≤周岁）
     */
    public static final String WSKH_OLDER_MAX_AGE_PROMPT = "wskh.older.max.age.prompt";
    /**
     * 超过最大年龄禁止开户
     */
    public static final String WSKH_MAXAGE_FORBID_OPEN_ACCOUNT = "wskh.maxage.forbid.open.account";
    /**
     * 数据库编码配置
     */
    public static final String DATABASE_CODE = "database.code";

    /**
     * 开户后台审核流程
     */
    public static final String WSKH_AUDIT_FLOW_MODEL = "wskh.audit.flow.model";


    /**
     * 是否开启证件识别质检
     */
    public static final String WSKH_ISUSE_IDCARD_QUALITY = "wskh.isuse.idcard.quality";
    /**
     * 档案文件临时目录路径
     */
    public static final String FILE_TEMP_PATH = "wskh.file.temp.path";

    /**
     * 每天允许风险测评的次数
     */
    public static final String WSKH_RISK_ALLOWED_NUMBER_EVERYDAY = "wskh.risk.allowed.number.everyday";
    /**
     * 最低风险等级是否允许开户
     */
    public static final String WSKH_RISK_MIN_LEVEL_ISOPENACCOUNT = "wskh.risk.min.level.isopenaccount";
    /**
     * 是否需要特殊情况确认
     */
    public static final String WSKH_RISK_CONSERVATIVE_ISCONFIRM = "wskh.risk.conservative.isconfirm";
    /**
     * 风险评测试题之间相关规则配置
     */
    public static final String WSKH_RISK_REGULATION_CONFIG = "wskh.risk.regulation.config";
    /**
     * 基本信息与题目答案对应关系
     */
    public static final String WSKH_RISK_BASEINFO_ANSWER = "wskh.risk.baseinfo.answer";


    /**
     * 开启风测试题自动充填
     */
    public static final String WSKH_RISK_AUTOFILL = "wskh.risk.autofill";
    /**
     * 风测试题自动充填是否可修改
     */
    public static final String WSKH_RISK_AUTOFILL_EDIT = "wskh.risk.autofill.edit";
    /**
     * 学历区间及对应选项id
     */
    public static final String WSKH_RISK_DEGREE_OPTIONS = "wskh.risk.degree.options";
    /**
     * 年龄区间及对应选项id
     */
    public static final String WSKH_RISK_AGE_OPTIONS = "wskh.risk.age.options";

    /**
     * 营销平台服务地址
     */
    public static final String WSKH_SNP_SERVICE_URL = "wskh.snp.service.url";
    /**
     * 顶点CRM服务地址
     */
    public static final String WSKH_LIVEBOS_SERVICE_URL = "wskh.livebos.service.url";
    /**
     * 顶点CRM服务用户名
     */
    public static final String WSKH_LIVEBOS_USER = "wskh.livebos.user";
    /**
     * 顶点CRM服务密码
     */
    public static final String WSKH_LIVEBOS_PASSWORD = "wskh.livebos.password";
    /**
     * 推送CRM触发节点
     */
    public static final String WSKH_LIVEBOS_PUSHTYPE_CRM = "wskh.livebos.pushtype.crm";

    /**
     * 西部一体化接口请求URL
     */
    public static final String WSKH_XBZQ_YTHGET_BRANCH_REQURL = "wskh.xbzq.ythGet.branch.reqUrl";
    /**
     * 西部一体化营业部查询接口请求功能号
     */
    public static final String WSKH_XBZQ_YTHGET_BRANCH_REQNO = "wskh.xbzq.ythGet.branch.reqNo";
    /**
     * 在线客服地址
     */
    public static final String WSKH_CUSTOMER_SERVICE_ADDRESS = "wskh.customer.service.address";
    /**
     * 在线客服显示需要在页面停留的时间
     */
    public static final String WSKH_CUSTOMER_SERVICE_DISPLAY_TIME = "wskh.customer.service.display.time";

    /**
     * 建行码上赢 渠道代码
     */
    public static final String WSKH_CCBMSY_CHANNEL_NO = "wskh.ccbmsy.channel.no";
    /**
     * 建行码上赢 是否向柜台提交数据
     */
    public static final String WSKH_CCBMSY_SYNCH_COUNTER = "wskh.ccbmsy.synch.counter";
    /**
     * 建行码上赢 银行编号
     */
    public static final String WSKH_CCBMSY_BANK_NO = "wskh.ccbmsy.bank.no";
    /**
     * 建行码上赢 券商代码
     */
    public static final String WSKH_CCBMSY_COMPANY_NO = "wskh.ccbmsy.company.no";
    /**
     * 建行码上赢 保证金账号
     */
    public static final String WSKH_CCBMSY_BOND_NO = "wskh.ccbmsy.bond.no";
    /**
     * 建行码上赢 报送文件存放路径
     */
    public static final String WSKH_CCBMSY_FILE_DIR = "wskh.ccbmsy.file.dir";
    /**
     * 工行码上赢 渠道代码
     */
    public static final String WSKH_ICBC_MSYING_CODE = "wskh.icbc.msying.code";
    /**
     * 工行 是否向柜台提交数据
     */
    public static final String WSKH_ICBC_MSYING_SYNCH_COUNTER = "wskh.icbc.msying.synch.counter";
    /**
     * 工行银行编号
     */
    public static final String WSKH_ICBC_MSYING_BANK_NO = "wskh.icbc.msying.bank.no";
    /**
     * 券商代码
     */
    public static final String WSKH_ICBC_MSYING_COMPANY_NO = "wskh.icbc.msying.company.no";
    /**
     * 文件存放地址
     */
    public static final String WSKH_ICBC_MSYING_FILE_DIR = "wskh.icbc.msying.file.dir";
    /**
     * 农行E码通 渠道代码
     */
    public static final String WSKH_ABCEMT_CHANNEL_NO = "wskh.abcemt.channel.no";
    /**
     * 农行E码通  是否向柜台提交数据
     */
    public static final String WSKH_ABCEMT_SYNCH_COUNTER = "wskh.abcemt.synch.counter";
    /**
     * 农行E码通 银行编号
     */
    public static final String WSKH_ABCEMT_BANK_NO = "wskh.abcemt.bank.no";
    /**
     * 农行E码通  券商代码
     */
    public static final String WSKH_ABCEMT_COMPANY_NO = "wskh.abcemt.company.no";
    /**
     * 农行E码通  报送文件存放路径
     */
    public static final String WSKH_ABCEMT_FILE_DIR = "wskh.abcemt.file.dir";

    /**
     * 宁波一码通 渠道代码
     */
    public static final String WSKH_NBYMT_CHANNEL_NO = "wskh.nbymt.channel.no";

    /**
     * 宁波一码通 银行编号
     */
    public static final String WSKH_NBYMT_BANK_NO = "wskh.nbymt.bank.no";
    /**
     * 宁波一码通活动编号
     */
    public static final String WSKH_NBYMT_ACTIVITY_NO = "wskh.nbymt.activity.no";
    /**
     * 宁波一码通券商代码
     */
    public static final String WSKH_NBYMT_COMPANY_NO = "wskh.nbymt.company.no";
    /**
     * 宁波一码通报送文件存放路径
     */
    public static final String WSKH_NBYMT_FILE_DIR = "wskh.nbymt.file.dir";

    /**
     * 中行银证通银行编号
     */
    public static final String WSKH_BOCYZT_BANK_NO = "wskh.bocyzt.bank.no";
    /**
     * 中行银证通渠道编号
     */
    public static final String WSKH_BOCYZT_CHANNEL_CODE = "wskh.bocyzt.channel.code";
    /**
     * 中行银证通券商代码
     */
    public static final String WSKH_BOCYZT_COMPANY_NO = "wskh.bocyzt.company.no";
    /**
     * 中行银证通报送文件存放路径
     */
    public static final String WSKH_BOCYZT_FILE_DIR = "wskh.bocyzt.file.dir";

    /**
     * 是否开启本地黑名单检查
     */
    public static final String WSKH_CHECK_BLACKLIST_LOCAL = "wskh.check.blacklist.local";
    /**
     * 是否开启柜台黑名单检查
     */
    public static final String WSKH_CHECK_BLACKLIST_COUNTER = "wskh.check.blacklist.counter";
    /**
     * 是否开启融资融券黑名单检测
     */
    public static final String WSKH_CHECK_BLACKLIST_RZRQLIST = "wskh.check.blacklist.rzrqlist";
    /**
     * 风险黑名单校验提示信息
     */
    public static final String WSKH_BLACKLIST_TIP = "wskh.blacklist.tip";
    /**
     * 柜台特殊客户名单拒绝提示信息
     */
    public static final String WSKH_COUNTER_SPECIALCLIENT_REGECTTIP = "wskh.counter.specialclient.regecttip";
    /**
     * 柜台特殊客户名单警示提示信息
     */
    public static final String WSKH_COUNTER_SPECIALCLIENT_CAUTIONTIP = "wskh.counter.specialclient.cautiontip";

    /**
     * 是否启用智能审核
     */
    public static final String WSKH_ISUSE_AI_AUDIT = "wskh.isuse.ai.audit";
    /**
     * 智能审核服务地址
     */
    public static final String WSKH_ISUSE_AI_AUDIT_ADDRESS = "wskh.isuse.ai.audit.address";

    /**
     * 智能识别节点
     */
    public static final String WSKH_ISUSE_AI_AUDIT_FRONT = "wskh.isuse.ai.audit.front";
    /**
     * 单向视频自述式话述
     */
    public static final String AIITEM_ONE_VIDEO_SELF = "aiitem.one.video.self";
    /**
     * 单向视频标准话述
     */
    public static final String AIITEM_ONE_VIDEO_STANDARD = "aiitem.one.video.standard";
    /**
     * 双向视频标准话述
     */
    public static final String AIITEM_TWO_VIDEO_STANDARD = "aiitem.two.video.standard";
    /**
     * 智能审核调用方式
     */
    public static final String WSKH_ISUSE_AI_AUDIT_FUNCTION = "wskh.isuse.ai.audit.function";

    /**
     * 显示中登查询功能
     */
    public static final String WSKH_CSDC_SHOW = "wskh.csdc.show";
    /**
     * 是否自动中登查询
     */
    public static final String WSKH_CSDC_AUTO_QUERY = "wskh.csdc.auto.query";
    /**
     * 开户代理机构名称
     */
    public static final String WSKH_CSDC_ACCOUNT_AGENT_NAME = "wskh.csdc.account.agent.name";
    /**
     * 中登查询结果有效时间 （分种）
     */
    public static final String WSKH_CSDC_QUERY_VALID_TIME = "wskh.csdc.query.valid.time";
    /**
     * 是否开启重点监控账户校验
     */
    public static final String WSKH_KEY_ACCOUNT_ISOPENCHACK = "wskh.key.account.isopenchack";
    /**
     * 二维码渠道开户请求地址
     */
    public static final String WSKH_CHANNEL_QRCODE_URL = "wskh.channel.qrcode.url";
    /**
     * 是否开启身份证有效期检查
     */
    public static final String WSKH_CHECK_ID_EFFECT_DATE = "wskh.check.id.effect.date";
    /**
     * 后台审核-证件有效期校验信息显示方式
     */
    public static final String WSKH_CARD_VALIDATE_ALLOW_AUDIT = "wskh.card.validate.allow.audit";
    /**
     * 有效期检查错误提示
     */
    public static final String WSKH_CHECK_ID_ERROR_TIP = "wskh.check.id.error.tip";
    /**
     * 手机实名制验证结果
     */
    public static final String WSKH_REALMOBILE_IS_PASS = "wskh.realmobile.is.pass";
    /**
     * 有无公安照
     */
    public static final String WSKH_NO_PHOTO_PASS = "wskh.no.photo.pass";
    /**
     * 强制公安认证通过
     */
    public static final String WSKH_POLICEVERIFY_USER_FLAG = "wskh.policeverify.user.flag";
    /**
     * 空开立账户
     */
    public static final String WSKH_ACCOUNT_WITHOUT = "wskh.account.without";


    /**
     * 电话回访失败后任务处理     说明：
     * [{"label":"打回到证件上传","value":"uploadArch"},{"label":"打回到视频见证","value":"video"},
     * {"label":"打回到账户选择","value":"account"},{"label":"流程断续","value":"pass"},
     * {"label":"办理失败","value":"fail"},{"label":"拒绝开户","value":"refuse"}]
     */
    public static final String WSKH_REVISIT_FAIL_HANDLE_MODE = "wskh.revisit.fail.handle.mode";


    /**
     * 开户预审模式     说明：
     */
    public static final String WSKH_PRELIMINARY_AUDIT_TYPE = "wskh.preliminary.audit.type";
    /**
     * 启用呼叫中心电话回访     说明：
     */
    public static final String WSKH_PHONE_REVISIT_CC_OPEN = "wskh.phone.revisit.cc.open";
    /**
     * 呼叫中心服务地址     说明：
     */
    public static final String WSKH_PHONE_REVISIT_CC_URL = "wskh.phone.revisit.cc.url";
    /**
     * 呼叫中心事件编号     说明：格式：开户意向回访,开户综合回访,开户整改回访,开户延后回访事件编号
     */
    public static final String WSKH_PHONE_REVISIT_CC_EVENTNO = "wskh.phone.revisit.cc.eventno";
    /**
     * 呼叫中心系统编号     说明：
     */
    public static final String WSKH_PHONE_REVISIT_CC_CALLFROM = "wskh.phone.revisit.cc.callfrom";
    /**
     * 呼叫中心重复推送最大次数     说明：
     */
    public static final String WSKH_PHONE_REVISIT_CC_PUSHMAXTIMES = "wskh.phone.revisit.cc.pushmaxtimes";
    /**
     * 呼叫中心推送白名单     说明：多个手机号使用英文逗号分隔
     */
    public static final String WSKH_PHONE_REVISIT_CC_WHITELIST = "wskh.phone.revisit.cc.whitelist";
    /**
     * 呼叫中心轮训时间范围     说明：单位：天，时间不能超过365天
     */
    public static final String WSKH_PHONE_REVISIT_CC_DAY = "wskh.phone.revisit.cc.day";

    /**
     * 开户回访任务办理页面地址     说明：呼叫中心打开回访办理页面地址
     */
    public static final String WSKH_PHONE_REVISIT_EMBED_URL = "wskh.phone.revisit.embed.url";

    /**
     * 意向回访不通过驳回原因     说明：
     */
    public static final String WSKH_PHONE_REVISIT_CC_REASON = "wskh.phone.revisit.cc.reason";

    /**
     * 是否校验分享链接重复开户     说明：
     */
    public static final String WSKH_CLOUD_SHARE_VERIFY_UNIQUE = "wskh.cloud.share.verify.unique";
    /**
     * 云分享开户数量配置     说明： 100,50  (开户申请数限制,开户成功数限制)，不配置对应项则不限制
     */
    public static final String WSKH_CLOUD_SHARE_OPEN_LIMIT = "wskh.cloud.share.open.limit";
    /**
     * 乐分享开户数量配置     说明： 100,50  (开户申请数限制,开户成功数限制)，不配置对应项则不限制
     */
    public static final String WSKH_LE_SHARE_OPEN_LIMIT = "wskh.le.share.open.limit";
    /**
     * 云分享国密SM4解密秘钥     说明：
     */
    public static final String WSKH_CLOUD_SHARE_SM4KEY = "wskh.cloud.share.sm4key";
    /**
     * 金阳光服务地址
     */
    public static final String WSKH_GOLDEN_SUNSHINE_SERVICE_URL = "wskh.golden.sunshine.service.url";
    /**
     * 预选靓号国密SM4解密秘钥
     */
    public static final String WSKH_CHOSEN_NO_SM4KEY = "wskh.chosen.no.sm4key";

    /**
     * 是否校验证件已有在途流程
     */
    public static final String WSKH_CHECK_USER_EXISTS_FLOW = "wskh.check.user.exists.flow";


    /**
     * 大智慧手机号解密密钥     说明：格式为每一组用【;】符号隔开 例 0:ghjklpoiuy;6:abcdefghij
     */
    public static final String WSKH_DZH_MOBILE_DECRYPT = "wskh.dzh.mobile.decrypt";

    /**
     * 工作单位名称检索服务地址     说明：
     */
    public static final String WSKH_COMPANY_SEARCH_SERVICE_URL = "wskh.company.search.service.url";
    /**
     * 工作单位名称检索服务token     说明：
     */
    public static final String WSKH_COMPANY_SEARCH_SERVICE_TOKEN = "wskh.company.search.service.token";
    /**
     * 是否显示OTC账户
     */
    public static final String WSKH_ISSHOW_OTCACCOUNT = "wskh.isshow.otcaccount";
    /**
     * 默认选择的OTC账户
     */
    public static final String WSKH_OTC_DEFAULT_ACCOUNT = "wskh.otc.default.account";


    /**
     * 开户营业部类型     说明：营业部类型编号，多个以逗号隔开（如：1,2）
     */
    public static final String WSKH_OPEN_BRANCH_TYPE = "wskh.open.branch.type";
    /**
     * 退休人员字典编号     说明：
     */
    public static final String WSKH_RETIREE_DICTIONARY_NUMBER = "wskh.retiree.dictionary.number";
    /**
     * 证券职业人员字典编号     说明：
     */
    public static final String WSKH_SECURITIES_PROFESSIONALS_DICTIONARY_NUMBER = "wskh.securities.professionals.dictionary.number";
    /**
     * 禁止开立A股账户的职业     说明：根据1015字典项配置，多个以逗号隔开（如：1,2）
     */
    public static final String WSKH_FORBID_STOCKACC_CAREER = "wskh.forbid.stockacc.career";
    /**
     * 退休年龄（男）     说明：
     */
    public static final String WSKH_MAN_RETIRE_AGE = "wskh.man.retire.age";
    /**
     * 退休年龄（女）     说明：
     */
    public static final String WSKH_WOMAN_RETIRE_AGE = "wskh.woman.retire.age";
    /**
     * 外国人预约柜台客户码
     */
    public static final String WSKH_FOREIGNER_CONSUMER_CODE = "wskh.foreigner.consumer.code";

    /**
     * 交易密码/资金密码长度控制
     */
    public static final String WSKH_PWD_SIZE_LIMIT = "wskh.pwd.size.limit";
    /**
     *
     */
    public static final String WSKH_PWD_PATTERN_LIMIT = "wskh.pwd.pattern.limit";
    /**
     * 是否开启密码关键信息验证
     */
    public static final String WSKH_PWD_CRUX_INFO_CHECK = "wskh.pwd.crux.info.check";
    /**
     * 光大默认开户营业部配置
     */
    public static final String WSKH_FLOW_DEFAULT_BRANCH = "wskh.flow.default.branch";
    /**
     * 无职务的字典编号
     */
    public static final String WSKH_DUTY_NO_POSITION_CODE = "wskh.duty.no.position.code";
    /**
     * 无不良诚信记录字典编号
     */
    public static final String WSKH_CREDIT_NO_DISHONEST_CODE = "wskh.credit.no.dishonest.code";
    /**
     * 开启配置【校验联系地址是否解析完整】
     */
    public static final String WSKH_CHECK_ADDRESS_ANALYSIS = "wskh.check.address.analysis";
    /**
     * 实名制违规账户名单检查
     */
    public static final String FUNC_IS_OPEN_KEY_ACCOUNT = "func.is.open.key.account";
    /**
     * 年收入显示模式
     */
    public static final String WSKH_YEARREVENUE_SHOWMODEL = "wskh.yearrevenue.showmodel";
    /**
     * 超时时间-开户未完成
     */
    public static final String WSKH_CANCEL_USER_TIMELIMIT = "wskh.cancel.user.timelimit";
    /**
     * 是否开户百度埋点     说明：
     */
    public static final String WSKH_BAIDU_TRACK_ISUSE = "wskh.baidu.track.isuse";
    /**
     * 百度埋点服务地址     说明：
     */
    public static final String WSKH_BAIDU_TRACK_SERVER_URL = "wskh.baidu.track.server.url";
    /**
     * 百度埋点服务token     说明：
     */
    public static final String WSKH_BAIDU_TRACK_SERVER_TOKEN = "wskh.baidu.track.server.token";
    /**
     * 本手机号一键登录url
     */
    public static final String WSKH_ZGYD_ONEKEYLOGIN_URL = "wskh.zgyd.onekeylogin.url";

    /**
     * 本手机号一键登录appid
     */
    public static final String WSKH_ZGYD_ONEKEYLOGIN_APPID = "wskh.zgyd.onekeylogin.appid";
    /**
     * 本手机号一键登录appkey
     */
    public static final String WSKH_ZGYD_ONEKEYLOGIN_APPKEY = "wskh.zgyd.onekeylogin.appkey";
    /**
     * 是否启用一键登录
     */
    public static final String WSKH_ZGYD_ONEKEYLOGIN_OPEN = "wskh.zgyd.onekeylogin.open";

    /**
     * 同花顺 推送开户信息接口地址
     */
    public static final String THS_USER_PUSH_URL = "wskh.ths.user.push.url";
    /**
     * 同花顺 推送开户信息券商ID
     */
    public static final String THS_USER_PUSH_QSID = "wskh.ths.user.push.qsid";
    /**
     * 同花顺 RSA公钥
     */
    public static final String THS_USER_PUSH_PUBLICKEY = "wskh.ths.user.push.publickey";
    /**
     * 同花顺渠道代码
     */
    public static final String THS_USER_PUSH_CHANNEL_CODE = "wskh.ths.user.push.channelcode";
    /**
     * 同花顺 推送开户信息应用id
     */
    public static final String THS_USER_PUSH_APP_ID = "wskh.ths.user.push.appid";
    /**
     * 同花顺接入方式
     */
    public static final String THS_USER_PUSH_EN_APP_ID = "wskh.ths.user.push.enappid";
    /**
     * 无单位无职务的职业字典编号
     */
    public static final String WSKH_OCCUPATION_NOWORK_CODES = "wskh.occupation.nowork.codes";
    /**
     * 是否允许养老金开户
     */
    public static final String WSKH_IS_PENSION_OPEN_ACCOUNT = "wskh.is.pension.open.account";
    /**
     * 养老金开户柜台模板
     */
    public static final String WSKH_PENSION_DEFAULT_MODEL_NO = "wskh.pension.default.model.no";
    /**
     * 养老金开户业务提示
     */
    public static final String SJKH_PENSION_OPEN_BUSINESS_PROMPT = "sjkh.pension.open.business.prompt";
    /**
     * 养老金开户温馨提示
     */
    public static final String SJKH_PENSION_OPEN_COZY_PROMPT = "sjkh.pension.open.cozy.prompt";

    /**
     * 是否支持港澳台证件上传
     */
    public static final String IDCARD_SUPPORT_OTHER_PLACE = "idcard.support.other.place";
    /**
     * 回调商城
     */
    public static final String WSKH_CALL_MALL_SERVER_URL = "wskh.call.mall.server.url";

    /**
     * 审核通过结果信息显示方式
     */
    public static final String WSKH_SHOW_AUDITSUBMIT_RESULTINFO = "wskh.show.auditsubmit.resultinfo";

    /**
     * 复核通过结果信息显示方式
     */
    public static final String WSKH_SHOW_REVIEWSUBMIT_RESULTINFO = "wskh.show.reviewsubmit.resultinfo";
    /**
     * 手机号码复核通过白名单
     */
    public static final String WSKH_REVIEW_MOBILE_WHITELIST = "wskh.review.mobile.whitelist";

    /**
     * 启用恒生呼叫中心     说明：
     */
    public static final String WSKH_ISUSE_HSIPCCWEB_CC = "wskh.isuse.hsipccweb.cc";
    /**
     * 恒生Webservice接口URL     说明：
     */
    public static final String WSKH_HSIPCCWEB_WEBSERVICE_URL = "wskh.hsipccweb.webservice.url";
    /**
     * 恒生私有key     说明：
     */
    public static final String WSKH_HSIPCCWEB_PRIVATEKEY = "wskh.hsipccweb.privatekey";

    /**
     * 查询CC处理结果biz_type
     * opCode=getExecuteAsvList
     * biz_type = "5550014";浙商 = "B_10031";国融 = "CRH_1001";西部 = "SJZT_001";
     */
    public static final String WSKH_HSIPCCWEB_EXECUTE_TYPE = "wskh.hsipccweb.execute.type";

    /**
     * 是否同步呼叫中心问卷答案
     */
    public static final String WSKH_HSIPCCWEB_SYNC_CC_RESULT = "wskh.hsipccweb.sync.cc.result";

    /**
     * 特殊职业不显示工作单位和职务
     */
    public static final String WSKH_PECIALWORKS_NOTSHOW_WORKUNITANDPOS = "wskh.pecialworks.notshow.workunitandpos";
    /**
     * 未到退休年龄职业是否展示退休人员
     */
    public static final String WSKH_PROFESSION_RETIRE_SHOW = "wskh.profession.retire.show";
    /**
     * 风测结果等级协议配置
     */
    public static final String WSKH_RISK_LEVEL_PROTOCOL = "wskh.risk.level.protocol";
    /**
     * 是否启用传输加密
     */
    public static final String WSKH_FLOW_SUBMIT_RSA_ENCRYPT = "wskh.flow.submit.rsa.encrypt";

    /**
     * 开通场外基金申请节点
     */
    public static final String WSKH_ACCOUNT_OPEN_NODE = "wskh.account.open.node";
    /**
     * 腾讯一键登录服务密钥
     */
    public static final String WSKH_TENCENT_ONEKEYLOGIN_APPIDCERT = "wskh.tencent.onekeylogin.appidcert";
    /**
     * 腾讯一键登录服务地址
     */
    public static final String WSKH_TENCENT_ONEKEYLOGIN_URL = "wskh.tencent.onekeylogin.url";
    /**
     * 活体appid
     */
    public static final String SJKH_APPCLIENT_LIVE_APPID = "sjkh.appclient.live.appid";
    /**
     * 活体secret
     */
    public static final String SJKH_APPCLIENT_LIVE_SECRET = "sjkh.appclient.live.secret";
    /**
     * 活体SDKlicense
     */
    public static final String SJKH_APPCLIENT_LIVE_LICENSE = "sjkh.appclient.live.license";
    /**
     * 活体服务提供商
     */
    public static final String SJKH_APPCLIENT_LIVE_PROVIDER = "sjkh.appclient.live.provider";

    /**
     * 大智慧回传接口请求url
     */
    public static final String WSKH_DZH_REQUEST_URL = "wskh.dzh.request.url";
    /**
     * 大智慧支持的接入方式或者渠道代码
     */
    public static final String WSKH_DZH_SUPPORT_CHANNELORAPP = "wskh.dzh.support.channelOrApp";
    /**
     * 大智慧回传接口开户渠道
     */
    public static final String WSKH_DZH_OPENING_CHANNEL = "wskh.dzh.opening.channel";
    /**
     * 大智慧回传接口券商编号
     */
    public static final String WSKH_DZH_BROKER_NO = "wskh.dzh.broker.no";
    /**
     * 大智慧回传接口客户端id
     */
    public static final String WSKH_DZH_CLIENT_ID = "wskh.dzh.client.id";
    /**
     * 大智慧回传接口签名key值
     */
    public static final String WSKH_DZH_SIGN_KEY = "wskh.dzh.sign.key";
    /**
     * 网开视频见证页面是否有智能审核项目
     */
    public static final String WSKH_VIDEO_PAGE_TYPE = "wskh.video.page.type";
    /**
     * 中登公安优先
     */
    public static final String WSKH_POLICEVERIFY_CSDC_FIRST = "wskh.policeverify.csdc.first";
    /**
     * 是否需要公安认证
     */
    public static final String WSKH_CHECK_POLICE_VERIFY = "wskh.check.police.verify";
    /**
     * 公安认证不通过提示语
     */
    public static final String WSKH_CHECK_POLICE_ERROR_TIP = "wskh.check.police.error.tip";
    /**
     * 用户中心系统服务地址
     */
    public static final String WSKH_USERCENTER_SERVICE_URL = "wskh.usercenter.service.url";
    /**
     * 业务系统app_key
     */
    public static final String WSKH_USERCENTER_SERVICE_APPKEY = "wskh.usercenter.service.appkey";
    /**
     * 业务系统appsecret
     */
    public static final String WSKH_USERCENTER_SERVICE_APPSECRET = "wskh.usercenter.service.appsecret";
    /**
     * 业务系统国密cipher
     */
    public static final String WSKH_USERCENTER_SERVICE_CIPHER = "wskh.usercenter.service.cipher";
    /**
     * 启用用户中心系统服务
     */
    public static final String WSKH_USERCENTER_SERVICE_ENABLE = "wskh.usercenter.service.enable";
    /**
     * 业务系统签名secret
     */
    public static final String WSKH_USERCENTER_SERVICE_SIGNSECRET = "wskh.usercenter.service.signsecret";
    /**
     * 业务系统签名加密算法
     */
    public static final String WSKH_USERCENTER_SERVICE_SIGNALGORITHM = "wskh.usercenter.service.signalgorithm";
    /**
     * 后台系统营销人员标题
     */
    public static final String WSKH_BACK_BROKERNAME_CONFIG = "wskh.back.brokername.config";
    /**
     * 同花顺h5埋点公钥
     */
    public static final String WSKH_THS_H5_TRACK_PUBKEY = "wskh.ths.h5.track.pubkey";
    /**
     * 同花顺h5埋点推送地址
     */
    public static final String WSKH_THS_H5_TRACK_URL = "wskh.ths.h5.track.url";
    /**
     * 是否启用兴业银行银银平台
     */
    public static final String WSKH_YYPT_LINK_FLAG = "wskh.yypt.link.flag";
    /**
     * 银银平台服务地址
     */
    public static final String WSKH_YYPT_REQUEST_URL = "wskh.yypt.request.url";
    /**
     * 商家编号
     */
    public static final String WSKH_YYPT_BUSINESS_CODE = "wskh.yypt.business.code";
    /**
     * 银银平台配置文件地址
     */
    public static final String WSKH_YYPT_CONFIGFILE_ADDRESS = "wskh.yypt.configfile.address";
    /**
     * 银银平台对应的银行编号
     */
    public static final String WSKH_YYPT_BANKCODE = "wskh.yypt.bankcode";
    /**
     * 银银平台SDK版本
     */
    public static final String WSKH_YYPT_SERVICE_VERSION = "wskh.yypt.service.version";
    /**
     * 长期有效日期
     */
    public static final String WSKH_YYPT_SERVICE_FOREVERDATE = "wskh.yypt.service.foreverdate";
    /**
     * 银银平台是否异步推送影像
     */
    public static final String WSKH_YYPT_ASYNC_UPLOAD = "wskh.yypt.async.upload";

    /**
     * * crh-rpa服务调用地址
     */
    public static final String WSKH_CRHRPA_SERVICE_URL = "wskh.crhrpa.service.url";

    /**
     * 是否实时调用底层公安接口，不取本地数据
     */
    public static final String WSKH_POLICE_REALTIME = "wskh.police.realtime";

    /**
     * 任务转交第一段持续时间(秒)
     */
    public static final String TASK_TRANSFER_FIRST_DURATION = "wskh.task.transfer.duration";

    /**
     * 任务转交第二段持续时间(秒)
     */
    public static final String TASK_TRANSFER_SECOND_DURATION = "wskh.task.transfer.duration";

    /**
     * 是否开启智能派单 开关
     */
    public static final String WSKH_ISOPEN_DISPATCH = "wskh.isopen.dispatch";

    /**
     * 超时退出白名单用户多个用户 逗号隔开
     */
    public static final String WSKH_AUDIT_STAFF_WHITE = "wskh.audit.staff.whiteList";

    public static final String WSKH_BRANCH_REPEAT_NUM = "wskh.branch.repeat.number";

    public static final String WSKH_PUSH_DELAYTIME = "wskh.push.delaytime";

    public static final String WSKH_LOGOUT_MINUTE = "wskh.logout.minute";

    /**
     * 业务办理映射关系
     */
    public static final String WSKH_HAND_MAPPING_RELATIONSHIP = "wskh.hand.mapping.relationship";

    /**
     * 智能审核业务类型映射
     */
    public static final String WSKH_AIAUDIT_BUSIN_TYPE_MAPPING = "wskh.aiaudit.busin_type.mapping";
    public static final String WSKH_CALL_TEST_PHONE = "wskh.call.test.phone";
    // vip渠道
    public static final String CHANNEL_VIP_LIST = "channel.vip.list";

    // 非见证任务超时推送派单
    public static final String WSKH_PUSH_NOT_AUDIT_DELAYTIME = "wskh.push.not.audit.delaytime";


    /**
     * 不参加派单的分支机构 输入框
     */
    public static final String WSKH_REFUSE_DISPATCH_BRANCHNO = "wskh.refuse.dispatch.branchno";


    /**
     * 不参加派单的任务类型 选择框
     */
    public static final String WSKH_REFUSE_DISPATCH_TASKTYPE = "wskh.refuse.dispatch.tasktype";


    /**
     * 不参加派单的业务类型 选择框
     */
    public static final String WSKH_REFUSE_DISPATCH_BUSINTYPE = "wskh.refuse.dispatch.busintype";


    /**
     * 不参加派单的渠道 输入框
     */
    public static final String WSKH_REFUSE_DISPATCH_CHANNELCODE = "wskh.refuse.dispatch.channelcode";

    /**
     * 任务统计时间刷新间隔 输入框 单位 秒
     */
    public static final String WSKH_COUNTTASK_REFRESH_TIME = "wskh.counttask.refresh.time";


    /**
     * 是否开启任务自动回收
     */
    public static final String WSKH_ISOPEN_AUTO_RESYCLE = "wskh.isopen.auto.resycle";


    /**
     * 任务回收提醒的时间 输入框  单位分钟
     */
    public static final String WSKH_REMIND_TASK_TIME = "wskh.remind.task.time";

    /**
     * 任务回收提醒的内容
     */
    public static final String WSKH_REMIND_TASK_CONTENT = "wskh.remind.task.content";


    /**
     * 回收待处理的时间 输入框 单位分钟
     */
    public static final String WSKH_RECYLE_TASK_TIME = "wskh.recycle.task.time";

    /**
     * 回收处理中的时间 输入框 单位分钟
     */
    public static final String WSKH_RECYLE_AUDITING_TASK_TIME = "wskh.recycle.auditing.task.time";

    /**
     * 双向视频回收待处理的时间 输入框 单位秒
     */
    public static final String WSKH_BIDIRECTIONAL_RECYLE_TASK_TIME = "wskh.bidirectional.recycle.suspend.time";

    /**
     * 双向视频心跳超时时间 输入框 单位秒
     */
    public static final String WSKH_BIDIRECTIONAL_HEARTBEAT_TIMEOUT_TIME = "wskh.bidirectional.heartbeat.timeout.time";

    /**
     * 双向视频作废待处理的时间 输入框 单位秒
     */
    public static final String WSKH_BIDIRECTIONAL_INVALID_TASK_TIME = "wskh.bidirectional.invalid.time";


    //审核通过校验配置

    /**
     * 启用无公安头像，人像比对分数达标允许复核提交
     */
    public static final String WSKH_ISOPEN_EMERGENCY_CHANNEL = "wskh.isopen.emergency.channel";

    /**
     * 特定渠道
     */
    public static final String WSKH_SPECIA_CHANNEL_OPEN = "wskh.special.channel.ope";

    /**
     * 特定类型
     */
    public static final String WSKH_SPECIA_BUSIN_TYPE_OPEN = "wskh.special.busin_type.ope";

    /**
     * 启用财人汇rpa的模式配置 1 自动调取 2 手动调取
     */
    public static final String WSKH_CRHRPA_MODEL_CONFIG = "wskh.crhrpa.model.config";
    /**
     * 中登分数置信区间    60-75 wskh.zd.score.confidence.range
     */
    public static final String WSKH_ZD_SCORE_CONFIDENCE_RANGE = "wskh.zd.score.confidence.range";

    /**
     * 证通分数置信区间    40-60 wskh.zt.score.confidence.range
     */
    public static final String WSKH_ZT_SCORE_CONFIDENCE_RANGE = "wskh.zt.score.confidence.range";

    /**
     * 是否启用财人汇RPA 0 关闭 1启动
     */
    public static final String WSKH_ISOPEN_CRHRPA = "wskh.isopen.crhrpa";

    /**
     * 回收挂起的时间
     */
    public static final String WSKH_RECYCLE_SUSPEND_TIME = "wskh.recycle.suspend.time";

    /**
     * 回收挂起的原因
     */
    public static final String WSKH_RECYCLE_SUSPEND_REASON = "wskh.recycle.suspend.reason";


    /**
     * 任务同步智能派单时间 说明：任务创建时间起。间隔时间未被分配操作员，单位：分钟 wskh.task.sync.aidispatch
     */
    public static final String WSKH_TASK_SYNC_AIDISPATCH_INTERVAL = "wskh.task.sync.aidispatch.interval";


    /**
     * 是否启用md5鉴权
     */
    public static final String WSKH_ISOPEN_MD5_AUTHENTICATION = "wskh.isopen.md5.authentication";


    //公安认证服务提供商  comp.id.verify.service.provider（组件配置项）
    public static final String COMP_ID_VERIFY_SERVICE_PROVIDER = "comp.id.verify.service.provider";


    /**
     * 地址截取位数
     */
    public static final String WSKH_ADDRESS_TRUNCATE_DIGITS = "wskh.address.truncate.digits";

    /**
     * 非托管分支机构
     */
    public static final String BASE_NOTMANAGE_BRANCH = "base.notmanage.branch";

    /**
     * 是否启用校验外国人三要素信息拦截 0 关闭 1启动
     */
    public static final String WSKH_CHECK_PERMANENT_ID_INTERCEPT_CONFIG = "wskh.check.permanent.id.intercept";

    /**
     * 双向视频证通分获取是否依赖中登时间 0 关闭 1启动
     */
    public static final String WSKH_SCORE_DEPEND_ZDGA_TIME_CONFIG = "wskh.score.depend.zdga.time";

    /**
     * 年龄+职业备注编码动态配置原因编码
     */
    public static final String WSKH_AGE_PROFESSION_REASON_CODE_CONFIG = "wskh.age.profession.reason.code";


    /**
     * 中登公安核查开关
     */
    public static final String WSKH_CSDC_VERIFICATION_SWITCH = "wskh.csdc.verification.switch";

    /**
     * 任务暂存开关
     */
    public static final String WSKH_TASK_STAGING_SWITCH = "wskh.task.staging.switch";

    /**
     * 任务暂存最大数量
     */
    public static final String WSKH_TASK_STAGING_MAX_NUM = "wskh.task.staging.max.num";

}

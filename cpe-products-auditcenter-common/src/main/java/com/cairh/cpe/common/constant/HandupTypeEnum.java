package com.cairh.cpe.common.constant;

/**
 * Description：任务操作流水类型
 * Author： slx
 * Date： 2024/4/15 下午4:05
 */
public enum HandupTypeEnum {

    SUSPEND("1", "挂起"),
    RECOVERY("2", "回收"),
    TRANSFER("3", "转交"),
    ;

    String code;
    String value;

    HandupTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getValue(String code) {
        for (HandupTypeEnum h : HandupTypeEnum.values()) {
            if (h.getCode().equals(code)) {
                return h.value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setValue(String value) {
        this.value = value;
    }
}

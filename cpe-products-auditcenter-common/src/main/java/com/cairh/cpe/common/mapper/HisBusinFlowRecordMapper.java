package com.cairh.cpe.common.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cairh.cpe.common.entity.His_BusinFlowRecord;
import com.cairh.cpe.common.entity.request.BusinFlowRecordForm;
import com.cairh.cpe.common.entity.response.BusinFlowRecordResp;
import com.cairh.cpe.db.config.MultiDataSource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@MultiDataSource(name = "his-db")
public interface HisBusinFlowRecordMapper extends BaseMapper<His_BusinFlowRecord> {

    /**
     * 业务流水查询
     */
    List<BusinFlowRecordResp> qryUserApplyRecord(@Param("queryForm") BusinFlowRecordForm recordForm);

}

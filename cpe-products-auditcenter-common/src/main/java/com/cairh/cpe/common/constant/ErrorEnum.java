package com.cairh.cpe.common.constant;


/**
 * 见证错误号，错误号段：240000-249999
 */
public enum ErrorEnum {
    USER_NOT_EXIST("240001", "用户信息不存在"),
    REDISSON_LOCK("240002", "重复提交请求"),
    AUDIT_TASK_MQ_ERROR_TIP("240003", "MQ通知处理异常"),
    AUDIT_TASK_NOT_ALLOW_AUDITOR("240004", "该任务审核状态下您没有操作权限"),
    AUDIT_EXIST_OTHER_PENDING_TASK("240005", "你有其他任务正在审核中,请稍后再试"),
    AUDIT_TRANSFER_NEW_TASK("240006", "该任务已派单给其他人,新的审核人是{%s}"),
    AUDIT_NOT_FIND_TASK("240007", "传参异常,数据库未发现该任务"),
    AUDIT_ALREADY_TASK("240008", "该任务已经被审核,请勿重复操作"),
    AUDIT_NOT_AUDITING_TASK("240009", "该任务状态不在审核中,请刷新后再试"),
    AUDIT_INTELLIGENT_REPORT_GENERATE("240010", "该任务智能报告还没有生成,请稍后再试"),
    AUDIT_TASK_NOT_EXIST("240011", "该任务不存在"),
    AUDIT_DIS_SYNC_ERROR("240013", "该任务已分配给其他人,请尝试其他任务"),
    AUDIT_NOT_ACQUIRED_LOCK("240014", "该任务未获取到锁,请刷新后再试"),
    AUDIT_NOT_PENDING_TASK("240015", "该任务状态不是待审核,无法认领"),
    AUDIT_NOT_AUTHORIZED_CLAIM("240016", "该任务您没有权限认领"),
    AUDIT_NOT_PUSH_DISPATCH("240017", "该任务还没有推送派单,无法进行认领"),
    AUDIT_CLAIM_ERROR("240018", "认领失败"),
    AUDIT_NOT_PUSH_DIS("240019", "该任务还没有推送至派单,无法进行处理"),
    REDIS_TRY_LOCK_FAIL("240020", "该任务正在处理中,请刷新后再试"),
    BIDIRECTIONAL_TASK_NOT_ALLOW_CLEAR("240021", "该双向视频任务不可清除"),
    BIDIRECTIONAL_AUDIT_INVALIDATE_TASK("240022", "该任务已失效，稍后为您分配新的任务~"),
    RECYCLE_TASK_TIP("240023", "该任务已被回收"),
    ALREADY_DEAL_TASK_TIP("-10086", "该任务已被处理，请勿重复处理"),
    AUDIT_AI_ID_CARD_ERROR("240025", "智能身份证证件信息识别对比异常"),
    AUDIT_AI_FACE_ERROR("240026", "人像比对信息异常"),
    BIDIRECTIONAL_INVALIDATE_TASK_ERROR("240027", "双向视频任务已作废"),
    BIDIRECTIONAL_TASK_JOIN_QUEUE_ERROR("240028", "双向视频加入队列异常"),
    AUDIT_TASK_TRANSFER_REFUSE_ERROR("240029", "任务转交拒绝异常"),
    BIDIRECTIONAL_QUEUE_NOT_EXIST("240030", "双向视频队列不存在"),
    HAND_APPLY_DATA_ERROR("240031", "网厅业务办理开户申请数据异常"),
    AUDIT_TASK_SUSPEND_ERROR("240032", "任务挂起异常"),
    AUDIT_AI_VIDEO_NOT_EXIST("240033", "视频智能审核时没有视频文件"),
    AUDIT_TASK_TRANSFER_NO_OPERATOR_ERROR("240034", "任务当前操作人为空，不可转交"),
    AUDIT_TASK_TRANSFER_ERROR("240035", "任务转交异常"),
    AUDIT_TASK_TRANSFER_STATUS_ERROR("240036", "当前任务状态不可转交"),
    AUDIT_TASK_TRANSFER_ACCEPT_ERROR("240037", "业务编号[%s]对应的[%s]任务正在处理中,请稍后再试"),
    AUDIT_TASK_TRANSFER_ACCEPT_STATUS_ERROR("240038", "业务编号[%s]对应的[%s]任务不为转交状态,不允许接受此次转交"),
    AUDIT_TASK_TRANSFER_ACCEPT_CLAIM_ERROR("240039", "该任务已被认领，请勿重复认领"),
    AUDIT_TASK_TRANSFER_ACCEPT_NO_PAUSE_ERROR("240040", "该任务还没有推送派单，请勿操作"),
    AUDIT_TASK_TRANSFER_ACCEPT_ONESELF_ERROR("240041", "业务编号[%s]对应的[%s]任务不允许转交给自身"),
    AUDIT_TASK_TRANSFER_REFUSE_ONESELF_ERROR("240042", "业务编号[%s]对应的[%s]任务不为转交状态,不允许拒绝此次转交"),
    BUSINFLOW_DEAL_BY_OTHER("240043", "任务办理已被其他操作人员抢先处理"),
    AUDIT_TASK_NOT_ALLOW_OPERATOR("240044", "该操作员无法处理该任务"),
    AUDIT_TASK_APPLY_DATA_ERROR("240045", "该视频见证id已存在"),
    AUDIT_AI_AUDIO_NOT_EXIST("240046", "语音智能时没有视频文件"),
    NOT_ALLOW_AUDIT_RESULT("240047", "用户状态不正常，无法获取审核结果"),
    ID_NO_IMAGE_ERROR("240048", "身份证图片编号不对"),
    TOO_MUCH_DATA_MESSAGE("240049", "数据导出过多，请重新选择筛选条件！"),
    AGE_RESTRICTIONS_MORE_70_MESSAGE("240050", "客户超过70周岁，不允许在线开户！"),
    AGE_RESTRICTIONS_LESS_18_MESSAGE("240051", "客户小于18周岁，不允许在线开户！"),
    NOT_DATA("240052", "请求状态不正常,不能提交审核操作"),
    NOT_ALLOW_SUBMIT_PARAM("240053", "业务处理中,不允许提交请求参数"),
    NOT_AUDIT_STATUS("240054", "状态不正常,不能进行审核操作"),
    AUDIT_STATUS_NOT_PERMIT("240055", "请求状态不正常,不能提交审核操作"),
    NOT_ALLOW_SUBMIT_AIAUDIT_DISPATCH("240056", "业务处理中,不允许提交智能审核以及智能派单"),
    AUDIT_TASK_CLAIM_ERROR("240057", "通知派单任务已被手动认领失败"),
    QUERY_USERINFO_BRANCH_ERROR("240058", "查询用户staff_no=[%s]分支结构branch_no=[%s]信息为空"),
    QUERY_USERINFO_UP_BRANCH_ERROR("240059", "查询用户staff_no=[%s]上级分支结构up_branch_no=[%s]信息为空"),
    BUSIN_APPLE_DATA_SAVE_ERROR("240060", "业务流程提交数据存储失败"),
    REDIS_LOCK_ERROR("240061", "等待时间过就请重试"),
    SAVE_PARAMS_DATA_ERROR("240062", "保存params异常"),
    REVIEW_DEFINITION_NOT_EXIST("240063", "审核流程定义节点不存在"),
    TASK_EXECUTE_ERROR("240064", "任务执行失败"),
    HAND_ID_NO_AI_CHECK_ERROR("240065", "身份证切边检查异常"),
    COMPONENT_DUBBO_QUERY_RULE_ERROR("240066", "组件dubbo服务_查询智能审核规则异常"),
    SAVE_PARAMS_SNAPSHOT_ERROR("240067", "保存saveSnapshot用户快照信息异常"),
    SUBMIT_PARAMS_ERROR("240068", "submitParamsByOperator保存用户参数信息异常"),
    PUSH_DISPATCH_ERROR("240069", "推送派单系统发生异常"),
    BIDIRECTIONAL_TASK_VIDEO_INTERRUPT_ERROR("240070", "视频中断更新任务异常"),
    BIDIRECTIONAL_TASK_HEART_ERROR("240071", "双向视频心跳记录异常"),
    QUERY_TIMEOUT_ERROR("240072", "查询异常或超时"),
    SAVE_CONTENTMAP_PARAMS_ERROR("240073", "saveContentMapParams保存用户信息异常"),
    COMPONENT_DUBBO_AI_VIDEO_ERROR("240074", "组件dubbo服务_智能视频审核异常"),
    FAILED_GET_EXPORT_DATA_MESSAGE("240075", "获取导出数据消息失败"),
    ISSUING_AUTHORITY_SAVE_ERROR("240076", "该签发机关已存在"),
    PERMANENT_ID_CHECK_ERROR("240077", "三要素校验失败"),
    DATA_NOT_EXIST("240078", "该数据不存在"),
    DATA_STATUS_ERROR("240079", "数据状态异常，不可操作"),
    DATA_MAX_HANDLE_ERROR("240080", "本次选择%s任务%d条，超过系统限制（最大%d条），请重新选择");


    private String value;
    private String desc;

    private ErrorEnum(String value, String desc) {
        this.setValue(value);
        this.setDesc(desc);
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String toString() {
        return "[" + this.value + "]" + this.desc;
    }
}

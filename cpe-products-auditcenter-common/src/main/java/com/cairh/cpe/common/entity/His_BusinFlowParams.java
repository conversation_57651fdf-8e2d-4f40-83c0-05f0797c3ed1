package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * his提交参数表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("His_BusinFlowParams")
public class His_BusinFlowParams implements Serializable {

    /**
     * 受理编号
     */
    @TableId("request_no")
    private String request_no;

    /**
     * 顺序号
     */
    private Integer order_no;

    /**
     * 数据内容
     */
    private String busi_content;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date tohis_datetime;


}

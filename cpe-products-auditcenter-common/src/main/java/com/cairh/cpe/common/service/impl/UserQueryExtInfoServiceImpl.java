package com.cairh.cpe.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.TaskTypeEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.UserQueryExtInfo;
import com.cairh.cpe.common.entity.clob.*;
import com.cairh.cpe.common.entity.request.CommonUserQueryExtInfo;
import com.cairh.cpe.common.mapper.BusinFlowTaskMapper;
import com.cairh.cpe.common.mapper.UserQueryExtInfoMapper;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.service.IUserQueryExtInfoService;
import com.cairh.cpe.common.util.BeanMapUtil;
import com.cairh.cpe.common.util.SqlDateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class UserQueryExtInfoServiceImpl extends ServiceImpl<UserQueryExtInfoMapper, UserQueryExtInfo> implements IUserQueryExtInfoService {

    @Autowired
    private IRequestService requestService;

    @Autowired
    private IBusinFlowTaskService businFlowTaskService;

    @Autowired
    private BusinFlowTaskMapper businFlowTaskMapper;

    @Override
    public void saveClobParamsToUserQueryExtInfo(String request_no) {
        ClobContentInfo clobInfo = requestService.getAllDataByRequestNo(request_no);
        BusinFlowTask currTaskType = businFlowTaskService.getCurrTaskType(request_no);
        BusinFlowRequest businFlowRequest = requestService.getByRequestNo(request_no);
        Map<String, Object> clob = BeanMapUtil.beanToMap(clobInfo);

        UserQueryExtInfo userQueryExtInfo = this.getOne(new LambdaQueryWrapper<>(UserQueryExtInfo.class).eq(UserQueryExtInfo::getRequest_no, request_no));
        if (userQueryExtInfo == null) {
            userQueryExtInfo = new UserQueryExtInfo();
        }

        // 扩展信息
        UserBaseInfo userBaseInfo = clobInfo.getUser_base_info();
        if (userBaseInfo != null) {
            clob.putAll(BeanMapUtil.beanToMap(userBaseInfo));
        }
        // 身份信息
        IDCardInfo idCardInfo = clobInfo.getId_card_info();
        if (idCardInfo != null) {
            clob.putAll(BeanMapUtil.beanToMap(idCardInfo));
        }
        // 税收
        RevenueInfo revenueInfo = clobInfo.getRevenue_info();
        if (revenueInfo != null) {
            clob.putAll(BeanMapUtil.beanToMap(revenueInfo));
        }
        // 存管
        UserBankInfo userBankInfo = clobInfo.getUser_bank_info();
        if (userBankInfo != null) {
            clob.putAll(BeanMapUtil.beanToMap(userBankInfo));
        }
        // if (currTaskType)
        auditInfo(currTaskType, userQueryExtInfo);

        BeanMapUtil.mapToBean(clob, userQueryExtInfo);
        if (FlowNodeConst.END.equals(userQueryExtInfo.getAnode_id()) && FlowStatusConst.AUDIT_PASS.equals(userQueryExtInfo.getRequest_status())) {
            String finish_node = clobInfo.getFinish_node();
            if (StringUtils.isNotBlank(finish_node)) {
                String[] split = finish_node.split(",");
                if (split.length > 0) {
                    userQueryExtInfo.setEnd_node(split[split.length - 1]);
                }
            }
        }
        userQueryExtInfo.setRequest_status(businFlowRequest.getRequest_status());
        userQueryExtInfo.setBusin_type(businFlowRequest.getBusin_type());

        SqlDateUtil.setDefaultValue(userQueryExtInfo);
        saveOrUpdate(userQueryExtInfo);
    }

    @Override
    public Page<UserQueryExtInfo> selectUserQueryExtInfoListByPage(Page<UserQueryExtInfo> page, CommonUserQueryExtInfo commonUserQueryExtInfo) {
        LambdaQueryWrapper<UserQueryExtInfo> wrapper = queryWrapper(commonUserQueryExtInfo);
        return this.page(page, wrapper);
    }

    @Override
    public void saveSnapshot(String request_no, String status) {

        //查询最新的任务id
        List<String> taskStatus = new ArrayList<>();
        taskStatus.add(FlowStatusConst.AUDIT_PASS);
        taskStatus.add(FlowStatusConst.AUDIT_NO_PASS);
        taskStatus.add(FlowStatusConst.AUDIT_INVALIDATE);
        QueryWrapper<BusinFlowTask> businFlowTaskQueryWrapper = new QueryWrapper<>();
        businFlowTaskQueryWrapper.eq("request_no", request_no);
        businFlowTaskQueryWrapper.in("task_status", taskStatus);
        businFlowTaskQueryWrapper.orderByDesc("create_datetime");
        List<BusinFlowTask> businFlowTasks = businFlowTaskMapper.selectList(businFlowTaskQueryWrapper);


        String taskId = "";
        if (CollectionUtils.isNotEmpty(businFlowTasks)) {
            taskId = businFlowTasks.get(0).getSerial_id();
        }

        ClobContentInfo clobInfo = requestService.getAllDataByRequestNo(request_no);
        BusinFlowRequest businFlowRequest = requestService.getByRequestNo(request_no);
        Map<String, Object> clob = BeanMapUtil.beanToMap(clobInfo);

        UserQueryExtInfo userQueryExtInfo = this.getOne(new LambdaQueryWrapper<>(UserQueryExtInfo.class).eq(UserQueryExtInfo::getRequest_no, taskId));
        if (userQueryExtInfo == null) {
            userQueryExtInfo = new UserQueryExtInfo();
        }

        // 扩展信息
        UserBaseInfo userBaseInfo = clobInfo.getUser_base_info();
        if (userBaseInfo != null) {
            clob.putAll(BeanMapUtil.beanToMap(userBaseInfo));
        }
        // 身份信息
        IDCardInfo idCardInfo = clobInfo.getId_card_info();
        if (idCardInfo != null) {
            clob.putAll(BeanMapUtil.beanToMap(idCardInfo));
        }
        // 税收
        RevenueInfo revenueInfo = clobInfo.getRevenue_info();
        if (revenueInfo != null) {
            clob.putAll(BeanMapUtil.beanToMap(revenueInfo));
        }
        // 存管
        UserBankInfo userBankInfo = clobInfo.getUser_bank_info();
        if (userBankInfo != null) {
            clob.putAll(BeanMapUtil.beanToMap(userBankInfo));
        }

        BusinFlowTask currTask = businFlowTasks.get(0);
        auditInfo(currTask, userQueryExtInfo);

        BeanMapUtil.mapToBean(clob, userQueryExtInfo);
        if (FlowNodeConst.END.equals(userQueryExtInfo.getAnode_id()) && FlowStatusConst.AUDIT_PASS.equals(userQueryExtInfo.getRequest_status())) {
            String finish_node = clobInfo.getFinish_node();
            if (StringUtils.isNotBlank(finish_node)) {
                String[] split = finish_node.split(",");
                if (split.length > 0) {
                    userQueryExtInfo.setEnd_node(split[split.length - 1]);
                }
            }
        }
        userQueryExtInfo.setRequest_status(businFlowRequest.getRequest_status());
        userQueryExtInfo.setBusin_type(businFlowRequest.getBusin_type());
        userQueryExtInfo.setRequest_no(taskId);
        userQueryExtInfo.setIs_snapshot("1");
        if (status.equals("pass")) {
            userQueryExtInfo.setRequest_status(FlowStatusConst.AUDIT_PASS);
        } else if (status.equals("invalidate")) {
            userQueryExtInfo.setRequest_status(FlowStatusConst.AUDIT_INVALIDATE);
        } else {
            userQueryExtInfo.setRequest_status(FlowStatusConst.AUDIT_NO_PASS);
        }
        SqlDateUtil.setDefaultValue(userQueryExtInfo);
        saveOrUpdate(userQueryExtInfo);
    }

    /**
     * 填充信息
     *
     * @param currTask
     * @param userQueryExtInfo
     */
    private void auditInfo(BusinFlowTask currTask, UserQueryExtInfo userQueryExtInfo) {
        if (FlowNodeConst.AUDIT.equals(currTask.getTask_type())) {
            userQueryExtInfo.setAudit_operator_no(currTask.getOperator_no());
            userQueryExtInfo.setAudit_operator_name(currTask.getOperator_name());
            userQueryExtInfo.setAudit_finish_datetime(currTask.getFinish_datetime());
            userQueryExtInfo.setReview_operator_no(" ");
            userQueryExtInfo.setReview_operator_name(" ");
            userQueryExtInfo.setReview_finish_datetime(null);
            userQueryExtInfo.setDouble_operator_no(" ");
            userQueryExtInfo.setDouble_operator_name(" ");
            userQueryExtInfo.setDouble_finish_datetime(null);
        } else if (FlowNodeConst.REVIEW.equals(currTask.getTask_type())) {
            BusinFlowTask auditTask = businFlowTaskService.getCurrTaskType(currTask.getRequest_no(), FlowNodeConst.AUDIT);
            userQueryExtInfo.setAudit_operator_no(auditTask.getOperator_no());
            userQueryExtInfo.setAudit_operator_name(auditTask.getOperator_name());
            userQueryExtInfo.setAudit_finish_datetime(auditTask.getFinish_datetime());
            userQueryExtInfo.setReview_operator_no(currTask.getOperator_no());
            userQueryExtInfo.setReview_operator_name(currTask.getOperator_name());
            userQueryExtInfo.setReview_finish_datetime(currTask.getFinish_datetime());
            userQueryExtInfo.setDouble_operator_no(" ");
            userQueryExtInfo.setDouble_operator_name(" ");
            userQueryExtInfo.setDouble_finish_datetime(null);
        } else if (FlowNodeConst.SECONDARY_REVIEW.equals(currTask.getTask_type())) {
            BusinFlowTask auditTask = businFlowTaskService.getCurrTaskType(currTask.getRequest_no(), FlowNodeConst.AUDIT);
            BusinFlowTask reviewTask = businFlowTaskService.getCurrTaskType(currTask.getRequest_no(), FlowNodeConst.REVIEW);
            userQueryExtInfo.setAudit_operator_no(auditTask.getOperator_no());
            userQueryExtInfo.setAudit_operator_name(auditTask.getOperator_name());
            userQueryExtInfo.setAudit_finish_datetime(auditTask.getFinish_datetime());
            userQueryExtInfo.setReview_operator_no(reviewTask.getOperator_no());
            userQueryExtInfo.setReview_operator_name(reviewTask.getOperator_name());
            userQueryExtInfo.setReview_finish_datetime(reviewTask.getFinish_datetime());
            userQueryExtInfo.setDouble_operator_no(currTask.getOperator_no());
            userQueryExtInfo.setDouble_operator_name(currTask.getOperator_name());
            userQueryExtInfo.setDouble_finish_datetime(currTask.getFinish_datetime());
        }
    }

    private LambdaQueryWrapper<UserQueryExtInfo> queryWrapper(CommonUserQueryExtInfo userQueryExtInfo) {
        LambdaQueryWrapper<UserQueryExtInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserQueryExtInfo::getIs_snapshot, "0");
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getClient_name()), UserQueryExtInfo::getClient_name, userQueryExtInfo.getClient_name());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getMobile_tel()), UserQueryExtInfo::getMobile_tel, userQueryExtInfo.getMobile_tel());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getId_no()), UserQueryExtInfo::getId_no, userQueryExtInfo.getId_no());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getVideo_type()), UserQueryExtInfo::getVideo_type, userQueryExtInfo.getVideo_type());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getChannel_code()), UserQueryExtInfo::getChannel_code, userQueryExtInfo.getChannel_code());
        //申请时间
        wrapper.ge(StringUtils.isNotBlank(userQueryExtInfo.getRequest_datetime_start()), UserQueryExtInfo::getRequest_datetime, SqlDateUtil.getParseDateStartDetail(userQueryExtInfo.getRequest_datetime_start()));
        wrapper.le(StringUtils.isNotBlank(userQueryExtInfo.getRequest_datetime_end()), UserQueryExtInfo::getRequest_datetime, SqlDateUtil.getParseDateEndDetail(userQueryExtInfo.getRequest_datetime_end()));
        //审核通过时间
        wrapper.ge(StringUtils.isNotBlank(userQueryExtInfo.getAudit_datetime_start()), UserQueryExtInfo::getAudit_finish_datetime, SqlDateUtil.getParseDateStartDetail(userQueryExtInfo.getAudit_datetime_start()));
        wrapper.le(StringUtils.isNotBlank(userQueryExtInfo.getAudit_datetime_end()), UserQueryExtInfo::getAudit_finish_datetime, SqlDateUtil.getParseDateEndDetail(userQueryExtInfo.getAudit_datetime_end()));
        //复核通过时间
        wrapper.ge(StringUtils.isNotBlank(userQueryExtInfo.getReview_datetime_start()), UserQueryExtInfo::getReview_finish_datetime, SqlDateUtil.getParseDateStartDetail(userQueryExtInfo.getReview_datetime_start()));
        wrapper.le(StringUtils.isNotBlank(userQueryExtInfo.getReview_datetime_end()), UserQueryExtInfo::getReview_finish_datetime, SqlDateUtil.getParseDateEndDetail(userQueryExtInfo.getReview_datetime_end()));
        //二次复核通过时间
        wrapper.ge(StringUtils.isNotBlank(userQueryExtInfo.getDouble_datetime_start()), UserQueryExtInfo::getDouble_finish_datetime, SqlDateUtil.getParseDateStartDetail(userQueryExtInfo.getDouble_datetime_start()));
        wrapper.le(StringUtils.isNotBlank(userQueryExtInfo.getDouble_datetime_end()), UserQueryExtInfo::getDouble_finish_datetime, SqlDateUtil.getParseDateEndDetail(userQueryExtInfo.getDouble_datetime_end()));
        // 业务状态
        if (StringUtils.isNotBlank(userQueryExtInfo.getRequest_status())) {
            String[] taskTypeArr = userQueryExtInfo.getRequest_status().split("-");
            if (taskTypeArr.length == 2) {
                // 复核通过or二次复核通过 需通过end_node作查询条件
                if (StringUtils.equalsAny(userQueryExtInfo.getRequest_status(), WskhConstant.REVIEW_3, WskhConstant.SECONDARY_REVIEW_3)) {
                    wrapper.and(
                            l -> l.eq(UserQueryExtInfo::getEnd_node, taskTypeArr[0])
                                    .or().eq(UserQueryExtInfo::getAnode_id, taskTypeArr[0]));
                } else {
                    wrapper.eq(UserQueryExtInfo::getAnode_id, taskTypeArr[0]);
                }
                wrapper.eq(UserQueryExtInfo::getRequest_status, taskTypeArr[1]);
            }
        }
        wrapper.in(CollectionUtils.isNotEmpty(userQueryExtInfo.getBranch_nos()), UserQueryExtInfo::getBranch_no, userQueryExtInfo.getBranch_nos());
        wrapper.in(StringUtils.isNotBlank(userQueryExtInfo.getBusin_type()), UserQueryExtInfo::getBusin_type, userQueryExtInfo.getBusin_type());
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getAudit_operator_name()), UserQueryExtInfo::getAudit_operator_name, userQueryExtInfo.getAudit_operator_name());
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getReview_operator_name()), UserQueryExtInfo::getReview_operator_name, userQueryExtInfo.getReview_operator_name());
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getDouble_operator_name()), UserQueryExtInfo::getAudit_operator_name, userQueryExtInfo.getAudit_operator_name());
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getClient_id()), UserQueryExtInfo::getClient_id, userQueryExtInfo.getClient_id());
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getFund_account()), UserQueryExtInfo::getFund_account, userQueryExtInfo.getFund_account());
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getBroker_name()), UserQueryExtInfo::getBroker_name, userQueryExtInfo.getBroker_name());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getApp_id()), UserQueryExtInfo::getApp_id, userQueryExtInfo.getApp_id());
        //wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getRequest_status()), UserQueryExtInfo::getRequest_status, userQueryExtInfo.getRequest_status());
        // 审核 复核 二次复核操作员编号
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getAudit_operator_no()), UserQueryExtInfo::getAudit_operator_no, userQueryExtInfo.getAudit_operator_no());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getReview_operator_no()), UserQueryExtInfo::getReview_operator_no, userQueryExtInfo.getReview_operator_no());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getDouble_operator_no()), UserQueryExtInfo::getDouble_operator_no, userQueryExtInfo.getDouble_operator_no());
        //业务流水号
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getActivity_no()), UserQueryExtInfo::getActivity_no, userQueryExtInfo.getActivity_no());
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getRequest_no()), UserQueryExtInfo::getRequest_no, userQueryExtInfo.getRequest_no());
        //活动名称activity_name
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getActivity_name()), UserQueryExtInfo::getActivity_name, userQueryExtInfo.getActivity_name());
        //活动名称marketing_team
        wrapper.like(StringUtils.isNotBlank(userQueryExtInfo.getMarketing_team()), UserQueryExtInfo::getMarketing_team, userQueryExtInfo.getMarketing_team());
        //开户渠道
        wrapper.eq(StringUtils.isNotBlank(userQueryExtInfo.getOpen_channel()), UserQueryExtInfo::getOpen_channel, userQueryExtInfo.getOpen_channel());
        wrapper.orderByAsc(UserQueryExtInfo::getRequest_datetime);
        return wrapper;
    }

    @Override
    public UserQueryExtInfo findExtInfo(String request_no) {
        return this.getById(request_no);
    }

    @Override
    public void updateOperatorInfo(String request_no, String operation_no, String operation_name, String request_status,
                                   String task_type) {
        LambdaUpdateWrapper<UserQueryExtInfo> updateWrapper = new LambdaUpdateWrapper<>();
        if (task_type.equals(TaskTypeEnum.AUDIT.getCode())) {
            updateWrapper.set(UserQueryExtInfo::getAudit_operator_no, operation_no);
            updateWrapper.set(UserQueryExtInfo::getAudit_operator_name, operation_name);
        } else if (task_type.equals(TaskTypeEnum.REVIEW.getCode())) {
            updateWrapper.set(UserQueryExtInfo::getReview_operator_no, operation_no);
            updateWrapper.set(UserQueryExtInfo::getReview_operator_name, operation_name);
        } else {
            updateWrapper.set(UserQueryExtInfo::getDouble_operator_no, operation_no);
            updateWrapper.set(UserQueryExtInfo::getDouble_operator_name, operation_name);
        }
        updateWrapper.set(UserQueryExtInfo::getAnode_id, task_type);
        updateWrapper.set(UserQueryExtInfo::getUpdate_datetime, new Date());
        updateWrapper.set(StringUtils.isNotBlank(request_status), UserQueryExtInfo::getRequest_status, request_status);
        updateWrapper.eq(UserQueryExtInfo::getRequest_no, request_no);
        this.update(updateWrapper);
    }
}
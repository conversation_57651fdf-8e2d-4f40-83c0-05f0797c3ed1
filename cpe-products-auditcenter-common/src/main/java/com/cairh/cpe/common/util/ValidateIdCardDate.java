package com.cairh.cpe.common.util;

import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.constant.TextConstant;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Map;

/**
 * 验证证件有效时间
 */
public class ValidateIdCardDate {

    public static String validateByChangeContent(String changeContent, String strBirthday) {
        return validateByChangeContent(changeContent, strBirthday, "0");
    }

    public static String validateByChangeContent(String changeContent, String strBirthday, String id_Kind) {
        String strResult = null;
        String[] items = changeContent.split("-");
        if (items.length == 2) {
            String beginDate = items[0].trim();
            String endDate = items[1].trim();
            strResult = validateByBirthday(beginDate, endDate, strBirthday, id_Kind);
        } else {
            strResult = "证件有效期限格式不对";
        }
        if (!TextConstant.SUCCESS.equals(strResult)) {
            strResult = StringUtils.defaultIfBlank(PropertiesUtils.get(PropKeyConstant.WSKH_CHECK_ID_ERROR_TIP), strResult);
        }
        return strResult;
    }

    public static String validateByBirthday(String strBeginDate, String strEndDate, String strBirthday, String id_kind) {
        strBeginDate = StringUtils.trimToEmpty(strBeginDate);
        strEndDate = StringUtils.trimToEmpty(strEndDate);
        strBirthday = StringUtils.trimToEmpty(strBirthday);
        if (strEndDate.equalsIgnoreCase("长期有效") || strEndDate.equalsIgnoreCase("长期")) {
            strEndDate = "3000.12.31";
        }
        Date todayDate = new Date();
        Map<String, Object> resultParams = new HashMap<>(2);
        // 1:证件起始日期是否有效
        String checkResult = checkBeginDate(strBeginDate, todayDate, resultParams);
        String tipContent = PropertiesUtils.get(PropKeyConstant.WSKH_CHECK_ID_ERROR_TIP);
        if (!TextConstant.SUCCESS.equals(checkResult)) {
            return StringUtils.defaultIfBlank(tipContent, checkResult);
        }
        // 2:证件截止日期是否有效
        checkResult = checkEndDate(strEndDate, todayDate, resultParams);
        if (!TextConstant.SUCCESS.equals(checkResult)) {
            return StringUtils.defaultIfBlank(tipContent, checkResult);
        }
        strBeginDate = resultParams.get("strBeginDate").toString();
        strEndDate = resultParams.get("strEndDate").toString();


        // 3:证件起始日期与截至日期的比较
        if ("0".equals(id_kind)) {
            checkResult = checkEffectDate((Date) (resultParams.get("beginDate")), strBeginDate, (Date) (resultParams.get("endDate")), strEndDate, strBirthday);
        } else if ("l".equals(id_kind)) {
            checkResult = checkResidenceDate((Date) (resultParams.get("beginDate")), strBeginDate, (Date) (resultParams.get("endDate")), strEndDate, strBirthday);
        } else if ("G".equals(id_kind)) {
            checkResult = checkGPassportDate((Date) (resultParams.get("beginDate")), strBeginDate, (Date) (resultParams.get("endDate")), strEndDate, strBirthday);
        } else if ("H".equals(id_kind)) {
            checkResult = checkHPassportDate((Date) (resultParams.get("beginDate")), strBeginDate, (Date) (resultParams.get("endDate")), strEndDate, strBirthday);
        }


        if (!TextConstant.SUCCESS.equals(checkResult)) {
            return StringUtils.defaultIfBlank(tipContent, checkResult);
        }
        return TextConstant.SUCCESS;
    }

    private static String checkBeginDate(String strBeginDate, Date todayDate, Map<String, Object> resultParams) {
        if (strBeginDate.length() == 0) {
            return "证件起始日期为空";
        }
        strBeginDate = StringUtils.replace(strBeginDate, ".", "", -1);
        try {
            Date beginDate = DateUtils.parseDateStrictly(strBeginDate, KHDateUtil.DATE_FORMAT_NO_DELIMITER);
            Date limitYear = DateUtils.addYears(beginDate, 150);
            if (todayDate.getTime() > limitYear.getTime()) {
                return "证件起始日期[" + strBeginDate + "]超150年";
            }
            if (beginDate.getTime() > todayDate.getTime()) {
                return "证件起始日期[" + strBeginDate + "]大于当前日期";
            }
            resultParams.put("beginDate", beginDate);
            resultParams.put("strBeginDate", strBeginDate);
        } catch (ParseException e) {
            return "证件起始日期[" + strBeginDate + "]解析错误";
        }
        return TextConstant.SUCCESS;
    }

    private static String checkEndDate(String strEndDate, Date todayDate, Map<String, Object> resultParams) {
        if (strEndDate.length() == 0) {
            return "证件截止日期为空";
        }
        strEndDate = StringUtils.replace(strEndDate, ".", "", -1);
        try {
            if (!"30001231".equals(strEndDate)) {
                Date endDate = DateUtils.parseDateStrictly(strEndDate, KHDateUtil.DATE_FORMAT_NO_DELIMITER);
                if (todayDate.getTime() > endDate.getTime()) {
                    return "证件截止日期[" + strEndDate + "]" + ",已经过期";
                }
                resultParams.put("endDate", endDate);
            } else {
                resultParams.put("endDate", todayDate);
            }
            resultParams.put("strEndDate", strEndDate);
        } catch (ParseException e) {
            return "证件截止日期[" + strEndDate + "]解析错误";
        }
        return TextConstant.SUCCESS;
    }

    /**
     * 证件有效期限验证
     * <p>
     * 有效：返回success 无效：返回错误信息 未满16周岁、自愿申领证件的公民，证件有效期5年 16~25周岁的，发给有效期10年的居民证件； 26~45周岁的，发给有效期20年的居民证件；
     * 46周岁以上的，发给长期有效的居民证件。
     */
    private static String checkEffectDate(Date beginDate, String strBeginDate, Date endDate, String strEndDate, String strBirthday) {
//        String roundYear = PropertiesUtils.get("wskh.card.validate.roundyear","2");
////        if ("0".equals(roundYear)) {
////            return TextConstant.SUCCESS;
////        }

        String roundYear = "2";

        int yearArea = -1;
        if (!"30001231".equals(strEndDate)) {
            if ("1".equals(roundYear)) {
                Calendar begin = Calendar.getInstance();
                begin.setTime(beginDate);
                Calendar end = Calendar.getInstance();
                end.setTime(endDate);
                yearArea = end.get(Calendar.YEAR) - begin.get(Calendar.YEAR);
            } else if ("2".equals(roundYear)) {
                if (isEqualYear(Calendar.YEAR, 5, beginDate, endDate, false)) {
                    yearArea = 5;
                }
                if (isEqualYear(Calendar.YEAR, 10, beginDate, endDate, false)) {
                    yearArea = 10;
                }
                if (isEqualYear(Calendar.YEAR, 20, beginDate, endDate, false)) {
                    yearArea = 20;
                }
            }
            if (yearArea != 5 && yearArea != 10 && yearArea != 20) {
                return String.format("证件有效期起止年份[%s-%s]间隔错误", strBeginDate, strEndDate);
            }
        }

//        String validateAge = PropertiesUtils.get("wskh.card.validate.age","1");
//        if ("0".equals(validateAge)) {
//            return TextConstant.SUCCESS;
//        }

        int age = -1;
        if (StringUtils.isNotBlank(strBirthday)) {
            age = AgeUtil.getAge(strBirthday, strBeginDate);
            if (age <= 0) {
                return String.format("证件起始日期[%s]与出生日期[%s]不符", strBirthday, strBeginDate);
            }
        } else {
            return String.format("出生日期不能为空");
        }
        if (age > -1) {
            if (yearArea == -1) {
                if (age < 46) {
                    return "证件长期有效,领证年龄[" + age + "]";
                }
            } else if (yearArea == 20) {
                if (age > 45 || age < 26) {
                    return "证件有效期20年,领证年龄[" + age + "]";
                }
            } else if (yearArea == 10) {
                if (age > 25 || age < 16) {
                    return "证件有效期10年,领证年龄[" + age + "]";
                }
            } else if (yearArea == 5) {
                if (age > 15) {
                    return "证件有效期5年,领证年龄[" + age + "]";
                }
            }
        }
        return TextConstant.SUCCESS;
    }

    private static boolean isEqualYear(int time_unit, int amount, Date beginDate, Date endDate, boolean oneDay) {
        final Calendar beginCal = cn.hutool.core.date.DateUtil.calendar(beginDate);
        final Calendar addBeginCal = cn.hutool.core.date.DateUtil.calendar(time_unit == Calendar.YEAR ? KHDateUtil.addYears(beginDate, amount) : KHDateUtil.addMonths(beginDate, amount));
        final Calendar endCal = cn.hutool.core.date.DateUtil.calendar(endDate);
        if (Calendar.FEBRUARY == beginCal.get(Calendar.MONTH) && Calendar.FEBRUARY == addBeginCal.get(Calendar.MONTH)) {
            if (beginCal.get(Calendar.DAY_OF_MONTH) == beginCal.getActualMaximum(Calendar.DAY_OF_MONTH) && addBeginCal.get(Calendar.DAY_OF_MONTH) == addBeginCal.getActualMaximum(Calendar.DAY_OF_MONTH)) {
                addBeginCal.set(Calendar.DAY_OF_MONTH, 1);
                addBeginCal.set(Calendar.MONTH, Calendar.MARCH);
            }
        }
        if (Calendar.FEBRUARY == endCal.get(Calendar.MONTH)) {
            if (addBeginCal.getTime().equals(endCal.getTime())) {
                return true;
            }
            if (endCal.get(Calendar.DAY_OF_MONTH) == endCal.getActualMaximum(Calendar.DAY_OF_MONTH)) {
                endCal.set(Calendar.DAY_OF_MONTH, 1);
                endCal.set(Calendar.MONTH, Calendar.MARCH);
            }
        }
        // 少一天也行
        final Calendar addBeginCalPlus = cn.hutool.core.date.DateUtil.calendar(KHDateUtil.addDate(addBeginCal.getTime(), -1));
        if (oneDay) {
            return addBeginCal.getTime().equals(endCal.getTime()) || addBeginCalPlus.getTime().equals(endCal.getTime());
        } else {
            return addBeginCal.getTime().equals(endCal.getTime());
        }
    }

    /**
     * 港澳台居住证有效期检测
     *
     * @param beginDate
     * @param strBeginDate
     * @param endDate
     * @param strEndDate
     * @param strBirthday
     * @return
     */
    private static String checkResidenceDate(Date beginDate, String strBeginDate, Date endDate, String strEndDate, String strBirthday) {
//        String roundYear = PropertiesUtils.get("wskh.card.validate.roundyear");
//        if ("0".equals(roundYear)) {
//            return TextConstant.SUCCESS;
//        }

        int yearArea = -1;

        if (isEqualYear(Calendar.YEAR, 5, beginDate, endDate, true)) {
            yearArea = 5;
        }

        if (yearArea != 5) {
            return String.format("证件有效期起止年份[%s-%s]间隔错误", strBeginDate, strEndDate);
        }

//        String validateAge = PropertiesUtils.get("wskh.card.validate.age");
//        if ("0".equals(validateAge)) {
//            return TextConstant.SUCCESS;
//        }
        int age = -1;
        if (StringUtils.isNotBlank(strBirthday)) {
            age = AgeUtil.getAge(strBirthday, strBeginDate);
            if (age <= 0) {
                return String.format("证件起始日期[%s]与出生日期[%s]不符", strBirthday, strBeginDate);
            }
        } else {
            return String.format("出生日期不能为空");
        }
        return TextConstant.SUCCESS;
    }

    /**
     * 港澳通行证有效期检测
     *
     * @param beginDate
     * @param strBeginDate
     * @param endDate
     * @param strEndDate
     * @param strBirthday
     * @return
     */
    private static String checkGPassportDate(Date beginDate, String strBeginDate, Date endDate, String strEndDate, String strBirthday) {
//        String roundYear = PropertiesUtils.get("wskh.card.validate.roundyear");
//        if ("0".equals(roundYear)) {
//            return TextConstant.SUCCESS;
//        }

        int yearArea = -1;

        if (isEqualYear(Calendar.YEAR, 5, beginDate, endDate, true)) {
            yearArea = 5;
        }
        if (isEqualYear(Calendar.YEAR, 10, beginDate, endDate, true)) {
            yearArea = 10;
        }
        if (yearArea != 5 && yearArea != 10) {
            return String.format("证件有效期起止年份[%s-%s]间隔错误", strBeginDate, strEndDate);
        }

//        String validateAge = PropertiesUtils.get("wskh.card.validate.age");
//        if ("0".equals(validateAge)) {
//            return TextConstant.SUCCESS;
//        }
        int age = -1;
        if (StringUtils.isNotBlank(strBirthday)) {
            age = AgeUtil.getAge(strBirthday, strBeginDate);
            if (age <= 0) {
                return String.format("证件起始日期[%s]与出生日期[%s]不符", strBirthday, strBeginDate);
            }
        } else {
            return String.format("出生日期不能为空");
        }
        if (age > -1) {
            if (yearArea == 10) {
                if (age < 18) {
                    return "证件有效期10年,领证年龄[" + age + "]";
                }
            } else if (yearArea == 5) {
                if (age > 17) {
                    return "证件有效期5年,领证年龄[" + age + "]";
                }
            }
        }
        return TextConstant.SUCCESS;
    }

    private static String checkHPassportDate(Date beginDate, String strBeginDate, Date endDate, String strEndDate, String strBirthday) {
//        String roundYear = PropertiesUtils.get("wskh.card.validate.roundyear");
//        if ("0".equals(roundYear)) {
//            return TextConstant.SUCCESS;
//        }

        int yearArea = -1;

        if (isEqualYear(Calendar.MONTH, 3, beginDate, endDate, true)) {
            yearArea = 3;
        }

        if (isEqualYear(Calendar.YEAR, 5, beginDate, endDate, true)) {
            yearArea = 5;
        }
        if (yearArea != 3 && yearArea != 5) {
            return String.format("证件有效期起止年份[%s-%s]间隔错误", strBeginDate, strEndDate);
        }
//        String validateAge = PropertiesUtils.get("wskh.card.validate.age");
//        if ("0".equals(validateAge)) {
//            return TextConstant.SUCCESS;
//        }
        int age = -1;
        if (StringUtils.isNotBlank(strBirthday)) {
            age = AgeUtil.getAge(strBirthday, strBeginDate);
            if (age <= 0) {
                return String.format("证件起始日期[%s]与出生日期[%s]不符", strBirthday, strBeginDate);
            }
        } else {
            return String.format("出生日期不能为空");
        }

        return TextConstant.SUCCESS;
    }


    /*公民出生日期为闰年2月29日的，在计算年岁时，到非闰年以3月1日为满周岁*/
    private static Date addMonths(Date beginDate, int amount) {
        Date date = DateUtils.addMonths(beginDate, amount);
        if (KHDateUtil.formatDate(beginDate, KHDateUtil.DATE_FORMAT_NO_DELIMITER).contains("0229")) {
            if (new GregorianCalendar().isLeapYear(date.getYear())) {
                return date;
            } else {
                return KHDateUtil.addDays(date, 1);
            }
        }
        return date;
    }

	/*private static Date addDays(Date beginDate, int amount){
		Date date = DateUtils.addDays(beginDate,amount);
		if(DateUtil.format(beginDate,DateUtil.DATE_FORMAT_NO_DELIMITER).contains("0229")){
			if(new GregorianCalendar().isLeapYear(date.getYear())){
				return date;
			}else{
				return DateUtil.addDay(date,1);
			}
		}
		return date;
	}*/


    /*公民出生日期为闰年2月29日的，在计算年岁时，到非闰年以3月1日为满周岁*/
    private static Date addYears(Date beginDate, int amount) {
        Date date = DateUtils.addYears(beginDate, amount);
        if (KHDateUtil.formatDate(beginDate, KHDateUtil.DATE_FORMAT_NO_DELIMITER).contains("0229")) {
            if (new GregorianCalendar().isLeapYear(date.getYear())) {
                return date;
            } else {
                return KHDateUtil.addDays(date, 1);
            }
        }
        return date;
    }


}

package com.cairh.cpe.common.entity.request;

import lombok.Data;

import java.io.Serializable;

@Data
public class BaseOperatDictReq implements Serializable {

    // 操作类型 Y 1：增加|2：删除|3：修改|4:查询
    private String operate_type;

    // 字典项编号 N
    private String dict_code;

    // 字典项名称 N
    private String dict_name;

    // 子项编号 N
    private String sub_code;

    // 子项名称 N
    private String sub_name;

    // 系统编号 N
    private String subsys_no;

    // 状态 N
    private String status;

    // 子项顺序 N
    private String order_no;

    // 子项说明 N
    private String remark;

    // 操作员 N
    private String staff_no;

    // 分页查询当前页数 N
    private Long current;

    // 分页查询页大小 N
    private Long size;
}


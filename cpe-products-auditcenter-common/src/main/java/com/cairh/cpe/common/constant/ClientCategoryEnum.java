package com.cairh.cpe.common.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum ClientCategoryEnum {

    CLIENT_PERSON("1", "个人"),
    CLIENT_INSTITUTION("2", "机构"),
    CLIENT_PRODUCT("3", "产品");



    private final String code;
    private final String value;

    ClientCategoryEnum(String code, String value) {
         this.code = code;
         this.value = value;
    }

}

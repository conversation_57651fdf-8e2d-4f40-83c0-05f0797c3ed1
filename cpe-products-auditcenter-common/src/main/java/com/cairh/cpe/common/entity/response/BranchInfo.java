package com.cairh.cpe.common.entity.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 营业部信息
 */
@Data
public class BranchInfo implements Serializable {

    // 营业部编号
    private String branch_no;

    // 营业部名称
    private String branch_name;

    // 升级营业部编号
    private String up_branch_no;

    // 营业部类型
    private String branch_type;

    // 状态
    private String status;

    // 省代码
    private String province_code;

    // 市代码
    private String city_code;

    // 区代码
    private String region_code;

    // 地址
    private String address;

    // 邮编
    private String zipcode;

    // 联系电话
    private String telephone;

    // 客服电话
    private String service_phone;

    // 排序
    private String order_no;

    // 经度
    private String addr_longitude;

    // 纬度
    private String addr_latitude;

    // 证监局代码
    private String csrc_code;

    // 描述
    private String description;

    // 分组
    private Integer group_id;
}

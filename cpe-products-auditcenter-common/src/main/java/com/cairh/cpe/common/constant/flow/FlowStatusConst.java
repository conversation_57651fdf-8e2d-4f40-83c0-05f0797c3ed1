package com.cairh.cpe.common.constant.flow;

public class FlowStatusConst {
    /**
     * 申请中
     */
    public static final String REQUEST_STATUS_APPLYING = "0";
    /**
     * 待审核
     */
    public static final String REQUEST_STATUS_PENDING_AUDIT = "1";
    /**
     * 审核中
     */
    public static final String REQUEST_STATUS_AUDITING = "2";
    /**
     * 审核通过
     */
    public static final String REQUEST_STATUS_AUDIT_PASS = "3";

    //********************请求状态*******************
    /**
     * 审核打回
     */
    public static final String REQUEST_STATUS_AUDIT_FAIL = "4";
    /**
     * 0 申请中,4整改
     */
    public static final String[] ALLOW_CHANGE_STATUS = {FlowStatusConst.REQUEST_STATUS_APPLYING, FlowStatusConst.REQUEST_STATUS_AUDIT_FAIL};
    /**
     * 办理中
     */
    public static final String REQUEST_STATUS_EXECUTING = "5";
    /**
     * 办理成功
     */
    public static final String REQUEST_STATUS_EXECUTE_SUCCESS = "6";
    /**
     * 办理失败
     */
    public static final String REQUEST_STATUS_EXECUTE_FAIL = "7";
    /**
     * 作废
     */
    public static final String REQUEST_STATUS_INVALIDATE = "8";
    public static final String[] ALLOW_SUBMIT_STATUS =
            {FlowStatusConst.REQUEST_STATUS_APPLYING, FlowStatusConst.REQUEST_STATUS_PENDING_AUDIT, FlowStatusConst.REQUEST_STATUS_AUDIT_FAIL, FlowStatusConst.REQUEST_STATUS_INVALIDATE};
    /**
     * 申请取消[仅仅（申请中、待审核、审核打回）的记录可以申请取消]
     */
    public static final String REQUEST_STATUS_CANCEL = "9";
    /**
     * 已受理 代办理
     */
    public static final String REQUEST_STATUS_ACCEPT = "a";
    /**
     * 部分办理
     */
    public static final String REQUEST_STATUS_EXECUTE_HALF = "b";
    /**
     * 不支持重发 三方存管失败
     */
    public static final String REQUEST_STATUS_NOREPEAT = "c";
    /**
     * 等待电话回访
     */
    public static final String REQUEST_STATUS_WAIT_CALLBACK = "d";
    /**
     * 拒绝开户
     */
    public static final String REQUEST_STATUS_REFUSE = "z";
    /**
     * 待审核
     */
    public static final String AUDIT_PENDING = "1";
    /**
     * 审核中
     */
    public static final String AUDIT_AUDITING = "2";

    //********************审核状态*******************
    /**
     * 审核通过
     */
    public static final String AUDIT_PASS = "3";
    /**
     * 审核不通过或者失败
     */
    public static final String AUDIT_NO_PASS = "4";
    /**
     * 审核退回
     */
    public static final String AUDIT_BACK = "5";
    /**
     * 队列中
     */
    public static final String AUDIT_QUEUE = "6";
    /**
     * 作废
     */
    public static final String AUDIT_INVALIDATE = "8";
    /**
     * 0申请中,4整改,c整改,8作废
     */
    public static final String[] ALLOW_APPLY_STATUS =
            {FlowStatusConst.REQUEST_STATUS_APPLYING, FlowStatusConst.REQUEST_STATUS_AUDIT_FAIL, FlowStatusConst.REQUEST_STATUS_NOREPEAT, FlowStatusConst.AUDIT_INVALIDATE};
    /**
     * 审核流程流水
     */
    public static final String AUDIT_RECORD_TYPE = "1";

    //********************流水类型*******************

    /**
     * 用户操作流水（没有前端流程不存在用户操作流水）
     */
    /*public static final String USER_RECORD_TYPE = "0";*/
    /**
     * 系统自动办理流水
     */
    public static final String SYSTEM_RECORD_TYPE = "2";
    /**
     * 审核操作流水
     */
    public static final String AUDIT_OPERATE_TYPE = "3";
    /**
     * 任务挂起
     */
    public static final String AUDIT_SUSPEND = "a";


    //********************任务状态*******************transfer
    //允许回收的状态
    public static final String[] ALLOW_RECYLE_STATUS = {FlowStatusConst.AUDIT_SUSPEND, FlowStatusConst.AUDIT_PENDING, FlowStatusConst.AUDIT_AUDITING};
    /**
     * 任务转交
     */
    public static final String AUDIT_TRANSFER = "b";

    /**
     * 网厅业务办理-审核中
     */
    public static final String HAND_STATUS_AUDITING = "9";

    /**
     * 签发机关-可用
     */
    public static final String ISSUING_AUTHORITY_AVAILABLE = "8";

    /**
     * 签发机关-不可用
     */
    public static final String ISSUING_AUTHORITY_NOT_AVAILABLE = "9";
}

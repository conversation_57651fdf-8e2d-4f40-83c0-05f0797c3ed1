package com.cairh.cpe.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.common.constant.ClientCategoryEnum;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.IdKindEnum;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.His_BusinFlowParams;
import com.cairh.cpe.common.entity.His_BusinFlowRequest;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.UserBaseInfo;
import com.cairh.cpe.common.mapper.HisBusinFlowParamsMapper;
import com.cairh.cpe.common.mapper.HisBusinFlowRequestMapper;
import com.cairh.cpe.common.service.IBusinFlowParamsService;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.AgeUtil;
import com.cairh.cpe.common.util.BeanMapUtil;
import com.cairh.cpe.common.util.IdentifyUtils;
import com.cairh.cpe.context.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
public class RequestServiceImpl implements IRequestService {

    @Autowired
    private IBusinFlowRequestService businFlowRequestService;
    @Autowired
    private IBusinFlowParamsService businFlowParamsService;
    @Autowired
    private HisBusinFlowRequestMapper hisBusinFlowRequestMapper;
    @Autowired
    private HisBusinFlowParamsMapper hisBusinFlowParamsMapper;


    @Override
    public ClobContentInfo getAllDataByRequestNo(String request_no) {
        Map<String, Object> map = getParamContentByRequestNo(request_no);
        return getClobContentInfo(map);
    }


    @Override
    public ClobContentInfo getAllDataByRequestNoV1(String request_no) {
        Map<String, Object> map = getParamContentByRequestNoV1(request_no);
        ClobContentInfo clobContentInfo = BeanMapUtil.mapToBean(map, new ClobContentInfo());

        if (StringUtils.isNotBlank(clobContentInfo.getId_no())) {
            if (("0".equals(clobContentInfo.getId_kind()) || "l".equals(clobContentInfo.getId_kind()))
                    && clobContentInfo.getId_no().trim().length() > 10) {
                String birthday = "";
                if (IdKindEnum.ID_CARD.getCode().equals(clobContentInfo.getId_kind())) {
                    birthday = IdentifyUtils.getIdCardBirthDay(clobContentInfo.getId_no());
                } else {
                    birthday = clobContentInfo.getBirthday();
                }
                if (clobContentInfo.getUser_base_info() != null) {
                    clobContentInfo.getUser_base_info().setAge(AgeUtil.ageUtil(birthday));
                } else {
                    clobContentInfo.setUser_base_info(new UserBaseInfo());
                    clobContentInfo.getUser_base_info().setAge(AgeUtil.ageUtil(birthday));
                }
            }
        }

        if (clobContentInfo.getId_card_info() != null && clobContentInfo.getUser_base_info() != null) {
            if (StringUtils.isBlank(clobContentInfo.getUser_base_info().getAddress())
                    && StringUtils.isNotBlank(clobContentInfo.getId_card_info().getAddress())) {
                clobContentInfo.getUser_base_info().setAddress(clobContentInfo.getId_card_info().getAddress());
            }
            if (StringUtils.isBlank(clobContentInfo.getUser_base_info().getSame_address())
                    && StringUtils.isNotBlank(clobContentInfo.getId_card_info().getSame_address())) {
                clobContentInfo.getUser_base_info().setSame_address(clobContentInfo.getId_card_info().getSame_address());
            }
            if (StringUtils.isBlank(clobContentInfo.getUser_base_info().getAddress_ssq())
                    && StringUtils.isNotBlank(clobContentInfo.getId_card_info().getAddress_ssq())) {
                clobContentInfo.getUser_base_info().setAddress_ssq(clobContentInfo.getId_card_info().getAddress_ssq());
            }
            if (StringUtils.isBlank(clobContentInfo.getUser_base_info().getAddress_ssq_code())
                    && StringUtils.isNotBlank(clobContentInfo.getId_card_info().getAddress_ssq_code())) {
                clobContentInfo.getUser_base_info().setAddress_ssq_code(clobContentInfo.getId_card_info().getAddress_ssq_code());
            }
            if (StringUtils.isBlank(clobContentInfo.getUser_base_info().getZipcode())
                    && StringUtils.isNotBlank(clobContentInfo.getId_card_info().getZipcode())) {
                clobContentInfo.getUser_base_info().setZipcode(clobContentInfo.getId_card_info().getZipcode());
            }

            String yearevenueUnit = clobContentInfo.getUser_base_info().getIncome_unit();
            if (StringUtils.isNotBlank(clobContentInfo.getUser_base_info().getYear_income())) {
                BigDecimal newRevenue = new BigDecimal(clobContentInfo.getUser_base_info().getYear_income());
                if (("1".equals(yearevenueUnit) || StringUtils.isBlank(yearevenueUnit))
                        && newRevenue.compareTo(new BigDecimal(10000)) == 1) {
                    String year_revenue = newRevenue.divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP).toString();
                    clobContentInfo.getUser_base_info().setYear_income(year_revenue);
                }
            }
        }

        return clobContentInfo;
    }

    @Override
    public Map<String, Object> getParamContentByRequestNo(String request_no) {
        BusinFlowRequest businFlowRequest = getByRequestNo(request_no);
        if (businFlowRequest == null) {
            throw new BizException(ErrorEnum.USER_NOT_EXIST.getValue(), ErrorEnum.USER_NOT_EXIST.getDesc());
        } else {
            return getParamContentById(businFlowRequest.getRequest_no());
        }
    }

    @Override
    public Map<String, Object> getParamContentByRequestNoV1(String request_no) {

        return getParamContentById(request_no);

    }

    @Override
    public BusinFlowRequest getByRequestNo(String request_no) {
        return businFlowRequestService.getById(request_no);
    }

    @Override
    public Map<String, Object> getParamContentById(String param_id) {
        return businFlowParamsService.getParamContentById(param_id);
    }

    @Override
    public ClobContentInfo getHisAllDataByRequestNo(String request_no) {
        Map<String, Object> map = getHisParamContentByRequestNo(request_no);
        return getClobContentInfo(map);
    }

    private ClobContentInfo getClobContentInfo(Map<String, Object> map) {
        ClobContentInfo clobContentInfo = BeanMapUtil.mapToBean(map, new ClobContentInfo());
        if (StringUtils.isNotBlank(clobContentInfo.getId_no())) {
            String id_kind = clobContentInfo.getId_kind();
            String id_no = clobContentInfo.getId_no();
            // 网厅业务办理-机构户
            if (StringUtils.equalsAny(clobContentInfo.getClient_category(), ClientCategoryEnum.CLIENT_INSTITUTION.getCode(), ClientCategoryEnum.CLIENT_PRODUCT.getCode())) {
                id_kind = clobContentInfo.getAgent_id_kind();
                id_no = clobContentInfo.getAgent_id_no();
            }
            if ((("0".equals(id_kind) || "l".equals(id_kind)) && clobContentInfo.getId_no().trim().length() > 10) || StringUtils.isNotBlank(id_kind)) {
                String birthday;
                // TODO 后续增加获取年龄逻辑
                // 身份证
                if (StringUtils.equalsAny(id_kind, IdKindEnum.ID_CARD.getCode())) {
                    birthday = IdentifyUtils.getIdCardBirthDay(id_no);
                } else {
                    birthday = clobContentInfo.getBirthday();
                }
                if (clobContentInfo.getUser_base_info() != null) {
                    clobContentInfo.getUser_base_info().setAge(AgeUtil.ageUtil(birthday));
                } else {
                    clobContentInfo.setUser_base_info(new UserBaseInfo());
                    clobContentInfo.getUser_base_info().setAge(AgeUtil.ageUtil(birthday));
                }
            }

        }
        if (clobContentInfo.getId_card_info() != null && clobContentInfo.getUser_base_info() != null) {
            if (StringUtils.isBlank(clobContentInfo.getUser_base_info().getAddress())
                    && StringUtils.isNotBlank(clobContentInfo.getId_card_info().getAddress())) {
                clobContentInfo.getUser_base_info().setAddress(clobContentInfo.getId_card_info().getAddress());
            }
            if (StringUtils.isBlank(clobContentInfo.getUser_base_info().getSame_address())
                    && StringUtils.isNotBlank(clobContentInfo.getId_card_info().getSame_address())) {
                clobContentInfo.getUser_base_info().setSame_address(clobContentInfo.getId_card_info().getSame_address());
            }
            if (StringUtils.isBlank(clobContentInfo.getUser_base_info().getAddress_ssq())
                    && StringUtils.isNotBlank(clobContentInfo.getId_card_info().getAddress_ssq())) {
                clobContentInfo.getUser_base_info().setAddress_ssq(clobContentInfo.getId_card_info().getAddress_ssq());
            }
            if (StringUtils.isBlank(clobContentInfo.getUser_base_info().getAddress_ssq_code())
                    && StringUtils.isNotBlank(clobContentInfo.getId_card_info().getAddress_ssq_code())) {
                clobContentInfo.getUser_base_info().setAddress_ssq_code(clobContentInfo.getId_card_info().getAddress_ssq_code());
            }
            if (StringUtils.isBlank(clobContentInfo.getUser_base_info().getZipcode())
                    && StringUtils.isNotBlank(clobContentInfo.getId_card_info().getZipcode())) {
                clobContentInfo.getUser_base_info().setZipcode(clobContentInfo.getId_card_info().getZipcode());
            }
            String yearevenueUnit = clobContentInfo.getUser_base_info().getIncome_unit();
            if (StringUtils.isNotBlank(clobContentInfo.getUser_base_info().getYear_income())) {
                BigDecimal newRevenue = new BigDecimal(clobContentInfo.getUser_base_info().getYear_income());
                if (("1".equals(yearevenueUnit) || StringUtils.isBlank(yearevenueUnit))
                        && newRevenue.compareTo(new BigDecimal(10000)) == 1) {
                    String year_revenue = newRevenue.divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP).toString();
                    clobContentInfo.getUser_base_info().setYear_income(year_revenue);
                }
            }
        }
        return clobContentInfo;
    }

    private Map<String, Object> getHisParamContentByRequestNo(String request_no) {
        His_BusinFlowRequest businFlowRequest = hisBusinFlowRequestMapper.selectById(request_no);
        if (businFlowRequest == null) {
            throw new BizException(ErrorEnum.USER_NOT_EXIST.getValue(), ErrorEnum.USER_NOT_EXIST.getDesc());
        } else {
            return getHisParamContentById(request_no);
        }
    }

    private Map<String, Object> getHisParamContentById(String request_no) {
        StringBuilder param_content = new StringBuilder();
        List<His_BusinFlowParams> list = hisBusinFlowParamsMapper.selectList(new LambdaQueryWrapper<>(His_BusinFlowParams.class)
                .eq(His_BusinFlowParams::getRequest_no, request_no)
                .orderByAsc(His_BusinFlowParams::getOrder_no));

        for (His_BusinFlowParams param : list) {
            param_content.append(param.getBusi_content());
        }
        String submit_content = param_content.toString();
        if (StringUtils.isNotBlank(submit_content)) {
            return JSON.parseObject(submit_content, Map.class);
        }
        return null;
    }
}

package com.cairh.cpe.common.entity.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 业务流水查询
 **/
@Data
public class BusinFlowRecordForm {

    // 流水类型
    private String record_type;
    // 业务编号
    @NotBlank(message = "业务编号不能为空")
    private String request_no;
    // 任务类型
    private List<String> task_types;
    // 业务标识
    private List<String> business_flags;
}

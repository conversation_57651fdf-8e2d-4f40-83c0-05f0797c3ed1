package com.cairh.cpe.common.constant;

/**
 * Description：更新BUSINPROCESSREQUESTAUDITTRAIL数据来源枚举
 * Author： slx
 * Date： 2024/4/17 下午16:29
 */
public enum UpdateBusinProcessRequestAuditTrailSourceEnum {

    SOURCE_TASK_AUDITING("userQueryExtInfo", "任务审核中更新"),
    SOURCE_TASK_SUSPEND("taskSuspend", "任务挂起更新"),
    SOURCE_TASK_TRANSFER("taskTransfer", "任务转交更新"),
    SOURCE_TASK_TRANSFERNOACCEPT("taskTransferNoAccept", "任务转交不接收更新"),
    SOURCE_TASK_TRANSFERACCEPT("taskTransferAccept", "任务转交接受更新"),
    SOURCE_TASK_RECOVERY("taskRecovery", "任务回收"),
    SOURCE_TASK_AUDITPASS("auditPass", "任务审核通过"),
    SOURCE_TASK_AUDITNOPASS("auditNoPass", "任务审核不通过"),
    SOURCE_TASK_AUDITINVALIDATE("auditInvalidate", "见证任务作废"),
    SOURCE_TASK_REVIEW_OR_SECOND_REVIEW("createReviewOrSecondReview", "创建复核or二次复核更新"),
    SOURCE_TASK_SPECIALFIELDS("specialFields", "特殊字段数据更新");

    private String code;
    private String desc;

    UpdateBusinProcessRequestAuditTrailSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

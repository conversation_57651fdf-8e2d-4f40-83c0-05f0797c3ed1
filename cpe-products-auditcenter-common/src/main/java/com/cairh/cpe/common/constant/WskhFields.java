package com.cairh.cpe.common.constant;

public class WskhFields {
    public static final String MODEL_ID = "model_id";
    public static final String MODEL_MEMO = "model_memo";
    public static final String MODEL_REMARK = "model_remark";
    public static final String AGREEMENTS = "agreements";
    public static final String VIDEO_OPERATOR_NAME = "video_operator_name";
    public static final String PHONE_VISIT_RESULT_ID = "phone_visit_result_id";
    public static final String FILE_CREDIT = "file_credit";// 诚信档案
    public static final String ARCHFULL_ID = "archfull_id";// 归档流水号
    public static final String REVENUE_INMATE_TYPE = "revenue_inmate_type";// 税收类别
    public static final String CONNECT_PASSWORD = "connect_password";// 加密通讯密码
    public static final String CAPITAL_PASSWORD = "capital_password";// 加密资金密码
    public static final String TRANSACTION_PASSWORD = "transaction_password";// 加密交易密码
    public static final String PENSION_PASSWORD = "pension_password";// 养老金密码

    public static final String FIRST_FROM_SOURCE = "first_from_source";// 金阳光APP注册
    public static final String FROM_SOURCE = "from_source";

    public static final String NOT_AUDITOR = "not_auditor";// 视频审核员

    public static final String KEY_ACCOUNT_FLAG = "key_account_flag";// 重点监控账户
    // 模板名称
    public static final String MODEL_NAME = "model_name";
    // 客户模板
    public static final String MODEL_NO = "model_no";
    public static final String BRANCH_NO = "branch_no";
    public static final String BRANCH_SSQ = "branch_ssq";
    public static final String DESCRIPTION = "description";
    public static final String STATUS = "status";
    public static final String REMARK = "remark";

    public static final String RPC_REMARK = "rpc_remark";
    public static final String RPC_OPTION = "rpc_option";
    public static final String RPC_FILE_ID = "rpc_file_id";
    // 佣金
    public static final String COMMISSION = "commission";

    public static final String QUESTION_HINT = "question_hint";
    // 视频见证方式
    public static final String VIDEO_AUTH_TYPE = "video_auth_type";
    // 试题标准答案
    public static final String QUESTION_ANSWER = "question_answer";
    // 试题选项编号对应id信息
    public static final String OPTION_RELATIONSHIP = "option_relationship";
    // 节点耗时
    public static final String ANODE_ID = "anode_id";
    // 操作耗时
    public static final String COST_SECOND = "cost_second";
    // 完成节点
    public static final String FINISH_NODE = "finish_node";
    // 整改节点
    public static final String AMEND_NODE = "amend_node";
    // 清除节点
    public static final String CLEAR_NODE = "clear_node";
    // 整改项
    public static final String RECTIFICATION_ITEM = "rectification_item";

    public static final String UPDATE_DATETIME = "update_datetime";
    public static final String SUBMIT_DATETIME = "submit_datetime";// 提交开户申请时间
    public static final String REVIEW_SUBMIT_DATETIME = "review_submit_datetime";// 提交复核申请时间
    public static final String CUNTER_DATETIME = "cunter_datetime";// 提交开户申请时间
    public static final String ACTIVE_DATETIME = "active_datetime";// 提交开户激活时间
    public static final String REVENUE_INFO = "revenue_info";// 税收居民信息
    public static final String USER_ACCOUNT_INFO = "user_account_info";// 账户信息
    public static final String VISIT_QUESTIONS_ANSWER = "visit_questions_answer";// 问卷回访回答
    public static final String RISK_QUESTIONS_ANSWER = "risk_questions_answer";// 风险测评答题
    public static final String BROKER_QUESTIONS_ANSWER = "broker_questions_answer";// 经纪人问卷答题
    // 银行信息
    public static final String USER_BANK_INFO = "user_bank_info";
    // 协议信息
    public static final String AGREEMENT_DATA_LIST = "agreement_data_list";
    // 视频见证人员
    public static final String VIDEO_OPERATOR_NO = "video_operator_no";
    // 身份补充信息
    public static final String USER_BASE_INFO = "user_base_info";
    // 身份基本信息
    public static final String ID_CARD_INFO = "id_card_info";
    // ocr信息
    public static final String OCR_USER_INFO = "ocr_user_info";
    // 通行证ocr
    public static final String OCR_PASS_INFO = "ocr_pass_info";
    // 居住证ocr
    public static final String OCR_RESIDENCE_INFO = "ocr_residence_info";
    // 通行证信息
    public static final String USER_PASS_INFO = "user_pass_info";
    // 居住证信息
    public static final String USER_RESIDENCE_INFO = "user_residence_info";
    // 用户视频信息
    public static final String USER_VIDEO_INFO = "user_video_info";
    // 柜台提交步骤信息
    public static final String SUBMIT_STEP_RESULT = "submit_step_result";
    // 智能分组
    public static final String AI_AUDIT_GROUP = "ai_audit_group";
    // 二次复核到二次复核工作流的标识
    public static final String ACTIVITI_CONTINUE_DOUBLE_FLAG = "activiti_continue_double_flag";

    // 复核到复核工作流的标识
    public static final String ACTIVITI_CONTINUE_REVIEW_FLAG = "activiti_continue_review_flag";

    // 复核到二次复核工作流的标识
    public static final String ACTIVITI_CONTINUE_REVIEW_DOUBLE_FLAG = "activiti_continue_review_double_flag";

    // 结束工作流的标识
    public static final String ACTIVITI_END_FLAG = "activiti_end_flag";


    // 失信记录
    public static final String DISHONEST_ID = "dishonest_id";

    // 任务转交发起时间
    public static final String TASK_TRANSFER_SPONSOR_TIME = "task_transfer_sponsor_time";

    // 任务转交应答时间
    public static final String TASK_TRANSFER_ANSWER_TIME = "task_transfer_answer_time";

    // 结果回调
    public static final String FLOW_REQUEST_INFORM = "inform";

    public static final String BUSI_SERIAL_NO = "busi_serial_no";

    public static final String TASK_ID = "task_id";//单笔提交任务id

    public static final String SPJZ_ID = "spjz_id";//视频见证id

    public static final String ORIGINAL_FILE_7C = "original_file_7C";
    public static final String ORIGINAL_FILE_7D = "original_file_7D";
    public static final String ORIGINAL_FILE_7X = "original_file_7X";
    public static final String ORIGINAL_FILE_7W = "original_file_7W";
    public static final String ORIGINAL_FILE_BM = "original_file_Bm";

    public static final String ORIGINAL_AUXILIARY_ID_ADDRESS = "original_auxiliary_id_address";

    public static final String ORIGINAL_AUXILIARY_ID_BEGINDATE = "original_auxiliary_id_begindate";

    public static final String ORIGINAL_AUXILIARY_ID_ENDDATE = "original_auxiliary_id_enddate";


    public static final String HAND_CLIENT_CATEGORY = "client_category";

    public static final String HAND_AGENT_NAME = "agent_name";

    public static final String HAND_AGENT_ID_KIND = "agent_id_kind";

    public static final String HAND_AGENT_ID_NO = "agent_id_no";

    public static final String HAND_AGENT_PHOTO_FRONT = "agent_photo_front";

    public static final String HAND_AGENT_PHOTO_BACK = "agent_photo_back";

    public static final String HAND_CERT_FILE_PATH = "cert_file_path";

    public static final String HAND_PHOTO_FRONT = "photo_front";

    public static final String HAND_PHOTO_BACK = "photo_back";

    public static final String HAND_MOBILE_TEL = "mobile_tel";

    public static final String FILE_8A = "file_8A";


}

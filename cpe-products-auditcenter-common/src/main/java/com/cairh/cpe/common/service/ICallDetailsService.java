package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.entity.CallDetails;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.entity.response.CallUuiResp;
import com.cairh.cpe.common.entity.support.BackendUser;
import com.cairh.cpe.context.BaseUser;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ICallDetailsService extends IService<CallDetails> {

    /**
     * 呼叫前参数
     * @param flowTaskId
     * @param baseUser
     * @param testMobile
     * @return
     */
    CallUuiResp sendCallRequest(String flowTaskId, BaseUser baseUser,
                                Map<String, BranchInfo> branchInfoMap,
                                BackendUser backendUser,
                                String testMobile);

    /**
     * 关闭呼叫
     * @param serial_id
     * @return
     */
    boolean cancelCall(String serial_id);

}

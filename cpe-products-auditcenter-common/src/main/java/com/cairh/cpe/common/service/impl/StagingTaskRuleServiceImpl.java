package com.cairh.cpe.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.RedisKeyConstant;
import com.cairh.cpe.common.entity.StagingTaskRule;
import com.cairh.cpe.common.mapper.StagingTaskRuleMapper;
import com.cairh.cpe.common.service.IStagingTaskRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2025/3/11 16:22
 */
@Slf4j
@Service
public class StagingTaskRuleServiceImpl extends ServiceImpl<StagingTaskRuleMapper, StagingTaskRule> implements IStagingTaskRuleService {


    @Cacheable(value = RedisKeyConstant.AUDTI_TASK_CACHE_RULE_LIST, unless = "#result?.size() == 0")
    @Override
    public List<StagingTaskRule> findAllStagingTaskRules() {
        LambdaQueryWrapper<StagingTaskRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StagingTaskRule::getStatus, "1");
        wrapper.orderByAsc(StagingTaskRule::getOrder_no);
        return this.list(wrapper);
    }

    @Scheduled(cron = "13 0 0 * * ?")
    @CacheEvict(value = RedisKeyConstant.AUDTI_TASK_CACHE_RULE_LIST, allEntries = true)
    @Override
    public void clearCache() {
        log.info("rule cache list 执行清除.");
    }

}

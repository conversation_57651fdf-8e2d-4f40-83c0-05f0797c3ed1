package com.cairh.cpe.common.entity.clob;

import com.cairh.cpe.common.entity.support.ArchfileinfoResp;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 暂存匹配字段信息
 *
 * <AUTHOR>
 * @since 2025/3/13 15:37
 */
@Data
public class StagingMatchInfo implements Serializable {

    /**
     * 受理编号
     */
    private String request_no;

    /**
     * 流程编号
     */
    private String businflow_no;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 注册时间
     */
    private Date request_datetime;

    /**
     * 提交审核时间
     */
    private Date submit_datetime;

    /**
     * 更新日期
     */
    private Date update_datetime;

    /**
     * 提交柜台时间
     */
    private Date cunter_datetime;

    /**
     * 激活时间
     */
    private Date active_datetime;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史日期
     */
    private Date tohis_datetime;

    /**
     * 当前业务状态
     */
    private String request_status;

    /**
     * 当前流程节点
     */
    private String anode_id;

    /**
     * 机构标志
     */
    private String organ_flag;

    /**
     * 开户类型
     */
    private String open_type;

    /**
     * 视频类型 1-单项，2-双向
     */
    private String video_type;

    /**
     * 用户编号
     */
    private String user_id;

    /**
     * 手机号码
     */
    private String mobile_tel;

    /**
     * 英文姓名
     */
    private String english_name;

    /**
     * 客户姓名
     */
    private String client_name;

    /**
     * 证件类别
     */
    private String id_kind;

    /**
     * 证件号码
     */
    private String id_no;

    /**
     * 性别
     */
    private String user_gender;
    /**
     * 主证件类型
     */
    private String main_type;
    /**
     * 辅助证件类型
     */
    private String assist_type;
    /**
     * 分支机构
     */
    private String branch_no;

    /**
     * 上级分支机构
     */
    private String up_branch_no;

    /**
     * 真实分支机构
     */
    private String real_branch_no;

    /**
     * 营业部名称
     */
    private String branch_name;

    /**
     * 客户编号
     */
    private String client_id;

    /**
     * 资产账户
     */
    private String fund_account;

    /**
     * 经纪人编号
     */
    private String broker_code;

    /**
     * 经纪人姓名
     */
    private String broker_name;
    /**
     * 推荐人姓名
     */
    private String referer_name;
    /**
     * 推荐人手机号
     */
    private String referer_mobile;
    /**
     * 接入方式
     */
    private String app_id;

    /**
     * 渠道编号
     */
    private String channel_code;
    /**
     * 渠道名称
     */
    private String channel_name;

    /**
     * 开户渠道(channel_code的父节点)
     */
    private String open_channel;

    /**
     * 选择开户的账户1 股东 2基金 3股东+基金
     */
    private String opt_account_type;

    /**
     * 支付宝编号
     */
    private String alipay_no;

    /**
     * 完成节点
     */
    private String finish_node;

    /**
     * 整改节点
     */
    private String amend_node;

    /**
     * 清除节点
     */
    private String clear_node;

    /**
     * 模板编号
     */
    private String model_no;

    /**
     * 佣金
     */
    private String commission;

    /**
     * 套餐编号
     */
    private String comb_no;

    /**
     * 站点信息
     */
    private String op_station;

    /**
     * 身份证人像面
     */
    private String file_6A;

    /**
     * 身份证国徽面
     */
    private String file_6B;
    private String file_7C;
    private String file_7D;

    /**
     * 大头照
     */
    private String file_80;

    /**
     * 公案照
     */
    private String file_82;

    /**
     * 视频信息
     */
    private String file_8A;

    /**
     * 资金密码
     */
    private String capital_password;

    /**
     * 通讯密码
     */
    private String connect_password;

    /**
     * 交易密码
     */
    private String transaction_password;

    /**
     * 养老金密码
     */
    private String pension_password;

    /**
     * 证书密码
     */
    private String cert_password;

    /**
     * 税收信息
     */
    private RevenueInfo revenue_info;

    /**
     * 初始投资额度
     */
    private String initial_investment_amount;

    /**
     * ocr信息,身份证
     */
    private IDCardOcrInfo ocr_user_info;

    /**
     * 通行证ocr
     */
    private IDCardOcrInfo ocr_pass_info;
    /**
     * 居住证ocr
     */
    private IDCardOcrInfo ocr_residence_info;

    /**
     * 身份基本信息
     */
    private IDCardInfo id_card_info;

    /**
     * 身份补充信息
     */
    private UserBaseInfo user_base_info;

    /**
     * 账户信息
     */
    private UserAccountAll user_account_info;

    /**
     * 银行信息
     */
    private UserBankInfo user_bank_info;

    /**
     * 视频见证信息
     */
    private VideoInfo user_video_info;

    /**
     * 主证件信息
     */
    private CardInfo main_card_info;

    /**
     * 辅证件信息
     */
    private CardInfo assist_card_info;

    /**
     * 诚信档案
     */
    private String file_23;
    /**
     * 整改原因
     */
    private List<AuditReason> rectification_item;
    /**
     * 档案信息
     */
    private ArchfileinfoResp archfile_info;
    /**
     * 国籍地区
     */
    private String nationality;
    /**
     * 营业部省市
     */
    private String branch_ssq;
    /**
     * 本人公安认证
     */
    private PoliceVerifyResult police_owner;
    /**
     * 非本人公安认证
     */
    private PoliceVerifyResult police_mobile_tel_owner;
    /**
     * 受益人公安认证
     */
    private PoliceVerifyResult police_benefit;
    /**
     * 手机实名制
     */
    private MobileVerifyResult mobile_owner;
    /**
     * 人脸比对结果 1-大头照与公安照
     */
    private FaceCompareResult face_compare_1;
    /**
     * 人脸比对结果 2-证件照与公案照
     */
    private FaceCompareResult face_compare_2;
    /**
     * 人脸比对结果 3-大头照与证件照
     */
    private FaceCompareResult face_compare_3;

    /**
     * 推荐信息
     */
    private String recommender;

    /**
     * 黑名单信息
     */
    private BlackInfo blackInfo;

    /**
     * 重点监控账户
     * 0 不是  1是
     */
    private String monitor_account_flag;
    /**
     * 手机号归属地
     */
    private String mobile_location;
    /**
     * 是否为渠道页面注册
     */
    private String channel_register_source;
    /**
     * 城市编号
     */
    private String city_no;
    /**
     * 城市名称
     */
    private String city_name;
    /**
     * 预约时间
     */
    private String appointment_date;
    /**
     * （委托方式）
     */
    private String service_type;
    /**
     * 反洗钱风险等级
     */
    private String aml_risk_level;
    /**
     * 客户标签
     */
    private String client_tags;
    /**
     * 智能审核申请编号
     */
    private String ai_audit_code;
    /**
     * 智能审核语音发起回传
     */
    private String ai_audio_identity;
    /**
     * 指定客户号
     */
    private String assign_client_id;

    /**
     * 开户来源
     */
    private String channel_flag;

    /**
     * 异地开户理由
     */
    private String choose_branch_reason;

    /**
     * 活动名称
     */
    private String activity_name;

    /**
     * 营销团队
     */
    private String marketing_team;

    /**
     * 人脸比对结果 3-大头照与证件照(证通分数)
     */
    private String face_score;

    /**
     * 证通分数说明
     */
    private String face_score_desc;

    /**
     * 人脸比对结果 公安照和大头照
     */
    private String face_score_82_80;

    /**
     * 开户人脸比对分数(公安照和免冠照)
     */
    private String kh_face_score_82_80;

    /**
     * 失信记录
     */
    private String dishonest_record;

    /**
     * 失信记录备注
     */
    private String dishonest_record_remark;

    /**
     * 失信记录文本内容
     */
    private String dishonest_content;

    /**
     * 失信记录文件id（crh-rpa查询结果）
     */
    private String dishonest_id;

    /**
     * rpc_file_id 文件
     */
    private String rpc_file_id;

    /**
     * rpc备注
     */
    private String rpc_remark;

    /**
     * rpc_options 核查选项
     */
    private String rpc_option;


    private String original_rpc_file_id;

    private String original_rpc_remark;

    private String original_rpc_option;

    /**
     * 活动编号
     */
    private String activity_no;


    private String video_speech_id;//话术id


    private String spjz_id;//审核流水id


    private String pc_id;//开户流水id

    //初始化进入详情页面的用户初始数据
    private String original_file_6A; //原始身份证人像面

    private String original_file_6B; //原始身份证国徽面

    private String original_file_7C; //原始居住证正面

    private String original_file_7D; //原始居住证反面

    private String original_file_80; //原始大头照

    private String original_id_address;// 证件地址

    private String original_id_begindate;// 证件有效期开始日期YYYYMMDD

    private String original_id_enddate;// 证件有效期结束日期YYYYMMDD

    private String original_auxiliary_id_address;// 辅助证件地址

    private String original_auxiliary_id_begindate;// 辅助证件有效期开始日期YYYYMMDD

    private String original_auxiliary_id_enddate;// 辅助证件有效期结束日期YYYYMMDD

    private String original_address;// 经常居住地址

    private String original_choose_branch_reason; // 异地开户理由

    private String original_choose_profession_reason; // 选择职业理由

    private String original_profession_code; // 职业

    private String original_work_unit; // 工作单位

    private String original_dishonest_record; // 失信记录

    private String original_dishonest_record_remark; // 失信记录备注信息

    private String original_profession_other; // 核查信息备注

    private String task_transfer_sponsor_time; // 任务转交发起时间

    private String task_transfer_answer_time; // 任务转交应答时间

    //流程流转标识
    private String activiti_continue_double_flag; //二次复核到二次复核工作流的标识

    private String activiti_continue_review_flag; //复核到复核工作流的标识

    private String activiti_continue_review_double_flag; //复核到二次复核工作流的标识

    private String activiti_end_flag; //结束工作流的标识

    // 翻译
    private String choose_branch_reason_content; // 异地开户理由文本

    private String choose_profession_reason_content; // 选择职业理由文本

    private String translation_address; // 经常居住地址(字典翻译)

    private String alternative_address; // 经常居住地址(使用!~分隔的地址)

    private String finish_flag;

    private String birthday;    //出生日期(跨境理财通身份证无法获取出生日期,使用前端传入的出生日期)

    /**
     * 开户是否修改
     */
    private String initial_address_modify_flag;

    /**
     * 开户类别
     */
    private String client_category;

    /**
     * 经办人姓名
     */
    private String agent_name;

    /**
     * 经办人证件类型
     */
    private String agent_id_kind;

    /**
     * 经办人证件号码
     */
    private String agent_id_no;

    /**
     * 经办人正面照片
     */
    private String agent_photo_front;

    /**
     * 经办人反面照片
     */
    private String agent_photo_back;

    /**
     * original经办人正面照片
     */
    private String original_agent_photo_front;

    /**
     * original经办人反面照片
     */
    private String original_agent_photo_back;

    /**
     * 资料信息
     */
    private String cert_file_path;

    /**
     * 客户证件正面
     */
    private String photo_front;

    /**
     * 客户证件反面
     */
    private String photo_back;

    /**
     * original客户证件正面
     */
    private String original_photo_front;

    /**
     * original客户证件反面
     */
    private String original_photo_back;

    /**
     * 国籍
     */
    private String original_nationality;

    /**
     * 英文姓名
     */
    private String original_english_name;

    /**
     * 曾持有证件号
     */
    private String original_prev_id_number;

    /**
     * 当前分数为中登or证通 1-中登 2-证通
     */
    private String score_type;

    /**
     * 状态码
     */
    private String deal_status;

    /**
     * 处理结果
     */
    private String deal_info;

    /**
     * 数据标记 老数据为空 新数据为1
     */
    private String data_sign;

    /**
     * 外国人永居证三要素校验结果
     * 0-验证失败 1-验证成功
     */
    private String permanent_id_info_check_result;

    /**
     * 任务类型
     */
    private String task_type;

    /**
     * 任务状态
     */
    private String task_status;
}

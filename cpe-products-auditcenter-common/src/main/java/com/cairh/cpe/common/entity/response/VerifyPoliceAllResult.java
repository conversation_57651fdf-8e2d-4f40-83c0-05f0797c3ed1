package com.cairh.cpe.common.entity.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 全要素公安认证出参
 */
@Data
@Accessors(chain = true)
public class VerifyPoliceAllResult implements Serializable {

    /**
     * 公安认证记录id
     */
    private String idverifyrecord_id;

    /**
     * 文件记录id
     */
    private String filerecord_id;

    /**
     * 人像图片
     */
    private String image_data;

    /**
     * 认证状态  公安认证状态  0-失败 1-通过
     */
    private String status;

    /**
     * 认证结果信息
     */
    private String result_info;


    /**
     * 认证分数
     */
    private String score;

    /**
     * 认证额外信息
     */
    private String ext_info;

    private String idc_result;
    private String face_result;
    private String auth_result;
    private String auth_info;
}

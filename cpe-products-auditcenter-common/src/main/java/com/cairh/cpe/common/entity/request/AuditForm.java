package com.cairh.cpe.common.entity.request;

import com.cairh.cpe.common.entity.clob.AuditReason;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 任务审核人信息
 *
 * <AUTHOR>
 */
@Data
public class AuditForm {


    @NotBlank(message = "任务ID不能为空")
    private String task_id;

    // 任务编号
    private String request_no;

    // 审核人编号
    private String operator_no;

    // 审核人姓名
    private String operator_name;

    // 任务状态  3通过,4不通过,5审核退回
    private String task_status;

    // 处理结果
    private String audit_remark;

    // 不通过原因
    private List<AuditReason> reasons;

    /**
     * 预约时间
     */
    private String pre_datetime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 延迟原因
     */
    private String delay_reason;

    // 不拦截字段
    private String ignoreInterceptorsFlag;

    //处理类型
    private String handle_type;

    // 删除智能审核不通过的原因
    private String ai_nopass_reason;

    // 推送智能派单的状态
    private String push_flag;

    //  通过后的状态(防止状态查询不正常，生成重复复核任务)
    private String pass_request_status;

    /**
     * 用户所属机构
     */
    private String branch_no;

    /**
     * 用户所属机构名称
     */
    private String branch_name;

    /**
     * 用户所属上级机构
     */
    private String up_branch_no;

    /**
     * 用户所属上级机构名称
     */
    private String up_branch_name;

    /**
     * 是否托管分支机构 0-托管分支机构 1-非托管分支机构
     */
    private String is_branch_managed;


    public String getReasonRemark() {
        StringBuilder str = new StringBuilder();
        if (CollectionUtils.isNotEmpty(reasons)) {
            for (AuditReason reason : reasons) {
                str.append("aiaudit_reject").append(":");
                if (reason.getReason_type().equals("aiaudit_reject")) {
                    str.append(reason.getReason_desc());
                }
            }
            str.substring(str.length() - 1);
            str.append(";");
            for (AuditReason reason : reasons) {
                str.append("reject").append(":");
                if (reason.getReason_type().equals("reject")) {
                    str.append(reason.getReason_desc());
                }
            }
            str.substring(str.length() - 1);
            str.append(";");
            for (AuditReason reason : reasons) {
                str.append("privateReject").append(":");
                if (reason.getReason_type().equals("privateReject")) {
                    str.append(reason.getReason_desc());
                }
            }
            str.substring(str.length() - 1);
            str.append(";");
        }
        return str.toString();
    }
}

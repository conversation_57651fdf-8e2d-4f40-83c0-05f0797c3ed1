package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.constant.UpdateBusinProcessRequestAuditTrailSourceEnum;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.FlowTaskRecordDetails;

import java.util.Map;

/**
 * Description：流程业务记录详情表服务
 * Author： slx
 * Date： 2024/4/25 下午3:03
 */
public interface IFlowTaskRecordDetailsService extends IService<FlowTaskRecordDetails> {

    /**
     * 开户、复核、二次复核-保存业务流程详情信息
     * 数量与BusinFlowTask表中数量一致
     *
     * @param businFlowTask 任务信息
     * @param params        请求参数
     */
    void saveFlowTaskRecordDetails(BusinFlowTask businFlowTask, Map<String, Object> params);


    /**
     * 更新业务流程信息
     *
     * @param businFlowTask 任务信息
     * @param userParams    操作用户信息（用户所属机构&上级机构）
     * @param sourceEnum    更新来源
     */
    void updateFlowTaskRecordDetails(BusinFlowTask businFlowTask, Map<String, Object> userParams, UpdateBusinProcessRequestAuditTrailSourceEnum sourceEnum);

}

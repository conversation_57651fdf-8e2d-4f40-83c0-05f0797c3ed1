package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.entity.Label;

import java.util.List;
import java.util.Map;


public interface ILabelService extends IService<Label> {

    Page<Label> queryLabelList(Page<Label> page);

    boolean addLabel(Label label);

    boolean updateLabel(Label label);

    List<Label> getMatchLabel(String request_no, Map<String,Object> map);

    Map<String,Label> getMapLabels();
}

package com.cairh.cpe.common.util;

import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class ParamsSavingUtils {

    /**
     * 根据参数length,将String类型对象，进行截取
     * 用于将长字符串，存入数据库中
     * 避免过长 数据库保存失败
     * 避免直接写死长度 产生不必要数据
     * 前提：一个汉字 占3个字节
     * 一个英文 占1个字节
     *
     * @param content   需要截取的字符串
     * @param lengthMax 数据库中存储的最大长度
     * @param lengthCN  汉字占的字节数
     * @return List<String>
     */
    public static List<String> getList(String content, int lengthMax, int lengthCN) {
        List<String> resultList = new ArrayList<>();
        if (StringUtils.isEmpty(content) || lengthMax <= 0 || lengthCN <= 0 || (lengthMax <= lengthCN)) {
            throw new RuntimeException("参数非法");
        }
        while (true) {
            if (content.length() <= lengthMax / lengthCN) {
                resultList.add(content);
                break;
            } else {
                int lenStart = 0;
                for (int i = 0; i < lengthMax / lengthCN; i++) {
                    String c = content.substring(i, i + 1);
                    lenStart += c.getBytes(StandardCharsets.UTF_8).length;
                }
                int i = lengthMax / lengthCN;
                while (lenStart <= lengthMax && i < content.length()) {
                    String c = content.substring(i, i + 1);
                    lenStart += c.getBytes(StandardCharsets.UTF_8).length;
                    i++;
                }
                if (lenStart <= lengthMax) {
                    resultList.add(content);
                    break;
                } else {
                    String s = content.substring(0, i - 1);
                    int index = s.lastIndexOf(",");
                    i = index == -1 ? i - 1 : index;

                    resultList.add(s.substring(0, i));

                    content = content.substring(i);
                }
            }
        }
        return resultList;
    }


    public static <T> T noNullStringAttr(T cls) {
        Field[] fields = cls.getClass().getDeclaredFields();
        if (fields == null || fields.length == 0) {
            return cls;
        }
        for (Field field : fields) {
            if ("String".equals(field.getType().getSimpleName())) {
                field.setAccessible(true);
                try {
                    Object value = field.get(cls);
                    if (value == null) {
                        field.set(cls, "");
                    }
                } catch (IllegalArgumentException | IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
        return cls;
    }
}
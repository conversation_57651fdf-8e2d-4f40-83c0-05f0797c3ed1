package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 双向视频对话记录表
 *
 * <AUTHOR>
 * @since 2024/12/3 15:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("BidirectionalVideoChatLog")
public class BidirectionalVideoChatLog {

    /**
     * 流水号
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * request_no
     */
    @TableField("request_no")
    private String request_no;

    /**
     * 话术内容
     */
    @TableField("words_content")
    private String words_content;

    /**
     * 操作员编号
     */
    @TableField("operator_no")
    private String operator_no;

    /**
     * 操作员名称
     */
    @TableField("operator_name")
    private String operator_name;

    /**
     * 创建时间
     */
    @TableField("create_datetime")
    private Date create_datetime;

    /**
     * 归档标识，取值范围，1-已归档、0-未归档
     */
    @TableField("tohis_flag")
    private String tohis_flag;

    /**
     * 归档时间
     */
    @TableField("tohis_datetime")
    private Date tohis_datetime;

}

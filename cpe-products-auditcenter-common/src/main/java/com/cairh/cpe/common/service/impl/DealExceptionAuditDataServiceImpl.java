package com.cairh.cpe.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cairh.cpe.common.constant.Fields;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.mapper.BusinFlowParamsMapper;
import com.cairh.cpe.common.mapper.UserQueryExtInfoMapper;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.IDealExceptionAuditDataService;
import com.cairh.cpe.common.service.IRequestFlowService;
import com.cairh.cpe.common.util.RedissonUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Slf4j
@Service
public class DealExceptionAuditDataServiceImpl implements IDealExceptionAuditDataService {

    @Resource
    private RedissonUtil redissonUtil;
    @Resource
    private IBusinFlowTaskService businFlowTaskService;

    @Resource
    private IBusinFlowRequestService businFlowRequestService;

    @Resource
    private UserQueryExtInfoMapper userQueryExtInfoMapper;

    @Resource
    private BusinFlowParamsMapper businFlowParamsMapper;

    @Autowired
    private IRequestFlowService requestFlowService;

    @Transactional(rollbackFor = Exception.class)
    public boolean backlExceptionAuditData(String serial_id,String request_no) {

        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, request_no);
        try {
            boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
            if (!isLock) {
                log.info("任务回退未获取到锁，request_no={},serial_id={}",request_no,serial_id);
                return Boolean.FALSE;
            }
            BusinFlowTask businFlowTask = businFlowTaskService.getById(serial_id);
            Assert.notNull(businFlowTask, "businFlowTask not find by serial_id");
            //重置任务表
            LambdaUpdateWrapper<BusinFlowTask> businFlowTaskLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            businFlowTaskLambdaUpdateWrapper
                    .set(BusinFlowTask::getOperator_no, " ")
                    .set(BusinFlowTask::getOperator_name, " ")
                    .set(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING)
                    .set(BusinFlowTask::getPush_flag, WskhConstant.NO_PUSH_FLAG)
                    .set(BusinFlowTask::getDeal_datetime, null)
                    .set(BusinFlowTask::getTask_source, " ");
            businFlowTaskLambdaUpdateWrapper.eq(BusinFlowTask::getSerial_id, businFlowTask.getSerial_id());
            businFlowTaskService.update(businFlowTaskLambdaUpdateWrapper);

            //重置主表
            LambdaUpdateWrapper<BusinFlowRequest> businFlowRequestLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            businFlowRequestLambdaUpdateWrapper.set(BusinFlowRequest::getOperator_no, " ")
                    .set(BusinFlowRequest::getRequest_status, FlowStatusConst.AUDIT_PENDING)
                    .set(BusinFlowRequest::getAnode_id,businFlowTask.getTask_type())
                    .eq(BusinFlowRequest::getRequest_no, businFlowTask.getRequest_no());
            businFlowRequestService.update(businFlowRequestLambdaUpdateWrapper);
            // 删除快照
            userQueryExtInfoMapper.deleteById(serial_id);
            //删除大字段
            businFlowParamsMapper.deleteById(serial_id);

            Map<String, Object> params = Maps.newHashMap();
            params.put(Fields.OPERATOR_NO, " ");
            params.put(Fields.OPERATOR_NAME," ");
            params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_RECORD_TYPE);
            params.put(Fields.REQUEST_STATUS, FlowStatusConst.REQUEST_STATUS_PENDING_AUDIT);
            params.put(Fields.ANODE_ID, businFlowTask.getTask_type());
            //重置大字段表 根据流程
            if (FlowNodeConst.AUDIT.equals(businFlowTask.getTask_type())) {
                params.put(Fields.FINISH_NODE, "theStart,audit");
            }else if (FlowNodeConst.REVIEW.equals(businFlowTask.getTask_type()) ){
                params.put(Fields.FINISH_NODE, "theStart,audit,review");
            }else if (FlowNodeConst.SECONDARY_REVIEW.equals(businFlowTask.getTask_type()) ){
                params.put(Fields.FINISH_NODE, "theStart,audit,review,secondary_review");
            }
            requestFlowService.saveParamsRecord(businFlowTask.getRequest_no(), params);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("推送派单系统发生异常信息 request_no={},serial_id={}",request_no,serial_id, e);
        } finally {
            redissonUtil.unlock(lockKey);
        }
        return Boolean.FALSE;
    }
}

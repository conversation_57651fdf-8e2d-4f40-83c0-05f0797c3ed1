package com.cairh.cpe.common.entity.response;

import com.cairh.cpe.common.entity.clob.AuditReason;
import com.cairh.cpe.common.entity.support.AuditInfo;
import com.cairh.cpe.common.entity.support.OperatorInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AuditResultResp {

    /**
     * 审核结果
     */
    private String audit_status;

    /**
     * 打回所处节点
     */
    private String back_code;

    /**
     * 打回原因
     */
    private List<AuditReason> back_reason;

    /**
     * 审核人信息
     */
    private OperatorInfo operator_info;

    /**
     * 可更改的所有信息（状态为审核完成时返回）
     */
    private AuditInfo audit_info;

    /**
     * 人脸比对分数（公安照和大头照）
     */
    private String face_score_82_80;

    /**
     * 开户人脸比对分数(公安照和免冠照)
     */
    private String update_info_flag;

    /**
     * 是否二次复核 0 不存在 1存在
     */
    private String exist_double_review;

    /**
     * 证通分数
     */
    private String face_score;

    /**
     * 证通分数描述说明
     */
    private String face_score_desc;

    /**
     * 完成标识 0 未完成 1 完成
     */
    private String finish_flag;

    /**
     * 流程状态
     */
    private String anode_id;

    /**
     * 见证作废原因
     */
    private String op_content;


    public AuditResultResp(String audit_status){
        this.audit_status = audit_status;
    }

    public AuditResultResp(){
    }
}

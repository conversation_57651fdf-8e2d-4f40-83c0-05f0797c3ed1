package com.cairh.cpe.common.entity.clob;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserAccountInfo implements Serializable {

    /**
     * 市场类别[数据字典] </br>10 上海A股账户 </br>11 上海场内基金 </br>20 深圳A股账户 </br>21 深圳场内基金 </br>G0 沪港通 </br>S0 深港通
     */
    private String exchange_kind;
    /**
     * 证券账号开通方式 </br>1 新开 </br>2 转户
     */
    private String stock_open_type;
    /**
     * 下挂证券账号
     */
    private String under_stock_account;
}

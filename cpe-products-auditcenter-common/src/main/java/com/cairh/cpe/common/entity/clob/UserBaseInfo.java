package com.cairh.cpe.common.entity.clob;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserBaseInfo implements Serializable {

    private String degree_code;// 学历代码[字典] </br>字典编号:1014

    private String profession_code;// 职业代码[字典] </br>字典编号:1015

    private String profession_dic;// 职业代码[字典] </br>字典编号:1015

    /*private String profession_other;// 其他职业描述*/

    private String industry_type;// 行业

    private String industry_other;// 行业其它

    private String work_unit;// 工作单位

    private String duty;// 职务

    private String duty_other;// 其它

    private String identity_category;// 自然人类别

    private String year_income;// 年收入

    private String income_other;// 其他年收入

    private String income_unit;// 年收入单位

    private String income_source_other;// 收入来源其它

    private String family_income;// 家庭年收入

    private String finance_asset;// 金融资产

    private String e_mail;// 电子信箱

    private String home_tel;// 家庭电话

    private String house_property;// 房产价值

    private String fund_source;// 入市资金来源 字典1024

    private String income_source; // 收入来源

    private String control_person;// 操作控制人  控制人是否是本人 1是 2否

    private String benefit_person;// 受益人 受益人是否是本人 1是 2否

    private String benefit_person_name;// 受益人姓名

    private String benefit_id_no;// 受益人身份证号

    private String benefit_relation; // 受益人关系

    private String benefit_mobile; // 受益人电话号码

    private String dishonest_flag;// 不良诚信记录标识 不良诚信记录1:良好 2：否

    private String credit_record;// 不良诚信记录来源

    private String credit_record_other;// 不良诚信记录其它

    private String sec_relation_name;// 第二联系人姓名

    private String sec_relation_phone;// 第二联系人联系电话

    private String sec_relation_relation;// 社会关系类型[字典] </br>数据字典:1068

    private String sec_relation_flag;// 是否增加第二联系人标志 1、添加 2、不添加

    private String sec_relation_type;// 第二联系人联系联系人类型

    private String sec_relation_id_kind;// 第二联系人联系证件类别

    private String sec_relation_id_no;// 第二联系人联系证件号码

    private String sec_relation_id_begindate;// 第二联系人联系证件起始时间

    private String sec_relation_id_enddate;// 第二联系人联系证件结束时间

    private String sec_relation_address;// 第二联系人联系地址

    private String sec_relation_zipCode;// 第二联系人邮政编码

    private String sec_relation_email;// 第二联系人邮箱

    private String mobile_tel_owner_flag; // 手机号码机主 0本人，1非本人

    private String mobile_tel_owner_name;  // 机主姓名

    private String mobile_tel_owner_id_no;  // 机主证件号

    private String mobile_tel_owner_relation;// 与移动电话机主关系

    private String referral_organ;// 引荐机构

    private String referral_people;// 引荐人

    private String referral_branch_organ;// 引荐分支机构

    private String birthplace;// 出生地

    private String emergency_contact_mobile;// 紧急联系电话

    private String solve_way;// 争议解决方式

    private String board_arbitrate;// 仲裁机构

    private String residential_address;// 现居住地址

    private String redsidence_address;// 住所地或工作单位地址

    private String financial_asset;// 金融类资产

    private String motor_vehicle_value;// 机动车价值

    private String other_references;// 其他经纪人编号

    private String investment_no;// 投顾编号

    private String investment_name;// 投顾名称

    private String detached_house;// 独栋别墅

    // 非对象存储信息

    private Integer age;// 年龄

    private String examAge;// 风测年龄


    private String zipcode;// 邮编

    private String address_ssq;// 省市区

    private String address_ssq_code;// 省市区编码

    private String same_address;// 同证件地址,0-不同，1-相同

    private String initial_address;//原始地址


    private String choose_branch_reason; // 异地开户理由

    private String choose_profession_reason; // 选择职业理由

    private String dishonest_record; // 失信记录

    private String dishonest_record_remark; // 失信记录备注信息

    private String profession_other;//核实备注信息

    private String address;// 经常居住地址

    private String real_branch_no;

    private String deal_status;// 状态码

    private String deal_info;// 结果描述

}

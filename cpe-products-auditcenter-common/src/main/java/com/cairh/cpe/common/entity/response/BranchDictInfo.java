package com.cairh.cpe.common.entity.response;


import lombok.Data;

@Data
public class BranchDictInfo {

    // 字典编号
    private String dict_code;

    // 字典名称
    private String dict_name;

    // 子项编号
    private String sub_code;

    // 子项名称
    private String sub_name;

    // 排序
    private Integer order_no;

    // 备注
    private String remark;

    // 状态
    private String status;

    // 营业部编号
    private String branch_no;

    // 营业部名称
    private String branch_name;

    // 升级营业部编号
    private String up_branch_no;

    // 营业部类型
    private String branch_type;

    // 省代码
    private String province_code;

    // 市代码
    private String city_code;

    // 区代码
    private String region_code;
}

package com.cairh.cpe.common.constant;

import lombok.Getter;

/**
 * 表达式
 *
 * <AUTHOR>
 * @since 2023/10/13 11:01
 */
@Getter
public enum ExpressionEnum {

    NOT_IN("not_in", "不属于"),
    IN("in", "属于"),
    CONTAIN("contain", "包含"),
    STARTSWITH("startsWith", "始于"),
    BETWEEN("between", "介于");

    private final String relationShip;
    private final String relationShipDesc;

    ExpressionEnum(String relationShip, String relationShipDesc) {
        this.relationShip = relationShip;
        this.relationShipDesc = relationShipDesc;
    }

}

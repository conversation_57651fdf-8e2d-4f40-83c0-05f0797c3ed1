package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;


/**
 * @program: cpe-products-ac
 * @description: his业务流程申请表
 * @author: dsk
 * @create:
 **/
@TableName("His_BusinFlowRequest")
@Data
@EqualsAndHashCode(callSuper = false)
public class His_BusinFlowRequest implements Serializable {

    /**
     * 受理编号
     */
    @TableId("request_no")
    private String request_no;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date request_datetime;

    /**
     * 提交审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date submit_datetime;


    /**
     * 更新日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date update_datetime;

    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date tohis_datetime;
    /**
     * 当前业务状态
     */
    private String request_status;

    /**
     * 当前流程节点
     */
    private String anode_id;

    /**
     * 上次操作员编号
     */
    private String operator_no;


    /**
     * 用户编号
     */
    private String user_id;

    /**
     * 手机号码
     */
    private String mobile_tel;

    /**
     * 客户姓名
     */
    private String client_name;

    /**
     * 证件类别
     */
    private String id_kind;

    /**
     * 证件号码
     */
    private String id_no;

    /**
     * 分支机构
     */
    private String branch_no;

    /**
     * 渠道编号
     */
    private String channel_code;

    /**
     * 是否已完成国泰开户结果回调
     * <p>
     * 0-否; 1-是
     */
    private String inform;

    /**
     * pc_id
     */
    private String busi_serial_no;

    /**
     * 开户重复
     */
    private String branch_repeated;


    /**
     * 地址重复
     */
    private String address_repeated;

    /**
     * 标签字段
     */
    private String match_labels;

    /**
     * 网厅业务办理-业务申请编号
     */
    private String business_apply_no;
}

package com.cairh.cpe.common.entity.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 经纪人查询信息
 */
@Data
public class BrokerInfo implements Serializable {

    // 经纪人编号
    private String broker_code;

    // 机构标志
    private String organ_flag;

    // 用户名
    private String user_name;
    // 经纪人姓名
    private String broker_name;
    // 经纪人手机号
    private String broker_mobile;

    // 父节点ID
    private String parent_id;

    // 手机号码
    private String mobile_tel;

    // 电子信箱
    private String e_mail;

    // 组织机构代码
    private String organ_code;

    // 联系地址
    private String address;

    // 职业证书号
    private String profession_cert;

    // 公司名称
    private String company_name;

    // 分支机构
    private String branch_no;

    // 网站备案号
    private String web_record;

    // 单位电话
    private String office_tel;

    // 负责人姓名
    private String duty_name;

    // 负责人联系电话
    private String duty_tel;

    // qq号码
    private String qq;

    // 微信账号
    private String wechat_id;

    // 客户关系人状态
    private String broker_status;

    // 开户日期
    private Integer open_date;

    // 经纪人级别
    private String broker_level;

    // 银行账号
    private String bank_account;

    // 银行名称
    private String bank_name;

    // 客户关系人类别
    private String broker_type;

    // 营销标志
    private String marketing_flag;

    // 合作方代码
    private String partner_code;

    // 来源
    private String source_info;

    // 省名
    private String province_name;

    // 市名
    private String city_name;

    // 图片保存路径
    private String image_path;

    // 人员状态
    private String staff_status;

    // 人员类型
    private String staff_type;

    // 入职日期
    private Integer joined_date;

    // 离职日期
    private Integer leave_date;

    // 是否通过考试
    private String is_pass_exam;

    // 证件号码
    private String id_no;

    // 执业范围
    private String practice_area;

    // 渠道状态
    private String channel_status;

    public String getBroker_name() {
        return user_name;
    }

    public String getBroker_mobile() {
        return mobile_tel;
    }
}

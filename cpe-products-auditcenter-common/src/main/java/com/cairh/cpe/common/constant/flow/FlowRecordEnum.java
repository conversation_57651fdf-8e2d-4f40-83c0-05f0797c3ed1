package com.cairh.cpe.common.constant.flow;

import java.util.HashMap;
import java.util.Map;

public enum FlowRecordEnum {

    B1001("1001", "开始见证"),
    B1002("1002", "见证通过"),
    B1003("1003", "见证打回"),
    B1004("1004", "见证移交"),

    B1101("1101", "开始复核"),
    B1102("1102", "复核通过"),
    B1103("1103", "复核打回"),
    B1104("1104", "复核移交"),

    B1201("1201", "开始二次复核"),
    B1202("1202", "二次复核通过"),
    B1203("1203", "二次复核打回"),
    B1204("1204", "二次复核移交"),

    B1017("1017", "客户信息修改");

    public static Map<String, FlowRecordEnum> BEGIN = new HashMap<>();
    public static Map<String, FlowRecordEnum> PASS = new HashMap<>();
    public static Map<String, FlowRecordEnum> NOPASS = new HashMap<>();
    public static Map<String, FlowRecordEnum> HANDOVER = new HashMap<>();

    static {
        BEGIN.put(FlowNodeConst.AUDIT, FlowRecordEnum.B1001);
        BEGIN.put(FlowNodeConst.REVIEW, FlowRecordEnum.B1101);
        BEGIN.put(FlowNodeConst.SECONDARY_REVIEW, FlowRecordEnum.B1201);

        PASS.put(FlowNodeConst.AUDIT, FlowRecordEnum.B1002);
        PASS.put(FlowNodeConst.REVIEW, FlowRecordEnum.B1102);
        PASS.put(FlowNodeConst.SECONDARY_REVIEW, FlowRecordEnum.B1202);

        NOPASS.put(FlowNodeConst.AUDIT, FlowRecordEnum.B1003);
        NOPASS.put(FlowNodeConst.REVIEW, FlowRecordEnum.B1103);
        NOPASS.put(FlowNodeConst.SECONDARY_REVIEW, FlowRecordEnum.B1203);

        HANDOVER.put(FlowNodeConst.AUDIT, FlowRecordEnum.B1004);
        HANDOVER.put(FlowNodeConst.REVIEW, FlowRecordEnum.B1104);
        HANDOVER.put(FlowNodeConst.SECONDARY_REVIEW, FlowRecordEnum.B1204);
    }

    private String value;
    private String desc;

    private FlowRecordEnum(String value, String desc) {
        this.setValue(value);
        this.setDesc(desc);
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}

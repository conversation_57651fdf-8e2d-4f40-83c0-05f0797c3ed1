package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.entity.ChannelDefine;

import java.util.List;

public interface IChannelDefineService extends IService<ChannelDefine> {

    /**
     * 通过渠道编号查询一条渠道信息
     */
    ChannelDefine getChannelByCode(String channel_code);

    /**
     * 渠道名称模糊查询
     */
    List<String> getChannelCodesByLikeName(String channel_name);

    List<ChannelDefine> getAllChannel();

    void refresh();
}

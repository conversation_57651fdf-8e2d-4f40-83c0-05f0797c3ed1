package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("rpaRecord")
public class RpaRecord {


    /**
     * 业务流水号
     */
    @TableId("request_no")
    private String request_no;


    /**
     * 客户姓名
     */
    private String client_name;

    /**
     * 客户身份证号
     */
    private String id_no;

    /**
     * 任务状态
     */
    private String task_status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 更改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date update_datetime;

    /**
     * 归历史标识
     */
    private String tohis_flag;

    /**
     * 归历史时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date tohis_datetime;


    /**
     * 备注
     */
    private String remark;


    /**
     * 文件id
     */
    private String archfileinfo_id;

}

package com.cairh.cpe.common.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.*;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.request.AuditForm;
import com.cairh.cpe.common.mapper.BusinFlowTaskMapper;
import com.cairh.cpe.common.service.*;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.common.util.SqlDateUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.db.config.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BusinFlowTaskServiceImpl extends ServiceImpl<BusinFlowTaskMapper, BusinFlowTask> implements IBusinFlowTaskService {

    @Resource
    private IdGenerator idGenerator;
    @Autowired
    private IRequestService requestService;
    @Resource
    private ILabelService labelService;
    @Resource
    private IBusinProcessRequestAuditTrailService businProcessRequestAuditTrailService;
    @Resource
    private IFlowTaskRecordDetailsService flowTaskRecordDetailsService;
    @Resource
    private IHandupDetailsService handupDetailsService;
    @Resource
    private IStagingTaskRecordService stagingTaskRecordService;
    @Resource
    private IBusinFlowRecordService businFlowRecordService;
    @Resource
    private RedissonUtil redissonUtil;


    @Override
    public BusinFlowTask createBusinFlowTask(BusinFlowRequest businFlowRequest, String task_type, String video_type, String allow_auditor, String not_auditor, String push_flag, Map<String, Object> contentMap, String task_id) {
        BusinFlowTask businFlowTask = this.getOne(new LambdaQueryWrapper<>(BusinFlowTask.class)
                .eq(BusinFlowTask::getRequest_no, businFlowRequest.getRequest_no())
                .eq(BusinFlowTask::getTask_type, task_type)
                .eq(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING));
        if (businFlowTask == null) {
            businFlowTask = new BusinFlowTask();
            businFlowTask.setSerial_id(idGenerator.nextUUID(null));
            businFlowTask.setRequest_no(businFlowRequest.getRequest_no());
            businFlowTask.setCreate_datetime(new Date());
            businFlowTask.setTask_type(task_type);
            businFlowTask.setTask_status(FlowStatusConst.AUDIT_PENDING);
            businFlowTask.setVideo_type(video_type);
            businFlowTask.setAllow_auditor(StringUtils.defaultIfBlank(allow_auditor, StrUtil.SPACE));
            businFlowTask.setNot_allow_auditor(StringUtils.defaultIfBlank(not_auditor, StrUtil.SPACE));
            businFlowTask.setTohis_flag(Constant.TOHIS_FLAG_N);
            businFlowTask.setPush_flag(push_flag);
            businFlowTask.setTask_id(StringUtils.isNotBlank(task_id) ? task_id : idGenerator.nextUUID(null));
            businFlowTask.setAddress_repeated(businFlowRequest.getAddress_repeated());
            businFlowTask.setBranch_repeated(businFlowRequest.getBranch_repeated());
            businFlowTask.setMatch_labels(businFlowRequest.getMatch_labels());
            if (FlowNodeConst.AUDIT.equals(task_type) || FlowNodeConst.REVIEW.equals(task_type) || FlowNodeConst.SECONDARY_REVIEW.equals(task_type)) {
                List<BusinFlowTask> auditTaskList = this.list(new LambdaQueryWrapper<>(BusinFlowTask.class)
                        .eq(BusinFlowTask::getRequest_no, businFlowRequest.getRequest_no())
                        .eq(BusinFlowTask::getTask_type, task_type)
                        .in(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_NO_PASS, FlowStatusConst.AUDIT_INVALIDATE));
                if (CollectionUtils.isNotEmpty(auditTaskList)) {
                    // 标志为再审
                    businFlowTask.setDeal_num(1);
                }
            }
            SqlDateUtil.setDefaultValue(businFlowTask);
            save(businFlowTask);
            // 保存参数到业务流程请求审核跟踪
            contentMap.put(Fields.MATCH_LABELS, getMatchLabelTypes(businFlowRequest.getMatch_labels()));
            businProcessRequestAuditTrailService.saveBusinProcessRequestAuditTrail(businFlowTask, contentMap);
        }
        return businFlowTask;
    }

    @Override
    public BusinFlowTask dealBusinFlowTask(AuditForm auditForm) {
        BusinFlowTask businFlowTask = this.getById(auditForm.getTask_id());
        //校验任务的允许操作员
        if (StringUtils.isNotBlank(businFlowTask.getNot_allow_auditor())) {
            //允许操作员和当前审核操作员不是同一个人
            if (("," + businFlowTask.getNot_allow_auditor() + ",").contains(auditForm.getOperator_no())) {
                log.error("该操作员{}无法处理该任务{}", auditForm.getOperator_no(), businFlowTask.getTask_id());
                throw new BizException("-9997", ErrorEnum.AUDIT_TASK_NOT_ALLOW_OPERATOR.getDesc());
            }
        }
        if (FlowStatusConst.AUDIT_PENDING.equals(businFlowTask.getTask_status()) || FlowStatusConst.AUDIT_SUSPEND.equals(businFlowTask.getTask_status())) {
            //处理则对该任务提醒数清空处理
            businFlowTask.setDeal_datetime(new Date());
            businFlowTask.setOperator_no(auditForm.getOperator_no());
            businFlowTask.setOperator_name(auditForm.getOperator_name());
            businFlowTask.setTask_status(FlowStatusConst.AUDIT_AUDITING);
            businFlowTask.setSuspend_remind_num(0);
            //如果不存在任务来源。则该任务为手动认领
            if (StringUtils.isBlank(businFlowTask.getTask_source())) {
                businFlowTask.setTask_source(DicConstant.TASK_SOURCE_2);
            }
            SqlDateUtil.setDefaultValue(businFlowTask);
            updateById(businFlowTask);
            // 获取用户信息userParams
            Map<String, Object> userParams = paddingMap(auditForm);
            // 更新业务流程信息
            businProcessRequestAuditTrailService.updateBusinProcessRequestAuditTrail(businFlowTask, userParams,
                    UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_AUDITING);
        } else if (FlowStatusConst.AUDIT_AUDITING.equals(businFlowTask.getTask_status())) {
            if (!auditForm.getOperator_no().equals(businFlowTask.getOperator_no())) {
                log.debug("dealBusinFlowTask-任务办理已被其他操作人员抢先处理");
                throw new BizException(ErrorEnum.BUSINFLOW_DEAL_BY_OTHER.getValue(), ErrorEnum.BUSINFLOW_DEAL_BY_OTHER.getDesc());
            } else {
                //处理则对该任务提醒数清空处理
                businFlowTask.setDeal_datetime(new Date());
                businFlowTask.setSuspend_remind_num(0);
                SqlDateUtil.setDefaultValue(businFlowTask);
                updateById(businFlowTask);
                // 更新任务跟踪表deal_datetime
                Map<String, Object> params = new HashMap<>(1);
                params.put(Fields.DEAL_DATETIME, businFlowTask.getDeal_datetime());
                businProcessRequestAuditTrailService.updateBusinProcessRequestAuditTrail(businFlowTask, params,
                        UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_SPECIALFIELDS);
            }
        }

        return businFlowTask;
    }

    @Override
    public BusinFlowTask finishBusinFlowTask(AuditForm auditForm) {
        BusinFlowTask businFlowTask = getById(auditForm.getTask_id());

        if (FlowStatusConst.AUDIT_AUDITING.equals(businFlowTask.getTask_status())) {
            if (StringUtils.isNotBlank(auditForm.getOperator_no()) && !auditForm.getOperator_no().equals(businFlowTask.getOperator_no())) {
                throw new BizException(ErrorEnum.BUSINFLOW_DEAL_BY_OTHER.getValue(), ErrorEnum.BUSINFLOW_DEAL_BY_OTHER.getDesc());
            }

            businFlowTask.setFinish_datetime(new Date());
            businFlowTask.setTask_status(auditForm.getTask_status());
            businFlowTask.setOp_content(auditForm.getAudit_remark());
            businFlowTask.setRemark(auditForm.getRemark());
            SqlDateUtil.setDefaultValue(businFlowTask);

            this.updateById(businFlowTask);
            // 审核通过or不通过更新审核跟踪记录表
            businProcessRequestAuditTrailService.updateBusinProcessRequestAuditTrail(businFlowTask, null,
                    StringUtils.equals(auditForm.getTask_status(), FlowStatusConst.AUDIT_PASS)
                            ? UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_AUDITPASS
                            : UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_AUDITNOPASS);
            //如果复核-复核 或者 二次复核-二次复核
            if (StringUtils.equals("auditNotPass", auditForm.getHandle_type())) {
                return businFlowTask;
            }
            ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(businFlowTask.getRequest_no());
            if (StringUtils.equals(clobContentInfo.getActiviti_continue_review_flag(), "1")
                    || StringUtils.equals(clobContentInfo.getActiviti_continue_double_flag(), "1")) {
                // 获取上一条任务信息
                String new_task_type = InverceFlowEnum.BACK_CODE.get(businFlowTask.getTask_type());
                LambdaQueryWrapper<BusinFlowTask> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BusinFlowTask::getRequest_no, businFlowTask.getRequest_no())
                        .eq(BusinFlowTask::getTask_type, new_task_type)
                        .orderByDesc(BusinFlowTask::getCreate_datetime);
                List<BusinFlowTask> oldBusinFlowTaskList = this.list(queryWrapper);
                // 更新当前任务类型
                BusinFlowTask updateTask = new BusinFlowTask();
                BaseBeanUtil.copyProperties(businFlowTask, updateTask);
                log.info("任务编号：{},对应的review_flag为:{},double_flag为：{},task_type为：{}", businFlowTask.getRequest_no(), clobContentInfo.getActiviti_continue_review_flag(), clobContentInfo.getActiviti_continue_double_flag(), businFlowTask.getTask_type());
                updateTask.setTask_type(new_task_type);
                this.updateById(updateTask);
                reportDataProcessing(oldBusinFlowTaskList, updateTask, auditForm, new_task_type);
            }
        } else {
            throw new BizException(ErrorEnum.NOT_AUDIT_STATUS.getValue(), ErrorEnum.NOT_AUDIT_STATUS.getDesc());
        }

        return businFlowTask;
    }

    @Override
    public BusinFlowTask getCurrTaskType(String request_no, String task_type) {
        LambdaQueryWrapper<BusinFlowTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusinFlowTask::getRequest_no, request_no)
                .orderByDesc(BusinFlowTask::getCreate_datetime);
        //如果是结束节点，则查最近一次通过的任务
        if (!StringUtils.equals(task_type, FlowNodeConst.END)) {
            wrapper.eq(BusinFlowTask::getTask_type, task_type);
        }
        List<BusinFlowTask> businFlowTask = this.list(wrapper);

        return CollectionUtils.isNotEmpty(businFlowTask) ? businFlowTask.get(0) : null;
    }

    @Override
    public BusinFlowTask getCurrTaskTypeByTaskId(String task_id, String task_type) {
        LambdaQueryWrapper<BusinFlowTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusinFlowTask::getTask_id, task_id)
                .orderByDesc(BusinFlowTask::getCreate_datetime);
        if (!StringUtils.equals(task_type, FlowNodeConst.END)) {//如果是结束节点，则查最近一次通过的任务
            wrapper.eq(BusinFlowTask::getTask_type, task_type);
        }
        List<BusinFlowTask> businFlowTask = this.list(wrapper);

        return CollectionUtils.isNotEmpty(businFlowTask) ? businFlowTask.get(0) : null;
    }

    @Override
    public BusinFlowTask getCurrTaskType(String request_no) {
        LambdaQueryWrapper<BusinFlowTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusinFlowTask::getRequest_no, request_no)
                .orderByDesc(BusinFlowTask::getCreate_datetime);
        List<BusinFlowTask> businFlowTask = this.list(wrapper);

        return CollectionUtils.isNotEmpty(businFlowTask) ? businFlowTask.get(0) : null;
    }

    @Override
    public void taskStagingHandle(BusinFlowTask businFlowTask, StagingTaskRule stagingTaskRule) {
        // push_flag=8、9
        LambdaUpdateWrapper<BusinFlowTask> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BusinFlowTask::getSerial_id, businFlowTask.getSerial_id());
        updateWrapper.set(BusinFlowTask::getPush_flag, WskhConstant.STAGING_PUSH_FLAG);
        this.update(updateWrapper);

        // 记录操作流水
        HashMap<String, Object> params = new HashMap<>();
        params.put(Fields.OPERATOR_NO, WskhConstant.SUPER_USER);
        params.put(Fields.OPERATOR_NAME, WskhConstant.SUPER_USER);
        params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22407);
        params.put(Fields.BUSINESS_REMARK, "自动暂存");
        params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
        businFlowRecordService.saveBusinFlowRecord(businFlowTask.getRequest_no(), params);

        // 记录暂存记录信息
        StagingTaskRecord stagingTaskRecord = new StagingTaskRecord();
        stagingTaskRecord.setSerial_id(idGenerator.nextUUID(null));
        stagingTaskRecord.setRule_id(stagingTaskRule.getSerial_id());
        stagingTaskRecord.setRule_name(stagingTaskRule.getRule_name());
        stagingTaskRecord.setRequest_no(businFlowTask.getRequest_no());
        stagingTaskRecord.setTask_id(businFlowTask.getTask_id());
        stagingTaskRecord.setFlow_task_id(businFlowTask.getSerial_id());
        stagingTaskRecord.setRecord_type(RecordTypeEnum.STAGING_OPTION.getCode());
        stagingTaskRecord.setOperator_no(WskhConstant.SYSTEM);
        stagingTaskRecord.setOperator_name(WskhConstant.SYSTEM);
        stagingTaskRecord.setMatch_info(JSON.toJSONString(businFlowTask));
        stagingTaskRecord.setCreate_datetime(new Date());
        stagingTaskRecordService.save(stagingTaskRecord);
    }

    private Map<String, Object> paddingMap(AuditForm auditForm) {
        Map<String, Object> map = new HashMap<>();
        if (auditForm != null) {
            map.put(Fields.BRANCH_NO, auditForm.getBranch_no());
            map.put(Fields.BRANCH_NAME, auditForm.getBranch_name());
            map.put(Fields.UP_BRANCH_NO, auditForm.getUp_branch_no());
            map.put(Fields.UP_BRANCH_NAME, auditForm.getUp_branch_name());
            map.put(Fields.IS_BRANCH_MANAGED, auditForm.getIs_branch_managed());
        }
        return map;
    }

    private String getMatchLabelTypes(String matchLabels) {
        if (StringUtils.isBlank(matchLabels)) {
            return StrUtil.SPACE;
        }
        Map<String, Label> labelMap = labelService.getMapLabels();
        return Arrays.stream(matchLabels.split(StrUtil.COMMA)).filter(i -> labelMap.containsKey(i))
                .map(e -> labelMap.get(e).getLabel_type())
                .collect(Collectors.joining(StrUtil.COMMA));
    }

    private void reportDataProcessing(List<BusinFlowTask> oldBusinFlowTaskList, BusinFlowTask businFlowTask, AuditForm auditForm, String new_task_type) {
        // 流程任务记录详情表更新对应的复核任务为见证任务，并且将之前的见证任务的状态invalid_flag设置为作废
        HashMap<String, Object> params = new HashMap<>(1);
        params.put(Fields.INVALID_FLAG, WskhConstant.INVALID_STATUS);
        // 更新上一条数据信息为作废状态
        BusinFlowTask previousBusinFlowTask = CollectionUtils.isNotEmpty(oldBusinFlowTaskList) ? oldBusinFlowTaskList.get(0) : null;
        flowTaskRecordDetailsService.updateFlowTaskRecordDetails(previousBusinFlowTask, params,
                UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_SPECIALFIELDS);

        // 更新当前数据信息任务类型为新的任务类型,并且将BusinProcessRequestAuditTrail表中的对应类型（audit&review&secondReview）操作人进行更新
        params.clear();
        params.put(Fields.TASK_TYPE, new_task_type);
        // 获取当前任务操作人分支结构信息
        paddingMap(auditForm, params);
        businProcessRequestAuditTrailService.updateBusinProcessRequestAuditTrail(businFlowTask,
                params, UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_SPECIALFIELDS);
        handupDetailsService.updateHandupDetails(businFlowTask);
    }

    private void paddingMap(AuditForm auditForm, Map<String, Object> map) {
        if (auditForm != null) {
            map.put(Fields.OPERATOR_NO, auditForm.getOperator_no());
            map.put(Fields.OPERATOR_NAME, auditForm.getOperator_name());
            map.put(Fields.BRANCH_NO, auditForm.getBranch_no());
            map.put(Fields.BRANCH_NAME, auditForm.getBranch_name());
            map.put(Fields.UP_BRANCH_NO, auditForm.getUp_branch_no());
            map.put(Fields.UP_BRANCH_NAME, auditForm.getUp_branch_name());
        }
    }
}

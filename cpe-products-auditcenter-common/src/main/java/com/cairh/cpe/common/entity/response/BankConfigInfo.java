package com.cairh.cpe.common.entity.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 存款银行信息
 */
@Data
public class BankConfigInfo implements Serializable {

    private String bank_no;// 银行编号

    private String bank_name;// 银行全名称

    private String bank_alias;// 银行简名称

    private String icon;// 图标url

    private String en_sign_flag;// 银行开通签约方式，多个已逗号分隔 </br>11 一步式需要密码 </br>12 一步式无需密码 </br>21 两步式需要卡号 </br>22 两步式无需卡号

    private String bank_memo;// 备注

    // 银行类型
    private String bank_type;

    // 存管标识
    private String trust_flag;

    // 网银验签标识
    private String cyber_bank_flag;

    // 状态
    private String status;

    // 机构签约类型
    private String organ_sign_flag;

    // 银行机构代码
    private String bank_organ_code;

    // 服务开始时间
    private String work_start_time;

    // 服务结束时间
    private String work_end_time;

    // 排序
    private Integer order_no;

    // 客服电话
    private String service_phone;

    // 单笔转账限额
    private String single_limit;

    // 单日转账限额
    private String daily_transfer_limit;

    // 单月转账限额
    private String month_transfer_limit;

    // 备注
    private String remark;
}

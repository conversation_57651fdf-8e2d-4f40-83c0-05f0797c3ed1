package com.cairh.cpe.common.support.source;

import cn.hutool.core.util.ObjectUtil;
import com.cairh.cpe.core.autoconfiure.env.AbstractSimplePropertySource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.core.Ordered;
import org.springframework.core.env.PropertySource;
import org.springframework.core.env.PropertySources;
import org.springframework.stereotype.Service;

@Service
public class DelegatingEnvironmentSystemPropertySource extends AbstractSimplePropertySource implements Ordered {

    @Autowired
    private PropertySourcesPlaceholderConfigurer propertySourcesPlaceholderConfigurer;

    public DelegatingEnvironmentSystemPropertySource() {
        super("delegating-environment-system-propertysource");
    }


    @Override
    public Object getProperty(String name) {
        PropertySources propertySources = propertySourcesPlaceholderConfigurer.getAppliedPropertySources();

        for (PropertySource<?> propertySource : propertySources) {
            String value = (String) propertySource.getProperty(name);
            if (ObjectUtil.isNotNull(value)) {
                return value;
            }
        }

        return null;
    }

    @Override
    public int getOrder() {
        return 101;
    }
}

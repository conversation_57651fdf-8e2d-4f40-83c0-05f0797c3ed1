package com.cairh.cpe.common.entity.clob;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 账户信息
 */
@Data
public class UserAccountAll implements Serializable {

    /**
     * 账户信息集合
     */
    private List<UserAccountInfo> list_stock_accounts;
    /**
     * //沪深开放式基金开通，开通多个已逗号分隔，如 98,99 </br>98 深市TA </br>99 沪市TA
     */
    private String hsofstock_accounts;
    /**
     * //多金融开通，开通多个已逗号分隔，如 98,99 </br>98 深市TA </br>99 沪市TA
     */
    private String prod_accounts;

    private String pension_accounts;// 养老金

    private String profession_code;// 职业

    private String ready_finish;// 已完成节点

    private boolean default_pension_choose;// 默认养老金勾选
}

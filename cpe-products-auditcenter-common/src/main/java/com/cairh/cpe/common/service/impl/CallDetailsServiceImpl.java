package com.cairh.cpe.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.BranchConstant;
import com.cairh.cpe.common.constant.CallConstant;
import com.cairh.cpe.common.constant.LabelTypeConst;
import com.cairh.cpe.common.entity.*;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.entity.response.CallUuiResp;
import com.cairh.cpe.common.entity.support.BackendUser;
import com.cairh.cpe.common.mapper.CallDetailsMapper;
import com.cairh.cpe.common.service.*;
import com.cairh.cpe.common.util.SM4Utils;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.db.config.IdGenerator;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CallDetailsServiceImpl extends ServiceImpl<CallDetailsMapper, CallDetails> implements ICallDetailsService {

    @Resource
    private IBusinFlowTaskService businFlowTaskService;

    @Resource
    private IBusinFlowRequestService businFlowRequestService;

    @Resource
    private ILabelService labelService;

    @Resource
    private IdGenerator idGenerator;

    @Resource
    private IBusinProcessRequestAuditTrailService businProcessRequestAuditTrailService;


    @Override
    public CallUuiResp sendCallRequest(String flowTaskId, BaseUser baseUser,
                                       Map<String, BranchInfo> branchInfoMap,
                                       BackendUser backendUser,
                                       String testMobile) {
        BusinFlowTask businFlowTask = businFlowTaskService.getById(flowTaskId);
        Assert.notNull(businFlowTask, "任务信息未找到flowTaskId= " + flowTaskId);
        BusinFlowRequest businFlowRequest = businFlowRequestService.getById(businFlowTask.getRequest_no());

        Date date = new Date();
        CallDetails callDetails = new CallDetails();
        callDetails.setSerial_id(idGenerator.nextUUID(null));
        callDetails.setRequest_no(businFlowRequest.getRequest_no());
        callDetails.setBizId(flowTaskId);
        callDetails.setBizType(CallConstant.CALL_BIZ_TYPE);
        callDetails.setOutboundCalleeNumber(CallConstant.CALL_OUT_BOUND_CALLEE_NUMBER);
        callDetails.setCreate_datetime(date);
        if (StringUtils.isNotBlank(testMobile)) {
            callDetails.setCalleeNumber(testMobile);
        } else {
            callDetails.setCalleeNumber(businFlowRequest.getMobile_tel());
        }
        callDetails.setStaffid(baseUser.getStaff_no());
        callDetails.setStaffname(baseUser.getUser_name());
        callDetails.setModify_datetime(date);
        callDetails.setCall_status(CallConstant.CALL_STATUS_INIT);
        callDetails.setClient_branch_no(businFlowRequest.getBranch_no());
        callDetails.setBusin_type(businFlowRequest.getBusin_type());
        if (null != backendUser) {
            BranchInfo branchInfo = branchInfoMap.getOrDefault(backendUser.getBranch_no(), new BranchInfo());
            if (StringUtils.equalsAny(branchInfo.getBranch_type(),BranchConstant.LEVEL_HEADQUARTERS,BranchConstant.LEVEL_SUBSIDIARY_COMPANY,BranchConstant.LEVEL_BRANCH_OFFICE)) {
                callDetails.setOperator_branch_no(branchInfo.getBranch_no());
                callDetails.setOperator_branch_name(branchInfo.getBranch_name());
                callDetails.setOperator_up_branch_no(branchInfo.getBranch_no());
                callDetails.setOperator_up_branch_name(branchInfo.getBranch_name());
            }else {
                callDetails.setOperator_branch_no(backendUser.getBranch_no());
                callDetails.setOperator_branch_name(branchInfo.getBranch_name());
                String upBranchNo = StringUtils.isNotBlank(branchInfo.getUp_branch_no()) ? branchInfo.getUp_branch_no() : branchInfo.getBranch_no();
                callDetails.setOperator_up_branch_no(upBranchNo);
                callDetails.setOperator_up_branch_name(branchInfoMap.getOrDefault(upBranchNo, new BranchInfo()).getBranch_name());
            }
        }

        // 匹配标签并丰富到callDetails表中
        StringBuffer labelList = new StringBuffer();
        Map<String, Label> mapLabels = labelService.getMapLabels();
        Arrays.asList(businFlowRequest.getMatch_labels().split(",")).forEach(x->{
            if (ObjectUtils.isNotEmpty(mapLabels.get(x))){
                labelList.append(mapLabels.get(x).getLabel_type()).append(",");
            }
        });
        if (StringUtils.isNotBlank(labelList.toString())){
            labelList.deleteCharAt(labelList.length() - 1);
            callDetails.setLabel_options(labelList.toString());
        }
        this.save(callDetails);

        Map<String, Object> parameters = Maps.newHashMap();
        parameters.put("calleeNumber", callDetails.getCalleeNumber());
        parameters.put("outboundCalleeNumber", CallConstant.CALL_OUT_BOUND_CALLEE_NUMBER);
        parameters.put("bizId", flowTaskId);
        parameters.put("bizType", CallConstant.CALL_BIZ_TYPE);
        parameters.put("staffid", baseUser.getStaff_no());
        parameters.put("callbizid", callDetails.getSerial_id());
        parameters.put("callbizkey", CallConstant.CALL_BIZ_KEY);
        parameters.put("extstr", businFlowTask.getTask_id());
        log.info("call-param={}", JSON.toJSONString(parameters));

        CallUuiResp resp = new CallUuiResp();
        resp.setSerial_id(callDetails.getSerial_id());
        resp.setUui(SM4Utils.encrypt(CallConstant.SM4_HEXCIPHER, JSON.toJSONString(parameters), StandardCharsets.UTF_8));

        //使用异步不影响主线程 同步至临时表
        CompletableFuture.runAsync(() -> {
            LambdaUpdateWrapper<BusinProcessRequestAuditTrail> trailLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            trailLambdaUpdateWrapper.set(BusinProcessRequestAuditTrail::getCall_flag, CallConstant.CALL_FLAG_YES);
            trailLambdaUpdateWrapper.eq(BusinProcessRequestAuditTrail::getTask_id, businFlowTask.getTask_id());
            businProcessRequestAuditTrailService.update(trailLambdaUpdateWrapper);

            LambdaUpdateWrapper<BusinFlowTask> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(BusinFlowTask::getCall_flag, CallConstant.CALL_FLAG_YES);
            wrapper.eq(BusinFlowTask::getSerial_id,flowTaskId);
            businFlowTaskService.update(wrapper);
            log.info("外呼标识更新完成 task={}", businFlowTask.getTask_id());
        });

        return resp;
    }


    @Override
    public boolean cancelCall(String serial_id) {
        LambdaUpdateWrapper<CallDetails> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CallDetails::getCall_status, CallConstant.CALL_STATUS_CANCEL);
        updateWrapper.set(CallDetails::getModify_datetime, new Date());
        updateWrapper.eq(CallDetails::getSerial_id, serial_id);
        return this.update(updateWrapper);
    }
}

package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.entity.StagingTaskRule;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/11 16:18
 */
public interface IStagingTaskRuleService extends IService<StagingTaskRule> {

    /**
     * 查询所有暂存任务规则
     *
     * @return 规则列表
     */
    List<StagingTaskRule> findAllStagingTaskRules();

    /**
     * 清理缓存
     */
    void clearCache();

}

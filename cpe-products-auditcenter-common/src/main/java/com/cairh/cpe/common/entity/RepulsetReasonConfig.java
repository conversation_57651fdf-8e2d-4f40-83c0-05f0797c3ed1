package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 打回原因配置
 * </p>
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("RepulsetReasonConfig")
public class RepulsetReasonConfig implements Serializable {

    /**
     * 流水号
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 任务类别
     */
    private String audit_type;

    /**
     * 前端节点
     */
    private String anode_id;

    /**
     * 整改原因名称
     */
    private String cause_name;

    /**
     * 整改原因内容
     */
    private String cause_content;

    /**
     * 整改原因分组
     */
    private String cause_group;

    /**
     * 顺序号
     */
    private Integer order_no;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 修改人
     */
    private String update_by;

    /**
     * 更新日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date update_datetime;


    /**
     * 上层节点 为空代表上层节点
     */
    private String un_serial_id;

    /**
     * 表示驳回原因名称和具体内容构成的一个id。
     */
    private String cause_id;


    /**
     * 任务类型
     */
    private String busin_type;

    @TableField(exist = false)
    private int cause_order_no;

}

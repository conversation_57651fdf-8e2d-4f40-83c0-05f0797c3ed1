package com.cairh.cpe.common.entity.response;

import lombok.Data;

import java.util.Date;

@Data
public class BusinFlowRecordResp {

    /**
     * 流水号
     */
    private String serial_id;
    // 申请时间
    private Date create_datetime;
    // 所属营业部编号
    private String branch_no;
    // 所属营业部名称
    private String branch_name;
    // 客户姓名
    private String client_name;
    // 客户手机号
    private String mobile_tel;
    // 客户身份证号
    private String id_no;
    /**
     * 证件类别
     */
    private String id_kind;
    // 接入方式
    private String app_id;
    // 操作员编号
    private String operator_no;
    // 操作员名称
    private String operator_name;
    // 视频见证方式
    private String video_type;
    // 业务标志
    private String business_flag;
    // 业务说明
    private String business_remark;
    /**
     * 渠道代码
     */
    private String channel_code;
    /**
     * 渠道名称
     */
    private String channel_name;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 当前流程节点
     */
    private String anode_id;

    /**
     * 业务状态
     */
    private String request_status;

    /**
     * 开户渠道
     */
    private String open_channel;

    /**
     * 操作内容
     */
    private String busi_content;

    /**
     * 所属分公司
     */
    private String company_name;

    /**
     * 活动名称
     */
    private String activity_name;

    /**
     * 营销团队
     */
    private String marketing_team;


    public String getBranch_name() {
        if (branch_name == null) {
            return branch_no;
        }
        return branch_name;
    }

    public String getOperator_name() {
        if (operator_name == null) {
            return operator_no;
        }
        return operator_name;
    }

    public String getChannel_name() {
        if (channel_name == null) {
            return channel_code;
        }
        return channel_name;
    }
}

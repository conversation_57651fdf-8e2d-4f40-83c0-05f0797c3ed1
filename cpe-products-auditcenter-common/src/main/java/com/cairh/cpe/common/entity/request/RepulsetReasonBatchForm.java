package com.cairh.cpe.common.entity.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RepulsetReasonBatchForm implements Serializable {


    /**
     * 任务类别 audit review second_review
     */
    @NotBlank
    private String audit_types;

    /**
     * 业务类型 100400,100500,100600
     */
    private String busin_types;

    /**
     * 整改原因分组 1 - 重传照片
     */
    @NotBlank
    private String cause_group;


    @NotNull
    private List<RepulsetReasonBatchChild> cause_children;


}

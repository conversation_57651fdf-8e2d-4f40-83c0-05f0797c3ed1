package com.cairh.cpe.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.entity.ChannelDefine;
import com.cairh.cpe.common.mapper.ChannelDefineMapper;
import com.cairh.cpe.common.service.IChannelDefineService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ChannelDefineServiceImpl extends ServiceImpl<ChannelDefineMapper, ChannelDefine> implements IChannelDefineService {

    @Override
    public ChannelDefine getChannelByCode(String channel_code) {
        List<ChannelDefine> list = this.list(new LambdaQueryWrapper<>(ChannelDefine.class)
                .eq(ChannelDefine::getChannel_code, channel_code)
        );
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<String> getChannelCodesByLikeName(String channel_name) {
        List<ChannelDefine> list = this.list(new LambdaQueryWrapper<>(ChannelDefine.class)
                .like(ChannelDefine::getChannel_name, channel_name));
        List<String> result = null;
        if (CollectionUtils.isNotEmpty(list)) {
            result = list.parallelStream().map(ChannelDefine::getChannel_code).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    @Cacheable(value = "ACChannel", key = "#root.targetClass.simpleName+'.'+#root.method.name+':ALL'", condition = "", unless = "#result?.size() == 0")
    public List<ChannelDefine> getAllChannel() {
        return this.list();
    }

    @Override
    @CacheEvict(value = "ACChannel", allEntries = true)
    public void refresh() {
        log.info("Cache[ACChannel]执行清除.");
    }
}

package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * his用户查询信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("His_UserQueryExtInfo")
public class His_UserQueryExtInfo implements Serializable {

    /**
     * 受理编号
     */
    @TableId("request_no")
    private String request_no;

    /**
     * 当前业务状态
     */
    private String request_status;

    /**
     * 请求时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date request_datetime;

    /**
     * 提交审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date submit_datetime;


    /**
     * 手机号码
     */
    private String mobile_tel;

    /**
     * 分支机构
     */
    private String branch_no;


    /**
     * 客户编号
     */
    private String client_id;

    /**
     * 资产账户
     */
    private String fund_account;

    /**
     * 推广关系（营销人员名称）
     */
    private String broker_name;

    /**
     * 推广关系编号
     */
    private String broker_code;


    /**
     * 接入方式
     */
    private String app_id;


    /**
     * 渠道编号
     */
    private String channel_code;

    /**
     * 渠道名称
     */
    private String channel_name;


    /**
     * 客户姓名
     */
    private String client_name;

    /**
     * 证件类别
     */
    private String id_kind;

    /**
     * 证件号码
     */
    private String id_no;

    /**
     * 性别
     */
    private String client_gender;

    /**
     * 民族编号
     */
    private String nation_id;

    /**
     * 签发机关
     */
    private String issued_depart;

    /**
     * 证件有效期开始日期
     */
    private Integer id_begindate;

    /**
     * 证件有效期结束日期
     */
    private Integer id_enddate;

    /**
     * 身份证地址
     */
    private String id_address;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String zipcode;

    /**
     * 行业代码
     */
    private String industry_type;

    /**
     * 学历代码
     */
    private String degree_code;

    /**
     * 职业代码
     */
    private String profession_code;

    /**
     * 自然人身份类别
     */
    private String identity_category;


    /**
     * 审核员编号
     */
    private String audit_operator_no;

    /**
     * 审核员姓名
     */
    private String audit_operator_name;

    /**
     * 审核完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date audit_finish_datetime;

    /**
     * 复核员编号
     */
    private String review_operator_no;

    /**
     * 复核员姓名
     */
    private String review_operator_name;

    /**
     * 复核完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date review_finish_datetime;

    /**
     * 二次复核员编号
     */
    private String double_operator_no;

    /**
     * 二次复核员姓名
     */
    private String double_operator_name;

    /**
     * 二次复核完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date double_finish_datetime;


    /**
     * 营业部对比手机号是否异地
     */
    private String mobile_diffplace;


    /**
     * 最后更新日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date update_datetime;

    /**
     * 当前流程节点
     */
    private String anode_id;


    /**
     * 选择异地开户的理由
     */
    private String choose_branch_reason;

    /**
     * 选择职业的理由
     */
    private String choose_profession_reason;

    /**
     * 手机号归属地
     */
    private String mobile_location;

    /**
     * 活动编号
     */
    private String activity_no;

    /**
     * 活动名称
     */
    private String activity_name;

    /**
     * 营销团队
     */
    private String marketing_team;

    /**
     * 失信记录
     */
    private String dishonest_record;

    /**
     * 失信记录备注
     */
    private String dishonest_record_remark;

    /**
     * 失信记录文本内容
     */
    private String dishonest_content;

    /**
     * 工作单位
     */
    private String work_unit;

    /**
     * 客户标签
     */
    private String client_tags;

    /**
     * 站点地址
     */
    private String op_station;


    /**
     * 视频见证类型
     */
    private String video_type;

    /**
     * 视频见证类型
     */
    private String ai_audit_code;


    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 双向见证人员编号
     */
    private String video_operator_no;

    /**
     * 双向视频见证人员姓名
     */
    private String video_operator_name;

    /**
     * 开户渠道
     */
    private String open_channel;

    /**
     * 审核通过时当前节点信息
     */
    private String end_node;

    /**
     * 当前数据是否为快照
     */
    private String is_snapshot;
    private String auxiliary_id_kind;// 辅助证件类型
    private String auxiliary_id_no;// 辅助证件号码
    private String oversea_client_name;// 境外客户姓名
    private String oversea_id_kind;// 境外证件类型
    private String oversea_id_no;// 境外证件号码

    private String auxiliary_id_begindate;
    private String auxiliary_id_enddate;
    private String auxiliary_id_address;


    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date tohis_datetime;

    /**
     * 网厅业务办理-开户类别
     */
    private String client_category;
}

package com.cairh.cpe.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.constant.Fields;
import com.cairh.cpe.common.entity.BusinFlowRecord;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.request.BusinFlowRecordForm;
import com.cairh.cpe.common.entity.request.BusinflowRecordReq;
import com.cairh.cpe.common.entity.response.BusinFlowRecordResp;
import com.cairh.cpe.common.mapper.BusinFlowRecordMapper;
import com.cairh.cpe.common.service.IBusinFlowRecordService;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.common.util.ParamsSavingUtils;
import com.cairh.cpe.common.util.SqlDateUtil;
import com.cairh.cpe.db.config.IdGenerator;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.cairh.cpe.common.constant.Constant.MAX_FILED_VALUE_LENGTH;

@Service
public class BusinFlowRecordServiceImpl extends ServiceImpl<BusinFlowRecordMapper, BusinFlowRecord> implements IBusinFlowRecordService {

    @Resource
    private IdGenerator idGenerator;

    @Autowired
    private BusinFlowRecordMapper businFlowRecordMapper;

    @Resource
    private IBusinFlowRequestService businFlowRequestService;


    @Override
    public void saveBusinFlowRecord(String request_no, Map<String, Object> params) {
        BusinFlowRequest businFlowRequest = businFlowRequestService.getById(request_no);
        saveBusinFlowRecord(businFlowRequest, params);
    }

    @Override
    public void saveBusinFlowRecord(BusinFlowRequest businFlowRequest, Map<String, Object> params) {
        BusinFlowRecord businflowrecord = new BusinFlowRecord();

        businflowrecord.setSerial_id(idGenerator.nextUUID(null));
        businflowrecord.setRequest_no(businFlowRequest.getRequest_no());
        businflowrecord.setBranch_no(businFlowRequest.getBranch_no());
        businflowrecord.setRequest_status(businFlowRequest.getRequest_status());
        businflowrecord.setTohis_flag(Constant.TOHIS_FLAG_N);
        businflowrecord.setCreate_datetime(new Date());

        businflowrecord.setAnode_id(MapUtils.getString(params, Fields.ANODE_ID, null));
        businflowrecord.setRecord_type(MapUtils.getString(params, Fields.RECORD_TYPE, "0"));
        businflowrecord.setBusiness_remark(MapUtils.getString(params, Fields.BUSINESS_REMARK, null));
        businflowrecord.setBusiness_flag(MapUtils.getString(params, Fields.BUSINESS_FLAG, null));
        businflowrecord.setOperator_no(MapUtils.getString(params, Fields.OPERATOR_NO, null));
        businflowrecord.setOperator_name(MapUtils.getString(params, Fields.OPERATOR_NAME, null));
        params.remove(Fields.REQUEST_NO);
        params.remove(Fields.OPERATOR_NO);
        params.remove(Fields.BUSINESS_REMARK);
        params.remove(Fields.RECORD_TYPE);
        String business_content =JSON.toJSONString(params);
        if (business_content.length() > MAX_FILED_VALUE_LENGTH) {
            business_content = business_content.substring(0, MAX_FILED_VALUE_LENGTH);
        }
        businflowrecord.setBusi_content(business_content);

        SqlDateUtil.setDefaultValue(businflowrecord);
        save(businflowrecord);
    }

    @Override
    public List<BusinFlowRecordResp> qryUserApplyRecord(BusinFlowRecordForm businFlowRecordForm) {
        return businFlowRecordMapper.qryUserApplyRecord(businFlowRecordForm);
    }

    @Override
    public Page<BusinFlowRecordResp> selectRecordListByPage(Page<BusinFlowRecordResp> page, BusinflowRecordReq businflowRecordReq) {
        businflowRecordReq.setCreate_datetime_start(SqlDateUtil.getDateStartDetail(businflowRecordReq.getCreate_datetime_start()));
        businflowRecordReq.setCreate_datetime_end(SqlDateUtil.getDateEndDetail(businflowRecordReq.getCreate_datetime_end()));
        if (StringUtils.isNotBlank(businflowRecordReq.getBranch_no())) {
            String[] arr = businflowRecordReq.getBranch_no().split(",");
            businflowRecordReq.setBranch_nos(Arrays.asList(arr));
        }
        //业务标识多选
        if (StringUtils.isNotBlank(businflowRecordReq.getBusiness_flag())) {
            String[] arrBusin = businflowRecordReq.getBusiness_flag().split(",");
            businflowRecordReq.setBusiness_flags(Arrays.asList(arrBusin));
        }
        //证件类型多选
        if (StringUtils.isNotBlank(businflowRecordReq.getId_kind())) {
            if (businflowRecordReq.getId_kind().startsWith(",")) {
                businflowRecordReq.setId_kind(businflowRecordReq.getId_kind().substring(1));
            }
            String[] arrKind = businflowRecordReq.getId_kind().split(",");
            businflowRecordReq.setId_kinds(Arrays.asList(arrKind));
        }
        //业务状态
        if (StringUtils.isNotBlank(businflowRecordReq.getRequest_status())) {
            String[] arrTaskStatus = businflowRecordReq.getRequest_status().split(",");
            businflowRecordReq.setRequest_status_list(Arrays.asList(arrTaskStatus));
            List<String> statusList = new ArrayList<>();
            //判断任务类型是否为空
            List<String> arrayTaskType = new ArrayList<>();
            for (String taskStatus : arrTaskStatus) {
                String[] taskStatusArr = taskStatus.split("-");
                statusList.add(taskStatusArr[1]);
                arrayTaskType.add(taskStatusArr[0]);
            }
            if (CollectionUtils.isNotEmpty(arrayTaskType)) {
                List<String> collect = arrayTaskType.stream().distinct().collect(Collectors.toList());
                businflowRecordReq.setTask_types(collect);
            }
            businflowRecordReq.setRequest_status_list(statusList);
        }
        //app_id多选
        if (StringUtils.isNotBlank(businflowRecordReq.getApp_id())) {
            String[] arrAppId = businflowRecordReq.getApp_id().split(",");
            businflowRecordReq.setApp_ids(Arrays.asList(arrAppId));
        }
        //选择的任务类型（审核，复核，二次复核）
        if (StringUtils.isNotBlank(businflowRecordReq.getTask_type())) {
            String[] arrTaskType = businflowRecordReq.getTask_type().split(",");
            businflowRecordReq.setTask_type_list(new ArrayList<>(Arrays.asList(arrTaskType)));
        }
        return baseMapper.selectRecordListByPage(page, businflowRecordReq);
    }


}

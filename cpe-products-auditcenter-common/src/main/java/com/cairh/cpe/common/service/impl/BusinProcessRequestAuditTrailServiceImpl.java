package com.cairh.cpe.common.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.BusinProcessRequestAuditTrail;
import com.cairh.cpe.common.mapper.BusinProcessRequestAuditTrailMapper;
import com.cairh.cpe.common.service.IBusinProcessRequestAuditTrailService;
import com.cairh.cpe.common.service.IFlowTaskRecordDetailsService;
import com.cairh.cpe.common.util.KHDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * Description：业务流程请求审核跟踪表服务实现
 * Author： slx
 * Date： 2024/4/16 下午6:39
 */
@Slf4j
@Service
public class BusinProcessRequestAuditTrailServiceImpl extends ServiceImpl<BusinProcessRequestAuditTrailMapper, BusinProcessRequestAuditTrail>
        implements IBusinProcessRequestAuditTrailService {

    @Autowired
    private IFlowTaskRecordDetailsService flowTaskRecordDetailsService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public void saveBusinProcessRequestAuditTrail(BusinFlowTask businFlowTask, Map<String, Object> params) {
        // 新增流程业务记录详情表
        flowTaskRecordDetailsService.saveFlowTaskRecordDetails(businFlowTask, params);
        // 通过request_no和task_id查询库中是否存在，不存在新增
        BusinProcessRequestAuditTrail auditTrail =
                getBusinProcessRequestAuditTrail(businFlowTask.getRequest_no(), businFlowTask.getTask_id());
        if (Objects.nonNull(auditTrail)) {
            // 复核&二次复核更新状态、复核创建时间or二次复核创建时间
            updateBusinProcessRequestAuditTrail(businFlowTask, null,
                    UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_REVIEW_OR_SECOND_REVIEW);
            log.info("创建复核or二次复核更新request_no={}, task_id={}完成！", businFlowTask.getRequest_no(), businFlowTask.getTask_id());
            return;
        }
        log.info("新增业务流程请求审核跟踪表[saveBusinProcessRequestAuditTrail]，类型={}, request_no={}, task_id={}, serial_id={}",
                businFlowTask.getTask_type(), businFlowTask.getRequest_no(), businFlowTask.getTask_id(), businFlowTask.getSerial_id());

        BusinProcessRequestAuditTrail businProcessRequestAuditTrail = createBusinProcessRequestAuditTrail(businFlowTask, params);
        save(businProcessRequestAuditTrail);
    }

    @Override
    public void updateBusinProcessRequestAuditTrail(BusinFlowTask businFlowTask, Map<String, Object> userParams, UpdateBusinProcessRequestAuditTrailSourceEnum sourceEnum) {
        if (businFlowTask == null || sourceEnum == null) {
            log.warn("更新业务流程请求审核跟踪表服务[updateBusinProcessRequestAuditTrail]失败，businFlowTask为空或sourceEnum为空!!");
            return;
        }
        try {
            // 更新流程业务记录详情表
            flowTaskRecordDetailsService.updateFlowTaskRecordDetails(businFlowTask, userParams, sourceEnum);
            BusinProcessRequestAuditTrail businProcessRequestAuditTrail = getById(businFlowTask.getTask_id());
            if (businProcessRequestAuditTrail != null) {
                updateAuditTrailFromSourceEnum(businProcessRequestAuditTrail, businFlowTask, userParams, sourceEnum);
                log.info("更新业务流程请求审核跟踪表[updateBusinProcessRequestAuditTrail]，数据来源={}, request_no={}, task_id={}, serial_id={}",
                        sourceEnum.getDesc(), businFlowTask.getRequest_no(), businFlowTask.getTask_id(), businFlowTask.getSerial_id());
                // 更新信息
                updateById(businProcessRequestAuditTrail);
            }
        } catch (Exception e) {
            log.error("更新业务流程请求审核跟踪表[updateBusinProcessRequestAuditTrail]异常, 数据来源={}, request_no={}, task_id={}, serial_id={}",
                    sourceEnum.getDesc(), businFlowTask.getRequest_no(), businFlowTask.getTask_id(), businFlowTask.getSerial_id(), e);
            throw new RuntimeException("更新业务流程请求审核跟踪表失败", e);
        }
    }

    private BusinProcessRequestAuditTrail createBusinProcessRequestAuditTrail(BusinFlowTask businFlowTask, Map<String, Object> params) {
        return new BusinProcessRequestAuditTrail()
                .setRequest_no(businFlowTask.getRequest_no())
                .setTask_id(businFlowTask.getTask_id())
                .setUser_name(getStringFieldValue(params, Fields.CLIENT_NAME, StrUtil.SPACE))
                .setId_no(getStringFieldValue(params, Fields.ID_NO, StrUtil.SPACE))
                .setId_kind(getStringFieldValue(params, Fields.ID_KIND, StrUtil.SPACE))
                .setTime_property(getTimeProperty(params))
                .setRequest_status(getRequestStatus(businFlowTask.getTask_type(), businFlowTask.getTask_status()))
                .setRequest_datetime(Objects.nonNull(params.get(Fields.REQUEST_DATETIME))
                        ? (Date) params.get(Fields.REQUEST_DATETIME) : businFlowTask.getCreate_datetime())
                .setAudit_create_datetime(businFlowTask.getCreate_datetime())
                .setChannel_code(getStringFieldValue(params, Fields.CHANNEL_CODE, StrUtil.SPACE))
                .setActivity_no(getStringFieldValue(params, Fields.ACTIVITY_NO, StrUtil.SPACE))
                .setBusin_type(getStringFieldValue(params, Fields.BUSIN_TYPE, StrUtil.SPACE))
                .setUser_branch_no(getStringFieldValue(params, Fields.BRANCH_NO, StrUtil.SPACE))
                .setUser_branch_name(getStringFieldValue(params, Fields.BRANCH_NAME, StrUtil.SPACE))
                .setUser_up_branch_no(getStringFieldValue(params, Fields.UP_BRANCH_NO, StrUtil.SPACE))
                .setUser_up_branch_name(getStringFieldValue(params, Fields.UP_BRANCH_NAME, StrUtil.SPACE))
                .setApp_id(getStringFieldValue(params, Fields.APP_ID, StrUtil.SPACE))
                .setIs_branch_managed(getStringFieldValue(params, Fields.IS_BRANCH_MANAGED, Constant.BRANCH_MANAGED_Y))
                .setMatch_labels(getStringFieldValue(params, Fields.MATCH_LABELS, StrUtil.SPACE))
                .setVideo_type(businFlowTask.getVideo_type())
                .setMarketing_team(getStringFieldValue(params, Fields.MARKETING_TEAM, StrUtil.SPACE));
    }


    private void updateAuditTrailFromSourceEnum(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask,
                                                Map<String, Object> userParams, UpdateBusinProcessRequestAuditTrailSourceEnum sourceEnum) {
        businProcessRequestAuditTrail
                .setRequest_status(getRequestStatus(businFlowTask.getTask_type(), businFlowTask.getTask_status()))
                .setTask_source(StringUtils.isEmpty(businFlowTask.getTask_source()) ? StrUtil.SPACE : businFlowTask.getTask_source());
        switch (sourceEnum) {
            case SOURCE_TASK_REVIEW_OR_SECOND_REVIEW:
                updateBusinProcessRequestAuditTrailFromCreate(businProcessRequestAuditTrail, businFlowTask);
                break;
            case SOURCE_TASK_AUDITING:
                updateBusinProcessRequestAuditTrailFromTaskAuditing(businProcessRequestAuditTrail, businFlowTask, userParams);
                break;
            case SOURCE_TASK_SUSPEND:
                updateBusinProcessRequestAuditTrailFromTaskSuspend(businProcessRequestAuditTrail, businFlowTask);
                break;
            case SOURCE_TASK_TRANSFER:
                updateBusinProcessRequestAuditTrailFromTaskTransfer(businProcessRequestAuditTrail, businFlowTask);
                break;
            case SOURCE_TASK_TRANSFERNOACCEPT:
                updateBusinProcessRequestAuditTrailFromTaskTransferNoAccept(businProcessRequestAuditTrail, businFlowTask);
                break;
            case SOURCE_TASK_TRANSFERACCEPT:
                updateBusinProcessRequestAuditTrailFromTaskTransferAccept(businProcessRequestAuditTrail, businFlowTask, userParams, sourceEnum);
                break;
            case SOURCE_TASK_RECOVERY:
                updateBusinProcessRequestAuditTrailFromTaskRecovery(businProcessRequestAuditTrail, businFlowTask);
                break;
            case SOURCE_TASK_AUDITPASS:
                updateBusinProcessRequestAuditTrailFromTaskAuditPass(businProcessRequestAuditTrail, businFlowTask);
                break;
            case SOURCE_TASK_AUDITNOPASS:
                updateBusinProcessRequestAuditTrailFromTaskAuditNoPass(businProcessRequestAuditTrail, businFlowTask);
                break;
            case SOURCE_TASK_SPECIALFIELDS:
                updateBusinProcessRequestAuditTrailFromSpecialFields(businProcessRequestAuditTrail, businFlowTask, userParams);
                break;
            case SOURCE_TASK_AUDITINVALIDATE:
                updateBusinProcessRequestAuditTrailFromTaskAuditInvalidate(businProcessRequestAuditTrail, businFlowTask);
                break;
            default:
                break;
        }
    }

    /**
     * 挂起更新businProcessRequestAuditTrail对象数据
     * 更新挂起标识：handup_flag
     *
     * @param businProcessRequestAuditTrail 对象
     * @param businFlowTask                 任务对象
     */
    private void updateBusinProcessRequestAuditTrailFromCreate(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask) {
        businProcessRequestAuditTrail.setRequest_status(getRequestStatus(businFlowTask.getTask_type(), FlowStatusConst.AUDIT_PENDING));
        // 见证
        if (FlowNodeConst.REVIEW.equals(businFlowTask.getTask_type())) {
            // 更新复核创建时间
            businProcessRequestAuditTrail.setReview_create_datetime(businFlowTask.getCreate_datetime());
        } else if (FlowNodeConst.SECONDARY_REVIEW.equals(businFlowTask.getTask_type())) {
            // 更新复核创建时间
            businProcessRequestAuditTrail.setSec_rv_create_datetime(businFlowTask.getCreate_datetime());
        }
    }

    /**
     * 设置businProcessRequestAuditTrail对象数据通过任务审核中
     * 主要为见证、复核以及二次复核的操作人、创建时间、处理时间以及完成时间
     *
     * @param businProcessRequestAuditTrail 对象
     * @param businFlowTask                 任务对象
     */
    private void updateBusinProcessRequestAuditTrailFromTaskAuditing(BusinProcessRequestAuditTrail businProcessRequestAuditTrail,
                                                                     BusinFlowTask businFlowTask, Map<String, Object> userParams) {
        if (Objects.isNull(businProcessRequestAuditTrail.getFirst_deal_datetime())) {
            businProcessRequestAuditTrail.setFirst_deal_datetime(businFlowTask.getDeal_datetime())
                    .setFirst_deal_operator_no(businFlowTask.getOperator_no());
        }
        businProcessRequestAuditTrail.setEnd_deal_datetime(businFlowTask.getDeal_datetime());
        switch (businFlowTask.getTask_type()) {
            // 见证
            case FlowNodeConst.AUDIT:
                businProcessRequestAuditTrail.setAudit_operator_no(businFlowTask.getOperator_no())
                        .setAudit_operator_name(businFlowTask.getOperator_name())
                        .setAudit_operator_branch_no(getStringFieldValue(userParams, Fields.BRANCH_NO, StrUtil.SPACE))
                        .setAudit_operator_branch_name(getStringFieldValue(userParams, Fields.BRANCH_NAME, StrUtil.SPACE))
                        .setAudit_operator_up_branch_no(getStringFieldValue(userParams, Fields.UP_BRANCH_NO, StrUtil.SPACE))
                        .setAudit_operator_up_branch_name(getStringFieldValue(userParams, Fields.UP_BRANCH_NAME, StrUtil.SPACE))
                        .setAudit_deal_datetime(businFlowTask.getDeal_datetime())
                        .setAudit_finish_datetime(businFlowTask.getFinish_datetime());

                break;
            // 复核
            case FlowNodeConst.REVIEW:
                businProcessRequestAuditTrail.setReview_operator_no(businFlowTask.getOperator_no())
                        .setReview_operator_name(businFlowTask.getOperator_name())
                        .setReview_operator_branch_no(getStringFieldValue(userParams, Fields.BRANCH_NO, StrUtil.SPACE))
                        .setReview_operator_branch_name(getStringFieldValue(userParams, Fields.BRANCH_NAME, StrUtil.SPACE))
                        .setReview_operator_up_branch_no(getStringFieldValue(userParams, Fields.UP_BRANCH_NO, StrUtil.SPACE))
                        .setReview_operator_up_branch_name(getStringFieldValue(userParams, Fields.UP_BRANCH_NAME, StrUtil.SPACE))
                        .setReview_deal_datetime(businFlowTask.getDeal_datetime())
                        .setReview_finish_datetime(businFlowTask.getFinish_datetime());
                break;
            // 二次复核
            case FlowNodeConst.SECONDARY_REVIEW:
                businProcessRequestAuditTrail.setSec_rv_operator_no(businFlowTask.getOperator_no())
                        .setSec_rv_operator_name(businFlowTask.getOperator_name())
                        .setSec_rv_operator_branch_no(getStringFieldValue(userParams, Fields.BRANCH_NO, StrUtil.SPACE))
                        .setSec_rv_operator_branch_name(getStringFieldValue(userParams, Fields.BRANCH_NAME, StrUtil.SPACE))
                        .setSec_rv_operator_up_branch_no(getStringFieldValue(userParams, Fields.UP_BRANCH_NO, StrUtil.SPACE))
                        .setSec_rv_operator_up_branch_name(getStringFieldValue(userParams, Fields.UP_BRANCH_NAME, StrUtil.SPACE))
                        .setSec_rv_deal_datetime(businFlowTask.getDeal_datetime())
                        .setSec_rv_finish_datetime(businFlowTask.getFinish_datetime());
                break;
            default:
                break;

        }

    }

    /**
     * 挂起更新businProcessRequestAuditTrail对象数据
     * 更新挂起标识：handup_flag
     *
     * @param businProcessRequestAuditTrail 对象
     * @param businFlowTask                 任务对象
     */
    private void updateBusinProcessRequestAuditTrailFromTaskSuspend(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask) {
        checkPauseAndUpdate(businProcessRequestAuditTrail, null);
        businProcessRequestAuditTrail.setRequest_status(getRequestStatus(businFlowTask.getTask_type(), FlowStatusConst.AUDIT_SUSPEND));
    }

    /**
     * 转交更新businProcessRequestAuditTrail对象数据
     *
     * @param businProcessRequestAuditTrail 对象
     * @param businFlowTask                 任务对象
     */
    private void updateBusinProcessRequestAuditTrailFromTaskTransfer(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask) {
        taskTransferNoAccept(businProcessRequestAuditTrail, businFlowTask, FlowStatusConst.AUDIT_TRANSFER);
    }

    /**
     * 转交未接受更新businProcessRequestAuditTrail对象数据
     *
     * @param businProcessRequestAuditTrail 对象
     * @param businFlowTask                 任务对象
     */
    private void updateBusinProcessRequestAuditTrailFromTaskTransferNoAccept(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask) {
        // 双向视频见证任务，需要回滚状态为待审核
        String taskStatus = FlowStatusConst.AUDIT_AUDITING;
        if (StringUtils.equals(businFlowTask.getVideo_type(), WskhConstant.VIDEO_TYPE_2)
                && StringUtils.equals(businFlowTask.getTask_type(), TaskTypeEnum.AUDIT.getCode())) {
            taskStatus = FlowStatusConst.AUDIT_PENDING;
        }
        taskTransferNoAccept(businProcessRequestAuditTrail, businFlowTask, taskStatus);
    }

    private void taskTransferNoAccept(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask, String taskStatus) {
        businProcessRequestAuditTrail.setRequest_status(getRequestStatus(businFlowTask.getTask_type(), taskStatus));
    }

    /**
     * 转交接受成功更新businProcessRequestAuditTrail对象数据
     * 1、转交
     *
     * @param businProcessRequestAuditTrail 对象
     * @param businFlowTask                 任务对象
     */
    private void updateBusinProcessRequestAuditTrailFromTaskTransferAccept(BusinProcessRequestAuditTrail businProcessRequestAuditTrail,
                                                                           BusinFlowTask businFlowTask, Map<String, Object> userParams,
                                                                           UpdateBusinProcessRequestAuditTrailSourceEnum sourceEnum) {
        checkPauseAndUpdate(businProcessRequestAuditTrail, sourceEnum);
        businProcessRequestAuditTrail.setRequest_status(getRequestStatus(businFlowTask.getTask_type(), FlowStatusConst.AUDIT_PENDING));
        switch (businFlowTask.getTask_type()) {
            // 见证
            case FlowNodeConst.AUDIT:
                businProcessRequestAuditTrail.setAudit_operator_no(businFlowTask.getOperator_no())
                        .setAudit_operator_name(businFlowTask.getOperator_name())
                        .setAudit_operator_branch_no(getStringFieldValue(userParams, Fields.BRANCH_NO, StrUtil.SPACE))
                        .setAudit_operator_branch_name(getStringFieldValue(userParams, Fields.BRANCH_NAME, StrUtil.SPACE))
                        .setAudit_operator_up_branch_no(getStringFieldValue(userParams, Fields.UP_BRANCH_NO, StrUtil.SPACE))
                        .setAudit_operator_up_branch_name(getStringFieldValue(userParams, Fields.UP_BRANCH_NAME, StrUtil.SPACE))
                        .setAudit_deal_datetime(null);
                break;
            // 复核
            case FlowNodeConst.REVIEW:
                businProcessRequestAuditTrail.setReview_operator_no(businFlowTask.getOperator_no())
                        .setReview_operator_name(businFlowTask.getOperator_name())
                        .setReview_operator_branch_no(getStringFieldValue(userParams, Fields.BRANCH_NO, StrUtil.SPACE))
                        .setReview_operator_branch_name(getStringFieldValue(userParams, Fields.BRANCH_NAME, StrUtil.SPACE))
                        .setReview_operator_up_branch_no(getStringFieldValue(userParams, Fields.UP_BRANCH_NO, StrUtil.SPACE))
                        .setReview_operator_up_branch_name(getStringFieldValue(userParams, Fields.UP_BRANCH_NAME, StrUtil.SPACE))
                        .setReview_deal_datetime(null);
                break;
            // 二次复核
            case FlowNodeConst.SECONDARY_REVIEW:
                businProcessRequestAuditTrail.setSec_rv_operator_no(businFlowTask.getOperator_no())
                        .setSec_rv_operator_name(businFlowTask.getOperator_name())
                        .setSec_rv_operator_branch_no(getStringFieldValue(userParams, Fields.BRANCH_NO, StrUtil.SPACE))
                        .setSec_rv_operator_branch_name(getStringFieldValue(userParams, Fields.BRANCH_NAME, StrUtil.SPACE))
                        .setSec_rv_operator_up_branch_no(getStringFieldValue(userParams, Fields.UP_BRANCH_NO, StrUtil.SPACE))
                        .setSec_rv_operator_up_branch_name(getStringFieldValue(userParams, Fields.UP_BRANCH_NAME, StrUtil.SPACE))
                        .setSec_rv_deal_datetime(null);
                break;
            default:
                break;

        }
    }

    /**
     * 任务回收更新businProcessRequestAuditTrail对象数据
     * 1 任务状态
     *
     * @param businProcessRequestAuditTrail 对象
     * @param businFlowTask                 任务对象
     */
    private void updateBusinProcessRequestAuditTrailFromTaskRecovery(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask) {
        checkPauseAndUpdate(businProcessRequestAuditTrail, null);
        businProcessRequestAuditTrail.setRequest_status(getRequestStatus(businFlowTask.getTask_type(), FlowStatusConst.AUDIT_PENDING))
                .setTask_source(StrUtil.SPACE);
        // 用户信息清理
        switch (businFlowTask.getTask_type()) {
            // 见证
            case FlowNodeConst.AUDIT:
                businProcessRequestAuditTrail.setAudit_operator_no(StrUtil.SPACE)
                        .setAudit_operator_name(StrUtil.SPACE)
                        .setAudit_operator_branch_no(StrUtil.SPACE)
                        .setAudit_operator_branch_name(StrUtil.SPACE)
                        .setAudit_operator_up_branch_no(StrUtil.SPACE)
                        .setAudit_operator_up_branch_name(StrUtil.SPACE)
                        .setAudit_deal_datetime(null);
                break;
            // 复核
            case FlowNodeConst.REVIEW:
                businProcessRequestAuditTrail.setReview_operator_no(StrUtil.SPACE)
                        .setReview_operator_name(StrUtil.SPACE)
                        .setReview_operator_branch_no(StrUtil.SPACE)
                        .setReview_operator_branch_name(StrUtil.SPACE)
                        .setReview_operator_up_branch_no(StrUtil.SPACE)
                        .setReview_operator_up_branch_name(StrUtil.SPACE)
                        .setReview_deal_datetime(null);
                break;
            // 二次复核
            case FlowNodeConst.SECONDARY_REVIEW:
                businProcessRequestAuditTrail.setSec_rv_operator_no(StrUtil.SPACE)
                        .setSec_rv_operator_name(StrUtil.SPACE)
                        .setSec_rv_operator_branch_no(StrUtil.SPACE)
                        .setSec_rv_operator_branch_name(StrUtil.SPACE)
                        .setSec_rv_operator_up_branch_no(StrUtil.SPACE)
                        .setSec_rv_operator_up_branch_name(StrUtil.SPACE)
                        .setSec_rv_deal_datetime(null);
                break;
            default:
                break;

        }
    }

    /**
     * 检查是否已经存在暂存标识，无则更新
     *
     * @param businProcessRequestAuditTrail 对象
     * @param sourceEnum                    来源
     */
    private void checkPauseAndUpdate(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, UpdateBusinProcessRequestAuditTrailSourceEnum sourceEnum) {
        if ("0".equals(businProcessRequestAuditTrail.getPause_flag())) {
            try {
                businProcessRequestAuditTrail.setPause_flag("1");
                // 转交需要获取转交发起时间进行保存
                if (UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_TRANSFERACCEPT.equals(sourceEnum)) {
                    // 获取redis中该任务的转交时间
                    String key = String.format(RedisKeyConstant.WSKH_AC_TASK_TRANSFER_TIME, businProcessRequestAuditTrail.getRequest_no());
                    String transferDate = redisTemplate.opsForValue().get(key);
                    // 转为时间类型
                    if (StringUtils.isNotEmpty(transferDate)) {
                        log.info("[BusinProcessRequestAuditTrail]转交时间={} request_no={}", transferDate, businProcessRequestAuditTrail.getRequest_no());
                        businProcessRequestAuditTrail.setFirst_pause_datetime(KHDateUtil.parseDate(transferDate, KHDateUtil.DATE_TIME_FORMAT));
                        return;
                    }
                }
                businProcessRequestAuditTrail.setFirst_pause_datetime(new Date());
            } catch (Exception e) {
                log.error("checkPauseAndUpdate error request_no={}", businProcessRequestAuditTrail.getRequest_no(), e);
                businProcessRequestAuditTrail.setPause_flag("1");
                businProcessRequestAuditTrail.setFirst_pause_datetime(new Date());
            }
        }
    }

    /**
     * 审核通过更新businProcessRequestAuditTrail对象数据
     *
     * @param businProcessRequestAuditTrail 对象
     * @param businFlowTask                 任务对象
     */
    private void updateBusinProcessRequestAuditTrailFromTaskAuditPass(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask) {
        taskAuditPassAndNoPass(businProcessRequestAuditTrail, businFlowTask, FlowStatusConst.AUDIT_PASS);
    }

    /**
     * 审核不通过更新businProcessRequestAuditTrail对象数据
     *
     * @param businProcessRequestAuditTrail 对象
     * @param businFlowTask                 任务对象
     */
    private void updateBusinProcessRequestAuditTrailFromTaskAuditNoPass(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask) {
        taskAuditPassAndNoPass(businProcessRequestAuditTrail, businFlowTask, FlowStatusConst.AUDIT_NO_PASS);
    }

    /**
     * 见证作废更新businProcessRequestAuditTrail对象数据 invalidate
     *
     * @param businProcessRequestAuditTrail 对象
     * @param businFlowTask                 任务对象
     */
    private void updateBusinProcessRequestAuditTrailFromTaskAuditInvalidate(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask) {
        taskAuditPassAndNoPass(businProcessRequestAuditTrail, businFlowTask, FlowStatusConst.AUDIT_INVALIDATE);
    }

    private void taskAuditPassAndNoPass(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask, String taskStatus) {
        businProcessRequestAuditTrail.setRequest_status(getRequestStatus(businFlowTask.getTask_type(), taskStatus));
        switch (businFlowTask.getTask_type()) {
            // 见证
            case FlowNodeConst.AUDIT:
                businProcessRequestAuditTrail.setAudit_deal_datetime(businFlowTask.getDeal_datetime())
                        .setAudit_finish_datetime(businFlowTask.getFinish_datetime())
                        .setAudit_op_content(businFlowTask.getOp_content());
                break;
            // 复核
            case FlowNodeConst.REVIEW:
                businProcessRequestAuditTrail.setReview_deal_datetime(businFlowTask.getDeal_datetime())
                        .setReview_finish_datetime(businFlowTask.getFinish_datetime())
                        .setReview_op_content(businFlowTask.getOp_content());
                break;
            // 二次复核
            case FlowNodeConst.SECONDARY_REVIEW:
                businProcessRequestAuditTrail.setSec_rv_deal_datetime(businFlowTask.getDeal_datetime())
                        .setSec_rv_finish_datetime(businFlowTask.getFinish_datetime())
                        .setSec_rv_op_content(businFlowTask.getOp_content());
                break;
            default:
                break;

        }
    }

    /**
     * 特殊字段更新businProcessRequestAuditTrail对象数据
     * 目前：task_source
     *
     * @param businProcessRequestAuditTrail 对象
     * @param businFlowTask                 任务对象
     */
    private void updateBusinProcessRequestAuditTrailFromSpecialFields(BusinProcessRequestAuditTrail businProcessRequestAuditTrail,
                                                                      BusinFlowTask businFlowTask, Map<String, Object> userParams) {
        // task_source
        if (userParams.containsKey(Fields.TASK_SOURCE)) {
            updateBusinProcessRequestAuditTrailField(businProcessRequestAuditTrail, userParams, Fields.TASK_SOURCE);
        }
        // deal_datetime(见证、复核、二次复核)
        if (userParams.containsKey(Fields.DEAL_DATETIME)) {
            updateDealDateTime(businProcessRequestAuditTrail, businFlowTask, userParams);
        }
        // task_type新的任务类型
        if (userParams.containsKey(Fields.TASK_TYPE)) {
            updateOperatorInfo(businProcessRequestAuditTrail, businFlowTask, userParams);
        }
    }

    private void updateDealDateTime(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask, Map<String, Object> userParams) {
        Object dealDateTime = MapUtils.getObject(userParams, Fields.DEAL_DATETIME);
        switch (businFlowTask.getTask_type()) {
            // 见证
            case FlowNodeConst.AUDIT:
                userParams.put("audit_deal_datetime", dealDateTime);
                updateBusinProcessRequestAuditTrailField(businProcessRequestAuditTrail, userParams, "audit_deal_datetime");
                break;
            // 复核
            case FlowNodeConst.REVIEW:
                userParams.put("review_deal_datetime", dealDateTime);
                updateBusinProcessRequestAuditTrailField(businProcessRequestAuditTrail, userParams, "review_deal_datetime");
                break;
            // 二次复核
            case FlowNodeConst.SECONDARY_REVIEW:
                userParams.put("secondary_review_deal_datetime", dealDateTime);
                updateBusinProcessRequestAuditTrailField(businProcessRequestAuditTrail, userParams, "secondary_review_deal_datetime");
                break;
            default:
                break;
        }
    }

    private void updateOperatorInfo(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, BusinFlowTask businFlowTask, Map<String, Object> userParams) {
        String new_task_type = MapUtils.getString(userParams, Fields.TASK_TYPE, " ");
        switch (new_task_type) {
            // 见证
            case FlowNodeConst.AUDIT:
                businProcessRequestAuditTrail.setAudit_operator_no(businFlowTask.getOperator_no())
                        .setAudit_operator_name(businFlowTask.getOperator_name())
                        .setAudit_operator_branch_no(getStringFieldValue(userParams, Fields.BRANCH_NO, StrUtil.SPACE))
                        .setAudit_operator_branch_name(getStringFieldValue(userParams, Fields.BRANCH_NAME, StrUtil.SPACE))
                        .setAudit_operator_up_branch_no(getStringFieldValue(userParams, Fields.UP_BRANCH_NO, StrUtil.SPACE))
                        .setAudit_operator_up_branch_name(getStringFieldValue(userParams, Fields.UP_BRANCH_NAME, StrUtil.SPACE))
                        .setAudit_create_datetime(businFlowTask.getCreate_datetime())
                        .setAudit_deal_datetime(businFlowTask.getDeal_datetime())
                        .setAudit_finish_datetime(businFlowTask.getFinish_datetime())
                        .setAudit_op_content(businFlowTask.getOp_content());
                break;
            // 复核
            case FlowNodeConst.REVIEW:
                businProcessRequestAuditTrail.setReview_operator_no(businFlowTask.getOperator_no())
                        .setReview_operator_name(businFlowTask.getOperator_name())
                        .setReview_operator_branch_no(getStringFieldValue(userParams, Fields.BRANCH_NO, StrUtil.SPACE))
                        .setReview_operator_branch_name(getStringFieldValue(userParams, Fields.BRANCH_NAME, StrUtil.SPACE))
                        .setReview_operator_up_branch_no(getStringFieldValue(userParams, Fields.UP_BRANCH_NO, StrUtil.SPACE))
                        .setReview_operator_up_branch_name(getStringFieldValue(userParams, Fields.UP_BRANCH_NAME, StrUtil.SPACE))
                        .setReview_create_datetime(businFlowTask.getCreate_datetime())
                        .setReview_deal_datetime(businFlowTask.getDeal_datetime())
                        .setReview_finish_datetime(businFlowTask.getFinish_datetime())
                        .setReview_op_content(businFlowTask.getOp_content());
                break;
            default:
                break;
        }
    }

    private void updateBusinProcessRequestAuditTrailField(BusinProcessRequestAuditTrail businProcessRequestAuditTrail, Map<String, Object> userParams, String fieldName) {
        try {
            Object obj = MapUtils.getObject(userParams, fieldName);
            Object value;
            if (obj instanceof String) {
                value = StringUtils.isNotEmpty((String) obj) ? obj : StrUtil.SPACE;
            } else {
                value = obj;
            }
            businProcessRequestAuditTrail.setFieldValue(fieldName, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("updateBusinProcessRequestAuditTrailField error", e);
        }
    }

    /**
     * 时间属性
     *
     * @return 1:工作时间 2:非工作时间
     */
    private String getTimeProperty(Map<String, Object> params) {
        // 工作时间
        if (MapUtils.getBoolean(params, Fields.IS_WORK_TIME, false)) {
            return TimePropertyEnum.WORKTIME.getCode();
        }
        return TimePropertyEnum.NOWORKTIME.getCode();
    }


    /**
     * 获取请求状态
     *
     * @param anodeId 任务类型
     * @param status  任务状态
     * @return eg: audit-1
     */
    private String getRequestStatus(String anodeId, String status) {
        return anodeId + "-" + status;
    }


    /**
     * 根据request_no和request_status获取BusinProcessRequestAuditTrail对象
     *
     * @param requestNo 请求编号
     * @param taskId    任务编号
     * @return BusinProcessRequestAuditTrail对象
     */
    private BusinProcessRequestAuditTrail getBusinProcessRequestAuditTrail(String requestNo, String taskId) {
        LambdaQueryWrapper<BusinProcessRequestAuditTrail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinProcessRequestAuditTrail::getRequest_no, requestNo)
                .eq(BusinProcessRequestAuditTrail::getTask_id, taskId);
        return getOne(queryWrapper);
    }

    /**
     * 获取字符串类型的参数
     *
     * @param params       map数据
     * @param fieldName    字段名
     * @param defaultValue 默认值
     * @return 字符串
     */
    private String getStringFieldValue(Map<String, Object> params, String fieldName, String defaultValue) {
        if (params == null) {
            return defaultValue;
        }
        Object object = params.get(fieldName);
        if (object instanceof String && !StringUtils.isEmpty((String) object)) {
            return object.toString();
        }
        return defaultValue;
    }

}

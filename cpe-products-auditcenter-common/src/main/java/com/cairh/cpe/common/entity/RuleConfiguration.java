package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 规则配置表
 *
 * <AUTHOR>
 * @since 2025/3/11 16:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("RULECONFIGURATION")
public class RuleConfiguration implements Serializable {

    /**
     * 主键ID
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 字段编码
     */
    @TableField("field_code")
    private String field_code;


    /**
     * 字段名称
     */
    @TableField("field_name")
    private String field_name;

    /**
     * 字段类型
     */
    @TableField("field_type")
    private String field_type;


    /**
     * 可选规则
     */
    @TableField("expression")
    private String expression;

    /**
     * 值类型
     */
    @TableField("value_type")
    private String value_type;

    /**
     * 输入值来源
     */
    @TableField("value_source")
    private String value_source;


    /**
     * 来源值
     */
    @TableField("source_value")
    private String source_value;

    /**
     * 顺序号
     */
    @TableField("order_no")
    private Integer order_no;

}

package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * Description：任务操作流水表
 * Author： slx
 * Date： 2024/4/15 下午3:34
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("HandupDetails")
public class HandupDetails {

    /**
     * 请求编号
     */
    private String request_no;

    /**
     * 流水编号
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 任务编号
     */
    private String task_id;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员名称
     */
    private String operator_name;

    /**
     * 操作类型 1：挂起、2：回收、3：转交
     */
    private String handup_type;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 归档标识 1：已归档 0：未归档
     */
    private String tohis_flag;

    /**
     * 归档时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private String tohis_datetime;

    /**
     * 作废标识 0-正常 1-作废
     */
    private String invalid_flag;

}

package com.cairh.cpe.common.entity.request;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * Description：查询业务参数请求类
 * Author： slx
 * Date： 2024/5/18 下午1:40
 */
@Data
@Accessors(chain = true)
public class QueryBizParamRequest {

    /**
     * md5鉴权
     */
    private String md5;

    /**
     * 任务编号
     */
    @NotNull
//    private String task_id;
    private String request_id;

}



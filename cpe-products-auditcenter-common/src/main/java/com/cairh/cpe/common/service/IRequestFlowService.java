package com.cairh.cpe.common.service;

import com.cairh.cpe.common.constant.flow.FlowRecordEnum;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.request.AuditForm;
import com.cairh.cpe.common.entity.response.CreateTaskResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 业务流 处理
 *
 * <AUTHOR>
 */
@Validated
public interface IRequestFlowService {

    /**
     * 业务流 数据提交
     */
    BusinFlowRequest submitParams(String request_no, Map<String, Object> params);

    /**
     * 操作员 业务流 数据提交
     */
    void submitParamsByOperator(String request_no, Map<String, Object> params);

    /**
     * 提交申请
     */
    void applyAudit(@NotBlank(message = "业务受理编号不能为空") String request_no);

    /**
     * 开始审核处理
     */
    void dealAudit(AuditForm auditForm);

    /**
     * 审核打回到用户整改
     */
    void auditNoPass(AuditForm auditForm);

    /**
     * 审核通过
     */
    void auditPass(AuditForm auditForm, Map<String, FlowRecordEnum> enumMap);


    /**
     * 保存大字段
     */
    void saveContentMapParams(BusinFlowRequest businFlowRequest, Map<String, Object> contentMap, Map<String, Object> params);

    /**
     * 不影响业务流程
     */
    void saveParamsRecord(String request_no, Map<String, Object> params);

    /**
     * 自动 创建任务
     */
    CreateTaskResult autoCreateTask(BusinFlowRequest businFlowRequest, String activity_id, String auditor_no, String task_id);

    /**
     * 自动 创建任务 (初始化状态)
     */
    CreateTaskResult autoCreateTask(BusinFlowRequest businFlowRequest, String activity_id, String auditor_no, String push_flag, String task_id);


    /**
     * 获取申请单的 申请人 信息
     */
    String getBranch_repeated(String id_no);

}

package com.cairh.cpe.common.constant;

public class VideoConstant {

    /**
     * 默认视频优先级
     */
    public final static int DEFAULT_VIDEO_LEVEL = 10;

    /**
     * 最小视频优先级
     */
    public final static int MIN_VIDEO_LEVEL = 1;

    /**
     * 最大视频优先级
     */
    public final static int MAX_VIDEO_LEVEL = 20;

    //未匹配就退出
    public static final String STATUS_N1_VANISH = "-1";

    /**
     * 排队等待
     */
    public static final String STATUS_0_WAITING = "0";

    /**
     * 已匹配
     */
    public static final String STATUS_1_MATCHED = "1";

    /**
     * 视频中
     */
    public static final String STATUS_2_VIDEOING = "2";

    //视频完成
    public static final String STATUS_3_VIDEOED = "3";
    //退出视频队列
    public static final String STATUS_4_VIDEOED = "4";
    //用户被清理出队列
    public static final String STATUS_5_VIDEOED = "5";
    //视频坐席中断视频
    public static final String STATUS_6_VIDEOED = "6";
    //视频用户中断视频
    public static final String STATUS_7_VIDEOED = "7";

    // 状态
    public static final String STATUS = "status";

    //排队位置
    public static final String QUEUE_POSITION = "queue_position";

    //排队总数
    public static final String QUEUE_COUNT = "queue_count";

    //跳转地址
    public static final String PAGE_ADDR = "page_addr";

    // 状态
    public static final String OPERATOR_NO = "operator_no";
    public static final String OPERATOR_NAME = "operator_name";
    public static final String PROFESSION_CERT = "profession_cert";
    public static final String OPERATOR_BRANCH_NO = "operator_branch_no";
    public static final String OPERATOR_BRANCH_NAME = "operator_branch_name";

    // 状态
    public static final String LAST_UPDATE_STATUS_TIME = "last_status_update_time";

    public static final String ROOM_ID = "room_id";

    public static final String ROOM_PASSWORD = "room_pwd";

    public static final String LOGIN_NAME = "login_name";

    public static final String REMOTE_USER_ID = "remote_user_id";

    public static final String VIDEO_JSON_PARAM = "json_param";

    public static final String ANYCHAT_H5_PORT = "anychat_h5_port";

    public static final String ANYCHAT_H5_ADDRESS = "anychat_h5_address";

    public static final String SERVER_ADDR_IN = "server_addr_in";

    public static final String SERVER_ADDR_OUT = "server_addr_out";

    public static final String SERVER_PORT = "server_port";

    public static final String ANYCHAT_GROUP_FLAG = "anychat_group_flag";

    public static final String ANYCHAT_APP_GUID = "anychat_appguid";

    public static final String ZEGO_UESR_TOKEN = "zegoUserToken";

    public static final String ZEGO_OPERATOR_TOKEN = "zegoOperatorToken";

    public static final String ZEGO_VIDEO_ADDRESS = "zego_video_address";
    // 内网地址
    public static final String ZEGO_VIDEO_ADDRESS_IN = "zego_video_address_in";
    public static final String ZEGO_VIDEO_ADDRESS_FILE = "zego_video_address_file";

    public static final String ZEGO_USE_HTTPS = "zegoUseHttps";

    public static final String ZEGO_APPID = "zego_appid";

    public static final String ZEGO_SIGNKEY = "zego_signkey";

    //实际的即构token
    public static final String ZEGO_TOKEN = "zego_token";

    public static final String ZEGO_ENV = "zegoEnv";

    public static final String APP_ID = "app_id";

    public static final String UNIQUE_ID = "unique_id";

    public static final String USER_ID = "user_id";

    public static final String USER_NAME = "user_name";

    public static final String QUEUE_ROLE = "queue_role";

    public static final String ROOM_ROLE = "room_role";

    //不归历史(视频流水)
    public static final String NOT_TOHIS = "0";

    //归历史(视频流水)
    public static final String TOHIS = "1";

    public static final String VIDEO_QUEUE_USER_WAIT_POSITION = "video_queue_user_wait_position";

    //视频组件-redis-用户统一前缀
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_USER_UNIFIED_PREFIX = "{cpe_esb_video}video_user";

    //视频用户状态
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_USER_STATUS = "status";

    //最近更新时间
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_USER_LAST_UPDATE_STATUS_TIME = "last_status_update_time";

    //视频用户状态 - 已匹配
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_USER_STATUS_MATCHED = "1";

    //视频用户状态 - 视频中
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_USER_STATUS_VIDEOING = "2";

    //视频用户状态 - 视频完成(暂保留)
    public static final String COMPONENT_VIDEO_REDIS_VIDEO_USER_STATUS_VIDEOED = "3";

    // 双向视频任务作废原因-双向视频重复提交退出
    public static final String BIDIRECTIONAL_CLEAR_REPEAT_SUBMIT = "双向视频重复提交退出";

    // 双向视频任务作废原因-双向视频定时处理退出
    public static final String BIDIRECTIONAL_CLEAR_TIMING_HANDLE = "双向视频定时处理退出";

    // 双向视频任务作废原因-双向视频任务处理队列异常退出
    public static final String BIDIRECTIONAL_CLEAR_TASK_QUEUE_EXCEPTION = "双向视频任务处理队列异常退出";

    // 双向视频任务作废原因-双向视频手动作废退出
    public static final String BIDIRECTIONAL_CLEAR_MANUAL = "双向视频手动作废退出";

    // 双向视频任务作废原因-心跳超时作废
    public static final String BIDIRECTIONAL_CLEAR_HEARTBEAT_TIMEOUT = "监控双向视频连接异常退出";

    // 双向视频任务作废原因-待处理任务超时作废
    public static final String BIDIRECTIONAL_CLEAR_TIMEOUT_HANDLE = "双向视频待处理任务超时退出";

}

package com.cairh.cpe.common.entity.clob;

import lombok.Data;

import java.io.Serializable;

@Data
public class VideoInfo implements Serializable {

    /**
     * 视频存储ID
     */
    private String video_file_id;
    /**
     * 见证人编号
     */
    private String operator_no;
    /**
     * 见证人姓名
     */
    private String operator_name;

    /**
     * 视频开始时间
     */
    private String begin_datetime;

    /**
     * 视频结束时间
     */
    private String end_datetime;

    /**
     * 处理结果
     */
    private String audit_remark;
    /**
     * 视频原生地址
     */
    private String video_address;
}

package com.cairh.cpe.common.entity.response;

import lombok.Data;

import java.util.List;

@Data
public class QueryDishonestResult {


    private String penOrg;//处罚处理机构
    private String penDate;//处罚处理时间
    private String penDoc;//处罚处理文件
    private String pentype;//处罚处理种类
    private String body;//处罚处理内容详情
    private String client_name;//客户名称
    private String id_no;//证件编号
    private String lsh;//流水号

    private String dishonest_id; // 失信记录文件id（crh-rpa查询结果）

    //private String result_info;//结果信息
    private String result_info_rpa;//crhRpa报错信息

    private String result_info_gt;//国泰报错信息

    private String dishonest_content; //失信记录内容

    private String dishonest_record; // 失信记录 是否有失信记录  0:没有 1:有 3:有但已过处罚期 4:系统故障待查

    private List<String> dishonest_record_list; //失信记录可选择记录
}

package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * Description：签发机关
 * Author： slx
 * Date： 2024/9/02 下午1:56
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("IssuingAuthority")
public class IssuingAuthority {

    /**
     * id
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 省份编号
     */

    private String province_no;

    /**
     * 签发机关名称
     */
    private String authority_name;

    /**
     * 证件地址行政区划
     */
    private String address_oragn;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态 8-可用 9-不可用
     */
    private String status;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 更新人
     */
    private String update_by;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date update_datetime;

}

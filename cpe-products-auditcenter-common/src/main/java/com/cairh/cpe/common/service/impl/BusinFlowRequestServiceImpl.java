package com.cairh.cpe.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.mapper.BusinFlowRequestMapper;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @program: cpe-products-wskh
 * @description: 业务流程申请对像
 * @author: syy
 * @create: 2022-01-19 15:25
 **/
@Service
public class BusinFlowRequestServiceImpl extends ServiceImpl<BusinFlowRequestMapper, BusinFlowRequest> implements IBusinFlowRequestService {

    @Override
    public List<BusinFlowRequest> getBusinFlowRequestList(String id_no) {
        if (StringUtils.isNotBlank(id_no)) {
            LambdaQueryWrapper<BusinFlowRequest> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(BusinFlowRequest::getRequest_no, BusinFlowRequest::getId_no)
                    .eq(BusinFlowRequest::getId_no, id_no)
                    .eq(BusinFlowRequest::getTohis_flag, Constant.TOHIS_FLAG_N);
            return this.list(queryWrapper);
        }
        return Collections.emptyList();
    }
}

package com.cairh.cpe.common.entity.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 城市信息
 */
@Data
public class CityInfo implements Serializable {

    /**
     * 省份代码
     */
    private String province_code;

    /**
     * 省份名称
     */
    private String province_name;

    /**
     * 城市代码
     */
    private String city_code;

    /**
     * 城市名称
     */
    private String city_name;

    /**
     * 区县代码
     */
    private String region_code;

    /**
     * 区县名称
     */
    private String region_name;

    /**
     * 邮政编码
     */
    private String zipcode;

    /**
     * 固定电话区号
     */
    private String zone_no;

    /**
     * 行政单位级别(省1市2区3)
     */
    private String default_level_no;

    /**
     * 父ID
     */
    private String parent_id;

    /**
     * 证件号码前缀
     */
    private String id_prefix;

    /**
     * 备注
     */
    private String remark;

    /**
     * 证件号码补充前缀
     */
    private String id_prefix_additional;

    /**
     * 经度
     */
    private String addr_longitude;

    /**
     * 纬度
     */
    private String addr_latitude;


    private String address;
}

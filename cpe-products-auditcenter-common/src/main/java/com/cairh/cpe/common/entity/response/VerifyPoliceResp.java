package com.cairh.cpe.common.entity.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 公安认证结果
 */
@Data
public class VerifyPoliceResp implements Serializable {

    /**
     * 公安认证id
     */
    private String idverifyrecord_id;

    /**
     * 文件记录id
     */
    private String filerecord_id;

    /**
     * 图像数据
     */
    private String image_data;

    /**
     * 公安认证状态
     */
    private String status;

    /**
     * 认证结果信息
     */
    private String result_info;

    /**
     * 认证分数
     */
    private String score;

    /**
     * 认证额外信息
     */
    private String ext_info;

    private String idc_result;
    private String face_result;
    private String auth_result;
    private String auth_info;

    /**
     * 中登时间 1-中登 2-证通
     */
    private String work_time;
}

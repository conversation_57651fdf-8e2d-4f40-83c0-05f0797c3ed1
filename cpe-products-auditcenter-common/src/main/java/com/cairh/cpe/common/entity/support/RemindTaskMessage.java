package com.cairh.cpe.common.entity.support;

import com.cairh.cpe.common.constant.WskhConstant;
import lombok.Data;

@Data
public class RemindTaskMessage {

    /**
     * 业务编号
     */
    private String request_no;

    /**
     * 转交人编号
     */
    private String operator_no;

    private String transfer_operator_no;
    private String transfer_operator_name;

    /**
     * 任务类型
     */
    private String task_type;

    /**
     * 转交原因
     */
    private String message_content;

    /**
     * 消息类型
     */
    private String message_type = WskhConstant.DEAL_MESSAGE;

    /**
     * 第一段持续时间(秒)
     */
    private Integer transfer_first_duration;

    /**
     * 第二段持续时间(秒)
     */
    private Integer transfer_second_duration;

    /**
     * 左边按钮
     */
    private String left_button;

    /**
     * 右边按钮
     */
    private String right_button;

    /**
     * 跳转链接
     */
    private String action_url;

    /**
     * 用户姓名
     */
    private String client_name;

    /**
     * 任务ID
     */
    private String flow_task_id;


}

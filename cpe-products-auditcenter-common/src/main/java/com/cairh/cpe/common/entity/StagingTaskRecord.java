package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 暂存记录表
 *
 * <AUTHOR>
 * @since 2025/3/11 16:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("STAGINGTASKRECORD")
public class StagingTaskRecord implements Serializable {

    /**
     * 主键ID
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 规则id
     */
    @TableField("rule_id")
    private String rule_id;

    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String rule_name;

    /**
     * 任务请求编号
     */
    @TableField("request_no")
    private String request_no;

    /**
     * 任务id
     */
    @TableField("task_id")
    private String task_id;

    /**
     * 任务唯一编号
     */
    @TableField("flow_task_id")
    private String flow_task_id;

    /**
     * 类型（1-暂存 2-释放）
     */
    @TableField("record_type")
    private String record_type;

    /**
     * 操作人编号（系统操作记system）
     */
    @TableField("operator_no")
    private String operator_no;

    /**
     * 操作人名称（系统操作记system）
     */
    @TableField("operator_name")
    private String operator_name;

    /**
     * 创建时间
     */
    @TableField("create_datetime")
    private Date create_datetime;

    /**
     * 匹配信息
     */
    @TableField("match_info")
    private String match_info;

}

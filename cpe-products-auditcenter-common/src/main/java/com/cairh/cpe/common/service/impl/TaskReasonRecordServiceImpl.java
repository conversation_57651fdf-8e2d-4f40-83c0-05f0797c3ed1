package com.cairh.cpe.common.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.TaskReasonRecord;
import com.cairh.cpe.common.entity.clob.AuditReason;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.mapper.TaskReasonRecordMapper;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.service.ITaskReasonRecordService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.db.config.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * Description：针对表【TASKREASONRECORD(驳回流水表)】的数据库操作Service实现
 * Author： slx
 * Date： 2024/5/6 上午11:22
 */
@Slf4j
@Service
public class TaskReasonRecordServiceImpl extends ServiceImpl<TaskReasonRecordMapper, TaskReasonRecord>
        implements ITaskReasonRecordService {

    @Resource
    private IdGenerator idGenerator;
    @Autowired
    private IRequestService requestService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveTaskReasonRecord(BusinFlowTask businFlowTask, List<AuditReason> reasons, BaseUser baseUser) {
        if (businFlowTask == null || CollectionUtils.isEmpty(reasons)) {
            log.error("saveTaskReasonRecord businFlowTask or reasons is null");
            return;
        }
        ClobContentInfo contentInfo = requestService.getAllDataByRequestNo(businFlowTask.getRequest_no());
        try {
            List<TaskReasonRecord> taskReasonRecordList = reasons.stream().map(auditReason -> {
                TaskReasonRecord taskReasonRecord = new TaskReasonRecord();
                taskReasonRecord.setTask_id(businFlowTask.getTask_id())
                        .setRequest_no(businFlowTask.getRequest_no())
                        .setSerial_id(idGenerator.nextUUID(null))
                        .setReason_type(replaceEmpty(auditReason.getReason_type()))
                        .setReason_name(replaceEmpty(auditReason.getReason_name()))
                        .setReason_group(replaceEmpty(auditReason.getReason_group()))
                        .setReason_desc(replaceEmpty(auditReason.getReason_desc()))
                        .setOperator_no(baseUser.getStaff_no())
                        .setOperator_name(baseUser.getUser_name())
                        .setTask_type(businFlowTask.getTask_type())
                        .setBranch_no(contentInfo.getBranch_no())
                        .setActivity_name(contentInfo.getActivity_name())
                        .setChannel_code(contentInfo.getChannel_code())
                        .setTask_apply_datetime(businFlowTask.getCreate_datetime())
                        .setMarketing_team(contentInfo.getMarketing_team());
                return taskReasonRecord;
            }).collect(Collectors.toList());
            this.saveBatch(taskReasonRecordList);
        } catch (Exception e) {
            log.error("saveTaskReasonRecord error, task_id:{}, request_no:{}",
                    businFlowTask.getTask_id(), businFlowTask.getRequest_no(), e);
        }

    }

    private String replaceEmpty(String str) {
        if (StringUtils.isEmpty(str)) {
            return StrUtil.SPACE;
        }
        return str;
    }
}





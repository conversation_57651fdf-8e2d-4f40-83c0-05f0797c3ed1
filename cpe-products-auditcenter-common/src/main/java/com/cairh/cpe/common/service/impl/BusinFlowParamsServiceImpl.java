package com.cairh.cpe.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowParams;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.mapper.BusinFlowParamsMapper;
import com.cairh.cpe.common.mapper.BusinFlowRequestMapper;
import com.cairh.cpe.common.mapper.BusinFlowTaskMapper;
import com.cairh.cpe.common.service.IBusinFlowParamsService;
import com.cairh.cpe.common.util.ParamsSavingUtils;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class BusinFlowParamsServiceImpl extends ServiceImpl<BusinFlowParamsMapper, BusinFlowParams> implements IBusinFlowParamsService {


    @Autowired
    private BusinFlowTaskMapper businFlowTaskMapper;

    @Autowired
    private BusinFlowRequestMapper businFlowRequestMapper;

    @Resource
    private RedissonUtil redissonUtil;

    @Override
    public Map<String, Object> getParamContentById(String request_no) {
        StringBuilder param_content = new StringBuilder();

        List<BusinFlowParams> list = this.list(new LambdaQueryWrapper<>(BusinFlowParams.class)
                .eq(BusinFlowParams::getRequest_no, request_no)
                .orderByAsc(BusinFlowParams::getOrder_no));

        for (BusinFlowParams param : list) {
            param_content.append(param.getBusi_content());
        }
        String submit_content = param_content.toString();
        if (StringUtils.isNotBlank(submit_content)) {
            return JSON.parseObject(submit_content, Map.class);
        }

        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveParams(String request_no, Map<String, Object> contentMap) {
        String submit_content = JSON.toJSONString(contentMap);
        if (StringUtils.isNotBlank(submit_content)) {
            List<BusinFlowParams> paramsList = new ArrayList<>();
            this.remove(new LambdaQueryWrapper<>(BusinFlowParams.class).eq(BusinFlowParams::getRequest_no, request_no));
            List<String> data = ParamsSavingUtils.getList(submit_content, 3990, 3);
            for (int i = 0; i < data.size(); i++) {
                BusinFlowParams params = new BusinFlowParams();
                params.setRequest_no(request_no);
                params.setOrder_no(i);
                params.setBusi_content(data.get(i));
                params.setTohis_flag(Constant.TOHIS_FLAG_N);
                paramsList.add(params);
            }
            if (CollectionUtils.isNotEmpty(paramsList)) {
                saveBatch(paramsList);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveSnapshot(String request_no, String note) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_SUBMITPARAMSBYOPERATOR, request_no);
        boolean isLock = redissonUtil.tryLock(lockKey, 30, 10, TimeUnit.SECONDS);
        if (!isLock) {
            log.error("[saveSnapshot]更新用户信息长时间未获取锁，【{}】", request_no);
            throw new BizException(ErrorEnum.REDIS_LOCK_ERROR.getValue(), ErrorEnum.REDIS_LOCK_ERROR.getDesc());
        }
        try {
            //查询最新的任务id
            List<String> taskStatus = new ArrayList<>();
            taskStatus.add(FlowStatusConst.AUDIT_PASS);
            taskStatus.add(FlowStatusConst.AUDIT_NO_PASS);
            taskStatus.add(FlowStatusConst.AUDIT_INVALIDATE);
            QueryWrapper<BusinFlowTask> businFlowTaskQueryWrapper = new QueryWrapper<>();
            businFlowTaskQueryWrapper.eq("request_no", request_no);
            businFlowTaskQueryWrapper.in("task_status", taskStatus);
            businFlowTaskQueryWrapper.orderByDesc("create_datetime");
            List<BusinFlowTask> businFlowTasks = businFlowTaskMapper.selectList(businFlowTaskQueryWrapper);
            if (CollectionUtils.isNotEmpty(businFlowTasks)) {
                BusinFlowTask businFlowTask = businFlowTasks.get(0);
                String serial_id = businFlowTask.getSerial_id();
                //查询BusinFlowParams
                List<BusinFlowParams> businFlowParams = this.list(new LambdaQueryWrapper<>(BusinFlowParams.class)
                        .eq(BusinFlowParams::getRequest_no, request_no));
                if (CollectionUtils.isNotEmpty(businFlowParams)) {
                    //批量保存BusinFlowParams
                    StringBuilder busi_content = new StringBuilder();
                    for (BusinFlowParams businFlowParam : businFlowParams) {
                        busi_content.append(businFlowParam.getBusi_content());
                    }
                    String submit_content = busi_content.toString();
                    //将submit_content转成map
                    Map<String, Object> contentMap = JSON.parseObject(submit_content, Map.class);
                    String request_status = String.valueOf(contentMap.get("request_status"));
                    String anode_id = String.valueOf(contentMap.get("anode_id"));
                    if (FlowStatusConst.AUDIT_PENDING.equals(request_status) && (FlowNodeConst.REVIEW.equals(anode_id) || FlowNodeConst.SECONDARY_REVIEW.equals(anode_id))) {
                        //finish_node=theStart,audit,review 如果状态为1，并且为复核或者二次复核需要将finish_node去掉最后一个
                        String finish_node = String.valueOf(contentMap.get("finish_node"));
                        String[] split = finish_node.split(",");
                        StringBuilder finish_node_new = new StringBuilder();
                        for (int i = 0; i < split.length; i++) {
                            if (i == split.length - 1) {
                                break;
                            }
                            finish_node_new.append(split[i]).append(",");
                        }
                        //去掉最后一个逗号
                        finish_node_new.deleteCharAt(finish_node_new.length() - 1);
                        contentMap.put("finish_node", finish_node_new.toString());
                    }
                    contentMap.put("request_status", businFlowTask.getTask_status());
                    contentMap.put("anode_id", businFlowTask.getTask_type());
                    BusinFlowRequest businFlowRequest = businFlowRequestMapper.selectById(request_no);
                    String main_request_status = businFlowRequest.getRequest_status();
                    String main_anode_id = businFlowRequest.getAnode_id();
                    if (StringUtils.equalsAny(note, "notPass", "invalidate") || (FlowStatusConst.AUDIT_PASS.equals(main_request_status) && FlowNodeConst.END.equals(main_anode_id))) {
                        //流程已走完，结束Finish_flag
                        contentMap.put("finish_flag", "1");
                    } else {
                        contentMap.put("finish_flag", "0");
                    }
                    //保存快照,将request_no为任务id
                    saveParams(serial_id, contentMap);
                }
                return serial_id;
            }
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            log.error("[saveSnapshot]保存用户快照信息，requestNo={}, 异常!", request_no, e);
            throw new BizException(ErrorEnum.SAVE_PARAMS_SNAPSHOT_ERROR.getValue(), e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
        return "";
    }
}

package com.cairh.cpe.common.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.common.entity.BusinFlowRecord;
import com.cairh.cpe.common.entity.request.BusinFlowRecordForm;
import com.cairh.cpe.common.entity.request.BusinflowRecordReq;
import com.cairh.cpe.common.entity.response.BusinFlowRecordResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusinFlowRecordMapper extends BaseMapper<BusinFlowRecord> {

    /**
     * 业务流水查询
     */
    List<BusinFlowRecordResp> qryUserApplyRecord(@Param("queryForm") BusinFlowRecordForm recordForm);

    /**
     * 业务流水菜单查询
     */
    Page<BusinFlowRecordResp> selectRecordListByPage(Page<BusinFlowRecordResp> page, @Param("queryForm") BusinflowRecordReq businflowRecordReq);
}

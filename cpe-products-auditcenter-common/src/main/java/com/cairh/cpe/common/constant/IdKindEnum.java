package com.cairh.cpe.common.constant;

public enum IdKindEnum {
    //0身份证 s香港身份证

    ID_CARD("0", "身份证"),
    ID_PASSPORT("1", "护照"),
    ID_LICENSE("2", "营业执照"),
    HK_ID_CARD("S", "香港居民身份证"),
    TAIWAN_PASS_CARD("H", "台湾居民来往大陆通行证"),
    GA_PASS_CARD("G", "港澳居民来往内地通行证"),
    GAT_RESIDENCE_PERMIT("l", "港澳台居民居住证"),
    FOREIGN_PREV_PERMIT("I", "外国人永居证");


    private String code;
    private String value;

    private IdKindEnum(String code, String value) {
        this.setCode(code);
        this.setValue(value);
    }

    public static String getValue(String code) {
        for (IdKindEnum idKindEnum : IdKindEnum.values()) {
            if (idKindEnum.getCode().equals(code)) {
                return idKindEnum.getValue();
            }
        }
        return null;
    }

    public static String getCode(String value) {
        for (IdKindEnum idKindEnum : IdKindEnum.values()) {
            if (idKindEnum.getValue().equals(value)) {
                return idKindEnum.getCode();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }


}

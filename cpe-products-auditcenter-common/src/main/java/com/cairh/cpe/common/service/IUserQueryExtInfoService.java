package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.entity.UserQueryExtInfo;
import com.cairh.cpe.common.entity.request.CommonUserQueryExtInfo;

public interface IUserQueryExtInfoService extends IService<UserQueryExtInfo> {

    /**
     * 大字段信息 同步 用户扩展信息
     */
    void saveClobParamsToUserQueryExtInfo(String request_no);

    Page<UserQueryExtInfo> selectUserQueryExtInfoListByPage(Page<UserQueryExtInfo> page, CommonUserQueryExtInfo commonUserQueryExtInfo);

    void saveSnapshot(String request_no, String status);

    /**
     * 查询信息
     *
     * @param request_no
     * @return
     */
    UserQueryExtInfo findExtInfo(String request_no);


    /**
     * 更新操作人信息
     * @param request_no
     * @param operation_no
     * @param operation_name
     * @param request_status
     * @param task_type
     */
    void updateOperatorInfo(String request_no,
                            String operation_no,
                            String operation_name,
                            String request_status,
                            String task_type);

}
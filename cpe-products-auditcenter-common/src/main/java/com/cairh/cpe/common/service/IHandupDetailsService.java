package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.constant.HandupTypeEnum;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.HandupDetails;

/**
 * Description：任务操作流水表服务
 * Author： slx
 * Date： 2024/4/15 下午3:46
 */
public interface IHandupDetailsService extends IService<HandupDetails> {

    /**
     * 保存任务操作流水
     *
     * @param businFlowTask 操作任务
     */
    void saveHandupDetails(BusinFlowTask businFlowTask, HandupTypeEnum typeEnum);

    /**
     * 更新任务操作流水
     *
     * @param businFlowTask 操作任务
     */
    void updateHandupDetails(BusinFlowTask businFlowTask);

}

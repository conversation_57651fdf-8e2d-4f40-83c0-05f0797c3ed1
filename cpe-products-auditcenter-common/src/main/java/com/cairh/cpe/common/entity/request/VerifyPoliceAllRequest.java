package com.cairh.cpe.common.entity.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 全要素公安认证
 */
@Data
public class VerifyPoliceAllRequest implements Serializable {

    /**
     * 机构标志
     */
    @NotNull
    private String organ_flag;

    /**
     * 证件类别
     */
    @NotNull
    private String id_kind;

    /**
     * 证件号码
     */
    @NotNull
    private String id_no;

    /**
     * 姓名
     */
    @NotNull
    private String full_name;

    /**
     * 实时标志  默认为0。传入1时实时调用底层公安接口，不取本地数据
     */
    private String realtime_flag;

    /**
     * 营业部编号
     */
    private String branch_no = "0";

    /**
     * 证书有效期
     */
    private String cert_valid_date;

    /**
     * 大头照base64
     */
    private String base64_image;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;

    /**
     * 出生日期：数据格式yyyymmdd
     */
    private String birthday;
    /**
     * 性别
     */
    private String client_gender;

    /**
     * 业务流水号
     */
    private String serial_no;

    /**
     * 1-证通 12-中登，不填自动选择
     */
    private String csdc_busi_kind;

    /**
     * 调用标识 默认1
     */
    private String flag = "1";

    /**
     * 操作类型 默认1,1-简项查询 3-人像比对
     */
    private String oper_flag = "1";
}

package com.cairh.cpe.common.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 客户资料修改项目字典关系
 */
public enum ModityItemEnum {

    NATION_ID("nation_id", "nation_code"),
    ID_KIND("id_kind", "id_kind"),
    DEGREE_CODE("degree_code", "degree_code"),
    PROFESSION_CODE("profession_code", "profession_code"),
    INDUSTRY_TYPE("industry_type", "industry_code"),
    DUTY("duty", "duty_code"),
    CREDIT_RECORD("credit_record", " dishonest_type"),
    CLIENT_GENDER("client_gender", "client_gender"),
    SEC_RELATION_RELATION("sec_relation_relation", "socialral_type"),
    MOBILE_TEL_OWNER_RELATION("mobile_tel_owner_relation", "socialral_type"),
    MOBILE_TEL_OWNER_FLAG("mobile_tel_owner_flag", "mobile_tel_owner_flag"),
    HOUSE_PROPERTY("house_property", "year_income"),
    INCOME_SOURCE("income_source", "income_source"),
    YEAR_INCOME("year_income", "year_income"),
    NATIONALITY("nationality", "nationality_code"),
    DOUBTFUL_INFO("doubtful_info", "doubtful_info"),
    CLIENT_TAGS("client_tags", "client_tags"),
    SAME_ADDRESS("same_address", "yes_or_no"),
    VIDEO_TYPE("video_type", "video_type"),
    IDENTITY_CATEGORY("identity_category", "natural_person_type"),
    CHOOSE_BRANCH_REASON("choose_branch_reason", "choose_branch_reason"),
    CHOOSE_PROFESSION_REASON("choose_profession_reason", "choose_profession_reason"),
    DISHONEST_RECORD("dishonest_record", "dishonest_record"),
    RPC_OPTION("rpc_option", "rpc_mis_option");

    private String code;
    private String value;

    private ModityItemEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static Map<String, String> getMap() {
        Map<String, String> map = new HashMap<String, String>();
        for (ModityItemEnum item : ModityItemEnum.values()) {
            map.put(item.getCode(), item.getValue());
        }
        return map;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}

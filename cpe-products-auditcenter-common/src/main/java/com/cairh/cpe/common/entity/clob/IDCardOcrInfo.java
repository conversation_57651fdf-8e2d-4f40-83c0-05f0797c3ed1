package com.cairh.cpe.common.entity.clob;

import lombok.Data;

import java.io.Serializable;

@Data
public class IDCardOcrInfo implements Serializable {

    private String client_name;// 客户姓名

    private String client_gender; // 客户性别

    private String birthday;// 出生日期YYYYMMDD

    private String nation_id;// 民族编号

    private String id_kind;// 证件类别[字典] 0 身份证

    private String id_no;// 证件号码

    private String id_address;// 证件地址

    private String issued_depart;// 签发机关

    private String id_begindate;// 证件有效期开始日期YYYYMMDD

    private String id_enddate;// 证件有效期结束日期YYYYMMDD

    private String passport_no;// 通行证号码

    private String gat_id_no;// 港澳台身份证号码

    private String issued_place;// 签发地点

    private String mrz_no;// MRZ码

    private Integer exchange_times;// 换证次数

    // 国籍地区
    private String nationality;

    private String crop_image;// 校正身份证图像

    // 质量类型 [0:无，1：复印件，2：拍屏，3：假证件，4：有水印，5：遮挡，6：切边，7：卡变形，8：有光斑]
    private String quality_kind;

    // 质量描述
    private String quality_desc;
}

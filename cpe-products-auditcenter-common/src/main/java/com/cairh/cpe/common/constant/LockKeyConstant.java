package com.cairh.cpe.common.constant;

public class LockKeyConstant {

    /**
     * 手机注册
     * %s 手机号
     */
    public static final String WSKH_LOCK_REGISTER = "wskh_lock_register_%s";

    /**
     * 语音短信
     * %s 手机号
     */
    public static final String WSKH_LOCK_SMS_VOICE = "wskh_lock_sms_voice_%s";

    /**
     * 参数提交
     * %s 申请编号
     */
    public static final String WSKH_LOCK_SUBMIT_PARAMS = "wskh_lock_submit_params_%s";

    /**
     * 智能审核
     * %s 分组
     * %s 申请编号
     */
    public static final String WSKH_LOCK_PROCESS_AI = "wskh_lock_process_ai_%s_request_no_%s";

    /**
     * 任务立即处理
     */
    public static final String WSKH_LOCK_TASK_DISPATCHER_ID_ = "wskh_lock_task_dispatcher_id_%s";

    /**
     * 操作员上传失信记录和rpa自动获取失信记录
     */
    public static final String WSKH_LOCK_TASK_DISHONEST_ID = "wskh_lock_task_dishonest_id_%s";

    /**
     * 任务推送派单/处理任务
     */
    public static final String WSKH_LOCK_TASK_SERIAL_NO = "wskh_lock_task_serial_no_%s";


    /**
     * 更改主流程表，更改大字段表，新增流水表
     */
    public static final String WSKH_AC_LOCK_SUBMITPARAMSBYOPERATOR = "wskh_ac_lock_submitParamsByOperator_%s";

    /**
     * 对比资料加锁控制并发
     */
    public static final String WSKH_CUST_PARAM_REQUEST_NO = "wskh_cust_params_request_no_%s";

    /**
     * 审核通过，审核驳回，定时任务回收，MQ消息更改流程表时，增加锁
     */
    public static final String WSKH_AC_LOCK_REQUEST_NO = "wskh_ac_lock_request_no_%s";

    public static final String WSKH_AC_LOCK_ID_NO = "wskh_ac_lock_id_no_%s";

    public static final String WSKH_AC_SUBMIT_CONTROL = "wskh:submit:control:serial_";
    /**
     * 审核通过，审核驳回，定时任务回收，MQ消息更改流程表时，增加锁
     */
    public static final String CALL_DETAILS_CALL_ID = "call:detals:callId:%s";

    /**
     * 拨打前加锁
     */
    public static final String CALL_FLOW_TASK_ID = "call:flow:taskId:%s";


    /**
     * rpc_mis 上传
     */
    public static final String WSKH_RPC_MIS_REQUEST_NO = "wskh_rpc_mis_request_no_%s";


    /**
     * 弹框
     */
    public static final String WSKH_AC_POPUP_LOCK_KEY = "wskh:popup:lock:key_%s";

}

package com.cairh.cpe.common.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.constant.HandupTypeEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.HandupDetails;
import com.cairh.cpe.common.mapper.HandupDetailsMapper;
import com.cairh.cpe.common.service.IHandupDetailsService;
import com.cairh.cpe.db.config.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;


/**
 * Description：操作任务流水表服务实现
 * Author： slx
 * Date： 2024/4/15 下午3:52
 */
@Slf4j
@Service
public class HandupDetailsServiceImpl extends ServiceImpl<HandupDetailsMapper, HandupDetails> implements IHandupDetailsService {

    @Resource
    private IdGenerator idGenerator;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveHandupDetails(BusinFlowTask businFlowTask, HandupTypeEnum typeEnum) {
        if (businFlowTask == null) {
            log.warn("操作任务流水表服务-[saveHandupDetails]保存异常，businFlowTask为空！");
            return;
        }
        try {
            log.info("新增操作任务流水表[saveHandupDetails], request_no={}, task_id={}", businFlowTask.getRequest_no(), businFlowTask.getTask_id());
            // 根据业务流程任务对象和操作类型构建对象
            HandupDetails handupDetails = buildHandupDetails(businFlowTask, typeEnum);
            // 保存对象到数据库
            this.save(handupDetails);
        } catch (Exception e) {
            log.error("操作任务流水表服务-[saveHandupDetails]保存异常request_no={}, task_id={}, type={}",
                    businFlowTask.getRequest_no(), businFlowTask.getTask_id(), typeEnum.getValue(), e);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateHandupDetails(BusinFlowTask businFlowTask) {
        if (businFlowTask == null) {
            log.warn("操作任务流水表服务-[updateHandupDetails]保存异常，businFlowTask为空！");
            return;
        }
        try {
            log.info("更新操作任务流水表[updateHandupDetails], request_no={}, task_id={}", businFlowTask.getRequest_no(), businFlowTask.getTask_id());
            // 查询数据
            LambdaQueryWrapper<HandupDetails> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HandupDetails::getRequest_no, businFlowTask.getRequest_no())
                    .eq(HandupDetails::getInvalid_flag, WskhConstant.NORMAL_STATUS)
                    .lt(HandupDetails::getCreate_datetime, businFlowTask.getCreate_datetime());
            List<HandupDetails> list = this.list(queryWrapper);
            if (CollectionUtil.isEmpty(list)) {
                return;
            }
            list.forEach(handupDetails -> handupDetails.setInvalid_flag(WskhConstant.INVALID_STATUS));
            // 更新数据
            this.updateBatchById(list);
        } catch (Exception e) {
            log.error("操作任务流水表服务-[updateHandupDetails]更新异常request_no={}, task_id={}",
                    businFlowTask.getRequest_no(), businFlowTask.getTask_id(), e);
        }
    }

    /**
     * 构建HandupDetails对象的工厂方法
     *
     * @param businFlowTask 业务流程任务对象
     * @param typeEnum      手动类型枚举
     * @return 构建完成的HandupDetails对象
     */
    private HandupDetails buildHandupDetails(BusinFlowTask businFlowTask, HandupTypeEnum typeEnum) {
        HandupDetails handupDetails = new HandupDetails();
        handupDetails.setRequest_no(businFlowTask.getRequest_no())
                .setSerial_id(idGenerator.nextUUID(null))
                .setTask_id(businFlowTask.getTask_id())
                .setHandup_type(typeEnum.getCode())
                .setOperator_no(businFlowTask.getOperator_no())
                .setOperator_name(businFlowTask.getOperator_name())
                .setTohis_flag(Constant.TOHIS_FLAG_N);
        return handupDetails;
    }

}

package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * Description：驳回流水表
 * Author： slx
 * Date： 2024/5/6 上午11:19
 */
@Data
@TableName("TASKREASONRECORD")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class TaskReasonRecord {
    /**
     * 流水号
     */
    @TableId(value = "SERIAL_ID")
    private String serial_id;

    /**
     * 任务编号
     */
    @TableField(value = "TASK_ID")
    private String task_id;

    /**
     * 原因分组
     */
    @TableField(value = "REASON_GROUP")
    private String reason_group;

    /**
     * 原因描述
     */
    @TableField(value = "REASON_DESC")
    private String reason_desc;

    /**
     * 原因类型
     */
    @TableField(value = "REASON_TYPE")
    private String reason_type;

    /**
     * 原因名称
     */
    @TableField(value = "REASON_NAME")
    private String reason_name;

    /**
     * 请求编号
     */
    @TableField(value = "REQUEST_NO")
    private String request_no;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_DATETIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     *  操作人
     */
    @TableField(value = "OPERATOR_NO")
    private String operator_no;

    /**
     * 操作人名称
     */
    @TableField(value = "OPERATOR_NAME")
    private String operator_name;

    /**
     * 任务类型
     */
    @TableField(value = "TASK_TYPE")
    private String task_type;

    /**
     * 所属机构
     */
    @TableField(value = "branch_no")
    private String branch_no;

    /**
     * 营销团队
     */
    @TableField(value = "marketing_team")
    private String marketing_team;

    /**
     * 渠道码
     */
    @TableField(value = "channel_code")
    private String channel_code;

    /**
     * 活动名称
     */
    @TableField(value = "activity_name")
    private String activity_name;

    /**
     * 见证任务申请时间
     */
    @TableField(value = "task_apply_datetime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date task_apply_datetime;
}
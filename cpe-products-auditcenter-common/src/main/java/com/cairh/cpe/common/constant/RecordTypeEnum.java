package com.cairh.cpe.common.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * 记录类型枚举
 *
 * <AUTHOR>
 * @since 2025/3/12 13:39
 */
@Getter
public enum RecordTypeEnum {

    STAGING_OPTION("1", "暂存"),
    ACTIVATION_OPTION("2", "释放");

    private String code;
    private String value;

    RecordTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getDesc(String code) {
        if (null == code) {
            return StringUtils.EMPTY;
        }
        Optional<RecordTypeEnum> optional =
                Arrays.stream(RecordTypeEnum.values()).filter(item -> item.getCode().equals(code)).findFirst();
        if (optional.isPresent()) {
            return optional.get().getValue();
        }
        return StringUtils.EMPTY;
    }
}

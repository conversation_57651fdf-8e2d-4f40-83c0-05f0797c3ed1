package com.cairh.cpe.common.constant;

public enum SuspendReasonEnum {

//    确认客户信息大写
    CONFIRM_CUSTOMER_INFO("1","确认客户信息大写"),
//    系统维护
    SYSTEM_MAINTENANCE("2","系统维护"),
//    中登故障不可用
    ZHONGDENG_FAULT("3","中登故障不可用");

    private String code;
    private String value;

    private SuspendReasonEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getValue(String code) {
        for (SuspendReasonEnum c : SuspendReasonEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setValue(String value) {
        this.value = value;
    }
}

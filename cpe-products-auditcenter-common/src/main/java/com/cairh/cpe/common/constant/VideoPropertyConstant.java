package com.cairh.cpe.common.constant;

import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;

/**
 * video properties for {@link CompositePropertySources#getProperty(String)}, here is an aggregate of them
 *
 * <AUTHOR>
 */
public class VideoPropertyConstant {

    // 视频页面跳转地址
    public static final String REDIRECT_PAGE_ADDR = "comp.video.redirect.page.addr";

    // anychat视频内网地址
    public static final String ANYCHAT_INTRANET_ADDR = "comp.video.anychat.intranet.addr";

    // anychat视频外网地址
    public static final String ANYCHAT_INTERNET_ADDR = "comp.video.anychat.internet.addr";

    // anychat端口
    public static final String ANYCHAT_PORT = "comp.video.anychat.port";

    // anychat集群标识
    public static final String ANYCHAT_CLUSTER_IDENTIFICATION = "comp.video.anychat.cluster.identification";

    // anychat集群场景分配的appguid
    public static final String ANYCHAT_CLUSTER_APPGUID = "comp.video.anychat.cluster.appguid";

    // anychat h5视频服务地址
    public static final String ANYCHAT_H5_ADDR = "comp.video.anychat.h5.addr";

    // anychat h5视频服务端口
    public static final String ANYCHAT_H5_PORT = "comp.video.anychat.h5.port";

    // zego视频服务地址
    public static final String ZEGO_H5_ADDR = "comp.video.zego.h5.addr";
    /**
     * 即构视频显示模式
     */
    public static final String CONFIG_VIDEO_ZEGO_DISPLAY_MODEL = "config.video.zego.display.model";
    public static final String ZEGO_H5_ADDR_IN = "comp.video.zego.intranet.addr";
    public static final String ZEGO_H5_token_ADDR = "comp.video.zego.token.url";
    /**
     * 双向视频水印
     */
    public static final String COMP_VIDEO_BOTHWAY = "comp_video_bothway";

    // zego app_id
    public static final String ZEGO_H5_APPID = "comp.video.zego.h5.appid";

    // 证通zego视频服务地址
    public static final String ZHENGTONG_H5_ADDR = "comp_video_zhengtong_h5_addr";

    // 证通zego app_id
    public static final String ZHENGTONG_H5_APPID = "comp_video_zhengtong_h5_appid";

    // 指定配置视频厂商
    public static final String VENDER = "comp.video.vender";

    // 非活跃视频用户信息清理任务周期
    public static final String UNLIVE_USER_INFO_REFRESH_INTERVAL = "comp.video.unlive.user.info.refresh.interval";

    // 非活跃视频用户信息过期时间
    public static final String USER_INFO_EXPIRE_INTERVAL = "comp.video.user.info.expire.interval";

    // 非活跃视频用户队列清理任务周期
    public static final String USER_QUEUE_REFRESH_INTERVAL = "comp.video.user.queue.refresh.interval";

    // 非活跃视频用户队列过期时间
    public static final String USER_QUEUE_UNLIVE_INTERVAL = "comp.video.user.queue.unlive.interval";

    // 视频用户位置刷新任务周期
    public static final String USER_POSITION_REFRESH_INTERVAL = "comp.video.user.position.refresh.interval";

    // 视频用户状态刷新任务周期
    public static final String USER_STATUS_REFRESH_INTERVAL = "comp.video.user.status.refresh.interval";

    // 是否启用视频用户位置记忆
    public static final String USER_SCORE_PRESERVE = "comp.video.user.score.preserve";
}

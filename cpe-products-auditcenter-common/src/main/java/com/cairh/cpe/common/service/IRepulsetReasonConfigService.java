package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.entity.RepulsetReasonConfig;
import com.cairh.cpe.common.entity.request.ReasonGroupLImitReq;
import com.cairh.cpe.common.entity.request.RectificationReasonReq;
import com.cairh.cpe.common.entity.request.RepulsetReasonBatchForm;
import com.cairh.cpe.common.entity.response.DictInfo;
import com.cairh.cpe.common.entity.response.NotAllowReasonGroup;
import com.cairh.cpe.common.entity.response.RepulsetReasonConfigQueryRes;
import com.cairh.cpe.context.BaseUser;

import java.util.List;

public interface IRepulsetReasonConfigService extends IService<RepulsetReasonConfig> {


    Page<RepulsetReasonConfigQueryRes> selectRectificationReasonListByPage(RectificationReasonReq rectificationReasonReq, List<DictInfo> dictInfos);


    List<NotAllowReasonGroup> reasonGroupLimit(ReasonGroupLImitReq req);

    /**
     * 批量新增
     *
     * @param baseUser
     * @param batchForm
     * @return
     */
    int batchAddReasonConfig(BaseUser baseUser, RepulsetReasonBatchForm batchForm);
}
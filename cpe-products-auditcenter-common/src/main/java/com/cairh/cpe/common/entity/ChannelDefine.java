package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 渠道定义表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ChannelDefine")
public class ChannelDefine implements Serializable {

    /**
     * 渠道代码
     */
    @TableId("channel_code")
    private String channel_code;

    /**
     * 渠道名称
     */
    private String channel_name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private String status;


    /**
     * 创建日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;
}

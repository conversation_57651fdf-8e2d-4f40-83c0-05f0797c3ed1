package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.entity.BusinFlowParams;

import java.util.Map;

public interface IBusinFlowParamsService extends IService<BusinFlowParams> {

    Map<String, Object> getParamContentById(String request_no);

    /**
     * 分段保存大字符串信息
     */
    void saveParams(String param_id, Map<String, Object> contentMap);

    /**
     * 保存快照
     */
    String saveSnapshot(String request_no,String note);
}

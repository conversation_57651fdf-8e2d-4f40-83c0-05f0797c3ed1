package com.cairh.cpe.common.entity.clob;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserIndependenceInfo implements Serializable {

    /**
     * 身份证人像面
     */
    private String file_6A;

    /**
     * 身份证国徽面
     */
    private String file_6B;

    /**
     * 大头照
     */
    private String file_80;

    /**
     * 公案照
     */
    private String file_82;
    /**
     * 诚信档案，多个逗号分割
     */
    private String file_23;

    /**
     * 港澳居民居住证正面
     */
    private String file_7C;
    /**
     * 港澳居民居住证反面
     */
    private String file_7D;
    /**
     * 港澳来往内地通行证正面
     */
    private String file_7E;
    /**
     * 港澳来往内地通行证反面
     */
    private String file_7F;
    /**
     * 台湾居民居住证正面
     */
    private String file_7I;
    /**
     * 台湾居民居住证反面
     */
    private String file_7J;
    /**
     * 台湾来往内地通行证正面
     */
    private String file_7K;
    /**
     * 台湾来往内地通行证反面
     */
    private String file_7L;
    /**
     * 居住证证明文件
     */
    private String file_8K;

    /**
     * 机构的营业执照
     */
    private String file_Bm;

    /**
     * 境内机构出具的就业证明
     */
    private String file_7W;

    /**
     * 住宿登记证明表原件
     */
    private String file_7X;

    /**
     * 指定客户号
     */
    private String assign_client_id;

    /**
     * 开户标记存疑字典值
     */
    private String doubtful_info;

    /**
     * 开户标记存疑其它详情
     */
    private String doubtful_info_detail;
    /**
     * 客户标签
     */
    private String client_tags;
    /**
     * 图片查看
     */
    private String look_pic;
    /**
     * 视频类型 1-单项，2-双向
     */
    private String video_type;
    /**
     * 开户营业部
     */
    private String branch_no;

    /**
     * 上传80的类型
     */
    private String upload_80_type;
}

package com.cairh.cpe.common.entity.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 审核操作记录
 */
@Data
public class AuditOperateRecord implements Serializable {

    /**
     * 操作员编号
     */
    private String operator_no;
    /**
     * 营业部编号
     */
    private String branch_no;
    /**
     * 操作员名称
     */
    private String operator_name;
    /**
     * 营业部名称
     */
    private String branch_name;
    /**
     * 备注
     */
    private String business_remark;
    /**
     * 业务标志
     */
    private String business_flag;
    /**
     * 时间
     */
    private String create_datetime;
}

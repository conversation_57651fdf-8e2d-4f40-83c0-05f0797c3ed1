package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.lang.reflect.Field;
import java.util.Date;

/**
 * Description：业务流程请求审核跟踪表
 * Author： slx
 * Date： 2024/4/16 下午5:40
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("BusinProcessRequestAuditTrail")
public class BusinProcessRequestAuditTrail {

    /**
     * 请求编号
     */
    private String request_no;

    /**
     * 任务id
     */
    @TableId("task_id")
    private String task_id;

    /**
     * 用户姓名
     */
    private String user_name;

    /**
     * 身份证号码
     */
    private String id_no;

    /**
     * 证件类型
     */
    private String id_kind;

    /**
     * 时间属性,进件时间 1-工作日 2-非工作日
     */
    private String time_property;

    /**
     * 请求状态
     */
    private String request_status;

    /**
     * 请求时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date request_datetime;

    /**
     * 见证操作员编号
     */
    private String audit_operator_no;

    /**
     * 见证操作员姓名
     */
    private String audit_operator_name;

    /**
     * 见证用户营业部编号
     */
    private String audit_operator_branch_no;

    /**
     * 见证用户所属分公司
     */
    private String audit_operator_branch_name;

    /**
     * 见证上级营业部编号
     */
    private String audit_operator_up_branch_no;

    /**
     * 见证上级营业部的名称
     */
    private String audit_operator_up_branch_name;

    /**
     * 见证创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date audit_create_datetime;

    /**
     * 见证处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date audit_deal_datetime;

    /**
     * 见证完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date audit_finish_datetime;

    /**
     * 见证驳回原因
     */
    private String audit_op_content;

    /**
     * 复核操作员编号
     */
    private String review_operator_no;

    /**
     * 复核操作员姓名
     */
    private String review_operator_name;

    /**
     * 复核用户营业部编号
     */
    private String review_operator_branch_no;

    /**
     * 复核用户所属分公司
     */
    private String review_operator_branch_name;

    /**
     * 复核上级营业部编号
     */
    private String review_operator_up_branch_no;

    /**
     * 复核上级营业部的名称
     */
    private String review_operator_up_branch_name;

    /**
     * 复核创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date review_create_datetime;

    /**
     * 复核处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date review_deal_datetime;

    /**
     * 复核完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date review_finish_datetime;

    /**
     * 复核驳回原因
     */
    private String review_op_content;

    /**
     * 二次复核操作员编号
     */
    private String sec_rv_operator_no;

    /**
     * 二次复核操作员姓名
     */
    private String sec_rv_operator_name;

    /**
     * 二次复核用户营业部编号
     */
    private String sec_rv_operator_branch_no;

    /**
     * 二次复核用户所属分公司
     */
    private String sec_rv_operator_branch_name;

    /**
     * 二次复核上级营业部编号
     */
    private String sec_rv_operator_up_branch_no;

    /**
     * 二次复核上级营业部的名称
     */
    private String sec_rv_operator_up_branch_name;

    /**
     * 二次复核创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date sec_rv_create_datetime;

    /**
     * 二次复核处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date sec_rv_deal_datetime;

    /**
     * 二次复核完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date sec_rv_finish_datetime;

    /**
     * 二次复核驳回原因
     */
    private String sec_rv_op_content;

    /**
     * 渠道码
     */
    private String channel_code;

    /**
     * 活动码
     */
    private String activity_no;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 用户营业部编号
     */
    private String user_branch_no;

    /**
     * 用户所属分公司
     */
    private String user_branch_name;

    /**
     * 上级营业部编号
     */
    private String user_up_branch_no;

    /**
     * 上级营业部的名称
     */
    private String user_up_branch_name;

    /**
     * 标签
     */
    private String match_labels;

    /**
     * 任务来源 1-系统派发 2-手动认领 3-转交任务 4-转派任务
     */
    private String task_source;

    /**
     * 接入方式
     */
    private String app_id;

    /**
     * 是否托管分支机构 0-托管分支机构 1-非托管分支机构
     */
    private String is_branch_managed;

    /**
     * 首次处理操作员编号
     */
    private String first_deal_operator_no;

    /**
     * 首次处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date first_deal_datetime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;


    /**
     * 外呼标识 0 未拨打  1已拨打
     */
    private String call_flag;

    /**
     * 暂存标志 0-未暂存 1-暂存过
     */
    private String pause_flag;

    /**
     * 首次暂存时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date first_pause_datetime;

    /**
     * 最后处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date end_deal_datetime;

    /**
     * 视频见证类型
     */
    private String video_type;

    /**
     * 营销团队
     */
    private String marketing_team;

    public void setFieldValue(String fieldName, Object value) throws NoSuchFieldException, IllegalAccessException {
        // 获取字段对应的Field对象
        Field field = BusinProcessRequestAuditTrail.class.getDeclaredField(fieldName);
        // 设置访问权限，以便可以修改私有字段
        field.setAccessible(true);
        // 将值设置到对应的字段
        field.set(this, value);
    }
}

package com.cairh.cpe.common.entity.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@Accessors(chain = true)
public class VerifyPassPortResult implements Serializable {

    /**
     * 认证结果信息
     */
    private String result_info;

    /**
     * 公安认证状态
     */
    private String status;

    /**
     * 图片
     */
    private String image_data;

    /**
     * 文件记录id
     */
    private String filerecord_id;

    /**
     * 认证记录id
     */
    private String idverifyrecord_id;
}
package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * Description：重复标签记录表
 * Author： slx
 * Date： 2024/4/24 上午10:33
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("RepeatedLabelRecord")
public class RepeatedLabelRecord {

    /**
     * 流水号
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 来源业务受理编号
     */
    private String source_request_no;

    /**
     * 目标业务受理编号
     */
    private String target_request_no;

    /**
     * 重复类型 1-idNo重复 2-客户地址重复
     */
    private String repeated_type;

    /**
     * 重复内容
     */
    private String repeated_content;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;
    
}

package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.constant.UpdateBusinProcessRequestAuditTrailSourceEnum;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.BusinProcessRequestAuditTrail;

import java.util.Map;

/**
 * Description：业务流程请求审核跟踪表服务
 * Author： slx
 * Date： 2024/4/16 下午6:33
 */
public interface IBusinProcessRequestAuditTrailService extends IService<BusinProcessRequestAuditTrail> {

    /**
     * 开户、复核、二次复核-保存业务流程请求
     *
     * @param businFlowTask 见证任务信息
     * @param params        请求参数
     */
    void saveBusinProcessRequestAuditTrail(BusinFlowTask businFlowTask, Map<String, Object> params);


    /**
     * 更新业务流程信息
     *
     * @param businFlowTask 见证任务信息
     * @param userParams    见证、复核、二次复核用户信息（用户所属机构&上级机构）
     * @param sourceEnum    更新来源
     */
    void updateBusinProcessRequestAuditTrail(BusinFlowTask businFlowTask, Map<String, Object> userParams, UpdateBusinProcessRequestAuditTrailSourceEnum sourceEnum);

}

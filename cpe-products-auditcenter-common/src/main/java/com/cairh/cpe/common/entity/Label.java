package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("LABEL")
public class Label implements Serializable {
    /**
     * 流水号
     */
    @TableId(value = "serial_id")
    private String serial_id;
    private String label_name;
    private String label_url;
    private String regular_expression;
    private String label_copy;
    private String label_detail;
    private String label_type;

}

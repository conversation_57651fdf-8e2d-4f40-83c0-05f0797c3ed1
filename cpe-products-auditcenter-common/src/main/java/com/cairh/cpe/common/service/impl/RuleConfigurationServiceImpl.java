package com.cairh.cpe.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.RedisKeyConstant;
import com.cairh.cpe.common.entity.RuleConfiguration;
import com.cairh.cpe.common.mapper.RuleConfigurationMapper;
import com.cairh.cpe.common.service.IRuleConfigurationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2025/3/11 16:21
 */
@Slf4j
@Service
public class RuleConfigurationServiceImpl extends ServiceImpl<RuleConfigurationMapper, RuleConfiguration> implements IRuleConfigurationService {

    @Cacheable(value = RedisKeyConstant.AUDTI_CACHE_RULE_LIST, unless = "#result?.size() == 0")
    @Override
    public List<RuleConfiguration> findAllRules() {
        LambdaQueryWrapper<RuleConfiguration> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(RuleConfiguration::getOrder_no);
        return this.list(wrapper);
    }

    @Scheduled(cron = "13 0 0 * * ?")
    @CacheEvict(value = RedisKeyConstant.AUDTI_CACHE_RULE_LIST, allEntries = true)
    @Override
    public void clearCache() {
        log.info("rule cache list 执行清除.");
    }

}

package com.cairh.cpe.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 功能说明: Date工具类<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 * 开发时间: 2015年8月3日<br>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATE_FORMAT_NO_DELIMITER = "yyyyMMdd";
    public static final String DATE_FORMAT_DOT_DELIMITER = "yyyy.MM.dd";
    public static final String DATE_FORMAT_WEN = "yyyy年MM月dd日";
    public static final String TIME_FORMAT = "HH:mm:ss";
    public static final String TIME_FORMAT_NO_SEC = "HH:mm";
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss.S";
    public static final String DATE_TIME_FORMAT_NO_SEC = "yyyy-MM-dd HH:mm";
    public static final String DATE_TIME_FORMAT_NO_DELIMITER = "yyyyMMddHHmmss";
    public static final String DATE_PRECISE_TO_MINUTE = "yyyyMMddHHmm";
    public static final String[] WEEKS = {"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"};
    public static final String[] DEFAULT_FORMATS = {TIMESTAMP_FORMAT, DATE_TIME_FORMAT, DATE_FORMAT, TIME_FORMAT, DATE_PRECISE_TO_MINUTE};
    public static final String DATE_TIME_FORMAT2 = "yyyyMMdd HH:mm:ss";
    private final static Logger logger = LoggerFactory.getLogger(DateUtils.class);

    /**
     * 自动判断日期字符串的格式，返回Date对象
     *
     * @param dateString 日期字符串
     * @param dateFormat 格式字符串数组。为空时使用<code>DateUtil.DEFAULT_FORMATS</code>
     * @return 日期Date对象
     * @throws ParseException
     * @see org.apache.commons.lang3.time.DateUtils#parseDate
     */
    @Deprecated
    public static Date parse(String dateString, String... dateFormat) throws ParseException {
        if (StringUtils.isEmpty(dateString)) {
            return null;
        }

        if (dateFormat == null || dateFormat.length == 0) {
            return org.apache.commons.lang3.time.DateUtils.parseDate(dateString, DEFAULT_FORMATS);
        } else {
            return org.apache.commons.lang3.time.DateUtils.parseDate(dateString, dateFormat);
        }
    }

    /**
     * 自动判断日期字符串的格式，返回Date对象 严格模式
     *
     * @param dateString 日期字符串
     * @param dateFormat 格式字符串数组。为空时使用<code>DateUtil.DEFAULT_FORMATS</code>
     * @return 日期Date对象
     * @throws ParseException
     * @see org.apache.commons.lang3.time.DateUtils#parseDate
     */
    public static Date parseDateStrictly(String dateString, String... dateFormat) throws ParseException {
        if (StringUtils.isEmpty(dateString)) {
            return null;
        }

        if (dateFormat == null || dateFormat.length == 0) {
            return org.apache.commons.lang3.time.DateUtils.parseDateStrictly(dateString, DEFAULT_FORMATS);
        } else {
            return org.apache.commons.lang3.time.DateUtils.parseDateStrictly(dateString, dateFormat);
        }
    }

    /**
     * 取指定格式的当前时间字符串
     *
     * @param dateFormat
     * @return
     */
    public static String getCurrentTime(String dateFormat) {
        Date date = new Date();
        return format(date, dateFormat);
    }

    /**
     * 将字符串转换成Date类型{}
     *
     * @param dateString
     * @param dateFormat
     * @return
     */
    @Deprecated
    public static Date parse(String dateString, String dateFormat) {
        if (StringUtils.isEmpty(dateString)) {
            return null;
        }
        try {
            return org.apache.commons.lang3.time.DateUtils.parseDate(dateString, dateFormat);
        } catch (ParseException e) {
            logger.error("日期格式化异常,详细信息={},{}", dateString, dateFormat);
            return null;
        }
    }

    /**
     * 将字符串转换成Date类型 严格模式
     *
     * @param dateString
     * @param dateFormat
     * @return
     */
    public static Date parseDateStrictly(String dateString, String dateFormat) {
        if (StringUtils.isEmpty(dateString) || dateString.equals("0")) {
            return null;
        }
        try {
            logger.debug("dateString={}", dateString);
            return org.apache.commons.lang3.time.DateUtils.parseDateStrictly(dateString, dateFormat);
        } catch (ParseException e) {
            logger.error("日期格式化异常,详细信息={},{}", dateString, dateFormat);
            return null;
        }
    }

    /**
     * 将Date类型转化成字符串
     *
     * @param date
     * @param dateFormat
     * @return
     */
    public static String format(Date date, String dateFormat) {
        if (date == null) {
            return "";
        } else {
            return DateFormatUtils.format(date, dateFormat);
        }
    }

    /**
     * 在传入的日期基础上往后加n天
     *
     * @param date
     * @param n    要加的天数
     * @return
     */
    public static Date addDay(Date date, int n) {
        return org.apache.commons.lang3.time.DateUtils.addDays(date, n);
    }

    /**
     * 判断当前时间是否在开始时间与结束时间之间
     *
     * @param time  当前时间
     * @param begin 开始时间
     * @param end   结束时间
     * @return boolen类型，true表示在两者间，false表示不在两者之间
     */
    public static boolean isTimeIn(Date time, Date begin, Date end) {
        return time.getTime() >= begin.getTime() && time.getTime() <= end.getTime();
    }

    /**
     * 判断指定日期是星期几
     *
     * @param time   要判断的日期
     * @param format 输入的日期格式
     * @return 返回数字[1:星期一，2：星期二，....，7：星期日]
     * @throws ParseException
     */
    @Deprecated
    public static int getWeek(String time, String format) throws ParseException {
        return getWeek(org.apache.commons.lang3.time.DateUtils.parseDate(time, format));
    }

    /**
     * 判断指定日期是星期几 严格模式
     *
     * @param time   要判断的日期
     * @param format 输入的日期格式
     * @return 返回数字[1:星期一，2：星期二，....，7：星期日]
     * @throws ParseException
     */
    public static int getWeekStrictly(String time, String format) throws ParseException {
        return getWeek(org.apache.commons.lang3.time.DateUtils.parseDateStrictly(time, format));
    }

    /**
     * 判断指定日期是星期几
     *
     * @param date 要判断的日期
     * @return 返回数字[1:星期一，2：星期二，....，7：星期日]
     * @throws ParseException
     */
    public static int getWeek(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int week = 0;
        if (c.get(Calendar.DAY_OF_WEEK) == 1) {
            week = 7;
        } else {
            week = c.get(Calendar.DAY_OF_WEEK) - 1;
        }
        return week;
    }

    public static int getYear(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.YEAR);
    }

    /**
     * 判断是否为有效的身份证日期
     *
     * @param date
     * @return
     */
    public static boolean isIdDate(String date) {
        return isDateFormat(date, "yyyyMMdd");
    }

    /**
     * 判断传入的字符串dateStr是否是日期格式patternStr的字符串 <AUTHOR>
     *
     * @param dateString
     * @param dateFormat
     * @return
     */
    public static boolean isDateFormat(String dateString, String dateFormat) {
        Date date = null;
        try {
            date = parse(dateString, dateFormat);
        } catch (Exception e) {
            logger.error("日期格式化异常,详细信息={},{}", dateString, dateFormat);
        }

        return date != null;
    }

    /**
     * 将字符串日期转成Timestamp类型
     *
     * @param dateString 字符串类型的时间
     * @param format     字符串类型的时间要转换的格式
     * @return Timestamp类型的时间戳
     * @throws ParseException
     */
    @Deprecated
    public static java.sql.Timestamp parse2Timestamp(String dateString, String format) throws ParseException {
        return new java.sql.Timestamp(org.apache.commons.lang3.time.DateUtils.parseDate(dateString, format).getTime());
    }

    /**
     * 将字符串日期转成Timestamp类型 严格模式
     *
     * @param dateString 字符串类型的时间
     * @param format     字符串类型的时间要转换的格式
     * @return Timestamp类型的时间戳
     * @throws ParseException
     */
    public static java.sql.Timestamp parse2TimestampStrictly(String dateString, String format) throws ParseException {
        return new java.sql.Timestamp(org.apache.commons.lang3.time.DateUtils.parseDateStrictly(dateString, format).getTime());
    }

    /**
     * 获取两个时间的间隔,字符串表示
     *
     * @param start
     * @param end
     * @return
     * <AUTHOR>
     */
    public static String getDiffTimeStr(Date start, Date end) {
        String time = "";
        if (start != null && end != null) {
            int t = (int) (end.getTime() - start.getTime()) / 1000;
            String h = "";
            String m = "";
            String s = "";
            h = (int) t / 3600 + "";
            m = (int) (t % 3600) / 60 + "";
            s = t % 60 + "";
            if (h.length() <= 1) {
                h = "0" + h;
            }
            if (m.length() <= 1) {
                m = "0" + m;
            }
            if (s.length() <= 1) {
                s = "0" + s;
            }
            time = h + ":" + m + ":" + s;
        }
        return time;
    }

    /**
     * 获取两个日期之间间隔的分钟数
     *
     * @param startDate
     * @param endDate
     * @return
     * <AUTHOR>
     */
    public static int getIntervalMinute(Date startDate, Date endDate) {
        int min = 0;
        if (null != startDate && null != endDate) {
            long end = endDate.getTime();
            long start = startDate.getTime();
            long betweenDate = (end - start) / (60 * 1000);
            min = Long.valueOf(betweenDate).intValue();
        }
        return min;
    }

    /**
     * 获取两个日期之间间隔的天数
     *
     * <AUTHOR>
     */
    public static int getIntervalDay(Date start_date, Date end_date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            start_date = sdf.parse(sdf.format(start_date));
            end_date = sdf.parse(sdf.format(end_date));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(start_date);
        long time1 = cal.getTimeInMillis();
        cal.setTime(end_date);
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);
        return Integer.parseInt(String.valueOf(between_days));
    }

    /**
     * 根据类型参数返回不同的日期
     *
     * @param type <pre>
     *                                                                                                                                                             @return 返回yyyy-MM-dd格式字符串
     *                                                                                                                                                             <AUTHOR>
     */
    public static String getSpecifiedDay(String type) {
        String time = "";
        if ("today".equals(type)) {
            time = getCurrentTime(DATE_FORMAT);
        } else if ("yesterday".equals(type)) {
            Date yesterday = addDay(new Date(), -1);
            time = format(yesterday, DATE_FORMAT);
        } else if ("less7".equals(type)) {
            Date yesterday = addDay(new Date(), -6);
            time = format(yesterday, DATE_FORMAT);
        } else if ("less30".equals(type)) {
            Date yesterday = addDay(new Date(), -29);
            time = format(yesterday, DATE_FORMAT);
        } else if ("all".equals(type)) {
            // 取全部就设置截至时间为30年以前
            Date yesterday = addYears(new Date(), -29);
            time = format(yesterday, DATE_FORMAT);
        }
        return time;
    }

    /**
     * 星期转换为星期索引
     */
    public static int weekToNum(String week) {
        int weekNum = -1;
        for (int i = 0, j = WEEKS.length; i < j; i++) {
            if (week != null && WEEKS[i].toLowerCase().contains(week.toLowerCase())) {
                weekNum = i + 1;
                break;
            }
        }
        return weekNum;
    }

    public static boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || year % 400 == 0;
    }

    public static boolean isTwoMonth(Date date) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date);
        int yearBirth = cal1.get(Calendar.YEAR);
        int monthBirth = cal1.get(Calendar.MONTH);
        int dayOfMonthBirth = cal1.get(Calendar.DAY_OF_MONTH);
        return isLeapYear(yearBirth) && 2 == (monthBirth + 1) && 29 == dayOfMonthBirth;
    }

    ////卡片日期处理部分

    /**
     * 获取起始日期
     *
     * @param validate_date 有效期,起始结束以'-'分隔,格式支持:yyyyMMdd-yyyyMMdd或yyyy.MM.dd-yyyy.MM.dd
     * @return 起始日期 yyyyMMdd
     */
    public static String getId_begindate(String validate_date) {
        if (null == validate_date) {
            return null;
        }
        String[] days = validate_date.split("-");
        if (days.length <= 0) {
            return null;
        }
        return "".equals(days[0]) ? "" : days[0].trim().replaceAll("\\.", "");
    }

    /**
     * 获取结束日期
     *
     * @param validate_date 有效期,起始结束以'-'分隔(结束允许长期),格式支持:yyyyMMdd-yyyyMMdd或yyyy.MM.dd-yyyy.MM.dd
     * @return 结束日期 yyyyMMdd
     */
    public static String getId_enddate(String validate_date) {
        if (null == validate_date) {
            return null;
        }
        String[] days = validate_date.split("-");
        if (days.length <= 1) {
            return null;
        }
        if (null == days[1] || "".equals(days[1].trim())) {
            return null;
        }
        if ("长期".equals(days[1])) {
            return "30001231";
        }
        return days[1].trim().replaceAll("\\.", "");
    }

    /**
     * 获取生日
     *
     * @param birthday 出生日期,格式支持:yyyyMMdd或yyyy年MM月dd日
     * @return 生日 yyyyMMdd
     */
    public static String getBirthdate(String birthday) {
        try {
            if (StringUtils.isBlank(birthday)) {
                return null;
            }
            String org = StringUtils.replacePattern(birthday, "[-.]", StringUtils.EMPTY);
            if (8 == org.length()) {
                return org;
            }
            Calendar cal = Calendar.getInstance();
            int indexYear = org.indexOf("年");
            int indexMonth = org.indexOf("月");
            int indexDay = org.indexOf("日");
            cal.set(Calendar.YEAR, Integer.parseInt(org.substring(0, indexYear)));
            cal.set(Calendar.MONTH, Integer.parseInt(org.substring(indexYear + 1, indexMonth)) - 1);
            cal.set(Calendar.DATE, Integer.parseInt(org.substring(indexMonth + 1, indexDay)));
            return new SimpleDateFormat("yyyyMMdd").format(cal.getTime());
        } catch (Exception e) {
            logger.error("日期格式化异常,详细信息={}", birthday);
        }
        return birthday;
    }

    /**
     * 居住证整年校验
     *
     * @param validate_date 有效期,起始结束以'-'分隔,格式支持:yyyyMMdd-yyyyMMdd或yyyy.MM.dd-yyyy.MM.dd
     * @return 是否满足
     */
    public static boolean checkGATResidenceEffectiveDate(String validate_date) {
        try {
            String id_begindate = getId_begindate(validate_date);
            String id_enddate = getId_enddate(validate_date);
            if (StringUtils.isAnyBlank(id_begindate, id_enddate)) {
                return false;
            }
            Date beginDay = DateUtils.parseDateStrictly(id_begindate, DateUtils.DATE_FORMAT_NO_DELIMITER);
            Date endDay = DateUtils.parseDateStrictly(id_enddate, DateUtils.DATE_FORMAT_NO_DELIMITER);
            if (DateUtils.isTwoMonth(beginDay)) {
                if (!DateUtils.isLeapYear(DateUtils.getYear(endDay))) {
                    beginDay = DateUtils.addDay(beginDay, 1);
                }
            }
            //5年减1天
            Calendar calendar_begin_1 = Calendar.getInstance();
            calendar_begin_1.setTime(beginDay);
            calendar_begin_1.add(Calendar.YEAR, 5);
            calendar_begin_1.add(Calendar.DAY_OF_MONTH, -1);

            //5年
            Calendar calendar_begin_2 = Calendar.getInstance();
            calendar_begin_2.setTime(beginDay);
            calendar_begin_2.add(Calendar.YEAR, 5);

            Calendar calendar_end = Calendar.getInstance();
            calendar_end.setTime(endDay);
            return calendar_begin_1.equals(calendar_end) || calendar_begin_2.equals(calendar_end);
        } catch (Exception e) {
            logger.error("居住证整年校验日期格式异常,详细信息={}", validate_date);
        }
        return false;
    }

    /**
     * 通行证整年校验
     *
     * @param validate_date 有效期,起始结束以'-'分隔,格式支持:yyyyMMdd-yyyyMMdd或yyyy.MM.dd-yyyy.MM.dd
     * @return 是否满足
     */
    public static boolean checkGATPassEffectiveDate(String validate_date, String birthday) {
        try {
            String id_begindate = getId_begindate(validate_date);
            String id_enddate = getId_enddate(validate_date);
            String birth_date = getBirthdate(birthday);
            if (StringUtils.isAnyBlank(id_begindate, id_enddate, birth_date)) {
                return false;
            }
            Date beginDay = DateUtils.parseDateStrictly(id_begindate, DateUtils.DATE_FORMAT_NO_DELIMITER);
            Date endDay = DateUtils.parseDateStrictly(id_enddate, DateUtils.DATE_FORMAT_NO_DELIMITER);
            if (DateUtils.isTwoMonth(beginDay)) {
                if (!DateUtils.isLeapYear(DateUtils.getYear(endDay))) {
                    beginDay = DateUtils.addDay(beginDay, 1);
                }
            }
            int valid_during = IdCardUtils.getAge(id_begindate, birth_date) >= 18 ? 10 : 5;

            //valid_during减1天
            Calendar calendar_begin_1 = Calendar.getInstance();
            calendar_begin_1.setTime(beginDay);
            calendar_begin_1.add(Calendar.YEAR, valid_during);
            calendar_begin_1.add(Calendar.DAY_OF_MONTH, -1);

            //valid_during
            Calendar calendar_begin_2 = Calendar.getInstance();
            calendar_begin_2.setTime(beginDay);
            calendar_begin_2.add(Calendar.YEAR, valid_during);

            Calendar calendar_end = Calendar.getInstance();
            calendar_end.setTime(endDay);
            return calendar_begin_1.equals(calendar_end) || calendar_begin_2.equals(calendar_end);
        } catch (Exception e) {
            logger.error("通行证整年校验日期格式异常,详细信息={}", validate_date);
        }
        return false;
    }

    /**
     * 台湾通行证整年校验
     *
     * @param validate_date 有效期,起始结束以'-'分隔,格式支持:yyyyMMdd-yyyyMMdd或yyyy.MM.dd-yyyy.MM.dd
     * @return 是否满足
     */
    public static boolean checkTwPassEffectiveDate(String validate_date) {
        try {
            String id_begindate = getId_begindate(validate_date);
            String id_enddate = getId_enddate(validate_date);
            if (StringUtils.isAnyBlank(id_begindate, id_enddate)) {
                return false;
            }
            Date beginDay = DateUtils.parseDateStrictly(id_begindate, DateUtils.DATE_FORMAT_NO_DELIMITER);
            Date endDay = DateUtils.parseDateStrictly(id_enddate, DateUtils.DATE_FORMAT_NO_DELIMITER);
            if (DateUtils.isTwoMonth(beginDay)) {
                if (!DateUtils.isLeapYear(DateUtils.getYear(endDay))) {
                    beginDay = DateUtils.addDay(beginDay, 1);
                }
            }
            //valid_during减1天
            Calendar calendar_begin_1 = Calendar.getInstance();
            calendar_begin_1.setTime(beginDay);
            calendar_begin_1.add(Calendar.MONTH, 3);

            //valid_during
            Calendar calendar_begin_2 = Calendar.getInstance();
            calendar_begin_2.setTime(beginDay);
            calendar_begin_2.add(Calendar.YEAR, 5);

            Calendar calendar_begin_3 = Calendar.getInstance();
            calendar_begin_3.setTime(beginDay);
            calendar_begin_3.add(Calendar.YEAR, 5);
            calendar_begin_3.add(Calendar.DAY_OF_MONTH, -1);

            Calendar calendar_end = Calendar.getInstance();
            calendar_end.setTime(endDay);

            return calendar_begin_1.equals(calendar_end) || calendar_begin_2.equals(calendar_end) || calendar_begin_3.equals(calendar_end);
        } catch (Exception e) {
            logger.error("台湾通行证整年校验日期格式异常,详细信息={}", validate_date);
        }
        return false;
    }

    public static void main(String[] args) {
        System.out.println(DateUtils.checkTwPassEffectiveDate("20211221-20261222"));
    }
}

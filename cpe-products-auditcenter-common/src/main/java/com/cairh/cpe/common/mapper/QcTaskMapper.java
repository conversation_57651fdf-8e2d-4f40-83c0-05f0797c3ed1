package com.cairh.cpe.common.mapper;

import com.cairh.cpe.db.config.MultiDataSource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@MultiDataSource(name = "qc-db")
public interface QcTaskMapper {

    /**
     * 查询QC任务处理人
     * @param operator_nos
     * @param finish_datetime
     * @return
     */
    List<String> selectQcTaskOperators(@Param("operator_nos") List<String> operator_nos,
                                       @Param("finish_datetime") Date finish_datetime);
}

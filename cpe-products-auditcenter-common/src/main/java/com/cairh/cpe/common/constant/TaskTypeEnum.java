package com.cairh.cpe.common.constant;

public enum TaskTypeEnum {

    AUDIT("audit","见证"),

    REVIEW("review","复核"),

    SECONDARY_REVIEW("secondary_review","二次复核");

    private String code;
    private String value;

    private TaskTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getValue(String code) {
        for (TaskTypeEnum c : TaskTypeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setValue(String value) {
        this.value = value;
    }
}

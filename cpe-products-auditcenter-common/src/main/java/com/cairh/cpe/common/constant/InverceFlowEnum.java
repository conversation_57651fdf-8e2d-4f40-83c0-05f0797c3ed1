package com.cairh.cpe.common.constant;

import com.cairh.cpe.common.constant.flow.FlowRecordEnum;

import java.util.HashMap;
import java.util.Map;

public enum InverceFlowEnum {

    REVIEW_BACK("review","audit"),
    SECONDARY_BACK("secondary_review","review");

    public static Map<String, String> BACK_CODE = new HashMap<>();

    static {
        BACK_CODE.put(REVIEW_BACK.now,REVIEW_BACK.before);
        BACK_CODE.put(SECONDARY_BACK.now,SECONDARY_BACK.before);

    }

    private String now;
    private String before;


    private InverceFlowEnum(String now, String before) {
        this.setNow(now);
        this.setBefore(before);
    }

    public String getNow() {
        return now;
    }

    public void setNow(String now) {
        this.now = now;
    }

    public String getBefore() {
        return before;
    }

    public void setBefore(String before) {
        this.before = before;
    }
}

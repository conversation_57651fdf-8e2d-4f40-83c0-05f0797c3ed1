package com.cairh.cpe.common.constant;

import java.util.Objects;

/**
 * 辅助类型枚举
 *
 * <AUTHOR>
 * @since 2025/5/6 14:03
 */
public enum AuxiliaryTypeEnum {
    RESIDENCE_PERMIT("1", "港澳台居民居住证"),
    EMPLOYMENT_AND_BUSINESS_LICENSE("2", "就业证明+营业执照"),
    EMPLOYMENT_AND_UNIFIED_CREDIT_CODE("3", "就业证明+统一社会信用代码证复印件"),
    LODGING_REGISTRATION_FORM("4", "住宿登记证明表原件");

    private final String code;
    private final String desc;

    AuxiliaryTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据编码获取枚举实例
     */
    public static AuxiliaryTypeEnum getAuxiliaryTypeEnum(String code) {
        for (AuxiliaryTypeEnum type : values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

package com.cairh.cpe.common.entity.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 手机实名认证
 */
@Data
public class VerifyMobileReq implements Serializable {

    /**
     * 证件类别
     */
    @NotBlank
    private String id_kind;

    /**
     * 证件号码
     */
    @NotBlank
    private String id_no;

    /**
     * 用户姓名
     */
    @NotBlank
    private String full_name;

    /**
     * 营业部编号 调用方如果没有营业部编号，传0也可以
     */
    private String branch_no;

    /**
     * 实时标志 默认为0。传入1时实时调用底层公安接口，不取本地数据
     */
    private String realtime_flag;

    /**
     * 手机号
     */
    @NotBlank
    private String mobile_tel;

    /**
     * 系统编号
     */
    private String subsys_id;
}

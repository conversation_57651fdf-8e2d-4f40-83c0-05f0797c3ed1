package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 业务流程任务表
 * </p>
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("BusinFlowTask")
public class BusinFlowTask implements Serializable {

    /**
     * 受理编号
     */
    private String request_no;

    /**
     * 流水号
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 任务状态
     */
    private String task_status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deal_datetime;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date finish_datetime;

    /**
     * 任务类型
     * <p>
     *  审核 audit 复核 review
     */
    private String task_type;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;

    /**
     * 操作处理内容
     */
    private String op_content;

    /**
     * 视频类型
     */
    private String video_type;

    /**
     * 指定审核人
     */
    private String allow_auditor;

    /**
     * 不允许审核人
     */
    private String not_allow_auditor;


    /**
     * 归历史标志
     */
    private String tohis_flag;

    /**
     * 归历史日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date tohis_datetime;


    /**
     * 处理次数
     */
    private Integer deal_num;

    /**
     * 备注
     */
    private String remark;


    /**
     * 备注 1 需要推送
     */
    private String push_flag;

    /**
     * 挂起提醒次数
     */
    private Integer suspend_remind_num;


    /**
     * 任务来源
     */
    private String task_source;

    private String match_labels;
    @TableField(exist = false)
    private List<Label> labels;
    private String white_flag;

    /**
     * 单笔提交任务id
     */
    private String task_id;


    /**
     * 重复开户
     */
    private String branch_repeated;


    /**
     * 联系地址重复
     */
    private String address_repeated;

    /**
     * 派单ID
     */
    private String dispatch_task_id;


    /**
     * 外呼标识 0-否 1-是
     */
    private String call_flag;

}

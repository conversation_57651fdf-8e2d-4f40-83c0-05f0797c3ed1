package com.cairh.cpe.common.util;

import com.cairh.cpe.common.constant.DicConstant;
import com.cairh.cpe.context.BizException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 身份证工具类
 */
public class IdentifyUtils {

    private static final Logger logger = LoggerFactory.getLogger(IdentifyUtils.class);

    private static final String[] citys = {"11"/*北京*/, "12"/*天津*/, "13"/*河北*/, "14"/*山西*/, "15"/*内蒙古*/, "21"/*辽宁*/, "22"/*吉林*/, "23"/*黑龙江*/, "31"/*上海*/, "32"/*江苏*/, "33"/*浙江*/, "34"/*安徽*/, "35"/*福建*/, "36"/*江西*/, "37"/*山东*/, "41"/*河南*/, "42"/*湖北*/, "43"/*湖南*/, "44"/*广东*/, "45"/*广西*/, "46"/*海南*/,
            "50"/*重庆*/, "51"/*四川*/, "52"/*贵州*/, "53"/*云南*/, "54"/*西藏*/, "61"/*陕西*/, "62"/*甘肃*/, "63"/*青海*/, "64"/*宁夏*/, "65"/*新疆*/, "83"/*台湾*/, "81"/*香港*/, "82"/*澳门*/, "91"/*国外*/};

    // 每位加权因子
    private static final int[] power = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};

    /**
     * 校验身份证号码是否合法
     *
     * @param idcard 证件号码
     */
    public static boolean verifyIdentity(String idcard) {
        List<String> list = Arrays.asList(citys);
        idcard = idcard.toUpperCase();

        Pattern p = Pattern.compile("(^\\d{17}[\\dX]$)");// 只允许18位身份证
        Matcher m = p.matcher(idcard);
        if (!m.find()) {
            logger.debug("正则表达式验证失败");
            return false;
        }
        // 校验地区
        String city = idcard.substring(0, 2);
        if (!list.contains(city)) {
            logger.debug(String.format("非法地区[%s]", city));
            return false;
        }
        // 校验日期
        String birthday = getIdCardBirthDay(idcard);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            Date date = sdf.parse(birthday);
            if (!birthday.equals(sdf.format(date))) {
                logger.debug(String.format("非法日期[%s]", birthday));
                return false;
            }
        } catch (ParseException ex) {
            logger.debug("非法日期", ex);
            return false;
        }

        // 18位身份证校验位
        if (idcard.length() == 18) {
            char code = idcard.charAt(17);
            if (code != getCheckCode18(idcard.toCharArray())) {
                logger.debug(String.format("校验位错误[%s]", code));
                return false;
            }
        }
        return true;
    }

    /**
     * 取证件号码中的出生日期，日期格式为:yyyyMMdd 15位的出生日期前面补19
     *
     * @param idcard 证件号码
     * @return String
     */
    public static String getIdCardBirthDay(String idcard) {
        if (StringUtils.isBlank(idcard)) {
            throw new IllegalArgumentException("证件号码不能为空");
        }
        String birthDay;
        if (idcard.length() == 15) {
            birthDay = "19" + idcard.substring(6, 12);
        } else if (idcard.length() == 18) {
            birthDay = idcard.substring(6, 14);
        } else {
            throw new BizException("证件号码[" + idcard + "]不是身份证");
        }
        return birthDay;
    }

    public static String getResidenceBirthDay(String idcard) {
        if (idcard == null || "".equals(idcard)) {
            throw new IllegalArgumentException("证件号码不能为空");
        }
        String birthDay = null;
        if (idcard.length() == 15) {
            birthDay = "19" + idcard.substring(6, 12);
        } else if (idcard.length() == 18) {
            birthDay = idcard.substring(6, 14);
        } else {
            throw new BizException("您的【居住证号码】信息有误，请再次确认");
        }
        return birthDay;
    }

    /**
     * 根据身份证,获取客户的性别 判断规则: 15位身份证号码：第7、8位为出生年份（两位数），第9、10位为出生月份，第11、12位代表出生日期，第15位代表性别，奇数为男，偶数为女
     * 18位身份证号码：第7、8、9、10位为出生年份（四位数），第11、第12位为出生月份，第13、14位代表出生日期，第17位代表性别，奇数为男，偶数为女。
     *
     * @param idcard 身份证号码
     * @return 性别{1:男; 0:女; 空:未知}
     */
    public static String getSexFromIdentityno(String idcard) {
        String sex = "";
        if (idcard == null || "".equals(idcard)) {
            // 直接返回空
            return sex;
        }
        char tmpchar = '0';
        if (idcard.length() == 15) {
            tmpchar = idcard.charAt(14);
        } else if (idcard.length() == 18) {
            tmpchar = idcard.charAt(16);
        } else { // 非身份证
            return sex;
        }

        int tmpint = Integer.parseInt(String.valueOf(tmpchar));
        if (tmpint % 2 != 0) {// 奇数为男
            sex = "0";
        } else {// 偶数为女
            sex = "1";
        }

        return sex;
    }

    /**
     * 获取18位身份证校验码
     *
     * @param src 身份证号码
     * @return char
     */
    private static char getCheckCode18(char[] src) {
        // code 为校验码
        char code = '\0';
        char codetmp;
        int i = 0, num = 0;
        if (src.length >= 17) {
            for (i = 2; i < 19; i++) {
                codetmp = src[18 - i];
                if ((codetmp >= 48) && (codetmp <= 57)) {
                    num += ((int) Math.pow(2 * 1.0d, (i - 1) * 1.0d) % 11) * Integer.parseInt(String.valueOf(codetmp));
                } else
                    return code;
            }
            num %= 11;

            switch (num) {
                case 0:
                    code = '1';
                    break;
                case 1:
                    code = '0';
                    break;
                case 2:
                    code = 'X';
                    break;
                default:
                    code = Character.forDigit(12 - num, 10);
            }
        }
        return code;
    }

    /**
     * 验证15位身份证的合法性,该方法验证不准确，最好是将15转为18位后再判断，该类中已提供。
     *
     * @param id_no 身份证号码
     */
    public static boolean verifyIdentity15(String id_no) {
        String idcard18 = convertIdCard18By15bit(id_no);
        if (idcard18 == null) {
            logger.debug("获取身份证失败");
            return false;
        }
        return verifyIdentity(idcard18);
    }

    /**
     * 身份证15位升18位
     */
    public static String convertIdCard18By15bit(String idcard) {
        String idcard17 = null;
        // 非15位身份证
        if (idcard.length() != 15) {
            return null;
        }

        if (isDigital(idcard)) {
            // 获取出生年月日，15位的默认都是19XX年的
            String birthday = "19" + idcard.substring(6, 12);
            Date birthdate = null;
            try {
                birthdate = new SimpleDateFormat("yyyyMMdd").parse(birthday);
            } catch (ParseException e) {
                logger.debug(String.format("解析出生年月日[%s]失败", idcard.substring(6, 12)));
            }
            // 2014-9-3 yejg 修改findbugs检测出来的问题，修改单：M201408310008 ----- begin
            if (birthdate == null) {
                logger.debug("解析出生年月日出错！");
                return null;
            }
            // 2014-9-3 yejg 修改findbugs检测出来的问题，修改单：M201408310008 ----- end
            Calendar cday = Calendar.getInstance();
            cday.setTime(birthdate);
            String year = String.valueOf(cday.get(Calendar.YEAR));

            idcard17 = idcard.substring(0, 6) + year + idcard.substring(8);

            char[] c = idcard17.toCharArray();
            String checkCode = "";

            int[] bit = new int[idcard17.length()];

            // 将字符数组转为整型数组
            bit = converCharToInt(c);
            int sum17 = 0;
            sum17 = getPowerSum(bit);

            // 获取和值与11取模得到余数进行校验码
            checkCode = getCheckCodeBySum(sum17);
            // 获取不到校验位
            if (null == checkCode) {
                return null;
            }

            // 将前17位与第18位校验码拼接
            idcard17 = idcard17 + checkCode;
        } else { // 身份证包含数字
            return null;
        }
        return idcard17;
    }

    /**
     * 身份证18位转15位
     *
     * @param id_no 身份证号码
     */
    public static String convertIdCard15By18bit(String id_no) {
        if (!verifyIdentity(id_no)) {
            logger.debug("身份证号校验失败");
            return null;
        }
        return id_no.substring(0, 6) + id_no.substring(8, 17);
    }

    /**
     * 获取国籍地址
     *
     * @param id_no
     * @return
     */
    public static String queryNationalityByIdNo(String id_no) {
        String nationality = DicConstant.DEFUALT_NATION;
        if (StringUtils.isNotBlank(id_no)) {
            if (StringUtils.startsWith(id_no.trim(), "81") || StringUtils.startsWith(id_no.trim(), "H")) {
                nationality = "HKG";
            } else if (StringUtils.startsWith(id_no.trim(), "82") || StringUtils.startsWith(id_no.trim(), "M")) {
                nationality = "MAC";
            } else if (StringUtils.startsWith(id_no.trim(), "83") || (id_no.trim().length() < 12 && StringUtils.isNumeric(id_no.trim()))) {
                nationality = "CTN";
            } else {
                return nationality;
            }
        }
        return nationality;
    }

    /**
     * 数字验证
     */
    public static boolean isDigital(String str) {
        return str != null && !"".equals(str) && str.matches("^[0-9]*$");
    }

    /**
     * 将字符数组转为整型数组
     */
    public static int[] converCharToInt(char[] c) throws NumberFormatException {
        int[] a = new int[c.length];
        int k = 0;
        for (char temp : c) {
            a[k++] = Integer.parseInt(String.valueOf(temp));
        }
        return a;
    }

    /**
     * 将身份证的每位和对应位的加权因子相乘之后，再得到和值
     */
    public static int getPowerSum(int[] bit) {
        int sum = 0;
        if (power.length != bit.length) {
            return sum;
        }
        for (int i = 0; i < bit.length; i++) {
            for (int j = 0; j < power.length; j++) {
                if (i == j) {
                    sum = sum + bit[i] * power[j];
                }
            }
        }
        return sum;
    }

    /**
     * 将和值与11取模得到余数进行校验码判断
     */
    public static String getCheckCodeBySum(int sum17) {
        String checkCode = null;
        switch (sum17 % 11) {
            case 10:
                checkCode = "2";
                break;
            case 9:
                checkCode = "3";
                break;
            case 8:
                checkCode = "4";
                break;
            case 7:
                checkCode = "5";
                break;
            case 6:
                checkCode = "6";
                break;
            case 5:
                checkCode = "7";
                break;
            case 4:
                checkCode = "8";
                break;
            case 3:
                checkCode = "9";
                break;
            case 2:
                checkCode = "X";
                break;
            case 1:
                checkCode = "0";
                break;
            case 0:
                checkCode = "1";
                break;
        }
        return checkCode;
    }
}

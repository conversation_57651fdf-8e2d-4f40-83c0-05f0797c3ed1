package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("CALLDETAILS")
public class CallDetails {

    /**
     * 主键ID
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 申请编号
     */
    @TableField("request_no")
    private String request_no;


    /**
     * 业务编号
     */
    @TableField("biz_id")
    private String bizId;

    /**
     * 呼叫标识
     */
    @TableField("CALL_ID")
    private String callId;

    /**
     * 呼叫标识
     */
    @TableField("BIZ_TYPE")
    private Integer bizType;

    /**
     * 被叫创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @TableField("CALLEE_CREATED_EPOCH")
    private Date calleeCreatedEpoch;

    /**
     * 被叫振铃时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @TableField("CALLEE_RINGING_EPOCH")
    private Date calleeRingingEpoch;


    /**
     * 被叫接听时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @TableField("CALLEE_ANSWER_EPOCH")
    private Date calleeAnswerEpoch;

    /**
     * 被叫挂断时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @TableField("CALLEE_HANGUP_EPOCH")
    private Date calleeHangupEpoch;

    /**
     * 录音文件路径（ 保存的相对路径）
     */
    @TableField("RECORDING_FILE_NAME")
    private String recordingFileName;

    /**
     * 拨打被叫号码时外显号码/外显号 95 或虚拟短号
     */
    @TableField("OUTBOUND_CALLEE_NUMBER")
    private String outboundCalleeNumber;

    /**
     * 被叫手机号码 / 客户手机号
     */
    @TableField("CALLEE_NUMBER")
    private String calleeNumber;

    /**
     *  员工号（ 字符型）
     */
    @TableField("STAFFID")
    private String staffid;

    /**
     * 客户代码
     */
    @TableField("CUSTOMERID")
    private String customerid;

    /**
     * 员工姓名
     */
    @TableField("STAFF_NAME")
    private String staffname;


    /**
     * 呼叫业务
     */
    @TableField("CALLBIZKEY")
    private String callbizkey;

    /**
     * 呼叫业务
     */
    @TableField("EXTSTR")
    private String extstr;


    /**
     * 主叫号码
     */
    @TableField("CALL_NUMBER")
    private String call_number;

    /**
     * 挂断方 0 经理挂断 1 客户挂断
     */
    @TableField("HANG_UP_SIDE")
    private Integer hangUpSide;


    /**
     * 是否接通 0 未接通 1 接通
     */
    @TableField("IS_ANSWER")
    private Integer isAnswer;


    /**
     * 呼叫时长
     */
    @TableField("CALL_DURATION")
    private Integer callDuration;


    /**
     * 部门信息
     */
    @TableField("DEPT_ID")
    private String deptId;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @TableField("FINISH_DATETIME")
    private Date finish_datetime;


    /**
     * 数据保存时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @TableField("CREATE_DATETIME")
    private Date create_datetime;

    /**
     * 數據更新時間
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @TableField("MODIFY_DATETIME")
    private Date modify_datetime;


    /**
     * 呼叫状态 状态 0-已创建 1-已完成 2-已关闭
     */
    @TableField("CALL_STATUS")
    private Integer call_status;


    /**
     * 用户营业部编号
     */
    @TableField("OPERATOR_BRANCH_NO")
    private String operator_branch_no;

    /**
     * 用户所属分公司
     */
    @TableField("OPERATOR_BRANCH_NAME")
    private String operator_branch_name;

    /**
     * 上级营业部编号
     */
    @TableField("OPERATOR_UP_BRANCH_NO")
    private String operator_up_branch_no;

    /**
     * 上级营业部的名称
     */
    @TableField("OPERATOR_UP_BRANCH_NAME")
    private String operator_up_branch_name;


    /**
     * 是否重点渠道
     */
    @TableField("KEYNOTE_FLAG")
    private String keynote_flag;

    /**
     * 客户开户营业部
     */
    @TableField("CLIENT_BRANCH_NO")
    private String client_branch_no;


    /**
     * 客户开户营业部
     */
    @TableField("BUSIN_TYPE")
    private String busin_type;

    /**
     * 标签
     */
    @TableField("LABEL_OPTIONS")
    private String label_options;
}

package com.cairh.cpe.common.constant;

/**
 * 数据字典
 */
public class DicConstant {

    /*************************** 字典表 start ******************************/
    /*
     * 投资类型: 1001 资金账户 1002 股票账户 1003 基金账户 1004 信托账户 1005 理财账户 1006 其他账户  1101 一码通账户
     */

    /*
     * 投资类型: 3001银行帐户
     */

    public static final String ACCOUNT_TYPE = "1000";
    public static final String ACCOUNT_CAPITAL = "1001";
    public static final String ACCOUNT_STOCK = "1002";
    public static final String ACCOUNT_FUND = "1003";
    public static final String ACCOUNT_TRUST = "1004";
    public static final String ACCOUNT_INVEST = "1005";
    public static final String ACCOUNT_OTHER = "1006";
    public static final String GENERAL_ACCOUNT = "1101"; // 一码通账户
    public static final String ACCOUNT_PENSION = "1102";// 养老金账户

    public static final String ACCOUNT_TYPE_FUND = "1003";
    public static final String ACCOUNT_TYPE_CAPITAL = "1001";
    public static final String ACCOUNT_TYPE_OTHER = "1006";
    public static final String ACCOUNT_TYPE_INVSET = "1005";
    public static final String ACCOUNT_TYPE_TRUST = "1004";

    public static final String ACCOUNT_BANKTYPE = "3001";// 银行帐户
    // 省份
    public static final String PROVINCE_TYPE = "1090";
    // 城市
    public static final String CITY_TYPE = "1091";
    /*************************** 字典表 end ******************************/

    public static final String MESS_LEVEL = "2000";
    public static final String CALLBACK_TIME = "1103";
    public static final String CALLBACK_TYPE = "1102";
    public static final String BUSINESS_TYPE = "1101";
    // public static final String USER_STATUS = "1100";
    public static final String SERVICE_TYPE = "1017";
    public static final String PROFESSION_CODE = "profession_code";// 用户职业
    public static final String YEARMON_CODE = "year_income";// 年收入
    public static final String DEGREE_CODE = "degree_code";// 用户学历
    public static final String GENDER = "client_gender";// 性别
    public static final String IDENTITY_TYPE = "id_kind";
    public static final String INDUSTRY_CODE = "industry_code";// 行业类别
    public static final String USER_ORDER_TIME = "1022";// 预约时间
    public static final String MINZU_CODE = "nation_code";// 民族
    public static final String AML_RISK_LEVEL = "1038";// 反洗钱风险等级 M201503090005 20150422 wmy add
    public static final String ID_KIND = "id_kind";// 证件类型
    public static final String NATIONALITY = "nationality_code"; // 国籍
    public static final String NODE_ID = "11310"; // 打回步骤

    /*************************** 字典 accessLevel ******************************/
    /**
     * 可修改
     **/
    public static final String ACCESSLEVEL_ABLE_EDIT = "1";
    /**
     * 不可修改
     **/
    public static final String ACCESSLEVEL_UNABLE_EDIT = "2";

    public static final String DEFUALT_NATION = "CHN";
    public static final String DEFUALT_IDENTITY_TYPE = "0";

    /**
     * 字典1100 用户状态 4 需要整改
     */
    public static final String NEED_MODIFY_STATUS = "4";

    /**
     * 2500 试卷类型 1:客户风险测评试卷 n:港股通客户知识测评
     **/
    public static final String PAPER_TYPE_RISK = "1";
    public static final String PAPER_TYPE_HKSTOCK = "n";
    public static final String PAPER_TYPE_CREDIT = "l";

    /* 投资类型: 1001 资金账户 1002 股票账户 1003 基金账户 1004 信托账户 1005 理财账户 1006 其他账户  1007 一码通账户 */

    /**
     * 1008 信用账户
     */
    public static final String ACCOUNT_CREDIT = "1008";
    /**
     * 1009 账户权限
     */
    public static final String ACCOUNT_PERMISION = "1009";

    /**
     * 1046学历
     */
    public static final String WT_DEGREE_CODE = "1046";
    /**
     * 1047职业
     */
    public static final String WT_PROFESSION_CODE = "1047";
    /**
     * 1016币种
     */
    public static final String MONEY_TYPE = "1016";

    /**
     * 1021服务时间
     */
    public static final String SERVICE_TIME = "1021";

    /**
     * 社会关系类别
     */
    public static final String RELATIVE_TYPE = "1068";

    // 港股通通用业务查询数据字典
    public static final String BUSIN_OP_TYPE_HK_OPEN = "1514"; // 业务操作类型 沪港通开户 数据字典1082
    // 通用业务办理查询数据字典
    public static final String ACPT_STATUS_5 = "5";// 待办理
    public static final String ACPT_STATUS_4 = "4";// 办理失败

    // dic = "1101
    /*************************** 任务类型 start ******************************/
    public static final String TASK_WT_AUDIT = "10";// 表示网上营业厅审核任务
    public static final String TASK_WT_REAUDIT = "11";// 表示网上营业厅复核任务
    public static final String TASK_WT_CA_DELIVERY = "12";// 表示网上营业厅证书颁发任务
    public static final String TASK_WT_AUDIT_COMMSSION = "15";// 网厅佣金调整审核任务
    /*************************** 任务类型 end ******************************/

    /*************************** 字典表 start ******************************/

    /**
     * 账户类型: 1 上海A股 3 上海B股 2 深圳A股 4 深圳B股 5 转让A 6 转让B
     */
    public static final String ACCOUNT_TYPE_SH_A = "1";
    public static final String ACCOUNT_TYPE_SH_B = "3";
    public static final String ACCOUNT_TYPE_SZ_A = "2";
    public static final String ACCOUNT_TYPE_SZ_B = "4";
    public static final String ACCOUNT_TYPE_ZH_A = "5";
    public static final String ACCOUNT_TYPE_ZH_B = "6";

    // 恒生接口账户类别
    public static final String ACCOUNT_TYPE_HS_SH_A = "10";
    public static final String ACCOUNT_TYPE_HS_SH_B = "11";
    public static final String ACCOUNT_TYPE_HS_SZ_A = "20";
    public static final String ACCOUNT_TYPE_HS_SZ_B = "21";

    public static final String ACCOUNT_CAPITAL_TYPE1 = "1001";


    public static final String VIDEO_WORK_TIME = "1021";// 视频工作时间


    public static final String BANK_DEPOSITY_WAY = "1300";
    public static final String AGREEMENT_TYPE = "1501";
    public static final String CA_KIND = "1502";
    public static final String ACCEPT_WAY = "1503";
    public static final String EFFECT_TERM = "1504";
    public static final String RISK_LEVEL = "1505";

    public static final String SMS_TYPE = "1201";// 短信类型

    public static final String USER_APP_ID = "1804"; // 接入来源
    public static final String VIDEO_TYPE = "1200"; // 视频类型
    public static final String BANK_BIZ_TYPE = "1805"; // 存管类型
    public static final String PAPER_TYPE = "1806"; // 风险评测类型
    public static final String DIC_OPEN_TYPE = "1601";
    public static final String ORG_TYPE = "1010";

    /**
     * 2500 试卷类型 1:客户风险测评试卷 n:港股通客户知识测评";x:股票质押征信问卷（klm一致试题）
     **/
    public static final String PAPER_TYPE_PLEDGE = "x";
    /*************************** 字典 accessLevel ******************************/
    public static final String DEFUALT_NATION_CIF = "156";
    public static final String NATION_HAN = "1";    // 汉族
    /**
     * 市场类型
     */
    public static final String EXCHANGE_KIND_SH_A = "10"; // 市场类别-上海A
    public static final String EXCHANGE_KIND_SH_B = "D0"; // 市场类别-上海B
    public static final String EXCHANGE_KIND_SH_OFUND = "11"; // 市场类别-上海场内基金
    public static final String EXCHANGE_KIND_SH_CRDT = "17"; // 市场类别-上海信用股东账户
    public static final String EXCHANGE_KIND_HK = "G0"; // 市场类别-沪港通
    public static final String EXCHANGE_KIND_SZ_A = "20"; // 市场类别-深圳A
    public static final String EXCHANGE_KIND_SZ_B = "H0"; // 市场类别-深圳B
    public static final String EXCHANGE_KIND_SZ_OFUND = "21"; // 市场类别-深圳场内基金
    public static final String EXCHANGE_KIND_SZ_CRDT = "27"; // 市场类别-深圳信用股东账户
    public static final String EXCHANGE_KIND_TZ_A = "90"; // 市场类别-特转A
    public static final String EXCHANGE_KIND_TZ_B = "A0"; // 市场类别-特转B
    public static final String ACCOUNT_TYPE_TTL = "1";// 天添利
    public static final String ACCOUNT_TYPE_SH_TA = "11"; // 沪TA
    public static final String ACCOUNT_TYPE_SZ_TA = "21"; // 深TA
    public static final String ACCOUNT_TYPE_FUND_SH = "1";
    public static final String ACCOUNT_TYPE_FUND_SZ = "2";
    public static final String ACCOUNT_FUND_TYPE_2 = "2"; // 开放式基金
    public static final String LOCATION_INFO = "1802";
    public static final String WT_USER_BIZ_CONIG = "6000";
    public static final String GENDER_F = "F";// 性别
    public static final String GENDER_M = "M";// 性别
    public static final String OPEN_TYPE = "1601"; // 开户类型
    // 开户类型
    public static final String OPEN_TYPE_1 = "1";// 开户
    public static final String OPEN_TYPE_3 = "3";// 转户
    public static final String OPEN_TYPE_5 = "5";// 简易开户
    public static final String OPEN_TYPE_6 = "6";// 见证开户（目前用于开户预约功能）
    public static final String OPEN_TYPE_7 = "7";// PAD见证开户
    public static final String OPEN_TYPE_9 = "9";// 期货
    public static final String OPEN_TYPE_11 = "11";// 港股通开户
    public static final String OPEN_TYPE_21 = "21";// 微信开户
    public static final String OPEN_TYPE_31 = "31";// 倍赚宝开户
    public static final String OPEN_TYPE_32 = "32";// 存量用户开户
    /**
     * 上海A股
     */
    public static final String ACCOUNT_STOCK_SH = "1";// 上海A股

    // 股东账号交易类别
    public static final String ACCOUNT_STOCK_SZ = "2";// 深圳A股
    public static final String ACCOUNT_FUND_HS = "99";// 恒生柜台沪市基金
    public static final String ACCOUNT_FUND_SS = "98";// 恒生柜台深市基金
    public static final String ACCOUNT_FUND_CF_HS = "499";// 恒生柜台沪市基金
    public static final String ACCOUNT_FUND_CF_SS = "498";// 恒生柜台深市基金
    public static final String ACCOUNT_FUND_OTC = "OTC";// OTC
    public static final String ACCESS_WAY = "1803";   // 开户端口
    /**
     * 1202 企业类型
     * 0 国营企业
     * 1 合资企业
     * 2 外资企业
     * 3 私人企业
     * 4 民营企业
     * 5 其它
     * 6 普通合伙企业
     * 7 特殊普通合伙企业
     * 8 有限合伙企业
     * 9 非法人非合伙制创业投资企业
     */
    public static final String COMPANY_KIND = "1202", COMPANY_KIND_0 = "0", COMPANY_KIND_1 = "1", COMPANY_KIND_2 = "2", COMPANY_KIND_3 = "3", COMPANY_KIND_4 = "4", COMPANY_KIND_5 = "5",
            COMPANY_KIND_6 = "6", COMPANY_KIND_7 = "7", COMPANY_KIND_8 = "8", COMPANY_KIND_9 = "9";
    /**
     * 1203 企业资本属性字典
     * 1 内资
     * 2 合资（合作）
     * 3 外资
     */
    public static final String REGISTER_FUND_PROP = "1203", REGISTER_FUND_PROP_1 = "1", REGISTER_FUND_PROP_2 = "2", REGISTER_FUND_PROP_3 = "3";
    /**
     * 11001 中登国有属性字典（请注意恒生柜台1国有2非国有）
     * 1:国务院国资委管辖
     * 2:地方国资委管辖
     * 3:其他国有企业
     * 4:非国有
     */

    public static final String STATE_OWNED_PROP = "11001", STATE_OWNED_PROP_1 = "1", STATE_OWNED_PROP_2 = "2", STATE_OWNED_PROP_3 = "3", STATE_OWNED_PROP_4 = "4";
    /**
     * 10200:用户状态
     * 0:正常
     * 1:冻结
     * 2:挂失
     * 3:销户
     * 4:未确定
     * F:系统锁定
     */
    public static final String USER_STATUS = "10200", USER_STATUS_0 = "0", USER_STATUS_1 = "1", USER_STATUS_2 = "2", USER_STATUS__3 = "3", USER_STATUS__4 = "4", USER_STATUS_F = "F";
    /**
     * 11200:渠道状态
     * 0:正常
     * 1:无效
     * 2:过期
     */
    public static final String CHANNEL_STATUS = "11200", CHANNEL_STATUS_0 = "0", CHANNEL_STATUS_1 = "1", CHANNEL_STATUS_2 = "2";
    /**
     * 1209 客户来源
     * 0:自然增长
     * 1:渠道网点
     * 2:外部推介会
     * 3:内部推荐
     */
    public static final String DEVELOP_SOURCE = "1209", DEVELOP_SOURCE_0 = "0", DEVELOP_SOURCE_1 = "1", DEVELOP_SOURCE_2 = "2", DEVELOP_SOURCE_3 = "3";
    /**
     * 1205 上市属性
     * 1:上市
     * 2:非上市
     */
    public static final String IPO_PROP = "1205", IPO_PROP_1 = "1", IPO_PROP_2 = "2";
    /**
     * 1210 法人类别
     * 01:内资企业法人
     * 02:外资及港、澳、台资企业法人
     * 03:金融机构法人
     * 04:事业法人
     * 05:社团法人
     * 06:机关法人
     * 07:境外法人
     * 08:合伙企业
     * 09:非法人非合伙制创业投资企业
     * 99:其他
     */
    public static final String INSTREPR_TYPE = "1210", INSTREPR_TYPE_01 = "01", INSTREPR_TYPE_02 = "02", INSTREPR_TYPE_03 = "03", INSTREPR_TYPE_04 = "04", INSTREPR_TYPE_05 = "05", INSTREPR_TYPE_06 = "06", INSTREPR_TYPE_07 = "07", INSTREPR_TYPE_08 = "08", INSTREPR_TYPE_09 = "09", INSTREPR_TYPE_99 = "99";
    /**
     * 2303 寄送方式
     * 1:不寄送
     * 2:月寄送
     * 3:季寄送
     * 4:半年寄送
     * 5:年寄送
     */
    public static final String POST_TYPE = "2303", POST_TYPE_1 = "1", POST_TYPE_2 = "2", POST_TYPE_3 = "3",
            POST_TYPE_4 = "4", POST_TYPE_5 = "5";

    /**
     * 972 中登机构类别（恒生柜台的）
     * 01:企业法人
     * 02:机关法人
     * 03:事业法人
     * 04:社团法人
     * 05:工会法人
     * 09:其他非金融机构
     * 10:证券公司
     * 11:银行
     * 12:信托投资公司
     * 13:基金管理公司
     * 14:保险公司
     * 19:其他金融机构
     * 21:普通合伙企业
     */
    // public static final String CSDC_ORGAN_TYPE = "972", CSDC_ORGAN_TYPE_01 = "01", CSDC_ORGAN_TYPE_02 = "02",
    // CSDC_ORGAN_TYPE_03 = "03",
    // CSDC_ORGAN_TYPE_04 = "04", CSDC_ORGAN_TYPE_05 = "05", CSDC_ORGAN_TYPE_09 = "09", CSDC_ORGAN_TYPE_10 = "10",
    // CSDC_ORGAN_TYPE_11 = "11",
    // CSDC_ORGAN_TYPE_12 = "12", CSDC_ORGAN_TYPE_13 = "13", CSDC_ORGAN_TYPE_14 = "14", CSDC_ORGAN_TYPE_19 = "19",
    // CSDC_ORGAN_TYPE_21 = "21";
    /**
     * 1056 成本中心
     * 0:内部
     */
    public static final String COST_CENTER = "1056", COST_CENTER_0 = "0";
    /**
     * 1751 联络方式
     * 0:不愿意被打扰
     * 1:家庭电话
     * 2:单位电话
     * 3:手机号码
     * 4:传真号码
     * 5:EMAIL
     * 6:信件
     */
    public static final String CONTACK_WAY = "1751", CONTACK_WAY_0 = "0", CONTACK_WAY_1 = "1",
            CONTACK_WAY_2 = "2", CONTACK_WAY_3 = "3", CONTACK_WAY_4 = "4", CONTACK_WAY_5 = "5", CONTACK_WAY_6 = "6";
    /**
     * 1752 联络频率
     * 0:可随意联络
     * 1:不超过1月3次
     * 2:不超过一季度一次
     * 3:不超过一年一次
     */
    public static final String CONTACK_FREQUENCY = "1752", CONTACK_FREQUENCY_0 = "0", CONTACK_FREQUENCY_1 = "1",
            CONTACK_FREQUENCY_2 = "2", CONTACK_FREQUENCY_3 = "3";
    /**
     * 11002 中登机构类别
     * 01 : 企业法人
     * 02 : 机关法人
     * 03 : 事业法人
     * 04 : 社团法人
     * 05 : 工会法人
     * 09 : 其他非金融机构法人
     * 10 : 证券公司
     * 11 : 银行
     * 12 : 信托投资公司
     * 13 : 基金管理公司
     * 14 : 保险公司
     * 19 : 其他金融机构法人
     * 21 : 普通合伙企业
     * 22 : 特殊普通合伙企业
     * 23 : 有限合伙企业
     * 24 : 非法人非合伙制创投企业
     * 31 : 境外一般机构
     * 32 : 境外代理人
     * 33 : 境外证券公司
     * 34 : 境外基金公司
     * 41 : 破产管理人
     * 51 : 中国金融期货交易所
     * 52 : 境外结算机构
     * 99 : 其他
     */
    public static final String CSDC_ORGAN_TYPE = "11002", CSDC_ORGAN_TYPE_01 = "01", CSDC_ORGAN_TYPE_02 = "02", CSDC_ORGAN_TYPE_03 = "03", CSDC_ORGAN_TYPE_04 = "04", CSDC_ORGAN_TYPE_05 = "05",
            CSDC_ORGAN_TYPE_09 = "09", CSDC_ORGAN_TYPE_10 = "10", CSDC_ORGAN_TYPE_11 = "11", CSDC_ORGAN_TYPE_12 = "12", CSDC_ORGAN_TYPE_13 = "13", CSDC_ORGAN_TYPE_14 = "14",
            CSDC_ORGAN_TYPE_19 = "19", CSDC_ORGAN_TYPE_21 = "21", CSDC_ORGAN_TYPE_22 = "22", CSDC_ORGAN_TYPE_23 = "23", CSDC_ORGAN_TYPE_24 = "24", CSDC_ORGAN_TYPE_31 = "31",
            CSDC_ORGAN_TYPE_32 = "32", CSDC_ORGAN_TYPE_33 = "33", CSDC_ORGAN_TYPE_34 = "34", CSDC_ORGAN_TYPE_41 = "41", CSDC_ORGAN_TYPE_51 = "51", CSDC_ORGAN_TYPE_52 = "52",
            CSDC_ORGAN_TYPE_99 = "99";
    /**
     * 异地开户理由 choose_branch_reason
     */
    public static final String CHOOSE_BRANCH_REASON = "choose_branch_reason";
    /**
     * 选择职业理由 choose_profession_reason
     */
    public static final String CHOOSE_PROFESSION_REASON = "choose_profession_reason";
    /**
     * 年收入 year_income
     */
    public static final String DIC_1020 = "year_income";
    /**
     * 挂起理由字典
     */
    public static final String SUSPEND_REASON = "suspend_reason";

    //TODO 补充字典常量
    /**
     * 删除智能审核原因选项
     */
    public static final String AI_NOPASS_REASON = "ai_nopass_reason";
    public static final String NATIONALITY_CODE = "nationality_code";
    /**
     * 原因分组
     */
    public static final String REJECT_REASON_GROUP = "reject_reason_group";
    /**
     * dishonest_record 失信记录
     */
    public static final String DISHONEST_RECORD = "dishonest_record";
    /**
     * 任务状态 失信记录
     */
    public static final String DICT_TASK_STATUS = "task_status";
    /**
     * 任务来源
     */
    public static final String TASK_SOURCE = "task_source";
    public static final String TASK_SOURCE_1 = "1"; //系统派发
    public static final String TASK_SOURCE_2 = "2"; //手动主动认领
    public static final String TASK_SOURCE_3 = "3"; //转交任务
    public static final String TASK_SOURCE_4 = "4"; //转派任务
    /**
     * 修改项目
     */
    public static final String MODIFY_ITEM = "modify_item";
    /**
     * 修改原因
     */
    public static final String MODIFY_REASON = "modify_cert_reason";
    /**
     * initial_investment_amount 初始投资额度
     */
    public static final String INITIAL_INVESTMENT_AMOUNT = "initial_investment_amount";

    public static final String AUDIT_STATUS_DICT = "audit_status";
    public static final String REVIEW_STATUS_DICT = "review_status";
    public static final String SECONDARY_REVIEW_STATUS_DICT = "secondary_review_status";
    public static final String PROCESS_STATUS_DICT = "process_status";
    public static final String BUSIN_TYPE_DICT = "busin_type";
    public static final String CLIENT_GENDER_DICT = "client_gender";
    public static final String APP_ID_DICT = "app_id";
    public static final String TASK_STATUS_DICT = "task_status";
    public static final String VIDEO_TYPE_DICT = "video_type";
    public static final String PROFESSION_CODE_DICT = "profession_code";
    public static final String ID_KIND_DICT = "id_kind";
    public static final String TASK_SOURCE_DICT = "task_source";
    public static final String CLIENT_CATEGORY_DICT = "client_category";


    public static class AGREEMENT_TYPE {
        public static final String CG = "0";
        public static final String KH = "1";
        public static final String ZS = "1";
        public static final String OTHER = "X";
    }
}

package com.cairh.cpe.common.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.entity.BusinFlowRecord;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.request.BusinFlowRecordForm;
import com.cairh.cpe.common.entity.request.BusinflowRecordReq;
import com.cairh.cpe.common.entity.response.BusinFlowRecordResp;

import java.util.List;
import java.util.Map;

public interface IBusinFlowRecordService extends IService<BusinFlowRecord> {


    /**
     * 保存申请步骤流水
     */
    void saveBusinFlowRecord(String request_no, Map<String, Object> params);
    /**
     * 保存用户申请步骤流水2
     */
    void saveBusinFlowRecord(BusinFlowRequest businFlowRequest, Map<String, Object> params);


    List<BusinFlowRecordResp> qryUserApplyRecord(BusinFlowRecordForm businFlowRecordForm);

    Page<BusinFlowRecordResp> selectRecordListByPage(Page<BusinFlowRecordResp> page, BusinflowRecordReq businflowRecordReq);
}

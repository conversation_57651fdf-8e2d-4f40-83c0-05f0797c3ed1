package com.cairh.cpe.common.entity.support;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UserBaseInfoResp {

    /**
     * 经常居住地址
     */
    private String address;

    /**
     * 工作单位
     */
    private String work_unit;

    /**
     * 异地开户理由
     */
    private String choose_branch_reason;

    /**
     * 选择职业理由
     */
    private String choose_profession_reason;

    /**
     * 失信记录
     */
    private String dishonest_record;

    /**
     * 失信记录备注信息
     */
    private String dishonest_record_remark;

    /**
     * 失信记录文本信息
     */
    private String profession_other;

    /**
     * 职业
     */
    private String profession_code;

    /**
     * 失信记录文件链接
     */
    private String dishonest_content;

    /**
     * 英文姓名
     */
    private String english_name;

    /**
     * 证件证件号
     */
    private String prev_id_number;

    /**
     * 初始投资额度
     */
    private String initial_investment_amount;

    /**
     * 出生日期
     */
    private String birthday;


}

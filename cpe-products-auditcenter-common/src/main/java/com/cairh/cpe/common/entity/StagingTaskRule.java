package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 暂存任务规则表
 *
 * <AUTHOR>
 * @since 2025/3/11 16:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("STAGINGTASKRULE")
public class StagingTaskRule implements Serializable {

    /**
     * 主键ID
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String rule_name;


    /**
     * 规则类型（1-暂存任务）
     */
    @TableField("rule_type")
    private String rule_type;

    /**
     * 规则开始时间
     */
    @TableField("rule_datetime_start")
    private Date rule_datetime_start;


    /**
     * 规则结束时间
     */
    @TableField("rule_datetime_end")
    private Date rule_datetime_end;

    /**
     * 可选规则
     */
    @TableField("expression")
    private String expression;

    /**
     * 状态（1-可用，0-禁用）
     */
    @TableField("status")
    private String status;


    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 顺序，优先级
     */
    @TableField("order_no")
    private Integer order_no;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 创建时间
     */
    @TableField("create_datetime")
    private Date create_datetime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String update_by;

    /**
     * 更新时间
     */
    @TableField("update_datetime")
    private Date update_datetime;

}

package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.entity.RuleConfiguration;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/11 16:18
 */
public interface IRuleConfigurationService extends IService<RuleConfiguration> {


    /**
     * 查询所有规则
     *
     * @return 规则列表
     */
    List<RuleConfiguration> findAllRules();


    /**
     * 清理缓存
     */
    void clearCache();

}

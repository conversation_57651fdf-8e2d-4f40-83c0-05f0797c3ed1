package com.cairh.cpe.common.constant;

public class TextConstant {

    public static final String ERROR_NETWORK_CONNECT_FAIL = "通信链路异常";
    public static final String ERROR_SYSTEM_CONFIG = "系统配置有误";
    public static final String LOGIN_WRONG = "用户名或密码不正确";
    public static final String USER_NOT_EXIST_ERROR = "用户信息不存在";

    /**
     * 三方存管银行指定
     */
    public static final String DEPOSITY_BANK_SELECT = "请指定三方存管银行";
    /**
     * 三方存管银行账户填写
     */
    public static final String DEPOSITY_BANK_ACCOUNT = "请填写三方存管银行账户";

    /**
     * 密码设置不能为空
     */
    public static final String SETTING_CASH_NOEMPTY = "资金密码为6位数字";
    public static final String SETTING_TRANS_NOEMPTY = "交易密码为6位数字";
    public static final String SETTING_CONNECT_NOEMPTY = "通信密码为6位数字";

    public static final String SETTING_CONNECT_NOTEQUAL = "两次通信密码不一致";
    public static final String SETTING_CASH_NOTEQUAL = "两次资金密码不一致";
    public static final String SETTING_TRANS_NOTEQUAL = "两次交易密码不一致";
    public static final String SETTING_PSW_FAILED = "密码保存失败";
    public static final String SIMPLE_PASSWORD_CHECK_FAILED = "弱密码检查失败";
    /**
     * 基本资料校验
     *
     * @auther zhanggj
     */
    public static final String SUCCESS = "success";            // 校验通过返回值
    public static final String VALIDATE_INVALID = "含有非法字符";
    // 校验姓名
    public static final String VALIDATE_NAME_NULL = "姓名不能为空";
    public static final String VALIDATE_NAME_ERROR1 = "姓名不符合规范";
    // 校验手机
    public static final String VALIDATE_MOBILE_NULL = "手机号码不能为空";
    public static final String VALIDATE_MOBILE_EXIST = "手机号码已被使用";
    public static final String VALIDATE_MOBILE_ERROR = "手机号码填写错误";

    // 校验身份证有效期限
    public static final String VALIDATE_CIDABLE_NULL = "有效期限不能为空";
    public static final String VALIDATE_CIDABLE2_NULL = "有效期限和长期有效必须填一个";
    public static final String VALIDATE_CIDABLE_ERROR = "有效期限填写错误";
    // 校验联系地址：省份、城市、地区
    public static final String VALIDATE_PROVI_NULL = "请选择省份";
    public static final String VALIDATE_CITY_NULL = "请选择城市";
    public static final String VALIDATE_AREA_NULL = "请选择地区";
    // 校验联系地址详细信息
    public static final String VALIDATE_ADDRESS_NULL = "联系地址不能为空";
    public static final String VALIDATE_ADDRESS_ERROR = "联系地址填写错误";
    public static final String VALIDATE_ADDRESS_ERROR2 = "请填写详细的联系地址,格式:XX省XX市+详细地址";
    public static final String VALIDATE_ADDRESS_LENGTH_ERROR = "address长度需在8~40之间";
    // 校验邮政编码
    public static final String VALIDATE_POSTCODE_NULL = "邮政编码不能为空";
    public static final String VALIDATE_POSTCODE_ERROR = "邮政编码填写错误";
    // 校验固定电话
    public static final String VALIDATE_TEL_NULL = "固定电话不能为空";
    public static final String VALIDATE_TEL_ERROR = "固定电话填写错误";
    public static final String VALIDATE_TEL_EXIST = "此号码已在柜台开过户";
    // 校验电子邮件
    public static final String VALIDATE_EMAIL_NULL = "电子邮件不能为空";
    public static final String VALIDATE_EMAIL_ERROR = "电子邮件填写错误";
    // 校验职业
    public static final String VALIDATE_JOB_NULL = "职业不能为空";
    public static final String VALIDATE_JOB_ERROR = "职业选择错误";
    // 校验年收入
    public static final String VALIDATE_YEAR_NULL = "年收入不能为空";
    public static final String VALIDATE_YEAR_ERROR = "年收入选择错误";
    // 校验学历
    public static final String VALIDATE_EDU_NULL = "学历不能为空";
    public static final String VALIDATE_EDU_ERROR = "学历选择错误";
    // 校验民族
    public static final String VALIDATE_MINZU_NULL = "民族不能为空";
    public static final String VALIDATE_MINZU_ERROR = "民族选择错误";
    // 行业检验
    public static final String VALIDATE_INDUSTRY_NULL = "行业不能为空";
    public static final String VALIDATE_INDUSTRY_ERROR = "行业选择错误";
    // 反洗钱风险等级检验 M201503090005 20150422 wmy add反洗钱风险等级aml_risk_level begin
    public static final String VALIDATE_AMLRISKLEVEL_NULL = "反洗钱风险等级不能为空";
    public static final String VALIDATE_AMLRISKLEVEL_ERROR = "反洗钱风险等级选择错误";
    // 反洗钱风险等级检验 M201503090005 20150422 wmy add反洗钱风险等级aml_risk_level end
    // 营业部选择出错
    public static final String VALIDATE_ORG_NULL = "请选择开户营业部";
    public static final String VALIDATE_ORG_ERROR = "开户营业部选择错误";
    public static final String VALIDATE_ORG_NE = "请先添加上级营业部";
    // 营业部选择
    public static final String NO_RECOMMEND_ORG = "没有推荐的营业部";
    public static final String VALIDATE_SUCCESS = "验证通过";
    public static final String SAVE_CID_INFO_ERROR = "保存身份信息有异常";
    public static final String SAVE_COM_INFO_ERROR = "保存常用信息有异常";
    public static final String SAVE_ORG_INFO_ERROR = "保存开户营业部信息有异常";

    // 身份证图像识别
    public static final String IDENTIFY_IMAGE_TYPE_ERROR = "上传档案图片类型错误";
    public static final String IDENTIFY_NO_IMAGE_ERROR = "无法获取上传图片内容";
    public static final String IDENTIFY_IMAGE_PARSE_ERROR = "客户公安信息[图像解码出错]";
    public static final String UPLOAD_PHOTO_EXCEPTION = "上传身份证免冠照异常";

    /***************************** 注册相关错误提示 ************************************/
    public static final String MPHONE_EMPTY = "手机号码不能为空";
    public static final String MPHONE_INVALID = "手机号码格式错误";
    public static final String MPHONE_USED_YET = "此手机号码已经注册，请点击登录按钮";
    public static final String MPHONE_NOT_LOGIN = "手机号码未被注册";
    public static final String GEN_VALID_CODE_WRONG = "生成手机验证码出错，请重新获取";
    public static final String MESS_CONTENT_EMPTY = "短信内容为空";
    public static final String VALIDATE_CODE_EMPTY = "验证码为空";
    public static final String VALIDATE_CODE_WRONG1 = "验证码格式错误";
    public static final String VALIDATE_CODE_WRONG2 = "验证码错误";
    public static final String VALIDATE_CODE_SEND_OVER_LIMITED = "验证码发送次数已超过每天最大发送次数";
    public static final String VALIDATE_CODE_SMS_CONTENT = "尊敬的客户，欢迎使用**证券网上开户，您的短信验证码是****，请确保由您本人完成开户操作。如需帮助请拨打客服热线400********。";

    /**************************** 审核相关错误提示 ************************************/
    public static final String AUDIT_USER_NOT_EXIST = "被修改用户不存在";
    public static final String AUDIT_USER_NOT_LOGIN = "用户未登录";
    public static final String AUDIT_NOT_NEED_MODIFY = "您没有待修改项";
    public static final String AUDIT_FIELD_NOT_NEED_MODIFY = "该项不需要修改";
    public static final String AUDIT_FIELD_NOT_MODIFY = "您还有待修改项";
    public static final String AUDIT_FIELD_EMPTY = "修改字段为空";
    public static final String AUDIT_CONTENT_EMPTY = "修改内容为空";
    public static final String VALIDATE_FIELD_ERROR = "该字段不需验证";
    public static final String AUDIT_DICT_EMPTY = "字典表中不存在此种类型的数据";
    public static final String AUDIT_FUND_COMPANY_EMPTY = "基金公司列表为空";
    public static final String AUDIT_STOCK_ACCOUNT_EMPTY = "不存在该类型的股票账户";
    public static final String AUDIT_FUND_ACCOUNT_EMPTY = "不存在该类型的基金账户";
    public static final String AUDIT_FUND_COMPANY_EMPTY2 = "请选择基金公司";
    public static final String AUDIT_FUND_COMPANY_EMPTY3 = "不存在该基金公司";
    public static final String AUDIT_SERVICE_TYPE_WRONG1 = "委托方式不能为空";
    public static final String AUDIT_SERVICE_TYPE_WRONG2 = "委托方式不存在";
    public static final String AUDIT_SERVICE_TYPE_WRONG3 = "委托方式不合法";
    public static final String AUDIT_DEPOSITY_BANK_WRONG1 = "存管银行为空";
    public static final String AUDIT_DEPOSITY_BANK_WRONG2 = "存管银行或银行卡号为空";
    public static final String AUDIT_DEPOSITY_BANK_WRONG3 = "存管银行或银行卡号为空不存在";
    public static final String AUDIT_DEPOSITY_BANK_WRONG4 = "银行卡号格式错误";
    public static final String AUDIT_DEPOSITY_BANK_WRONG5 = "数据库中没有银行列表";
    public static final String AUDIT_DEPOSITY_BANK_WRONG6 = "没有该银行的三方存管协议";
    public static final String AUDIT_DEPOSITY_BANK_WRONG7 = "三方存管签名为空";
    public static final String AUDIT_FUND_RESOUCE_WRONG1 = "不存在该权限";
    public static final String AUDIT_ID_PIC_WRONG1 = "图片地址为空";
    public static final String AUDIT_ORG_WRONG2 = "不存在该营业部";
    public static final String AUDIT_ORG_WRONG1 = "数据库中没有营业部列表";
    public static final String AUDIT_IMG_URL_WRONG1 = "图片地址为空";
    public static final String AUDIT_IMG_URL_WRONG2 = "图片地址格式不正确";

    public static final String USER_NOT_LOGIN = "用户未登录";
    /**
     *
     */
    public static final String ILLEGAL_REQUEST = "非法请求";
    public static final String REQUEST_FAIL = "系统繁忙,请稍后再试!";
    public static final String DEPOSITY_BANK = "请选择存管银行";
    public static final String DEPOSITY_ACCOUNT_NOEMPTY = "存管银行账户不能为空";
    public static final String SIGN_INVALID = "无效数字签名";
    public static final String AGREEMENT_SIGN = "您已经签署相关协议";
    public static final String PWD_VALIDATE_FAIL = "密码过于简单,请重新设置!";
    public static final String SIGN_REVOCATION = "您的数字证书已被注销,请重新申请！";

    /********************* 登录错误相关提示 **************************/
    public final static String HTTP_POST_WRONG1 = "返回结果为空";
    public final static String HTTP_POST_WRONG2 = "解析返回数据出错";

    // 注册错误
    public static final String REGISTER_WRONG1 = "营业部不存在";
    public static final String REGISTER_WRONG2 = "发送验证码失败，请重试";
    public static final String REGISTER_WRONG3 = "短信验证码验证失败";
    // 上传图片错误
    public static final String TXT_UPLOAD_FAILED = "上传文件失败";
    public static final String ID_UPLOAD_WRONG1 = "您还未上传照片";
    public static final String ID_UPLOAD_WRONG2 = "上传照片失败，请重新上传";
    public static final String ID_UPLOAD_WRONG3 = "身份证正面照识别出错，请重新上传";
    public static final String ID_UPLOAD_WRONG4 = "身份证反面照识别出错，请重新上传";
    public static final String PAPERS_UPLOAD_WRONG3 = "证件正面照识别出错，请重新上传";
    public static final String PAPERS_UPLOAD_WRONG4 = "证件反面照识别出错，请重新上传";
    public static final String ID_UPLOAD_WRONG5 = "您上传的图片不符合要求，请重新上传";
    public static final String ID_UPLOAD_WRONG6 = "您尚未登录或已超时，请重新<a href=\"/\">登录</a>";
    public static final String ID_UPLOAD_WRONG7 = "您的身份证信息未更新，请重新上传";
    public static final String ID_UPLOAD_WRONG8 = "只支持类型[.jpg,.png,.gif,.bmp,.jpeg]";
    public static final String ID_UPLOAD_WRONG9 = "图片base64解码失败";
    public static final String ID_UPLOAD_WRONG10 = "判断文件大小时异常";

    // 基本资料错误
    public static final String BASIC_INFO_WRONG1 = "提交资料出错，请稍后重试";
    public static final String BASIC_INFO_WRONG2 = "您尚未登录或已超时，请重新<a href=\"/\">登录</a>";
    public static final String BASIC_INFO_WRONG3 = "该客户已存在，请重新输入";
    public static final String BASIC_INFO_WRONG4 = "身份证件信息不存在";
    public static final String BASIC_INFO_WRONG5 = "修改后的身份证与原身份证差别较大";
    // 视频验证出错
    public static final String VIDEO_VERIFY_WRONG1 = "视频验证失败，请重试";
    public static final String VIDEO_VERIFY_WRONG2 = "您已在待见证队列中,不能重复申请";
    public static final String VIDEO_VERIFY_WRONG3 = "当前待见证队列已满，请稍后再试";
    public static final String VIDEO_VERIFY_WRONG4 = "视频验证未通过";
    public static final String VIDEO_VERIFY_WRONG5 = "您尚未登录或已超时，请重新<a href=\"/\">登录</a>";
    // 证书申请出错
    public static final String CA_WRONG1 = "无法获取证书内容，请重试";
    public static final String CA_WRONG2 = "您尚未登录或已超时，请重新<a href=\"/\">登录</a>";
    // 选择账户出错
    public static final String ACCOUNT_TYPE_WRONG1 = "您尚未登录或已超时，请重新<a href=\"/\">登录</a>";
    public static final String ACCOUNT_TYPE_WRONG2 = "请选择您要开设的账户";
    public static final String ACCOUNT_TYPE_WRONG3 = "您选择的账户不存在";
    public static final String ACCOUNT_TYPE_WRONG4 = "开户协议签名出错，请确认是否正确安装数字证书";
    // 密码设置出错
    public static final String PWD_SET_WRONG1 = "您尚未登录或已超时，请重新<a href=\"/\">登录</a>";
    public static final String PWD_SET_WRONG2 = "密码不能为空";
    public static final String PWD_SET_WRONG3 = "密码类型错误";
    public static final String PWD_SET_WRONG4 = "密码格式错误";
    // 绑定三方存管出错
    public static final String THIRD_DEPOSITY_WRONG1 = "您尚未登录或已超时，请重新<a href=\"/\">登录</a>";
    public static final String THIRD_DEPOSITY_WRONG2 = "银行卡号格式错误";
    public static final String THIRD_DEPOSITY_WRONG3 = "银行卡密码格式错误";
    public static final String THIRD_DEPOSITY_WRONG4 = "银行卡类型错误";
    public static final String THIRD_DEPOSITY_WRONG5 = "不存在该银行";
    public static final String THIRD_DEPOSITY_WRONG6 = "绑定三方存管出错";
    public static final String THIRD_DEPOSITY_WRONG7 = "不存在该银行三方存管协议";
    public static final String THIRD_DEPOSITY_WRONG8 = "无法获取三方存管协议电子签名";
    public static final String THIRD_DEPOSITY_WRONG9 = "当前时间为服务器维护时间，无法办理三方存管重新绑定业务";
    public static final String THIRD_DEPOSITY_WRONG10 = "还未选择要绑定的账户";
    public static final String THIRD_DEPOSITY_WRONG11 = "需短信验证绑定三方存管";
    // 提交风险出错
    public static final String RISK_WRONG1 = "您尚未登录或已超时，请重新<a href=\"/\">登录</a>";
    public static final String RISK_WRONG2 = "填写答案有误";
    // 提交风险出错
    public static final String CALLBACK_WRONG1 = "您尚未登录或已超时，请重新<a href=\"/\">登录</a>";
    // 签署协议出错
    public static final String SIGN_WRONG1 = "该协议不存在";
    public static final String SIGN_WRONG2 = "签署电子协议出错";
    public static final String SIGN_WRONG3 = "三方存管协议不存在";
    public static final String SIGN_WRONG4 = "三方存管银行不存在";
    public static final String SIGN_WRONG5 = "用户天添利协议未签署";

    // 委托方式
    public static final String OPEN_SERVICE_TYPE_WRONG = "开设委托方式出错";
    // 预约视频出错
    public static final String ORDER_VIDEO_WRONG1 = "qq号格式错误";
    public static final String ORDER_VIDEO_WRONG2 = "请选择预约时间";
    public static final String ORDER_VIDEO_WRONG3 = "预约时间格式错误";
    public static final String ORDER_VIDEO_WRONG4 = "预约视频错误";
    public static final String ORDER_VIDEO_WRONG5 = "该QQ号已存在，请重新输入";
    // 保存套餐出错
    public static final String SAVE_PACKAGE_INFO_ERROR = "保存套餐信息异常";
    // 设置模板出错
    public static final String SET_MODEL_INFO_ERROR = "设置用户模板异常";
    public static final String MODEL_NO_NOT_EXISTS = "model_no=[%s]对应模板不存在";

    public static final String VIDEO_FLOW_IN = "用户加入队列";
    public static final String VIDEO_FLOW_ANSWER = "用户被应答";
    public static final String VIDEO_FLOW_PASS = "用户验证通过";
    public static final String VIDEO_FLOW_UNPASS = "用户验证不通过";
    public static final String VIDEO_FLOW_USER_ACCIDENT = "用户异常中断";
    public static final String VIDEO_FLOW_EMP_ACCIDENT = "坐席异常中断";
    public static final String VIDEO_FLOW_TIMEOUT = "排队超时被移除 ";
    public static final String VIDEO_FLOW_REIN_UNANSWER = "重新排队（无应答）";
    public static final String VIDEO_FLOW_REIN_ANSWERED = "重新排队（已经被应答）";
    public static final String VIDEO_FLOW_USER_LEAVE = "客户GG已离开";
    public static final String VIDEO_FLOW_ENTER_ROOM = "用户进入房间";
    /************************** 视频类型说明 **********************************/
    public static final String VIDEO_TYPE_KH = "开户";
    public static final String VIDEO_TYPE_WTCERT = "网厅证书";
    public static final String VIDEO_TYPE_PWD_RESET = "网厅密码重置";
    public static final String VIDEO_TYPE_INFO_MODIFY = "网厅重要资料修改";
    public static final String VIDEO_TYPE_GEM = "网厅创业板";
    public static final String VIDEO_TYPE_RZRQ = "网厅融资融券";
    public static final String VIDEO_TYPE_GPZYKT = "网厅股票质押开通";
    public static final String VIDEO_TYPE_GPZY = "网厅股票质押";

    /************************** 数字证书类错误 ***************************************/
    public static final String CERT_NO_CA_CERT_ERROR = "还未申请中登证书";
    public static final String CERT_NO_PASS_ERROR = "后台还未颁发证书，暂无法申请，请稍后再试";
    public static final String CERT_SAVE_ERROR = "保存中登证书失败";
    public static final String CERT_ISSUE_CA_CERT_ERROR = "证书颁发失败";
    public static final String CERT_WAIT_ISSUE_CA_CERT_ERROR = "等待证书颁发";

    /**
     * 公告内容截取的字符长度
     **/
    public static final int NOTICCONTENLEN = 40;

    public static final String GLOBAL_NO_PERMISSION = "对不起，您没有此操作权限！";
    public static final String PARAM_NOT_CORRECT = "请求参数错误";
    public static final String OP_SUCCESS = "保存成功";
    public static final String LOGIN_BRANCH_WRONG = "无此营业部";

    public static final String SYSTEM_EXCEPTION = "当前服务不可用，请稍后尝试！";
    public static final String SYSTEM_EXCEPTION_TIME = "当前时间是非交易时间，服务不可用，请稍后尝试！";


    /** 密码设置不能为空 */
    // public static final String SETTING_CASH_NOEMPTY = "资金密码不能为空";
    // public static final String SETTING_TRANS_NOEMPTY = "交易秘密不能为空";
    // public static final String SETTING_CONNECT_NOEMPTY = "通信秘密不能为空";

    /**
     * 基金代码已存在
     */
    public static final String FUNC_CODE_ERROR = "请填写正确基金公司代码";
    /**
     * 基金公司代码为整数
     */
    public static final String FUNC_ENTRY_ERROR = "请填写正确基金公司代码";
    /**
     * 公司名称不能为空
     */
    public static final String FUNC_SUBENTRY_NOEMPTY = "公司名称不能为空";

    /**
     * 字典编号为整数
     */
    public static final String DICT_ENTRY_ERROR = "请填写正确字典编号";
    /**
     * 字典选择项不能为空
     */
    public static final String DICT_SUBENTRY_NOEMPTY = "字典选择项不能为空";
    /**
     * 字典显示项不能为空
     */
    public static final String DICT_PROMOT_NOEMPTY = "字典显示项不能为空";
    /**
     * 字典选择项已经存在
     */
    public static final String DICT_SUBENTRY_EXISTS = "字典选择项已经存在";
    /**
     * 该字典项目不能被修改
     */
    public static final String DICT_UNABLE_MODIFY = "该字典项目不能被修改";
    /**
     * 字典维护出现异常
     */
    public static final String DICT_EXCEPTION = "字典维护出现异常";

    /**
     * 协议名称不能为空
     */
    public static final String AGREEMENT_NAME_NOEMPTY = "协议名称不能为空";
    /**
     * 协议内容不能为空
     */
    public static final String AGREEMENT_CONTENT_NOEMPTY = "协议内容不能为空";
    public static final String AGREEMENT_AGREEMENT_TYPE = "协议类型不能为空";
    public static final String AGREEMENT_ESXITS = "该协议编号已经存在";

    /**
     * 公告标题不能为空
     */
    public static final String MESS_TITLE_NOEMPTY = "公告标题不能为空";
    /**
     * 公告内容不能为空
     */
    public static final String MESS_CONTENT_NOEMPTY = "公告内容不能为空";
    /**
     * 请填写有效日期
     */
    public static final String MESS_TIME_ERROR = "请填写有效日期";

    // 身份校验错误提示
    public final static String IDENTIFY_RESULT_WRONG1 = "登录出错1";
    public final static String IDENTIFY_RESULT_WRONG2 = "登录出错2";
    public final static String IDENTIFY_RESULT_WRONG3 = "登录出错3";
    public final static String IDENTIFY_RESULT_WRONG4 = "用户名/密码错误";
    public final static String IDENTIFY_RESULT_WRONG5 = "用户身份错误";
    public final static String IDENTIFY_RESULT_WRONG6 = "账号已过期";
    public final static String IDENTIFY_RESULT_WRONG7 = "账号被冻结";
    public final static String IDENTIFY_RESULT_WRONG8 = "余额不足";
    public final static String IDENTIFY_RESULT_WRONG9 = "服务器错误";
    public final static String IDENTIFY_RESULT_WRONG10 = "未知的返回结果";
    public final static String IDENTIFY_RESULT_WRONG11 = "无法获取用户密钥";
    public final static String IDENTIFY_RESULT_WRONG12 = "身份核查返回结果为空1";
    public final static String IDENTIFY_RESULT_WRONG13 = "身份核查返回结果为空2";
    public final static String IDENTIFY_RESULT_WRONG14 = "身份核查返回结果为空3";
    public final static String IDENTIFY_RESULT_WRONG15 = "记录数太多";
    public final static String IDENTIFY_RESULT_WRONG16 = "传入参数错误";
    public final static String IDENTIFY_RESULT_WRONG17 = "身份核查返回结果为空4";
    public final static String IDENTIFY_RESULT_WRONG18 = "身份核查不通过（身份证号码错误或身份证号码和名称不一致）";
    public final static String IDENTIFY_RESULT_WRONG19 = "库中无此号";
    // public static final String SUCCESS = "success"; // 校验通过返回值
    // public static final String VALIDATE_INVALID = "含有非法字符";
    // 校验姓名
    // public static final String VALIDATE_NAME_NULL = "姓名不能为空";
    // public static final String VALIDATE_NAME_ERROR1 = "姓名非法";
    // 校验手机
    // public static final String VALIDATE_MOBILE_NULL = "手机号码不能为空";
    // public static final String VALIDATE_MOBILE_EXIST = "手机号码已被使用";
    public static final String VALIDATE_MOBILE_ERROR1 = "手机号码位数错误";
    public static final String VALIDATE_MOBILE_ERROR2 = "手机号码非法";
    // 校验身份证号
    public static final String VALIDATE_CID_NULL = "身份证号不能为空";
    public static final String VALIDATE_CID_EXIST = "身份证号已被使用";
    public static final String VALIDATE_CID_ERROR1 = "身份证号长度有误";
    public static final String VALIDATE_CID_ERROR2 = "除最后一位外都应为数字";
    public static final String VALIDATE_CID_ERROR3 = "生日格式无效";
    public static final String VALIDATE_CID_ERROR4 = "生日不在有效范围";
    public static final String VALIDATE_CID_ERROR5 = "身份证月份无效";
    public static final String VALIDATE_CID_ERROR6 = "身份证日期无效";
    public static final String VALIDATE_CID_ERROR7 = "地区编码错误";
    public static final String VALIDATE_CID_ERROR8 = "身份证校验码有误";
    public static final String VALIDATE_CID_ERROR9 = "未满18周岁不能开户";
    public static final String VALIDATE_CID_ERROR10 = "birthday[%s]需满足[yyyyMMdd]格式";
    // 校验身份证和姓名
    public static final String VALIDATE_CIDNAME_EXIST = "身份证号码已被使用";
    // 校验身份证发证机关
    public static final String VALIDATE_CIDFROM_NULL = "发证机关不能为空";
    public static final String VALIDATE_CIDFROM_ERROR = "发证机关有误";
    // 校验证件地址
    public static final String VALIDATE_CIDADDR_NULL = "证件地址不能为空";
    public static final String VALIDATE_CIDADDR_ERROR = "证件地址有误";
    // 校验联系地址：省份、城市、地区
    // public static final String VALIDATE_PROVI_NULL = "请选择省份";
    // public static final String VALIDATE_CITY_NULL = "请选择城市";
    // public static final String VALIDATE_AREA_NULL = "请选择地区";
    // 校验身份证有效期限
    // public static final String VALIDATE_CIDABLE_NULL = "有效期限不能为空";
    // public static final String VALIDATE_CIDABLE2_NULL = "有效期限和长期有效必须填一个";
    // public static final String VALIDATE_CIDABLE_ERROR = "有效期限有误";
    // 校验联系地址
    // public static final String VALIDATE_ADDRESS_NULL = "联系地址不能为空";
    // public static final String VALIDATE_ADDRESS_ERROR = "联系地址有误";
    // public static final String VALIDATE_ADDRESS_ERROR2 = "联系地址不完整";
    public static final String VALIDATE_ADDRESS_ERROR3 = "联系地址含有特殊字符";
    // 校验邮政编码
    // public static final String VALIDATE_POSTCODE_NULL = "邮政编码不能为空";
    public static final String VALIDATE_POSTCODE_ERROR1 = "邮政编码位数有误";
    public static final String VALIDATE_POSTCODE_ERROR2 = "邮政编码格式有误";
    // 校验固定电话
    // public static final String VALIDATE_TEL_NULL = "固定电话不能为空";
    public static final String VALIDATE_TEL_ERROR1 = "固定电话有误";
    // 校验电子邮件
    // public static final String VALIDATE_EMAIL_NULL = "电子邮件不能为空";
    public static final String VALIDATE_EMAIL_ERROR1 = "电子邮件有误";
    // 校验职业
    // public static final String VALIDATE_JOB_NULL = "职业不能为空";
    public static final String VALIDATE_JOB_ERROR1 = "职业有误";
    // 校验学历
    // public static final String VALIDATE_EDU_NULL = "学历不能为空";
    public static final String VALIDATE_EDU_ERROR1 = "学历有误";
    // 校验年收入
    // public static final String VALIDATE_YEAR_NULL = "年收入不能为空";
    public static final String VALIDATE_YEAR_ERROR1 = "年收入有误";
    // 校验国籍
    public static final String VALIDATE_GROUP_NULL = "国籍不能为空";
    public static final String VALIDATE_GROUP_ERROR1 = "国籍有误";
    // 校验民族
    public static final String VALIDATE_NATION_NULL = "民族不能为空";
    public static final String VALIDATE_NATION_ERROR1 = "民族有误";
    // 校验行业
    public static final String VALIDATE_IND_NULL = "行业不能为空";
    public static final String VALIDATE_IND_ERROR1 = "行业有误";


    public static final String STATUS_VIDEO_VERIFY_SUCCESS = "验证通过";
    public static final String STATUS_VIDEO_VERIFY_FAIL = "验证未通过";

    // 审核、复核相关错误提示
    public static final String APP_WRONG = "程序出错";
    public static final String AUDIT_WRONG_1 = "审核通过，没有待修改项";
    public static final String AUDIT_WRONG_2 = "审核结果不正确";
    public static final String AUDIT_WRONG_3 = "没有整改意见";
    public static final String AUDIT_WRONG_4 = "整改意见格式错误";
    public static final String AUDIT_WRONG_5 = "整改意见不能为空";
    public static final String AUDIT_WRONG_6 = "审核提交出错";
    public static final String AUDIT_WRONG_7 = "整改意见不能超过140个字";
    public static final String REVIEW_WRONG_1 = "复核结果不正确";
    public static final String REVIEW_WRONG_2 = "复核不通过原因有误";
    public static final String REVIEW_WRONG_3 = "请选择复核不通过原因";
    public static final String REVIEW_WRONG_6 = "整改意见有误";
    public static final String REVIEW_WRONG_7 = "整改意见不能为空";
    public static final String REVIEW_WRONG_4 = "请不要重复提交";
    public static final String REVIEW_WRONG_5 = "整改意见不能超过140个字";
    public static final String AUDIT_WRONG_8 = "请选择审批不通过原因";

    // 身份检测错误提示
    public static final String RESULT_NOT_MATCH = "不一致";// 身份证号码、姓名不一致
    public static final String RESULT_MATCH = "一致";// 身份证号码、姓名一致
    public static final String NAME_AND_IDNO_NOT_MATCH = "姓名与身份证号码不一致";
    public static final String IDINFO_RESULT_NOT_FOUND = "用户身份认证时无法找到相关的姓名或身份证信息";
    public static final String ID_VERIFY_EXCEPTION = "公安认证出现异常";

    // 获取权限列表
    public static final String GET_RESOURCE_LIST_NULL = "权限列表为空";
    public static final String GET_RESOURCE_LIST_FAIL = "获取权限列表出错";

    // 角色管理
    public static final String VALIDATE_ROLE_ID_NULL = "角色ID不能为空";
    public static final String VALIDATE_ROLE_CODE_NULL = "角色代码不能为空";
    public static final String VALIDATE_ROLE_CODE_EXIST = "角色代码已经存在";
    public static final String VALIDATE_ROLE_NAME_NULL = "角色名称不能为空";
    public static final String VALIDATE_ROLE_NAME_EXIST = "角色名称已经存在";
    public static final String VALIDATE_RESOURCE_LIST_NULL = "权限列表不能为空";
    public static final String VALIDATE_RESOURCE_LIST_ERROR = "权限列表有误";
    public static final String SAVE_RESOURCE_LIST_ERROR = "新增角色出错";
    public static final String EDIT_ROLE_ERROR = "修改角色出错";
    public static final String EDIT_RESOURCE_LIST_ERROR = "修改角色出错";
    public static final String DEL_ROLE_ERROR = "删除角色出错";
    // 身份验证银行
    public static final String VALIDATE_VBANKNO_NULL = "显示序号不能为空";
    public static final String SAVE_VBANK_ERROR = "新增身份验证银行出错";
    public static final String DEL_VBANK_ERROR = "删除身份验证银行出错";
    // 三方存管银行
    public static final String VALIDATE_TBANKNO_NULL = "显示序号不能为空";
    public static final String VALIDATE_TBANKNO_EXIST = "显示序号已经存在";
    public static final String VALIDATE_TBANKID_EXIST = "已经支持该银行";
    public static final String VALIDATE_TBANKID_banktype_EXIST = "已经支持该银行此类绑定方式";// M201505250358 wmy ********
    // 支持同一个银行支持多种绑定方式 add
    public static final String VALIDATE_TBANK_AGRNAME_NULL = "协议名称不能为空";
    public static final String VALIDATE_TBANK_AGRVERS_NULL = "协议版本不能为空";
    public static final String VALIDATE_TBANK_AGRCONT_NULL = "协议内容不能为空";
    public static final String VALIDATE_TBANK_URL_NULL = "协议url不能为空";
    public static final String VALIDATE_TBANK_FILE_NULL = "协议文件不能为空";
    public static final String ADD_TBANK_ERROR = "新增三方存管银行出错";
    public static final String EDIT_TBANK_ERROR = "修改三方存管银行出错";
    public static final String DEL_TBANK_ERROR = "删除三方存管银行出错";
    // 开户协议
    public static final String ADD_PROTOCOL_ERROR = "新增开户协议出错";
    public static final String EDIT_PROTOCOL_ERROR = "修改开户协议出错";
    public static final String DEL_PROTOCOL_ERROR = "删除开户协议出错";
    public static final String SEND_PROTOCOL_ERROR = "上传协议出错";
    // 业务品种
    public static final String EDIT_BSKIND_ERROR = "修改业务品种出错";
    public static final String EDIT_BSKIND_ERROR1 = "请确保至少有一项是显示状态的";
    // 委托方式
    public static final String EDIT_WTKIND_ERROR = "修改委托方式出错";
    // 系统参数设置
    public static final String VALIDATE_SYSSET_TIME_NULL = "开户时间耗时不能为空";
    public static final String VALIDATE_SYSSET_DAYS_NULL = "日期不能为空";
    public static final String VALIDATE_SYSSET_TIMES_NULL = "时间段不能为空";
    public static final String EDIT_SYSSET_ERROR = "修改系统参数出错";
    public static final String TASK_NOT_EXISTS = "系统错误，不存在该任务，请重新操作！";
    public static final String TASK_TURNOFF = "该任务已经被回拨，请重新获取任务！";
    // 审核错误提示
    public static final String VALIDATE_EMAIL_ERROR2 = "电子邮件含有特殊字符";
    public static final String AUDIT_NOT_MODIFY = "您还有待修改项";
    public static final String AUDIT_NEED_IMPROVE = "资料待整改";
    public static final String AUDIT_SYS_USER_WRONG1 = "系统用户不存在";
    public static final String AUDIT_SYS_USER_WRONG2 = "系统用户没有该权限";
    public static final String AUDIT_ZD_OA_WRONG1 = "账户正常";
    public static final String AUDIT_ZD_OA_WRONG2 = "账户异常";
    public static final String AUDIT_ZD_OA_WRONG3 = "中登账户信息校验时异常";
    public static final String AUDIT_ZD_OA_WRONG4 = "无法获取用户身份信息";
    public static final String AUDIT_DUE_DATE_ERROR = "身份证证件有效期有误";

    public static final String ID_VERIFY_NAME_NOT_ALLOW_MODIFY = "该用户身份证姓名[%s]已通过公安认证, 禁止修改为[%s]";
    public static final String ID_VERIFY_ID_NO_NOT_ALLOW_MODIFY = "该用户身份证号码[%s]已通过公安认证, 禁止修改为[%s]";


    // 开户错误提示
    public static final String OPEN_ACCOUNT_WRONG0 = "开股票账户出错";
    public static final String OPEN_ACCOUNT_WRONG1 = "提交风险测评信息出错";
    public static final String OPEN_ACCOUNT_WRONG2 = "未设置资金账号";
    public static final String OPEN_ACCOUNT_WRONG3 = "资金账号开户出错";
    public static final String OPEN_ACCOUNT_WRONG4 = "资金账号为空";
    public static final String OPEN_ACCOUNT_WRONG5 = "提交股东账户开户信息出错";
    public static final String OPEN_ACCOUNT_WRONG6 = "提交三方存管开户信息出错";
    public static final String OPEN_ACCOUNT_WRONG7 = "提交基金账户开户信息出错";
    public static final String OPEN_ACCOUNT_WRONG8 = "返回结果为空";
    public static final String OPEN_ACCOUNT_WRONG9 = "三方存管为一步式，需输入密码";
    public static final String NEED_CERT = "新开通证券账户需要申请中登证书";
    public static final String OPEN_ACCOUNT_WRONG10 = "用户转户时未查询到用户股东账户";
    public static final String OPEN_ACCOUNT_WRONG11 = "保存理财户异常";
    public static final String OPEN_ACCOUNT_WRONG12 = "深圳98TA账户不存在";

    // 港股通开户错误
    public static final String HKOPEN_ACCOUNT_WRONG1 = "您已存在沪A股东账户，请前往网厅办理港股通权限业务";

    // biz 相关
    public static final String BIZ_UNIT_NOT_EXISTS = "该业务单元不存在";

    public static final String ORG_EXISTS = "该营业部已经存在";
    public static final String ORG_NAME_NOT_EMPTY = "营业部名称不能为空";
    public static final String ORG_PROVINCE_NOT_EMPTY = "营业部身份不能为空";
    public static final String ORG_CITY_NOT_EMPTY = "营业部城市不能为空";
    public static final String ORG_ADDRESS_NOT_EMPTY = "营业部地址不能为空";

    public static final String ORG_PACK_EXISTS = "该服务套餐已经存在";
    public static final String ORG_PACK_NAME_NOT_EMPTY = "服务套餐名称不能为空";
    public static final String HAVE_NO_REMARK = "无备注信息";

    public static class EXPORT_BIZ {

        public static final String CHANNEL_USER = "CHANNEL_USER";
        public static final String CHANNEL_STAT = "CHANNEL_STAT";
        public static final String CHANNEL_ALL = "CHANNEL_ALL";

    }

    public static final String PIC_NOT_CLEAR = "照片不清晰";
    public static final String SET_SUCCESS = "设置成功";
    public static final String SET_FAILURE = "设置失败";
    public static final String NULL_TMODEL_ERROR = "佣金模板不存在";
    public static final String ADD_TMODEL_ERROR = "新增佣金模板出错";
    public static final String EDIT_TMODEL_ERROR = "修改佣金模板出错";
    public static final String DEL_TMODEL_ERROR = "删除佣金模板出错";
    public static final String SET_USER_MODEL_FAILURE = "佣金模板不存在或用户不存在";
    public static final String SET_USER_PACKAGE_FAILURE = "设置用户套餐不存在";

    public static final String INCOMING_PARAMETERS_INCORRECT = "传入参数为有误";
    public static final String USERINFO_NOT_FOUND = "未查询到该用户信息";
    public static final String ERR_QUEUE_TIME_OUT = "排队超时，请重新排队";
    public static final String QUEUELIST_IS_EMPTY = "队列信息为空";
    public static final String QUERY_USER_INFORMATION_EXCEPTION = "查询用户信息异常";
    public static final String QUERY_ALL_USER_INFORMATION_EXCEPTION = "查询所有用户信息异常";
    public static final String DELETE_EXCEPTION = "删除异常";
    public static final String ADD_EXCEPTION = "添加异常";
    public static final String SERVER_IS_NOT_AVAILABLE = "此服务器不可用";
    public static final String TOO_MANY_PEOPLE_QUEUING = "排队人数过多";
    public static final String TASKS_HAVE_BEEN_RECEIVED = "任务已被领取";
    public static final String ERR_UPDATE_QUEUELIST = "更新队列异常";
    public static final String ABNORMAL_PRE_ANSWER = "预抢答异常";
    public static final String ALL_TASKS_HAVE_BEEN_RECEIVED = "所有任务已被领取";

    public static final String USERID_IS_EMPTY = "用户ID未传";
    public static final String ORGANIZATION_PARAMETER_IS_EMPTY = "营业部参数未传";
    public static final String QUERY_ORGANIZATION_IS_NULL = "未查询到营业部";
    public static final String RECORDED_FLOW_ANOMALY = "记录流水异常";
    public static final String VALID_CSDCQRY_IDNO_NAME_NULL = "中登查询用户的身份证与姓名不能为空";

    public static final String USERS_ILLEGALLY = "用户非法";
    public static final String NO_TEXT_MESSAGE_PERMISSIONS = "没有自写短信权限";
    public static final String ILLEGAL_MESSAGE_CONTENT = "信息内容非法";
    public static final String PHONE_NUMBERS_ILLEGALLY = "接收手机号码非法";
    public static final String SEND_TEXT_MESSAGES_FAILED = "发送短信数据失败";
    public static final String ILLEGAL_ADDRESS = "非法地址";
    public static final String VALIDATE_CODE_ERROR_OVER_LIMITED = "验证码出错次数超过限制";
    public static final String ENCODING_FORMAT_NOT_SUPPORTED = "不支持的编码格式";
    public static final String TASK_NOT_EXISTS_OR_OVER = "客户任务不存在或已结束";
    public static final String EMP_INFO_NOT_EXISTS = "柜员信息不存在";

    public static final String NOT_ALLOWED_NULL = "不能为空";
    public static final String FUND_ACCOUNT_NOT_CREATED = "还未创建资金账户";
    public static final String THIRD_DEPOSITY_WAS_BINDING = "用户三方存管已绑定成功";
    public static final String REVISIT_QUESTION_NOT_FOUND = "回访问卷问题数据未配置";
    public static final String OPTION_NOT_EXISTS = "选项不存在，选项为：";
    public static final String QUESTION_NOT_EXISTS = "题目不存在，题号为：";
    public static final String ERROR_USER_CERT_NOT_FOUND = "未找到证书";

    public static final String NO_LOCATION = "不定位";
    public static final String BASE_ORGANIZATION_NOT_FOUND = "未查询到营业部信息";
    public static final String BAIDU_ANALYSIS_FAILED = "baidu解析失败";

    public static final String REQUEST_CSDCCERT_FAILED_NO_RETURN = "申请中登证书失败，无返回数据";
    public static final String CANCEL_CSDCCERT_FAILED_NO_RETURN = "作废中登证书失败，无返回数据";
    public static final String REQUEST_SELFCERT_FAILED_NO_RETURN = "申请自建证书失败，无返回数据";
    public static final String CANCEL_SELFCERT_FAILED_NO_RETURN = "作废自建证书失败，无返回数据";

    public static final String DOWNLOARD_CERT_FAILED = "下载证书失败";
    public static final String REQUEST_CSDCCERT_SUCCESS_ANALYSIS_FAILED = "中登证书正常获取，但后台解析异常";
    public static final String REQUEST_FAILED_TRY_LATER = "申请失败，请稍后重试";
    public static final String REQUEST_TWO_CODES_FAILED = "申请两码失败";
    public static final String UPDATE_CERT_FAILED = "更新证书失败";

    public static final String FACE_RECOGNITION_SCORE_NOT_PASS = "人脸识别分值小于验证通过阀值";
    public static final String QRY_ACCOUTN_INFO_FAILED = "查询账户信息超时或失败!";
    public static final String QRY_CSDC_STOCKACCOUNT_INFO_FAILED_TRY_LATER = "中登股东账户查询失败[请求编号为空]，请重试！";

    public static final String SAVE_EXAMTESTRESULT_FAILED = "保存知识答题失败";
    public static final String INCOMING_PARAMETERS_ERR_QUESTION_NOT_EXISTS = "传入参数为有误[paper_answer],不存在对应试题";

    public static final String HKSTOCK_PAPER_ANSWER_EMPTY = "用户还未进行港股通知识评测";
    public static final String PAPER_ANSWER_EMPTY = "用户还未进行风险评测";

    public static final String USER_VIDEO_APLLY_NOT_FINISH = "用户视频请求未完成";
    public static final String USER_SUBMIT_TO_AUDIT = "用户已提交审核";
    public static final String IDCARD_PIC_NOT_UPLOAD = "身份证正反面未上传或上传不全";

    public static final String GET_BIRTHDAY_ANALYTICAL_FAIL = "出生日期[%s]解析失败";
    public static final String CAMCARD_OCR_CALL_FAIL = "调用合合公安图像失败";
    public static final String CAMCARD_OCR_GET_IMAGE_FAIL_RETURN = "获取合合公安图像识别结果失败，返回结果[%s]";
    public static final String CLOUD_OCR_CALL_FAIL = "调用云脉公安图像失败";
    public static final String CLOUD_OCR_GET_RESULT_FAIL = "获取云脉公安识别结果失败";
    public static final String CLOUD_OCR_GET_IMAGE_FAIL_RETURN = "获取云脉公安图像识别结果失败，返回结果[%s]";
    public static final String TURUI_OCR_CALL_FAIL = "调用锐图公安图像失败";
    public static final String TURUI_OCR_GET_IMAGE_FAIL_RETURN = "获取图锐公安图像识别结果失败，返回结果[%s]";
    public static final String ZHONGZHUO_OCR_CALL_FAIL = "调用中灼公安图像失败";
    public static final String ZHONGZHUO_OCR_GET_IMAGE_FAIL_RETURN = "获取中灼公安图像识别结果失败，返回结果[%s]";
    public static final String NOT_ID_IMAGE__NO_IDENTITY_INFOMATION = "身份证识别失败，无身份信息";
    public static final String IMAGE_LIGHT_SPOT = "图片有光斑无法识别，请重新上传";
    public static final String GET_ID_VALIDTY_PERIOD_FAIL = "身份证有效期解析失败，请重新上传";
    public static final String GET_PAPERS_VALIDTY_PERIOD_FAIL = "证件有效期解析失败，请重新上传";
    public static final String GET_LSSUING_AUTHORITY_RESOLUTION_FAIL = "签发机关解析失败，请重新上传";
    public static final String XYZQ_OCR_ERROR_IMAGE_DECODING = "客户公安信息[图像解码出错][%s][%s]";

    public static final String DATE_FORMATE_ERROR = "日期格式转换异常";
    public static final String USER_ID_AND_MOBILE_NOT_NULL = "用户id和手机号码不能同时为空";
    public static final String UERR_INFO_NOT_EXISTS_OR_NOT_UPLOAD = "用户不存在或用户还未上传身份信息";

    public static final String REGISTER = "注册";
    public static final String LOGIN = "登录";
    public static final String ONLINE_OPEN_ACCOUNT = "网上开户";
    public static final String ONLINE_TRANSFER_ACCOUNT = "网上转户";
    public static final String ONLINE_RESERVATION_OPEN_ACCOUNT = "网上预约开户";
    public static final String RESERVATION_OPEN_ACCOUNT = "预约开户";

    public static final String FILE_DOWNLOAD_ERROR = "文件下载出错";
    public static final String FILE_SAVE_ERROR = "文件保存出错";
    public static final String ID_VERIFY_RESULT = "公安认证比对结果";
    public static final String ID_VERIFY_FAILED = "公安认证失败";
    public static final String ID_VERIFY_ANALYSIS_ERROR = "解析公安认证返回数据出错";
    public static final String USER_IDENTIFY_HEAD_PICTURE = "用户公安认证头像";
    public static final String SYSTEM_ERROR = "系统异常";
    public static final String ID_VERIFY_SUCCESS = "公安认证成功";
    public static final String ID_VERIFY_QUERY_NO_DATA_FOUND = "公安认证查询尚未获取到数据";
    public static final String ID_VERIFY_QUERY_FAILED = "公安身份认证查询调用失败";
    public static final String ID_VERIFY_QUERY_NO_RESOURCE = "公安身份认证查询请求条数已用完";

    public static final String FUND_ACCOUNT = "资金账号";
    public static final String SYNC_RESULT = "同步结果：";

    public static final String GOLD_NET_SYNC_FAILED = "金网数据同步失败：";
    public static final String CICC_OPEN_CALLBACK_API_INITIALIZATION = "中金开户回调接口初始化：url=";


    public static final String USER_STATE_HAS_AUDIT_NOT_COUNTING_CHANNELS = "用户[{}]状态为[{}]已进入审核流程, 不计算渠道。";
    public static final String USER_STATUS_EXCEPTION_CHANNEL_IS_NOT_PROCESSED = "用户[{}],status异常[{}]渠道不做处理。";
    public static final String USER_SHORT_URL_EXCEPTION_NOT_COUNTING_CHANNELS = "用户[{}],短链接[{}]异常,不计算渠道。";
    public static final String USERS_SETTING_CHANNEL_DOES_NOT_EXIST = "用户[{}]设置渠道[{}]不存在。";
    public static final String USERS_THERE_ARE_CHANNELS_NOT_COUNTING_CHANNEL = "用户[{}],已有渠道[{}],不计算渠道。";
    public static final String USER_CHANGE_FROM_NON_CHANNELS_NOT_PROCESSED = "用户[{}], 从非渠道变更[{}]渠道，不做处理。";
    public static final String USER_SET_BROKER_CHANNEL_CODE = "用户[{}]设置渠道经纪人 code[{}]!";
    public static final String USER_SET_CHANNELS_SALES_BRANCH_CODE = "用户[{}]设置渠道营业部 branch code[{}]!";
    public static final String USER_COMMISSION_SET_UP_CHANNELS = "用户[{}]设置渠道佣金[{}]!";
    public static final String REGISTER_LOGIN_USER_SETTING_CHANNEL_THE_DAY_OF_STATISTICS = "注册登录:用户[{}]设置渠道[{}],当天统计量[{}:{}]";
    public static final String CHANNLEID_STATDATE_PARAMETER_ERROR = "channleId[{}], statDate[{}]参数错误";
    public static final String DEFAULT_CHANNELS_BASIC_INFO_SUBMITTED_NO_STATISTICAL = "默认渠道，在基本资料提交时不统计渠道信息。";
    public static final String USER_NOT_OPEN_NOT_STAT_TO_CHANNEL_ = "用户[{}] 还未开户成功, 不统计渠道。";
    public static final String USER_STATUS_NOT_STAT_TO_CHANNEL_ = "用户[{}] 非渠道用户不统计。";
    public static final String USER_OPEN_SUCCESS_USER_CHANNEL_STAT = "开户成功:用户[{}], 渠道[{}],当天统计量[{}:{}]";
    public static final String USER_NOT_CHANNEL_SET_DEFAULT_IS = "用户[{}]为非渠道用户。设置为默认渠道：[{}]";
    public static final String USER_NOT_CHANNEL_DEFAULT_IS_INVALID = "用户[{}]为非渠道用户。默认渠道[{}]无效";

    public static final String ANALYSE_MOBILE_LOCATION_URL_EXCEPTION = "解析手机获取归属地URL异常";

    public static final String OPEN_ACCOUNT_SUCCESS = "开户成功";
    public static final String OPEN_ACCOUNT_FAILED = "开户失败";
    public static final String EASY_OPEN_ACCOUNT_ACCEPTING = "简易开户受理中";
    public static final String OPEN_ACCOUNT_SUCCESS_WAIT_FOR_ACTIVE = "开户成功，等待激活";
    public static final String OPEN_ACCOUNT_ACCEPTING = "开户受理中";

    public static final String APPLICATION_FORM_AUTO_SYNTHESIS_ERROR = "申请表自动合成出错";
    public static final String DEBUG_OPERATOR_UPLOAD_PIC_SUCCESS = "DEBUG模式-----操作员上传图片成功";
    public static final String DEBUG_QUERY_CUSTOMER_INFO_SUCCESS = "DEBUG模式-----查询客户信息成功";
    public static final String DEBUG_QUESTION_BACK_ACTIVE_SUCCESS = "DEBUG模式-----问卷回访激活成功";
    public static final String NO_OPEN_FUND_ACCOUNT = "还未开资金户";
    public static final String OPEN_SUCCESS_NO_AGAIN_ACTIVE = "已开户成功，无需再次激活";
    public static final String SUBMIT_ANSWER_OR_QUERY_STATUS_EXCEPTION = "激活用户账户时提交问卷或查询用户状态异常";
    public static final String ACCOUNT_ACTIVE_OPEN_SUCCESS = "账户激活，开户成功";
    public static final String ACCOUNT_ACTIVE_FAIL = "账户激活失败";
    public static final String IFS_INTERFACE_EXCEPTION_LINK_OUTTIME = "IFS接口异常或连接超时";
    public static final String ACTIVE_SUCCESS_CALL_BACK_EXECUTE_FAILED = "开户激活成功，OpenCallback执行失败";
    public static final String USER_DEPART_STILL_DEAL_NOT_ACTIVE = "此用户的部分账户柜台仍在处理中，暂不能激活";
    public static final String USER_DEPOSITORY_ACCOUNT_STILL_DEAL_NOT_ACTIVE = "此用户的三方存管账户还未处理成功，暂不能激活";

    public static final String OPEN_NOT_FINISH_GOTO_WX_OPEN_FLOW = "您有尚未完成的开户操作，请前往微信开户流程继续操作";
    public static final String OPEN_NOT_FINISH_WX_NOT_ALLOWED = "您有尚未完成的开户操作，暂不允许进行微信开户；请前往原开户流程继续操作！";
    public static final String APP_ID_NOT_SUPPORT = "[[%s]]接入方式不支持";
    public static final String AUTHORIZATION_IS_INVALID = "[[%s]]授权认证无效，请向服务方获取有效授权";
    public static final String ADD_VIDEO_FLOW_EXCEPTION = "记录视频流水异常";

    public static final String NUMBER_FORMAT_EXCEPTION = "数字类型转换异常";

    public static final String HAS_CHANNEL_BRANCH = "有营业部挂接属性";
    public static final String NO_CHANNEL_BRANCH = "无营业部挂接属性";
    public static final String NO_CHANNEL_DEFINE = "渠道不存在";

    public static final String OPEN_APPLY_COUNT = "开户申请人数";
    public static final String REGISTER_COUNT = "注册人数";

    public static final String NOT_UPLOADED_PICTURES = "当前用户还未上传图片";
    public static final String EXAM_PAPER_ERROR = "题库信息出错!";
    public static final String EXAM_QUESTION_ERROR = "试题信息出错!";
    public static final String VALIDATE_EMAIL_CODE_FAILURE = "邮箱验证失败!";
    public static final String NOT_WEIXIN_ERROR = "该客服还未上传微信图片!";

    public static final String SUCCESS_SEND_MAIL = "邮件发送成功!";
    public static final String FAILURE_SEND_MAIL = "邮件发送失败!";
    public static final String VALIDATE_EMAIL_CODE_SUCCESS = "邮箱验证成功!";
    public static final String VALIDATE_EMAIL_CODE_HAD = "邮箱已经验证过!";
    public static final String ONLINE_FINANCING_ACCOUNT = "开理财户";
    public static final String ONLINE_SALE_ACCOUNT_REGISTER = "营销平台注册";

    // 用户存在性检验
    public static final String CF_EXIST_ACCOUNT = "您好，您已是财富证券的客户，请您前往所在营业部或通过我公司网上营业厅（敬待推出）办理相关业务。";
    public static final String CF_IS_BLACK_LIST = "您好，您的证件信息属国际金融反洗钱“黑名单”范围，按照中国人民银行反洗钱相关规定，我公司不能为您提供开户服务。";
    // 风险类名
    public static final String RISK_TYPE_HMD_TEXT = "黑名单";// 黑名单用户
    public static final String RISK_TYPE_BLXY_TEXT = "不良信用户";// 不良信用用户
    public static final String RISK_TYPE_DJG_TEXT = "董监高";// 董监高
}

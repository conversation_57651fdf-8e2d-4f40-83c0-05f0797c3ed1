package com.cairh.cpe.common.util;

import cn.hutool.core.util.StrUtil;
import com.cairh.cpe.common.constant.TaskTypeRoleConstant;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;

public class TaskTypeRoleTranslator {

    public static String translate(String task_type) {
        if (StrUtil.equals(task_type, FlowNodeConst.AUDIT)) {
            return TaskTypeRoleConstant.ROLE_TASK_AUDIT;
        } else if (StrUtil.equals(task_type, FlowNodeConst.REVIEW)) {
            return TaskTypeRoleConstant.ROLE_TASK_REVIEW;
        } else if (StrUtil.equals(task_type, FlowNodeConst.SECONDARY_REVIEW)) {
            return TaskTypeRoleConstant.ROLE_TASK_SECONDARY_REVIEW;
        }

        return task_type;
    }
}

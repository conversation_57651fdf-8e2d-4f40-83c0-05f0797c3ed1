package com.cairh.cpe.common.entity.clob;

import lombok.Data;

import java.io.Serializable;

@Data
public class IDCardInfo implements Serializable {

    private String client_name;// 客户姓名

    private String client_gender; // 客户性别

    private String birthday;// 出生日期YYYYMMDD

    private String nation_id;// 民族编号

    private String id_kind;// 证件类别[字典] 0 身份证

    private String id_no;// 证件号码

    private String id_address;// 证件地址

    private String issued_depart;// 签发机关

    private String id_begindate;// 证件有效期开始日期YYYYMMDD

    private String id_enddate;// 证件有效期结束日期YYYYMMDD

    private String address;// 联系地址

    private String address_ssq;// 省市区

    private String address_ssq_code;// 省市区编码

    private String same_address;// 同证件地址,0-不同，1-相同

    private String zipcode;// 邮政编码

    private String nationality;// 国籍地区

    /**
     * 曾持有证件号
     */
    private String prev_id_number;

    // 非对象存储信息
    private Integer age;// 年龄

    private String auxiliary_id_kind;// 辅助证件类型
    private String auxiliary_id_no;// 辅助证件号码
    private String oversea_client_name;// 境外客户姓名
    private String oversea_id_kind;// 境外证件类型
    private String oversea_id_no;// 境外证件号码

    private String auxiliary_id_begindate;//辅助证件有效期开始日期
    private String auxiliary_id_enddate;//辅助证件有效期结束日期
    private String auxiliary_id_address;//辅助证件地址

    private String permit_no; // 港澳台居民居住证-通行证号码

    private String english_name;// 英文姓名

    private String auxiliary_client_name; // 辅助证件客户姓名

    private String auxiliary_type; // 辅助证件类型
}

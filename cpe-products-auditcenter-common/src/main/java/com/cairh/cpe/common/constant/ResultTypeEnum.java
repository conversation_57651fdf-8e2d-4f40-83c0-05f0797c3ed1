package com.cairh.cpe.common.constant;

public enum ResultTypeEnum {

    NOTCERTIFIED("1", "未认证"),
    CONSISTENCY("2", "一致"),
    INCONSISTENCY("3", "不一致"),
    TOBECONFIRMED("4", "待确认"),
    ;

    String code;
    String value;

    ResultTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getValue(String code) {
        for (ResultTypeEnum h : ResultTypeEnum.values()) {
            if (h.getCode().equals(code)) {
                return h.value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}

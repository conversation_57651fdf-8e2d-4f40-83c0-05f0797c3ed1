package com.cairh.cpe.common.entity.request;

import com.cairh.cpe.common.entity.clob.*;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 审核修改项
 */
@Data
public class AuditChangeForm implements Serializable {

    @NotBlank
    private String request_no;

    private String operator_no;
    private String operator_name;

    private String modify_link;

    /**
     * 保存客户资料修改记录的标识 0 不保存 1 保存
     */
    private String saveCustmodifyRecordFlag;

    /**
     * 单独的信息
     */
    private UserIndependenceInfo independenceInfo;

    /**
     * 身份基本信息
     */
    private IDCardInfo id_card_info;

    /**
     * 身份补充信息
     */
    private UserBaseInfo user_base_info;

    /**
     * 账户信息
     */
    private UserAccountAll user_account_info;

    /**
     * 银行信息
     */
    private UserBankInfo user_bank_info;


    /**
     * 具体修改信息
     */
    private UserIndependenceInfo independenceInfo_one;

    /**
     * rpc 信息
     */
    private UserRpcInfo user_rpc_info;

    /**
     * 税收信息
     */
    private RevenueInfo revenue_info;

    private String choose_branch_reason_content; // 异地开户理由文本

    private String choose_profession_reason_content; // 选择职业理由文本

    private String translation_address; // 经常居住地址(字典翻译)

    private String alternative_address; // 经常居住地址(使用!~分隔的地址)

    private String address_modify_reason;
    private String choose_branch_reason_modify_reason;
    private String choose_profession_reason_modify_reason;
    private String profession_code_modify_reason;
    private String work_unit_modify_reason;
    private String modify_reason;

    private String busin_type;
    private String flow_task_id;

}

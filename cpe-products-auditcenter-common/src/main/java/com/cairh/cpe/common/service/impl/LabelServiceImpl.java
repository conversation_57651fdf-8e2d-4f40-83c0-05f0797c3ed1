package com.cairh.cpe.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.RedisKeyConstant;
import com.cairh.cpe.common.entity.Label;
import com.cairh.cpe.common.mapper.LabelMapper;
import com.cairh.cpe.common.service.ILabelService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.SqlDateUtil;
import com.cairh.cpe.context.expression.ExpressionParser;
import com.cairh.cpe.db.config.IdGenerator;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Service
public class LabelServiceImpl extends ServiceImpl<LabelMapper, Label> implements ILabelService {

    @Resource
    private IdGenerator idGenerator;
    @Autowired
    private ExpressionParser expressionParser;
    @Autowired
    private IRequestService requestService;


    @Resource
    private RedisTemplate<String, String> redisTemplate;


    @Override
    public Page<Label> queryLabelList(Page<Label> page) {
        QueryWrapper<Label> lableQueryWrapper = new QueryWrapper<>();
        return this.page(page, lableQueryWrapper);
    }

    @Override
    public boolean addLabel(Label label) {
        String serial_id = idGenerator.nextUUID(null);
        label.setSerial_id(serial_id);
        SqlDateUtil.setDefaultValue(label);
        return this.save(label);
    }

    @Override
    public boolean updateLabel(Label label) {
        try {
            SqlDateUtil.setDefaultValue(label);
            return this.updateById(label);
        } catch (Exception e) {
            log.error("更新标签失败", e);
            return false;
        }

    }

    @Override
    public List<Label> getMatchLabel(String request_no, Map<String, Object> map) {
        List<Label> result = new ArrayList<>();
        //判断map是否为空
        if (map == null || map.isEmpty()) {
            //获取大字段数据
            map = requestService.getParamContentByRequestNo(request_no);
        }
        //获取所有标签
        List<Label> labels = this.baseMapper.selectList(null);
        if (map == null || map.isEmpty() || labels == null || labels.isEmpty()) {
            return result;
        }

        for (Label label : labels) {
            try {
                Boolean parse = expressionParser.parse(map, label.getRegular_expression(), Boolean.class);
                if (parse) {
                    result.add(label);
                }
            } catch (Exception e) {
            }
        }
        return result;

    }

    @Override
    public Map<String, Label> getMapLabels() {
        String redisKey = RedisKeyConstant.WSKH_AC_LABEL;
        if (redisTemplate.hasKey(redisKey)) {
            return JSON.parseObject(redisTemplate.opsForValue().get(redisKey), new TypeReference<Map<String, Label>>() {
            });
        }
        LambdaQueryWrapper<Label> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Label::getSerial_id, Label::getLabel_type, Label::getLabel_name);
        List<Label> labelList = baseMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(labelList)) {
            Map<String, Label> labelMap = labelList.stream().collect(Collectors.toMap(Label::getSerial_id, x -> x));
            redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(labelMap), 10, TimeUnit.MINUTES);
            return labelMap;
        }
        return Maps.newHashMap();
    }
}




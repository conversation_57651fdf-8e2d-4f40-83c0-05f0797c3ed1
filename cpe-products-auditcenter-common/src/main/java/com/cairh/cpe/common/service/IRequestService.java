package com.cairh.cpe.common.service;

import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;

import java.util.Map;

/**
 * 用户注册申请信息查询
 */
public interface IRequestService {

    ClobContentInfo getAllDataByRequestNo(String request_no);

    ClobContentInfo getAllDataByRequestNoV1(String request_no);

    Map<String, Object> getParamContentByRequestNo(String request_no);

    Map<String, Object> getParamContentByRequestNoV1(String request_no);

    BusinFlowRequest getByRequestNo(String request_no);

    Map<String, Object> getParamContentById(String param_id);

    ClobContentInfo getHisAllDataByRequestNo(String request_no);
}

package com.cairh.cpe.common.entity.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class VerifyPassPortRequest implements Serializable {

    private static final long serialVersionUID = 8224635114985857551L;

    /**
     * 证件类型
     * 1-(02护照),G-(07港澳居民来往内地通行证),H-(08台湾居民来往大陆通行证),I-(09外国人永久居留证)；
     */
    private String id_kind;

    /**
     * 证件号码
     */
    private String id_no;

    /**
     * 账户全称
     */
    private String full_name;

    /**
     * 营业部编号 默认0
     */
    private String branch_no = "0";

    /**
     * 国籍/地区代码:字典项1013
     */
    private String area_code;

    /**
     * 出生日期  YYYYMMDD csdc_busi_kind=02或03时必传
     */
    private String birthday;

    /**
     * 证件开始日期
     */
    private String id_begindate;

    /**
     * 证件结束日期
     */
    private String id_enddate;

    /**
     * csdc_busi_kind=03时必传
     */
    private String csfc_id_enddate;

    /**
     * csdc_busi_kind=03时必传
     */
    private String gender;

    /**
     * 业务类别  01：认证模式为 id_kind + id_no + area_code + full_name
     * <p>
     * 02：认证模式为 id_kind + id_no + area_code + birthday
     * <p>
     * 03：认证模式为 id_kind + id_no + area_code + csfc_id_enddate + full_name + birthday + gender
     * 默认值 01
     */
    private String csdc_busi_kind = "01";

    /**
     * 是否实时调用公安认证 1：是；0：否
     */
    private String realtime_flag;

    /**
     * 大头照base64 csdc_busi_kind=04时必传
     * <p>
     * 查询类型为14必填，13不填
     */
    private String base64_image;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;
    /**
     * 操作员营业部编号
     */
    private String op_branch_no;

    /**
     * 操作员营业部名称
     */
    private String op_branch_name;

    /**
     * 用户营业部名称
     */
    private String branch_name;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;

    /******************************************国泰*******************************/

    /**
     * 查询类型 13-中登出入境证件信息核查；14-证通出入境证件信息核查；注：查询类型为14的时候hclb不填
     */
    private String gt_cxlx;

    /**
     * 核查类别 11-(01证件与客户名称核查);12-(02证件与出生日期核查);13-(03完整信息核查);14-(04证件+客户名称+人像核查)
     */
    private String gt_hclb;
}
package com.cairh.cpe.common.constant;

import lombok.Getter;

/**
 * 规则关系
 */
@Getter
public enum RelationShipEnum {

    AND("and", "且"),
    OR("or", "或");

    private final String relationShip;
    private final String relationShipDesc;

    RelationShipEnum(String relationShip, String relationShipDesc) {
        this.relationShip = relationShip;
        this.relationShipDesc = relationShipDesc;
    }

}

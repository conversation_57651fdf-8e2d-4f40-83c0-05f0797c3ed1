<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cairh.cpe.common.mapper.BusinFlowParamsMapper">


    <update id="restParams" parameterType="string">
        UPDATE BUSINFLOWPARAMS
        SET BUSI_CONTENT = REGEXP_REPLACE(REGEXP_REPLACE(
        REGEXP_REPLACE(BUSI_CONTENT, '"request_status":"([^"]*)"', '"request_status":"1"', 1, 0),
        '"operator_no":"([^"]*)"', '"operator_no":" "', 1, 0),'"operator_name":"([^"]*)"', '"operator_name":" "', 1, 0)
        WHERE REQUEST_NO = #{request_no}
    </update>
</mapper>
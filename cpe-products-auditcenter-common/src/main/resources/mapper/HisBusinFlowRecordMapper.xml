<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cairh.cpe.common.mapper.HisBusinFlowRecordMapper">

    <select id="qryUserApplyRecord" resultType="com.cairh.cpe.common.entity.response.BusinFlowRecordResp">
        SELECT
        a.serial_id,
        a.create_datetime,
        a.business_flag,
        a.business_remark,
        a.app_id,
        (case
        when a.record_type = '2' then '系统管理员'
        else a.operator_no end)
        operator_no,
        b.branch_no,
        b.client_name,
        b.mobile_tel,
        b.id_no,
        b.id_kind,
        b.channel_code,u.channel_name,a.busi_content
        FROM his_businflowrecord a LEFT JOIN his_businflowrequest b ON a.request_no = b.request_no
        left join his_userqueryextinfo u on a.request_no = u.request_no
        where 1=1 and a.business_flag != 0

        <include refid="task_condition"/>
    </select>

    <sql id="task_condition">
        <if test="queryForm.request_no != null and queryForm.request_no != ''">
            and a.request_no = #{queryForm.request_no}
        </if>
        <if test="queryForm.record_type != null and queryForm.record_type!=''">
            and a.record_type = #{queryForm.record_type}
        </if>
        <if test="queryForm.task_types!=null and queryForm.task_types.size()!=0">
            and a.anode_id IN
            <foreach collection="queryForm.task_types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.business_flags!=null and queryForm.business_flags.size()!=0">
            and a.business_flag IN
            <foreach collection="queryForm.business_flags" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by a.create_datetime desc,a.serial_id desc
    </sql>
</mapper>
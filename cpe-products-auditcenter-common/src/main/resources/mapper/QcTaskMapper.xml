<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cairh.cpe.common.mapper.QcTaskMapper">

    <!-- 获取相关操作人集合-->
    <select id="selectQcTaskOperators" resultType="java.lang.String" parameterType="map">
        select actual_quality_operator_no as operator_no
        from QCTASK
        where
        quality_operator_finish_datetime > #{finish_datetime}
        <if test="operator_nos !=null and operator_nos.size()!=0">
            and actual_quality_operator_no IN
            <foreach collection="operator_nos" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        union all
        select actual_quality_operator_no as operator_no
        from QCTASK
        where
        review_quality_finish_datetime > #{finish_datetime}
        <if test="operator_nos !=null and operator_nos.size()!=0">
            and review_quality_operator_no IN
            <foreach collection="operator_nos" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cairh.cpe.common.mapper.UserQueryExtInfoMapper">


    <update id="resetExtInfo" parameterType="com.cairh.cpe.common.entity.BusinFlowTask">
        update CRH_AC.userqueryextinfo
        set request_status       = '1',
            AUDIT_OPERATOR_NO    = case when #{businFlowTask.task_type} = 'audit' then ' ' else AUDIT_OPERATOR_NO end,
            audit_operator_name  = case when #{businFlowTask.task_type} = 'audit' then ' ' else audit_operator_name end,
            review_operator_no   = case when #{businFlowTask.task_type} = 'review' then ' ' else review_operator_no end,
            review_operator_name = case
                                       when #{businFlowTask.task_type} = 'review' then ' '
                                       else review_operator_name end,
            double_operator_no   = case
                                       when #{businFlowTask.task_type} = 'secondary_review' then ' '
                                       else double_operator_no end,
            double_operator_name = case
                                       when #{businFlowTask.task_type} = 'secondary_review' then ' '
                                       else double_operator_name end
        where request_no = #{businFlowTask.request_no}
    </update>
</mapper>
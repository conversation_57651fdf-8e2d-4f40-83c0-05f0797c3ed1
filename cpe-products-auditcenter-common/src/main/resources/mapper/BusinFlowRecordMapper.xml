<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cairh.cpe.common.mapper.BusinFlowRecordMapper">

    <select id="qryUserApplyRecord" resultType="com.cairh.cpe.common.entity.response.BusinFlowRecordResp">
        SELECT
        a.serial_id,
        a.create_datetime,
        a.business_flag,
        a.business_remark,
        a.app_id,
        (case
        when a.record_type = '2' then '系统管理员'
        else a.operator_no end)
        operator_no,
        b.branch_no,
        b.client_name,
        b.mobile_tel,
        b.id_no,
        b.id_kind,
        b.channel_code,u.channel_name,a.busi_content
        FROM businflowrecord a LEFT JOIN businflowrequest b ON a.request_no = b.request_no
        left join userqueryextinfo u on a.request_no = u.request_no
        where 1=1 and a.business_flag != 0

        <include refid="task_condition"/>
    </select>

    <sql id="task_condition">
        <if test="queryForm.request_no != null and queryForm.request_no != ''">
            and a.request_no = #{queryForm.request_no}
        </if>
        <if test="queryForm.record_type != null and queryForm.record_type!=''">
            and a.record_type = #{queryForm.record_type}
        </if>
        <if test="queryForm.task_types!=null and queryForm.task_types.size()!=0">
            and a.anode_id IN
            <foreach collection="queryForm.task_types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.business_flags!=null and queryForm.business_flags.size()!=0">
            and a.business_flag IN
            <foreach collection="queryForm.business_flags" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by a.create_datetime desc,a.serial_id desc
    </sql>


    <select id="selectRecordListByPage" resultType="com.cairh.cpe.common.entity.response.BusinFlowRecordResp">
        SELECT
        a.serial_id,
        a.create_datetime,
        a.business_flag,
        a.business_remark,
        u.app_id,
        (case
        when a.record_type = '2' then '系统管理员'
        else a.operator_no end)
        operator_no,
        u.branch_no,
        u.client_name,
        u.mobile_tel,
        u.id_no,
        u.anode_id,
        u.id_kind,
        u.channel_code,
        u.request_status,
        u.busin_type,
        u.video_type,
        u.open_channel,
        u.channel_name,
        u.activity_name,
        u.marketing_team
        FROM businflowrecord a
        left join userqueryextinfo u on a.request_no = u.request_no and u.is_snapshot = '0'
        where  a.business_flag != 0
        <include refid="task_condition_menu"/>
    </select>

    <sql id="task_condition_menu">
        <if test="queryForm.record_type != null and queryForm.record_type!=''">
            and a.record_type = #{queryForm.record_type}
        </if>
        <if test="queryForm.operator_no != null and queryForm.operator_no!=''">
            and a.operator_no = #{queryForm.operator_no}
        </if>
        <if test="queryForm.operator_name != null and queryForm.operator_name!=''">
            and a.operator_name like '%' || #{queryForm.operator_name} || '%'
        </if>

        <if test="queryForm.open_channel != null and queryForm.open_channel!=''">
            and u.open_channel like '%' || #{queryForm.open_channel} || '%'
        </if>

        <if test="queryForm.video_type != null and queryForm.video_type!=''">
            and u.video_type = #{queryForm.video_type}
        </if>
        <if test="queryForm.client_name != null and queryForm.client_name!=''">
            and u.client_name =  #{queryForm.client_name}
        </if>
        <if test="queryForm.id_no != null and queryForm.id_no!=''">
            and u.id_no = #{queryForm.id_no}
        </if>

        <if test="queryForm.channel_code != null and queryForm.channel_code!=''">
            and u.channel_code = #{queryForm.channel_code}
        </if>
        <if test="queryForm.mobile_tel != null and queryForm.mobile_tel!=''">
            and u.mobile_tel = #{queryForm.mobile_tel}
        </if>

        <if test="queryForm.create_datetime_start != null and queryForm.create_datetime_start!=''">
            and a.create_datetime &gt;= ${queryForm.create_datetime_start}
        </if>

        <if test="queryForm.create_datetime_end != null and queryForm.create_datetime_end!=''">
            and a.create_datetime &lt; ${queryForm.create_datetime_end}
        </if>
        <if test="queryForm.broker_code != null and queryForm.broker_code != ''">
            and u.broker_code = #{queryForm.broker_code}
        </if>
        <if test="queryForm.busin_type != null and queryForm.busin_type != ''">
            and u.busin_type = #{queryForm.busin_type}
        </if>

        <if test="queryForm.activity_name != null and queryForm.activity_name != ''">
            and u.activity_name like '%' || #{queryForm.activity_name} || '%'
        </if>

        <if test="queryForm.marketing_team != null and queryForm.marketing_team != ''">
            and u.marketing_team like '%' || #{queryForm.marketing_team} || '%'
        </if>

        <if test="queryForm.business_flags!=null and queryForm.business_flags.size()!=0">
            and a.business_flag IN
            <foreach collection="queryForm.business_flags" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="queryForm.app_ids!=null and queryForm.app_ids.size()!=0">
            and u.app_id IN
            <foreach collection="queryForm.app_ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


        <if test="queryForm.request_status_list!=null and queryForm.request_status_list.size()!=0">
            and u.request_status IN
            <foreach collection="queryForm.request_status_list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="queryForm.task_types!=null and queryForm.task_types.size()!=0">
            and u.anode_id IN
            <foreach collection="queryForm.task_types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


        <if test="queryForm.id_kinds!=null and queryForm.id_kinds.size()!=0">
            and u.id_kind IN
            <foreach collection="queryForm.id_kinds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="queryForm.branch_nos!=null and queryForm.branch_nos.size()!=0">
            and u.branch_no IN
            <foreach collection="queryForm.branch_nos" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.operator_branch!=null and queryForm.operator_branch.size()!=0">
            and u.branch_no IN
            <foreach collection="queryForm.operator_branch" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order  by a.serial_id desc
    </sql>
</mapper>